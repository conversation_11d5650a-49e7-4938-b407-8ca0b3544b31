version: '3.8'

services:
  app:
    image: superegowebapp.azurecr.io/dev:latest
    env_file:
      - .env.dev
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: doppler run -- uvicorn app.main:app --host 0.0.0.0 --reload --port 8080 --timeout-keep-alive 60
    depends_on:
      - redis

  worker:
    image: superegowebapp.azurecr.io/dev:latest
    command: doppler run -- celery -A app.core.utils.workers.celery worker --concurrency=1
    env_file:
      - .env.dev
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis

  redis:
    image: redis:latest

  dashboard:
    image: superegowebapp.azurecr.io/dev:latest
    command: celery --broker=redis://redis:6379/0 flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis
      - worker

  nginx:
    image: superegowebapp.azurecr.io/nginx:latest
    ports:
      - 80:80
    depends_on:
      - app
      - dashboard
