version: '3.8'

services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile.debug
    ports:
      - 8080:8080
      - 5678:5678
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: doppler run -- sh -c "python /tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 -m uvicorn app.main:app --host 0.0.0.0 --reload --port 8080 --timeout-keep-alive 60"
    volumes:
      - .:/usr/src/app
    depends_on:
      - redis

  worker:
    build: .
    command: doppler run -- celery -A app.core.utils.workers.celery worker --concurrency=1 --loglevel=info
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis

  redis:
    image: redis:latest
    ports:
      - 6379:6379

  dashboard:
    build: .
    command: celery --broker=redis://redis:6379/0 flower --port=5555
    ports:
      - 5555:5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis
      - worker
