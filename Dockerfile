FROM python:3.11-slim

WORKDIR /usr/src/app

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1
# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    apt-transport-https \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Doppler CLI
RUN curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | gpg --dearmor -o /usr/share/keyrings/doppler-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/doppler-archive-keyring.gpg] https://packages.doppler.com/public/cli/deb/debian any-version main" | tee /etc/apt/sources.list.d/doppler-cli.list && \
    apt-get update && \
    apt-get -y install doppler && \
    rm -rf /var/lib/apt/lists/*

# Install core Python packages first
RUN pip install --upgrade pip --no-cache-dir
RUN pip install --no-cache-dir fastapi uvicorn pydantic python-dotenv

# Copy application code
COPY . .

# Install remaining dependencies with retries and timeout handling
RUN pip install --no-cache-dir --timeout 300 --retries 3 \
    openai \
    redis \
    celery \
    flower \
    requests \
    aiohttp \
    httpx \
    || echo "Some packages failed to install but continuing..."