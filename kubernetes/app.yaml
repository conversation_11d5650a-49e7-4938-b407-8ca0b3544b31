apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: app
  name: app-deployment
  namespace: rehoboam
spec:
  replicas: 3
  selector:
    matchLabels:
      app: app
  template:
    metadata:
      labels:
        app: app
    spec:
      containers:
        - args:
            - doppler
            - run
            - --
            - uvicorn
            - app.main:app
            - --host
            - 0.0.0.0
            - --reload
            - --port
            - "8080"
            - --timeout-keep-alive
            - "60"
          env:
            - name: CELERY_BROKER_URL
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
            - name: CELERY_RESULT_BACKEND
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
          envFrom:
            - secretRef:
                name: doppler-token
          image: us-docker.pkg.dev/subconsciousai-shared/rehoboam/app
          imagePullPolicy: Always
          name: app
          ports:
            - name: default
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: default
            failureThreshold: 1
            periodSeconds: 10
          startupProbe:
            httpGet:
              path: /
              port: default
            failureThreshold: 30
            periodSeconds: 15
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: app
  name: app-service
  namespace: rehoboam
spec:
  ports:
    - name: "8080"
      port: 8080
      targetPort: 8080
  selector:
    app: app
