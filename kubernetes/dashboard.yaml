apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: dashboard
  name: dashboard-deployment
  namespace: rehoboam
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dashboard
  strategy: {}
  template:
    metadata:
      labels:
        app: dashboard
    spec:
      containers:
        - args:
            - celery
            - --broker=redis://redis-service.rehoboam.svc.cluster.local:6379/0
            - flower
            - --port=5555
            - --purge_offline_workers=10
          env:
            - name: CELERY_BROKER_URL
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
            - name: CELERY_RESULT_BACKEND
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
          image: us-docker.pkg.dev/subconsciousai-shared/rehoboam/app
          imagePullPolicy: Always
          name: dashboard
          ports:
            - name: default
              containerPort: 5555
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: default
            failureThreshold: 1
            periodSeconds: 10
          startupProbe:
            httpGet:
              path: /
              port: default
            failureThreshold: 30
            periodSeconds: 10
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: dashboard
  name: dashboard-service
  namespace: rehoboam
spec:
  ports:
    - name: "5555"
      port: 5555
      targetPort: 5555
  selector:
    app: dashboard
