apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: rehoboam
data:
  nginx.conf: |
    events {}
    http {
        server {
            listen 80;

            location / {
                proxy_pass http://app-service.rehoboam.svc.cluster.local:8080;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
            }

            location /dashboard {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555;
                proxy_set_header Host $host;
                proxy_redirect off;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            location /tasks {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555/tasks;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            location /task {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555/task;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            location /broker {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555/broker;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            location /worker {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555/worker;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }

            location /static/js {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555;
            }

            location /static/css {
                proxy_pass http://dashboard-service.rehoboam.svc.cluster.local:5555;
            }
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: nginx
  name: nginx-deployment
  namespace: rehoboam
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
        - args:
            - nginx
            - -g
            - daemon off;
          name: nginx
          image: nginx:latest
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
      volumes:
        - name: nginx-config
          configMap:
            name: nginx-config
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: nginx
  name: nginx-service
  namespace: rehoboam
spec:
  ports:
    - name: "80"
      port: 80
      targetPort: 80
  type: LoadBalancer
  selector:
    app: nginx
