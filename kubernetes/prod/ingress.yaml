apiVersion: v1
kind: Service
metadata:
  name: nginx-np-service
  namespace: rehoboam
spec:
  type: NodePort
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      name: https
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress
  namespace: rehoboam
  annotations:
    kubernetes.io/ingress.global-static-ip-name: api
    networking.gke.io/managed-certificates: api-certificate
    networking.gke.io/v1beta1.FrontendConfig: "http-to-https"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.allow-http: “false”
    ingressClassName: "gce"
spec:
  defaultBackend:
    service:
      name: nginx-np-service
      port:
        number: 80
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: api-certificate
  namespace: rehoboam
spec:
  domains:
    - api.subconscious.ai
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  namespace: rehoboam
  name: http-to-https
spec:
  redirectToHttps:
    enabled: true
    responseCodeName: MOVED_PERMANENTLY_DEFAULT
