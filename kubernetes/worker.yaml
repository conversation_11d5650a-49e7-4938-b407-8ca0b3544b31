apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: worker
  name: worker-deployment
  namespace: rehoboam
spec:
  replicas: 3
  selector:
    matchLabels:
      app: worker
  template:
    metadata:
      labels:
        app: worker
    spec:
      containers:
        - args:
            - doppler
            - run
            - --
            - celery
            - -A
            - app.core.utils.workers.celery
            - worker
            - --loglevel=info
            - --concurrency=1
          env:
            - name: CELERY_BROKER_URL
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
            - name: CELERY_RESULT_BACKEND
              value: redis://redis-service.rehoboam.svc.cluster.local:6379/0
          envFrom:
            - secretRef:
                name: doppler-token
          image: us-docker.pkg.dev/subconsciousai-shared/rehoboam/app
          imagePullPolicy: Always
          name: worker-service
      restartPolicy: Always
