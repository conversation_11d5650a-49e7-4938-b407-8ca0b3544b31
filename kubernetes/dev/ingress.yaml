apiVersion: v1
kind: Service
metadata:
  name: nginx-np-service-dev
  namespace: rehoboam
spec:
  type: NodePort
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      name: https
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress-dev
  namespace: rehoboam
  annotations:
    kubernetes.io/ingress.global-static-ip-name: api
    networking.gke.io/managed-certificates: api-certificate-dev
    networking.gke.io/v1beta1.FrontendConfig: "http-to-https-dev"
    kubernetes.io/ingress.allow-http: "true"
    ingressClassName: "gce"
spec:
  defaultBackend:
    service:
      name: nginx-np-service-dev
      port:
        number: 80
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: api-certificate-dev
  namespace: rehoboam
spec:
  domains:
    - api.dev.subconscious.ai
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  name: http-to-https-dev
  namespace: rehoboam
spec:
  redirectToHttps:
    enabled: true
    responseCodeName: MOVED_PERMANENTLY_DEFAULT
