version: '3.8'

services:
  # base:
  #   build:
  #     file: Dockerfile.rpybase
  #     context: .
  #     x-bake:
  #       platforms:
  #       - linux/amd64
  #       - linux/arm64
  #     tags:
  #     - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/base:latest

  app:
    build:
      context: .
      x-bake:
        platforms:
        - linux/amd64
        - linux/arm64
      tags:
      - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/rehoboam:latest
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - 8080:8080
    command: doppler run -- uvicorn app.main:app --host 0.0.0.0 --reload --port 8080 --timeout-keep-alive 60
    depends_on:
      - redis

  worker:
    build:
      context: .
      x-bake:
        platforms:
        - linux/amd64
        - linux/arm64
      tags:
      - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/rehoboam:latest
      - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/rehoboam:2
    env_file:
      - .env
    command: doppler run -- celery -A app.core.utils.workers.celery worker --loglevel=info --concurrency=1
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis

  redis:
    image: redis:7

  dashboard:
    build:
      context: .
      tags:
      - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/rehoboam:latest
      - us-central1-docker.pkg.dev/nonprod-442916/rehoboam/rehoboam:2
      x-bake:
        platforms:
        - linux/amd64
        - linux/arm64
    command: celery --broker=redis://redis:6379/0 flower --port=5555
    ports:
      - 5555:5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - app
      - redis
      - worker
