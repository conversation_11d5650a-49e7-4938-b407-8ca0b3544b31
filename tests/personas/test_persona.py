from app.api.v1.helpers.personas import TraitLevel, choose_trait_levels


# TODO: This obviously isn't a real test - was useful for debugging, and thus we're keeping it.
# TODO: Turn into real test.
def test_choose_trait_levels():
    levels = {
        "age": [generate_level("age", i) for i in range(10, 0, -1)],
        "income": [generate_level("income", i) for i in range(10)],
        "sex": [generate_level("sex", i) for i in range(10)],
    }

    for l in levels["sex"]:
        l.ordinal_rank = None
        l.measurement_type = "nominal"

    levels["income"][0].ordinal_rank = None

    for i in range(15):
        out = choose_trait_levels(levels, i)
        print(out)
    return


def generate_level(type: str, ordinal_rank: int) -> TraitLevel:
    return TraitLevel(
        id=f"id-{ordinal_rank}",
        type=type,
        measurement_type="ordinal",
        ordinal_rank=ordinal_rank,
        set_type=None,
        short_description="blah",
        long_description="blah",
    )
