{"survey_choices": {"7168": [2, 6, 12, 16, 23, 28], "6151": [1, 9, 14, 16, 20, 26], "527": [0, 5, 14, 16, 20, 27], "12304": [3, 9, 13, 17, 20, 29], "8211": [2, 8, 10, 18, 22, 26], "2581": [0, 9, 10, 18, 21, 26], "10795": [3, 7, 11, 16, 24, 25], "7726": [2, 7, 11, 19, 20, 26], "4146": [1, 6, 13, 15, 24, 26], "13877": [4, 7, 11, 15, 20, 27], "12341": [3, 9, 13, 18, 23, 26], "15415": [4, 9, 13, 16, 23, 25], "6714": [2, 5, 13, 18, 22, 29], "14396": [4, 8, 10, 15, 24, 26], "2120": [0, 8, 11, 19, 24, 25], "12374": [3, 9, 13, 19, 24, 29], "5727": [1, 9, 10, 19, 20, 27], "15461": [4, 9, 13, 18, 22, 26], "4213": [1, 6, 13, 18, 22, 28], "4730": [1, 7, 12, 19, 21, 25], "8316": [2, 8, 11, 17, 23, 26], "2687": [0, 9, 11, 17, 22, 27], "647": [0, 6, 10, 15, 24, 27], "13970": [4, 7, 11, 18, 24, 25], "153": [0, 5, 11, 16, 20, 28], "9890": [3, 5, 14, 15, 23, 25], "7843": [2, 7, 12, 18, 23, 28], "13481": [4, 6, 12, 19, 21, 26], "6322": [2, 5, 10, 17, 24, 27], "14531": [4, 8, 11, 16, 21, 26], "14536": [4, 8, 11, 16, 22, 26], "6857": [2, 5, 14, 19, 21, 27], "15051": [4, 9, 10, 17, 20, 26], "15568": [4, 9, 14, 17, 23, 28], "5862": [1, 9, 11, 19, 22, 27], "6891": [2, 6, 10, 15, 23, 26], "15087": [4, 9, 10, 18, 22, 27], "6907": [2, 6, 10, 16, 21, 27], "1291": [0, 7, 10, 16, 23, 26], "10532": [3, 6, 14, 16, 21, 27], "11049": [3, 7, 13, 16, 24, 29], "4909": [1, 7, 14, 16, 21, 29], "6969": [2, 6, 10, 18, 23, 29], "2376": [0, 8, 14, 15, 20, 26], "1871": [0, 7, 14, 19, 24, 26], "1877": [0, 8, 10, 15, 20, 27], "3424": [1, 5, 12, 16, 24, 29], "5476": [1, 8, 13, 19, 20, 26], "6504": [2, 5, 12, 15, 20, 29], "9578": [3, 5, 11, 18, 20, 28], "1388": [0, 7, 11, 15, 22, 28], "3439": [1, 5, 12, 17, 22, 29], "12148": [3, 9, 12, 15, 24, 28], "2934": [0, 9, 13, 17, 21, 29], "11127": [3, 7, 14, 15, 20, 27], "7543": [2, 7, 10, 16, 23, 28], "2937": [0, 9, 13, 17, 22, 27], "15222": [4, 9, 11, 18, 24, 27], "4984": [1, 7, 14, 19, 21, 29], "11642": [3, 8, 13, 15, 23, 27], "7038": [2, 6, 11, 16, 22, 28], "9099": [2, 9, 12, 18, 24, 29], "9629": [3, 5, 12, 15, 20, 29], "9124": [2, 9, 12, 19, 24, 29], "6053": [1, 9, 13, 17, 20, 28], "10664": [3, 7, 10, 16, 22, 29], "13226": [4, 6, 10, 19, 20, 26], "5546": [1, 8, 14, 16, 24, 26], "1963": [0, 8, 10, 18, 22, 28], "10155": [3, 6, 11, 16, 21, 25], "8626": [2, 8, 14, 15, 20, 26], "1972": [0, 8, 10, 18, 24, 27], "3509": [1, 5, 13, 15, 21, 29], "11722": [3, 8, 13, 18, 24, 27], "6619": [2, 5, 12, 19, 23, 29], "2012": [0, 8, 11, 15, 22, 27], "6629": [2, 5, 13, 15, 20, 29], "14317": [4, 7, 14, 17, 23, 27], "4087": [1, 6, 12, 18, 22, 27], "6649": [2, 5, 13, 15, 24, 29]}, "experimentor why question type": "Policy", "experimentor why question prompt": "I would like to understand US Immigration Policy.", "attributes_and_levels_lookup": {"get_attribute_text": {"0": "Prior trips to the United States", "1": "Country of Origin", "2": "Language Skills", "3": "Profession", "4": "Employment Plans", "5": "Education Level"}, "get_level_ids": {"0": [0, 1, 2, 3, 4], "1": [5, 6, 7, 8, 9], "2": [10, 11, 12, 13, 14], "3": [15, 16, 17, 18, 19], "4": [20, 21, 22, 23, 24], "5": [25, 26, 27, 28, 29]}, "get_attribute_id": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 3, "16": 3, "17": 3, "18": 3, "19": 3, "20": 4, "21": 4, "22": 4, "23": 4, "24": 4, "25": 5, "26": 5, "27": 5, "28": 5, "29": 5}, "get_level_text": {"0": "Never been to US", "1": "Entered US once before on tourist visa", "2": "Entered US once before without proper authorization", "3": "Has visited US many times on tourist visa", "4": "Spent six months with family members in the US", "5": "France", "6": "Mexico", "7": "China", "8": "Somalia", "9": "Iraq", "10": "Fluent English", "11": "Broken English", "12": "<PERSON><PERSON> but unable to speak English", "13": "Only able to speak via interpreter", "14": "No English skills", "15": "Teacher", "16": "<PERSON><PERSON>", "17": "Construction Worker", "18": "Doctor", "19": "Computer Programmer", "20": "Has a contract with a US employer", "21": "Does not have a contract with a US employer but has done job interviews", "22": "Will look for work when arriving in US", "23": "Has no plans to work in the US", "24": "Is unable to work", "25": "Has no formal education", "26": "Has a US eighth grade education", "27": "Has a US high school education", "28": "Has a college degree", "29": "Has a graduate degree"}}, "personas_lookup": {"age": {"3": ["Age-25", " My age is between 18 and 25"], "5": ["Age-45", " My age is between 35 and 45"], "7": ["Age-65", " My age is between 55 and 65"]}, "income": {"18": ["Income-20K", "My annual family income is less than 20K dollars"], "20": ["Income-60K", "My annual family income is between 40K and 60K dollars"], "23": ["Income-250K", "My annual family income is between 150K and 250K dollars"], "25": ["Income-500K", "My annual family income is more than 500K dollars"]}, "gender": {"26": ["Gender-Female", "I identify as a Female"], "27": ["Gender-Male", "I identify as a Male"]}}, "task_consistency": 0.8042203985932005, "task_failure_rate": 0.11145833333333334, "gpt3_response_parse_error_rate": 0.11145833333333334, "task_logs": {"0": {"(3, 18, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is less than 20K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is less than 20K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is between 40K and 60K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is between 40K and 60K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is between 150K and 250K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is between 150K and 250K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is more than 500K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 18 and 25. My annual family income is more than 500K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is less than 20K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is less than 20K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is between 40K and 60K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is between 40K and 60K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is between 150K and 250K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is between 150K and 250K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is more than 500K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 35 and 45. My annual family income is more than 500K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is less than 20K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is less than 20K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 20, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is between 40K and 60K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is between 40K and 60K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is between 150K and 250K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is between 150K and 250K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is more than 500K dollars. I identify as a Female. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "Respondent Instructions: Imagine you are a human with the following characteristics - My age is between 55 and 65. My annual family income is more than 500K dollars. I identify as a Male. Imagine you live in the United States in 2015.  \nInstructions: Respond to a Policy questionaire about I would like to understand US Immigration Policy. \nConsider Options A, B and C in the Policy Questionaire below. Please read the descriptions of the potential immigrants carefully. Then, please indicate which of the two immigrants you would personally prefer to see admitted to the United StatesWrite your response as a single letter.. \n\nPolicy Questionaire:\nOption A: Never been to US, and China, and No English skills, and Computer Programmer, and Is unable to work, and Has a US eighth grade education.\nOption B: Has visited US many times on tourist visa, and Iraq, and <PERSON>es but unable to speak English, and Teacher, and Is unable to work, and Has a college degree.\nOption C: Neither of these.\nReponse: I choose option", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "1": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "2": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "3": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "4": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "5": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}}, "6": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "7": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "8": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "9": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "10": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "11": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "12": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "13": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "16": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "17": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "18": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "19": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "20": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "21": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "22": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "24": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "25": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "27": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "28": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "29": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "30": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "31": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "32": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "33": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}}, "34": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "35": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "37": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "38": {"(3, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "14": {"(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "23": {"(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}}, "36": {"(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(5, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 18, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}, "(7, 25, 26)": {"prompt": "", "gpt3_response_raw": " C.", "gpt3_response_parsed": "C"}, "(7, 25, 27)": {"prompt": "", "gpt3_response_raw": " B.", "gpt3_response_parsed": "B"}}, "39": {"(3, 18, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 25, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 20, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(7, 23, 27)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}, "26": {"(3, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(3, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 20, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}, "(5, 23, 26)": {"prompt": "", "gpt3_response_raw": " A.", "gpt3_response_parsed": "A"}}}, "average_seconds_per_task": 0.33576013495524726}