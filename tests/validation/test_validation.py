import json
from pathlib import Path

import pytest

from app.core.global_config import settings
from app.validation.validation import ValidationMetrics


@pytest.fixture()
def survey_data_path():
    csv_path = settings.TEST_RESOURCES_DIRECTORY / Path(
        "modelling/hainmueller-survey_results-2whc6w9m-restful-dew-403.csv"
    )
    return csv_path


@pytest.fixture()
def experiment_metrics_values():
    with open(
        settings.TEST_RESOURCES_DIRECTORY / Path("validation/experiment_metrics.json"),
        "r",
    ) as f:
        experiment_metrics_values = json.load(f)
    return experiment_metrics_values


@pytest.fixture()
def randomness_metrics_values():
    with open(
        settings.TEST_RESOURCES_DIRECTORY / Path("validation/randomness_metrics.json"),
        "r",
    ) as f:
        randomness_metrics_values = json.load(f)
    return randomness_metrics_values


@pytest.fixture()
def model_metrics_values():
    with open(
        settings.TEST_RESOURCES_DIRECTORY / Path("validation/model_metrics.json"), "r"
    ) as f:
        model_metrics_values = json.load(f)
    return model_metrics_values


@pytest.fixture()
def human_baseline_metrics_values():
    with open(
        settings.TEST_RESOURCES_DIRECTORY
        / Path("validation/human_baseline_metrics.json"),
        "r",
    ) as f:
        human_baseline_metrics_values = json.load(f)
    return human_baseline_metrics_values


def test_validate_data_generation(experiment_metrics_values):
    v = ValidationMetrics()
    metric = v.data_generation_metrics(
        experiment_metrics_values["parse_error_rate"],
        experiment_metrics_values["task_consistency"],
        experiment_metrics_values["task_failure_rate"],
    )
    test = True
    if not metric:
        test = False

    assert metric is False
    assert test is False


def test_validate_randomness(randomness_metrics_values):
    v = ValidationMetrics()
    metric = v.randomness_metrics(
        randomness_metrics_values["Bartels Rank: Noise Metric"],
        randomness_metrics_values["Wald Wolfowitz: Noise Metric"],
    )
    test = True
    if not metric:
        test = False

    assert metric is True
    assert test is True


def test_validate_model(model_metrics_values):
    v = ValidationMetrics()
    metric = v.model_metrics(
        model_metrics_values["VIF factor Ols"],
        model_metrics_values["Residual Normality Ols p-value"],
        model_metrics_values["Number of Outliers"],
    )
    test = True
    if not metric:
        test = False

    assert metric is False
    assert test is False


def test_human_baseline(human_baseline_metrics_values):
    v = ValidationMetrics()
    metric = v.human_baseline_metric(human_baseline_metrics_values["Spearman P-Value"])
    test = True
    if not metric:
        test = False

    assert metric is False
    assert test is False


def test_survey_data(survey_data_path):
    v = ValidationMetrics()
    survey_metric = v.inference_metric(survey_data_path)
    persona_metric = v.persona_clustering_metric(survey_data_path)
    survey_test = bool(survey_metric)
    persona_test = bool(persona_metric)
    assert survey_metric is True
    assert survey_test
    assert not persona_metric
    assert not persona_test
