import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app


class BaseTestCase:
    @pytest.fixture(scope="function")
    def client(self):
        return TestClient(app)

    @pytest.fixture(scope="function")
    async def async_client(self):
        async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
            yield ac

    @pytest.fixture(autouse=True)
    def setup(self, client, async_client, mock_llm, mock_novu):
        self.client = client
        self.async_client = async_client
        self.mock_llm = mock_llm
        self.mock_novu = mock_novu

    def assert_successful_response(self, response, expected_status_code=200):
        assert response.status_code == expected_status_code, (
            f"Expected status code {expected_status_code}, but got"
            f" {response.status_code}"
        )
        assert response.json() is not None, "Response body is empty"
