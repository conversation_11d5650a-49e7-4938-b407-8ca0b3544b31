import os
import sys
from unittest.mock import MagicMock

import pytest

os.environ["COHERE_PRODUCTION"] = "mock_api_key"
os.environ["ANYSCALE_PRODUCTION"] = "mock_api_key"
os.environ["ANTHROPIC_PROD"] = "mock_api_key"
os.environ["COHERE_API_KEY"] = "mock_api_key"
os.environ["NOVU_API_KEY"] = "mock_novu_api_key"

sys.modules["novu"] = MagicMock()
sys.modules["novu.api"] = MagicMock()
sys.modules["novu.api.event"] = MagicMock()
sys.modules["novu.config"] = MagicMock()

from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.main import app


@pytest.fixture(scope="module")
def client():
    return TestClient(app)


@pytest.fixture(scope="module")
async def async_client():
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture(scope="function")
def mock_novu(mocker):
    return mocker.patch("app.core.utils.novu.EventApi")


@pytest.fixture(scope="function")
def mock_llm(mocker):
    return mocker.patch("app.llm_prompt.prompt_executor.LCELPromptExecutor")
