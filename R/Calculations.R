# Importing libraries
library(data.table)
library(tidyr)
library(plyr)
library(dplyr)
library(stringr)
library(survival)
library(magrittr)
library(foreach)
library(doParallel)
library(openai)
library(performance)
library(stringdist)
library(httr)
library(jsonlite)

# Getting the experiment files paths
args <- commandArgs(trailingOnly = TRUE)
json_file_path <- args[1]
csv_file_path <- args[2]
coefs_file_path <- args[3]
output_path_dashboard <- args[4]
output_AMCE <- args[5]

# Reading the files using the provided paths
csv <- read.csv(csv_file_path)
json <- jsonlite::fromJSON(json_file_path)
exp_type <- json$is_hb_run

if (exp_type == TRUE){
  
  # Reading the files using the provided paths
  coefs_precalc <- read.csv(coefs_file_path)
  
} else{
  # Getting the current working directory
  r_dir <- normalizePath(dirname(sub("--file=", "", commandArgs()[4])))
  project_root <- normalizePath(file.path(r_dir, ".."))
  r_path <- file.path(project_root, "R")
  
  cat(paste0("\nCWD in R: ", getwd(), "\n"))
  cat(paste0("R Directory: ", r_dir, "\n"))
  cat(paste0("R Files Path: ", r_path, "\n"))
  cat(paste0("Project Root Directory: ", project_root, "\n"))
  
  
  # Setting the path to the Prompts folder
  prompts_path <- file.path(r_path, "Prompts")
  
  # Setting the path to the prompts txt files
  
  # Tab 1 Graphs
  exp1_path <- file.path(prompts_path, "1_Feature_Importance.txt")
  exp2_path <- file.path(prompts_path, "2_Feature_Level_Importance.txt")
  amce_title_path <- file.path(prompts_path, "2_Feature_Level_Importance_Graph_Title.txt")
  exp3_path <- file.path(prompts_path, "3_Feature_Level_WTP.txt")
  
  # Tab 3 Graphs
  exp_t_path <- file.path(prompts_path, "4_Feature_Level_Importance_per_Trait.txt")
  
  # Tab 4 Graphs
  prompt_input_path <- file.path(prompts_path, "5_Mindset_Title.txt")
  exp11_path <- file.path(prompts_path, "6_Feature_Importance_per_Mindset.txt")
  exp8_path <- file.path(prompts_path, "7_Traits_by_Mindset.txt")
  exp9_path <- file.path(prompts_path, "8_Feature_Level_Importance_per_Mindset.txt")
  exp10_path <- file.path(prompts_path, "9_Feature_Level_WTP_per_Mindset.txt")
  
  # Tab Insights
  ins_1_path <- file.path(prompts_path, "Tab1_Insights.txt")
  ans_1_path <- file.path(prompts_path, "Tab1_Answer.txt")
  ins_3_path <- file.path(prompts_path, "Tab3_Insights.txt")
  ins_4_path <- file.path(prompts_path, "Tab4_Insights.txt")
  
  # Tab 5 Insights
  prompt_input_q1_path <- file.path(prompts_path, "Tab5_Q1.txt")
  prompt_input_q2_path <- file.path(prompts_path, "Tab5_Q2.txt")
  prompt_input_q3_path <- file.path(prompts_path, "Tab5_Q3.txt")
  prompt_input_q4_path <- file.path(prompts_path, "Tab5_Q4.txt")
  
  
  # Checking if the experiment files exist
  cat("Verifying experiment files existence...\n")
  cat(file.exists(json_file_path), json_file_path, "\n")
  cat(file.exists(csv_file_path), csv_file_path, "\n")
  
  # Checking if the prompts txt files exist
  cat("Verifying R txt files existence...\n")
  cat(file.exists(exp1_path), exp1_path, "\n")
  cat(file.exists(exp2_path), exp2_path, "\n")
  cat(file.exists(amce_title_path), amce_title_path, "\n")
  cat(file.exists(exp3_path), exp3_path, "\n")
  cat(file.exists(exp8_path), exp8_path, "\n")
  cat(file.exists(exp9_path), exp9_path, "\n")
  cat(file.exists(exp10_path), exp10_path, "\n")
  cat(file.exists(exp11_path), exp11_path, "\n")
  cat(file.exists(exp_t_path), exp_t_path, "\n")
  
  cat(file.exists(ins_1_path), ins_1_path, "\n")
  cat(file.exists(ins_3_path), ins_3_path, "\n")
  cat(file.exists(ins_4_path), ins_4_path, "\n")
  
  cat(file.exists(ans_1_path), ans_1_path, "\n")
  
  cat(file.exists(prompt_input_path), prompt_input_path, "\n")
  cat(file.exists(prompt_input_q1_path), prompt_input_q1_path, "\n")
  cat(file.exists(prompt_input_q2_path), prompt_input_q2_path, "\n")
  cat(file.exists(prompt_input_q3_path), prompt_input_q3_path, "\n")
  cat(file.exists(prompt_input_q4_path), prompt_input_q4_path, "\n")
  
}

# Additional required settings
options(scipen = 999)
options(warn = -1)

# Detect the number of available cores and create cluster
cl <- parallel::makeCluster(detectCores())
doParallel::registerDoParallel(cl)

# API Key
key <- Sys.getenv("OPENAI_API_KEY")
api_key <- Sys.getenv("AZURE_OPENAI_API_KEY1")
endpoint <- Sys.getenv("AZURE_OPENAI_ENDPOINT")
model_name <- "gpt-4o-mini"

#' Utility Estimation Function
#'
#' @param attributes A JSON object containing attribute information (levels, codings).
#' @param survey A dataframe containing survey data.
#'
#' @return A list containing AMCE data, utility data and willingness to pay data (if price attribute is available),
#' AI-aided interpretations of the results, and precalculated data to be used in upcoming functions.
#'
#' @description This function takes two main parameters which are attribute information and survey data.
#' The function performs several steps to estimate the utility of each attribute. The steps involve
#' loading attribute data and survey data, extracting encoded attribute names and levels, data manipulation and
#' data cleaning steps, calculating AMCEs and utilities, etc.
#' The output is a list object containing AMCEs and utility data, as well as other information related
#' to the conducted conjoint analysis.
#' @examples
#' \dontrun{
#' utility_estimation(attributes = your_attr_data, survey = your_survey_data)
#' }
#' @export

# Overall estimations for the survey data
utility_estimation <- function(attributes, survey) {

  # Loading json data with attribute information (levels, coded ids)
  attr_json <- attributes

  # Extracting the question and dependent variable
  question <- attr_json$`experimentor why question prompt`
  dependent_variable <- attr_json$`respondent_dependent_variable`

  # Extracting attribute names,coded id and number of levels
  attrs <- data.frame(
    attribute_id = as.numeric(names(
      unlist(attr_json$attributes_and_levels_lookup$get_attribute_text)
    )),
    attribute_text = unlist(
      attr_json$attributes_and_levels_lookup$get_attribute_text
    ),
    rep = sapply(attr_json$attributes_and_levels_lookup$get_level_ids, length)
  )
  
  # Getting variable type
  attr_type <- cbind(attrs[,1:2], type = unlist(attr_json$attribute_type))

  # Attribute data rows multiplied my number of levels
  attributes <- as.data.frame(lapply(attrs, rep, attrs$rep))
  
  # Extracting attribute levels and re-ordering
  attrs_lev <- data.frame(
    level_ids = as.numeric(names(unlist(attr_json$attributes_and_levels_lookup$get_level_text))),
    level_text = unlist(attr_json$attributes_and_levels_lookup$get_level_text)
  ) %>% arrange(level_ids)

  # Matching attribute names and levels. Dropping 'rep' column
  att <- as.data.frame(cbind(attributes, attrs_lev)) %>% select(-rep)
  
  # Loading csv survey results data
  survey <- survey[c(c("ID", "Persona", "Task", "Alts","chosen_choice_letter"), colnames(survey)[(((ncol(survey) - length(unique(att$attribute_text)))+1): ncol(survey))])]
  
  # Looking for ids of continuous variables
  attr_continuouis <- attr_type$attribute_id[which(attr_type$type == "Continuous")]
  
  if (attr_json$null_levels_included == FALSE){
  if(length(attr_continuouis) > 0){
    for(i in c(attr_continuouis)){
      attr_col <- survey[,6+i]
      
      # non zero values
      non_zero_value <- attr_col[which(attr_col != 0)]
      
      get_unique_quantiles <- function(values, n = 5) {
        # Remove duplicates and NA values
        clean_values <- unique(na.omit(values))
        
        if (length(clean_values) < n) {
          warning("Not enough unique values to generate ", n, " quantiles")
          # If we have fewer unique values than requested quantiles,
          # we'll space out the available values
          return(seq(min(clean_values), max(clean_values), length.out = n))
        }
        
        # Try standard quantiles first
        standard_quantiles <- quantile(clean_values, probs = seq(0, 1, length.out = n))
        
        # If we got unique values, return them
        if (length(unique(standard_quantiles)) == n) {
          return(standard_quantiles)
        }
        
        # If we didn't get unique values, use sequence of order statistics
        sorted_values <- sort(clean_values)
        indices <- round(seq(1, length(sorted_values), length.out = n))
        return(sorted_values[indices])
      }
      
      quintile_test <- quantile(non_zero_value, probs = seq(0.2, 1, by = 0.2))
      if (length(unique(quintile_test)) == 5){
        quintiles_raw <- quintile_test
      } else{
        quintiles_raw <- get_unique_quantiles(non_zero_value, 5)
      }
      
      if(length(unique(round(quintiles_raw))) == 5){
        quintiles <- round(quintiles_raw)
      } else{
        quintiles <- round(quintiles_raw,digits = 1)
      }
      
      
      Variable_ID <- att %>% filter(attribute_id %in% i)
      
      attr_complete_name <- Variable_ID$level_text[1]
      
      numbers <- c(round(min(non_zero_value)),quintiles)
      
      # Create labels for our categories
      
      if(numbers[1] == numbers[2]){
          if(grepl("Up to", attr_complete_name, ignore.case=TRUE) || grepl("[$€£¥₹]", attr_complete_name)){
            labels <- c(paste0(numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0(numbers[5], "-", numbers[6]))
          } else {
            labels <- c(paste0(numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0("more than ",numbers[5]))
          }
          
          
        } else {
          if(grepl("Up to", attr_complete_name, ignore.case=TRUE) || grepl("[$€£¥₹]", attr_complete_name)){
            labels <- c(paste0(numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0(numbers[5], "-", numbers[6]))
          } else {
            labels <- c(paste0("less than ",numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0("more than ",numbers[5]))
          }
          
        }
      
      # Polishing "Price Levels" to match JSON format
      create_attr_labels <- function(price_complete_name, labels) {
        # Define common currency patterns
        currency_patterns <- list(
          # Currency codes (e.g., USD, EUR, GBP, etc.)
          suffix_codes = "(?:[A-Z]{3})$",
          # Common currency symbols (can be extended)
          symbols = "[$€£¥₹₽₪₩₿]",
          # Handle "million", "billion" etc.
          magnitude = "(million|billion|trillion)",
          # Storage units
          storage_units = "(?:GB|TB|MB|KB)"
        )
        
        # Helper function to extract numbers from string
        extract_number <- function(str) {
          numbers <- as.numeric(unlist(regmatches(str, gregexpr("[0-9]+(\\.[0-9]+)?", str))))
          if(length(numbers) > 0) return(numbers[1])
          return(NULL)
        }
        
       
        replace_number_with_labels <- function(str, labels) {
          result <- vector("character", length(labels))
          base_str <- str
          
          # Case 1: Handle currency codes at the end (e.g., "525 USD", "40 EUR", "10 - 15 USD", "10-15 USD")
          if(grepl(currency_patterns$suffix_codes, base_str)) {
            # Check if there's a range with currency code at the end (with or without spaces around the dash)
            if(grepl(paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*", currency_patterns$suffix_codes), base_str, perl = TRUE)) {
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s*", currency_patterns$suffix_codes, ")"),
                  labels[i],
                  base_str,
                  perl = TRUE
                )
              }
            } else {
              # Handle single number with currency code
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s*", currency_patterns$suffix_codes, ")"),
                  labels[i],
                  base_str,
                  perl = TRUE
                )
              }
            }
          }
          # Case 2: Handle currency symbols (e.g., $, €, £, ¥, etc.)
          else if(grepl(currency_patterns$symbols, base_str)) {
            for(i in seq_along(labels)) {
              # First, remove any commas from the original number
              base_str_clean <- gsub(",", "", base_str)
              
              # Check if there's a range pattern with currency symbols on both sides (e.g., $100 - $200)
              if(grepl(paste0("(", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*", currency_patterns$symbols, "[0-9]+(,[0-9]+)*(\\.[0-9]+)?"), base_str_clean, perl = TRUE)) {
                # Handle price range with currency symbols on both sides - preserve first symbol only
                result[i] <- sub(
                  paste0("(", currency_patterns$symbols, ")([0-9]+(,[0-9]+)*(\\.[0-9]+)?)\\s*-\\s*", currency_patterns$symbols, "[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                  paste0("\\1", labels[i]),
                  base_str_clean,
                  perl = TRUE
                )
              }
              # Check if there's a range pattern with currency symbol only on first number (e.g., $100 - 200)
              else if(grepl(paste0("(", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?"), base_str_clean, perl = TRUE)) {
                # Handle price range with currency symbol only on first number - preserve symbol
                result[i] <- sub(
                  paste0("(", currency_patterns$symbols, ")([0-9]+(,[0-9]+)*(\\.[0-9]+)?)\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                  paste0("\\1", labels[i]),
                  base_str_clean,
                  perl = TRUE
                )
              }
              # Single price with currency symbol
              else {
                result[i] <- sub(
                  paste0("(?<=", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                  labels[i],
                  base_str_clean,
                  perl = TRUE
                )
              }
            }
          }
          # Case 3: Handle numbers with magnitude words (e.g., "375 million USD", "10-20 million")
          else if(grepl(currency_patterns$magnitude, base_str, ignore.case = TRUE)) {
            # Check if there's a range with magnitude words (with or without spaces around the dash)
            if(grepl(paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s+", currency_patterns$magnitude), base_str, perl = TRUE, ignore.case = TRUE)) {
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+", currency_patterns$magnitude, ")"),
                  labels[i],
                  base_str,
                  perl = TRUE,
                  ignore.case = TRUE
                )
              }
            } else {
              # Handle single number with magnitude word
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+", currency_patterns$magnitude, ")"),
                  labels[i],
                  base_str,
                  perl = TRUE,
                  ignore.case = TRUE
                )
              }
            }
          }
          # Add a case to handle ranges with unit suffixes like "10-25 GB", "5-10 kg"
          else if(grepl("[0-9]+(,[0-9]+)*(\\.[0-9]+)?-[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s+[A-Za-z]+", base_str, perl = TRUE)) {
            for(i in seq_along(labels)) {
              result[i] <- sub(
                "[0-9]+(,[0-9]+)*(\\.[0-9]+)?-[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+[A-Za-z]+)",
                labels[i],
                base_str,
                perl = TRUE
              )
            }
          }
          # Case 4: Handle general number ranges (e.g., "100-200", "5,000 - 10,000", "1 - 2 Days")
          else if(grepl("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?", base_str, perl = TRUE)) {
            for(i in seq_along(labels)) {
              result[i] <- sub(
                "[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?",
                labels[i],
                base_str,
                perl = TRUE
              )
            }
          }
          # Case 5: Default case - replace first number found (with commas)
          else {
            for(i in seq_along(labels)) {
              result[i] <- sub("[0-9]+(,[0-9]+)*(\\.[0-9]+)?", labels[i], base_str)
            }
          }
          
          return(result)
        }
       





        
        # Generate all variations for the input string
        result <- replace_number_with_labels(price_complete_name, labels)
        
         # # Remove "Up to" from cases with "Less than/More than"
          # if(any(grepl("Up to",result))){
          #   replace_index <- which(grepl("Up to",result,ignore.case = T) & 
          #                            grepl("(Less than|More than)",result, ignore.case = T))
          #   for(j in replace_index){
          #     result[j] <- gsub("Up to ","",result[j])
          #   }
          # }

        return(result)
      }
      
      attr_complete_name_full <- Variable_ID$level_text[Variable_ID$level_text != "Not available"]
      
      if (length(labels) != length(attr_complete_name_full)){
      labels <- c(create_attr_labels(attr_complete_name, labels))
      } else{
        labels_c <- c()
        for (p in 1: length(labels)){
          labels_c[p] <- create_attr_labels(attr_complete_name_full[p], labels[p])
        }
        labels <- labels_c
      }
      
      names(labels) <- NULL
      
      # Function to capitalize first letter
      capitalize_first <- function(x) {
        # Check if input is a vector
        if (!is.vector(x)) {
          stop("Input must be a vector")
        }
        
        # Convert each element to character if not already
        x <- as.character(x)
        
        # Apply capitalization to each element
        result <- sapply(x, function(str) {
          if (nchar(str) > 0) {
            paste0(toupper(substr(str, 1, 1)), substr(str, 2, nchar(str)))
          } else {
            str  # Return empty strings as is
          }
        })
        
        # Return result with the same class as input when possible
        return(result)
      }
      labels <- capitalize_first(labels)

      att <- att %>% filter(!attribute_id %in% i)
      
      survey_no_opt_out <- survey[which(survey$Alts != max(survey$Alts)),][6+i]
      if (sum(survey_no_opt_out == 0) > 0){
        
        zero_value <-  c(create_attr_labels(attr_complete_name, c("0")))
        add_p <- data.frame(attribute_id = rep(Variable_ID$attribute_id[1], 6), 
                            attribute_text = rep(Variable_ID$attribute_text[1],6),
                            level_ids = c(min(Variable_ID$level_ids): (min(Variable_ID$level_ids) + 5)),
                            level_text = c(zero_value,labels))
        
        survey[[6+i]] <- ifelse(survey[[6+i]] == 0, "0",
                                factor(cut(survey[[6+i]], 
                                           breaks = c(-Inf, quintiles_raw, Inf),
                                           labels = FALSE,
                                           include.lowest = TRUE),
                                       levels = 1:5,
                                       labels = labels))
        
        val <- c(min(Variable_ID$level_ids) + 1: (min(Variable_ID$level_ids) + 4))
        survey[[6+i]] <- ifelse(survey[[6+i]] == "1", val[1], 
                                (ifelse(survey[[6+i]] == "2", val[2],
                                        (ifelse(survey[[6+i]] == "3", val[3], 
                                                (ifelse(survey[[6+i]] == "4", val[4],
                                                        (ifelse(survey[[6+i]] == "5", val[5], survey[[6+i]])))))))))
        
        
        
        survey[[6+i]] <- ifelse((survey[[6+i]]== 0) & (survey$Alts != max(survey$Alts)),
                                min(Variable_ID$level_ids), survey[[6+i]])
        
      } else{
  
      
      add_p <- data.frame(attribute_id = rep(Variable_ID$attribute_id[1], 5), 
                          attribute_text = rep(Variable_ID$attribute_text[1],5),
                          level_ids = c(min(Variable_ID$level_ids): (min(Variable_ID$level_ids) + 4)),
                          level_text = labels)
      
      survey[[6+i]] <- ifelse(survey[[6+i]] == 0, "0",
                                    factor(cut(survey[[6+i]], 
                                               breaks = c(-Inf, quintiles_raw, Inf),
                                               labels = FALSE,
                                               include.lowest = TRUE),
                                           levels = 1:5,
                                           labels = labels))
      
      val <- c(min(Variable_ID$level_ids): (min(Variable_ID$level_ids) + 4))
      survey[[6+i]] <- ifelse(survey[[6+i]] == "1", val[1], 
                                    (ifelse(survey[[6+i]] == "2", val[2],
                                            (ifelse(survey[[6+i]] == "3", val[3], 
                                                    (ifelse(survey[[6+i]] == "4", val[4],
                                                            (ifelse(survey[[6+i]] == "5", val[5], survey[[6+i]])))))))))
      
    }
      
      att <- rbind(att,add_p) %>% arrange(attribute_id, level_ids)
      
      
      
      
    }
    
  }
    # If there is a "Not available" level
  } else{
    # If there is at least 1 continuous variable
    if(length(attr_continuouis) > 0){
      for(i in c(attr_continuouis)){
        attr_col <- survey[,6+i]
        
        # non zero values
        
        non_zero_value_1 <- attr_col[which(attr_col != 0)]
        non_zero_value <- as.numeric(non_zero_value_1[which(non_zero_value_1 != "Not available")])
        
        get_unique_quantiles <- function(values, n = 5) {
          # Remove duplicates and NA values
          clean_values <- unique(na.omit(values))
          
          if (length(clean_values) < n) {
            warning("Not enough unique values to generate ", n, " quantiles")
            # If we have fewer unique values than requested quantiles,
            # we'll space out the available values
            return(seq(min(clean_values), max(clean_values), length.out = n))
          }
          
          # Try standard quantiles first
          standard_quantiles <- quantile(clean_values, probs = seq(0, 1, length.out = n))
          
          # If we got unique values, return them
          if (length(unique(standard_quantiles)) == n) {
            return(standard_quantiles)
          }
          
          # If we didn't get unique values, use sequence of order statistics
          sorted_values <- sort(clean_values)
          indices <- round(seq(1, length(sorted_values), length.out = n))
          return(sorted_values[indices])
        }
        
        quintile_test <- quantile(non_zero_value, probs = seq(0.2, 1, by = 0.2))
        if (length(unique(quintile_test)) == 5){
          quintiles_raw <- quintile_test
        } else{
          quintiles_raw <- get_unique_quantiles(non_zero_value, 5)
        }
        
        if(length(unique(round(quintiles_raw))) == 5){
          quintiles <- round(quintiles_raw)
        } else if ((length(unique(round(quintiles_raw, digits = 1))) == 5)){
          quintiles <- round(quintiles_raw,digits = 1)
        } else{
          quintiles <- round(quintiles_raw,digits = 2)
        }
        
        Variable_ID <- att %>% filter(attribute_id %in% i)
        
        attr_complete_name <- Variable_ID$level_text[which(Variable_ID$level_text != "Not available")][1]
        
        
        numbers <- c(round(min(as.numeric(gsub(",", "", as.character(non_zero_value))))), quintiles)
        
        if(numbers[1] == numbers[2]){
          if(grepl("Up to", attr_complete_name, ignore.case=TRUE) || grepl("[$€£¥₹]", attr_complete_name)){
            labels <- c(paste0(numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0(numbers[5], "-", numbers[6]))
          } else {
            labels <- c(paste0(numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0("more than ",numbers[5]))
          }

          
        } else{
          if(grepl("Up to", attr_complete_name, ignore.case=TRUE) || grepl("[$€£¥₹]", attr_complete_name)){
            labels <- c(paste0(numbers[1], "-", numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0(numbers[5], "-", numbers[6]))
          } else {
            labels <- c(paste0("less than ",numbers[2]), 
                        paste0(numbers[2], "-", numbers[3]), 
                        paste0(numbers[3], "-", numbers[4]),
                        paste0(numbers[4], "-", numbers[5]),
                        paste0("more than ",numbers[5]))
          }
        }
        
        create_attr_labels <- function(price_complete_name, labels) {
          # Define patterns
          currency_patterns <- list(
            # Currency codes
            suffix_codes = "(?:[A-Z]{3})$",
            # Currency symbols
            symbols = "[$€£¥₹₽₪₩₿]",
            # Magnitude words
            magnitude = "(million|billion|trillion)",
            # Storage units
            storage_units = "(?:GB|TB|MB|KB)"
          )
          
          # Helper function to extract numbers from string
          extract_number <- function(str) {
            numbers <- as.numeric(unlist(regmatches(str, gregexpr("[0-9]+(\\.[0-9]+)?", str))))
            if(length(numbers) > 0) return(numbers)
            return(NULL)
          }
          
          # Function to find the closest number to the label range
          find_closest_number <- function(numbers, label_range) {
            # Convert label to a numeric value if possible
            label_num <- as.numeric(gsub("[^0-9.]", "", label_range))
            if(is.na(label_num)) return(1) # Default to first number if label is not numeric
            
            # Calculate differences
            diffs <- abs(numbers - label_num)
            # Return index of closest number
            return(which.min(diffs))
          }
          
          replace_number_with_labels <- function(str, labels) {
            result <- vector("character", length(labels))
            base_str <- str
            
            # Special case for "$X - $Y USD" format with range labels
            if(grepl("\\$[0-9,.]+\\s*-\\s*\\$[0-9,.]+\\s+[A-Z]{3}", base_str) && any(grepl("-", labels))) {
              for(i in seq_along(labels)) {
                if(grepl("-", labels[i])) {
                  # Split the label into start and end values
                  label_parts <- strsplit(labels[i], "-")[[1]]
                  start_label <- label_parts[1]
                  end_label <- label_parts[2]
                  
                  # Create the formatted result with the new labels
                  currency_symbol <- sub("^(\\$).*", "\\1", base_str)
                  suffix <- sub(".*\\s+([A-Z]{3})$", "\\1", base_str)
                  result[i] <- paste0(currency_symbol, start_label, " - ", currency_symbol, end_label, " ", suffix)
                } else {
                  # If label is not a range, fall back to original logic
                  result[i] <- base_str # Default unchanged
                }
              }
              return(result)
            }
            
            # Case 1: Handle currency codes at the end (e.g., "525 USD", "40 EUR", "10 - 15 USD", "10-15 USD")
            if(grepl(currency_patterns$suffix_codes, base_str)) {
              # Check if there's a range with currency code at the end (with or without spaces around the dash)
              if(grepl(paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*", currency_patterns$suffix_codes), base_str, perl = TRUE)) {
                for(i in seq_along(labels)) {
                  result[i] <- sub(
                    paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s*", currency_patterns$suffix_codes, ")"),
                    labels[i],
                    base_str,
                    perl = TRUE
                  )
                }
              } else {
                # Handle single number with currency code
                for(i in seq_along(labels)) {
                  result[i] <- sub(
                    paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s*", currency_patterns$suffix_codes, ")"),
                    labels[i],
                    base_str,
                    perl = TRUE
                  )
                }
              }
            }
            # Case 2: Handle currency symbols (e.g., $, €, £, ¥, etc.)
            else if(grepl(currency_patterns$symbols, base_str)) {
              for(i in seq_along(labels)) {
                # First, remove any commas from the original number
                base_str_clean <- gsub(",", "", base_str)
                
                # Check if there's a range pattern with currency symbols on both sides (e.g., $100 - $200)
                if(grepl(paste0("(", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*", currency_patterns$symbols, "[0-9]+(,[0-9]+)*(\\.[0-9]+)?"), base_str_clean, perl = TRUE)) {
                  # Handle price range with currency symbols on both sides - preserve first symbol only
                  result[i] <- sub(
                    paste0("(", currency_patterns$symbols, ")([0-9]+(,[0-9]+)*(\\.[0-9]+)?)\\s*-\\s*", currency_patterns$symbols, "[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                    paste0("\\1", labels[i]),
                    base_str_clean,
                    perl = TRUE
                  )
                }
                # Check if there's a range pattern with currency symbol only on first number (e.g., $100 - 200)
                else if(grepl(paste0("(", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?"), base_str_clean, perl = TRUE)) {
                  # Handle price range with currency symbol only on first number - preserve symbol
                  result[i] <- sub(
                    paste0("(", currency_patterns$symbols, ")([0-9]+(,[0-9]+)*(\\.[0-9]+)?)\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                    paste0("\\1", labels[i]),
                    base_str_clean,
                    perl = TRUE
                  )
                }
                # Single price with currency symbol
                else {
                  result[i] <- sub(
                    paste0("(?<=", currency_patterns$symbols, ")[0-9]+(,[0-9]+)*(\\.[0-9]+)?"),
                    labels[i],
                    base_str_clean,
                    perl = TRUE
                  )
                }
              }
            }
            # Case 3: Handle numbers with magnitude words (e.g., "375 million USD", "10-20 million")
            else if(grepl(currency_patterns$magnitude, base_str, ignore.case = TRUE)) {
              # Check if there's a range with magnitude words (with or without spaces around the dash)
              if(grepl(paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s+", currency_patterns$magnitude), base_str, perl = TRUE, ignore.case = TRUE)) {
                for(i in seq_along(labels)) {
                  result[i] <- sub(
                    paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+", currency_patterns$magnitude, ")"),
                    labels[i],
                    base_str,
                    perl = TRUE,
                    ignore.case = TRUE
                  )
                }
              } else {
                # Handle single number with magnitude word
                for(i in seq_along(labels)) {
                  result[i] <- sub(
                    paste0("[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+", currency_patterns$magnitude, ")"),
                    labels[i],
                    base_str,
                    perl = TRUE,
                    ignore.case = TRUE
                  )
                }
              }
            }
            # Add a case to handle ranges with unit suffixes like "10-25 GB", "5-10 kg"
            else if(grepl("[0-9]+(,[0-9]+)*(\\.[0-9]+)?-[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s+[A-Za-z]+", base_str, perl = TRUE)) {
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  "[0-9]+(,[0-9]+)*(\\.[0-9]+)?-[0-9]+(,[0-9]+)*(\\.[0-9]+)?(?=\\s+[A-Za-z]+)",
                  labels[i],
                  base_str,
                  perl = TRUE
                )
              }
            }
            # Case 4: Handle general number ranges (e.g., "100-200", "5,000 - 10,000", "1 - 2 Days")
            else if(grepl("[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?", base_str, perl = TRUE)) {
              for(i in seq_along(labels)) {
                result[i] <- sub(
                  "[0-9]+(,[0-9]+)*(\\.[0-9]+)?\\s*-\\s*[0-9]+(,[0-9]+)*(\\.[0-9]+)?",
                  labels[i],
                  base_str,
                  perl = TRUE
                )
              }
            }
            # Case 5: Default case - replace first number found (with commas)
            else {
              for(i in seq_along(labels)) {
                result[i] <- sub("[0-9]+(,[0-9]+)*(\\.[0-9]+)?", labels[i], base_str)
              }
            }
            
            return(result)
          }
          
          return(replace_number_with_labels(price_complete_name, labels))
        }
          
        attr_complete_name_full <- Variable_ID$level_text[Variable_ID$level_text != "Not available"]
        
        if (length(labels) != length(attr_complete_name_full)){
          labels <- c(create_attr_labels(attr_complete_name, labels))
        } else{
          labels_c <- c()
          for (p in 1: length(labels)){
            labels_c[p] <- create_attr_labels(attr_complete_name_full[p], labels[p])
          }
          labels <- labels_c
        }
        

        # Function to capitalize first letter
        capitalize_first <- function(x) {
          # Check if input is a vector
          if (!is.vector(x)) {
            stop("Input must be a vector")
          }
          
          # Convert each element to character if not already
          x <- as.character(x)
          
          # Apply capitalization to each element
          result <- sapply(x, function(str) {
            if (nchar(str) > 0) {
              paste0(toupper(substr(str, 1, 1)), substr(str, 2, nchar(str)))
            } else {
              str  # Return empty strings as is
            }
          })
          
          # Return result with the same class as input when possible
          return(result)
        }
        labels <- capitalize_first(labels)
        names(labels) <- NULL
        
        att_c <- att
        att <- att %>% filter(!attribute_id %in% i)
        
        survey_no_opt_out <- survey[which(survey$Alts != max(survey$Alts)),][6+i]
        if (sum(survey_no_opt_out == 0) > 0){
          
          zero_value <-  c(create_attr_labels(attr_complete_name, c("0")))
          add_p <- data.frame(attribute_id = rep(Variable_ID$attribute_id[1], 7), 
                              attribute_text = rep(Variable_ID$attribute_text[1],7),
                              level_ids = c(min(Variable_ID$level_ids): (min(Variable_ID$level_ids) + 6)),
                              level_text = c(zero_value,labels, "Not available"))
          
          survey[[6+i]] <- ifelse(survey[[6+i]] == "Not available", "Not available",
                                  (ifelse(survey[[6+i]] == "0", "0",
                                          factor(cut(as.numeric(survey[[6+i]]), 
                                                     breaks = c(-Inf, quintiles_raw, Inf),
                                                     labels = FALSE,
                                                     include.lowest = TRUE),
                                                 levels = 1:5,
                                                 labels = labels))))
          
          val <- c(min(Variable_ID$level_ids) + 1: (min(Variable_ID$level_ids) + 5))
          survey[[6+i]] <- ifelse(survey[[6+i]] == "1", val[1], 
                                  (ifelse(survey[[6+i]] == "2", val[2],
                                          (ifelse(survey[[6+i]] == "3", val[3], 
                                                  (ifelse(survey[[6+i]] == "4", val[4],
                                                          (ifelse(survey[[6+i]] == "5", val[5], 
                                                                  (ifelse(survey[[6+i]] == "Not available", val[6],       
                                                                          survey[[6+i]])))))))))))
          
          
          
          
          survey[[6+i]] <- ifelse((survey[[6+i]]== 0) & (survey$Alts != max(survey$Alts)),
                                  min(Variable_ID$level_ids), survey[[6+i]])
          
        } else{
          
          att_check <- att_c %>% filter(!attribute_id %in% i)
          att_s <- att_c %>% filter(attribute_id %in% i)
          
          if(nrow(att_s) < 6){
            id_1 <- max(att_check$level_ids)+1
          } else{
            id_1 <- min(Variable_ID$level_ids)
          }
          
          
          add_p <- data.frame(attribute_id = rep(Variable_ID$attribute_id[1], 6), 
                              attribute_text = rep(Variable_ID$attribute_text[1],6),
                              level_ids = c(id_1 : (min(id_1) + 5)),
                              level_text = c(labels, "Not available"))
          
          survey[[6+i]] <- ifelse(survey[[6+i]] == "Not available", "Not available",
                                  (ifelse(survey[[6+i]] == "0", "0",
                                          factor(cut(as.numeric(survey[[6+i]]), 
                                                     breaks = c(-Inf, quintiles_raw, Inf),
                                                     labels = FALSE,
                                                     include.lowest = TRUE),
                                                 levels = 1:5,
                                                 labels = labels))))
          
          val <- c(id_1: (id_1 + 5))
          survey[[6+i]] <- ifelse(survey[[6+i]] == "1", val[1], 
                                  (ifelse(survey[[6+i]] == "2", val[2],
                                          (ifelse(survey[[6+i]] == "3", val[3], 
                                                  (ifelse(survey[[6+i]] == "4", val[4],
                                                          (ifelse(survey[[6+i]] == "5", val[5], 
                                                                  (ifelse(survey[[6+i]] == "Not available", val[6],       
                                                                          survey[[6+i]])))))))))))
          
        }
        
        att <- rbind(att,add_p) %>% arrange(attribute_id, level_ids)
        
      }
      
    }
    
  }
  


  # Getting the order of attributes and levels (to be further used for visualization)
  order_att <- with(att, {
    dplyr::select(att, attribute_text, level_ids, level_text)
  })
  names(order_att)[2] <- "row"

  # Loading survey data
  data_survey <- survey
  names(data_survey)[4] <- "Alt"

  # Remove column
  keep.cols <- names(data_survey) %in% c("")
  
  data_survey <- data_survey [! keep.cols]
  

  n_alt <- length(unique(data_survey$Alt))

  n_responses <- nrow(data_survey)/n_alt

  n_alt_possible <- LETTERS

  none_sel <- paste0(round((nrow(data_survey[data_survey$chosen_choice_letter == n_alt_possible[n_alt], ])/n_alt) /
    (nrow(data_survey)/n_alt) * 100), "%")


  # Feature levels present in attribute list but not selected by the respondents
  levels_present <- data_survey[, (which(
    colnames(data_survey) == "chosen_choice_letter"
  ) + 1):ncol(data_survey)]
  remove_levels <- setdiff(
    c(unique(unlist(att$level_ids))),
    as.numeric(unique(unlist(levels_present)))[(c(as.numeric(unique(unlist(levels_present)))) != 0)]
  )

  
  # Removing unnecessary columns
  data_survey$X <- NULL

  # Identifying unique users
    data_survey$Choice_ID <- c(rep((1:(nrow(data_survey)/n_alt)), each = n_alt))
    names(data_survey)[3] <- "QES"
    data_survey <- data_survey %>% dplyr::select(ID, QES, everything())

  # Getting the number of respondents
  data_persona_id <- data_survey
  n_resp <- length(unique(data_persona_id$ID))
  data_raw <- data_survey %>% dplyr::select(-Persona)
  
  Persona_Type <- ifelse(is.null(attr_json$external_persona_provided), "Internal", 
                         ifelse(attr_json$external_persona_provided == TRUE, "External", "Internal"))
  
  #############################################
  ######## Demographics External Data ########
  #############################################
  
  if(Persona_Type == "External"){
    demos <- unique(data_persona_id[c("Persona", "ID")])
    demos <- demos %>% select (ID, everything())
    
    # Country, State and Year
    if ("Country of residence" %in% colnames(demos)){
      Survey_Country <- demos$`Country of residence`[1]
    } else{
      Survey_Country <- "No Country"
    }
    
    if ("State of residence" %in% colnames(demos)){
      states <- unique(demos$`State of residence`)
      if (length(states) > 1){
        state_mapping <- c(
          "Alabama" = "AL",
          "Alaska" = "AK",
          "Arizona" = "AZ",
          "Arkansas" = "AR",
          "California" = "CA",
          "Colorado" = "CO",
          "Connecticut" = "CT",
          "Delaware" = "DE",
          "Florida" = "FL",
          "Georgia" = "GA",
          "Hawaii" = "HI",
          "Idaho" = "ID",
          "Illinois" = "IL",
          "Indiana" = "IN",
          "Iowa" = "IA",
          "Kansas" = "KS",
          "Kentucky" = "KY",
          "Louisiana" = "LA",
          "Maine" = "ME",
          "Maryland" = "MD",
          "Massachusetts" = "MA",
          "Michigan" = "MI",
          "Minnesota" = "MN",
          "Mississippi" = "MS",
          "Missouri" = "MO",
          "Montana" = "MT",
          "Nebraska" = "NE",
          "Nevada" = "NV",
          "New Hampshire" = "NH",
          "New Jersey" = "NJ",
          "New Mexico" = "NM",
          "New York" = "NY",
          "North Carolina" = "NC",
          "North Dakota" = "ND",
          "Ohio" = "OH",
          "Oklahoma" = "OK",
          "Oregon" = "OR",
          "Pennsylvania" = "PA",
          "Rhode Island" = "RI",
          "South Carolina" = "SC",
          "South Dakota" = "SD",
          "Tennessee" = "TN",
          "Texas" = "TX",
          "Utah" = "UT",
          "Vermont" = "VT",
          "Virginia" = "VA",
          "Washington" = "WA",
          "West Virginia" = "WV",
          "Wisconsin" = "WI",
          "Wyoming" = "WY",
          "District of Columbia" = "DC"
        )
        
        # Replace state names with abbreviations and remove those without a match
        states_abbr <- states[states %in% names(state_mapping)]
        states_abbr <- state_mapping[states_abbr]
        if (length(states) > 10){
        Survey_State <- paste(states_abbr, collapse = ", ")
        } else {
          Survey_State <- paste(states, collapse = ", ")
        }
      } else{
      Survey_State <- states
      }
    } else {
      Survey_State <- "No State"
    }
    
    if ("Current year" %in% colnames(demos)){
      Survey_Year <- demos$`Current year`[1]
    } else{
      Survey_Year <- "No Year"
    }
    
  } else{
    
  #############################################
  ######## Demographics Internal Data ########
  #############################################
    
  # Getting demographics data
  demos <- unique(data_persona_id[c("Persona", "ID")])
  
  demos$Persona <- gsub("[[]", "", demos$Persona)
  demos$Persona <- gsub("[]]", "", demos$Persona)
  demos$Persona <- gsub("[()]", "", demos$Persona)
  splitdat <- do.call("rbind", strsplit(demos$Persona, "', '"))
  colnames(splitdat) <-   gsub("'", "", sub("^\\s*([^:]+):.*", "\\1", splitdat[1,]))
  
  # Find duplicate column names
  duplicate_cols <- duplicated(colnames(splitdat))
  
  # Keep only columns with unique names
  splitdat <- splitdat[, !duplicate_cols]
  
  demos <- cbind(demos, splitdat) %>% select(-Persona)
  
  demos[] <- lapply(demos, gsub, pattern = "'", replacement = "") %>%
    as.data.frame(check.names = FALSE)
  
  
  demos <- demos %>%
    mutate_if(is.character, str_trim)
  
  demos$ID <- as.numeric(demos$ID)
  
  demos <- lapply(demos, function(x) sub(".*:\\s*", "", x))
  demos <- as.data.frame(demos, stringsAsFactors = FALSE, check.names = FALSE)
  
  # Country, State and Year
  if ("Country of residence" %in% colnames(demos)){
    Survey_Country <- demos$`Country of residence`[1]
  } else{
    Survey_Country <- "No Country"
  }
  
  if ("State of residence" %in% colnames(demos)){
    states <- unique(demos$`State of residence`)
    if (length(states) > 1){
      state_mapping <- c(
        "Alabama" = "AL",
        "Alaska" = "AK",
        "Arizona" = "AZ",
        "Arkansas" = "AR",
        "California" = "CA",
        "Colorado" = "CO",
        "Connecticut" = "CT",
        "Delaware" = "DE",
        "Florida" = "FL",
        "Georgia" = "GA",
        "Hawaii" = "HI",
        "Idaho" = "ID",
        "Illinois" = "IL",
        "Indiana" = "IN",
        "Iowa" = "IA",
        "Kansas" = "KS",
        "Kentucky" = "KY",
        "Louisiana" = "LA",
        "Maine" = "ME",
        "Maryland" = "MD",
        "Massachusetts" = "MA",
        "Michigan" = "MI",
        "Minnesota" = "MN",
        "Mississippi" = "MS",
        "Missouri" = "MO",
        "Montana" = "MT",
        "Nebraska" = "NE",
        "Nevada" = "NV",
        "New Hampshire" = "NH",
        "New Jersey" = "NJ",
        "New Mexico" = "NM",
        "New York" = "NY",
        "North Carolina" = "NC",
        "North Dakota" = "ND",
        "Ohio" = "OH",
        "Oklahoma" = "OK",
        "Oregon" = "OR",
        "Pennsylvania" = "PA",
        "Rhode Island" = "RI",
        "South Carolina" = "SC",
        "South Dakota" = "SD",
        "Tennessee" = "TN",
        "Texas" = "TX",
        "Utah" = "UT",
        "Vermont" = "VT",
        "Virginia" = "VA",
        "Washington" = "WA",
        "West Virginia" = "WV",
        "Wisconsin" = "WI",
        "Wyoming" = "WY",
        "District of Columbia" = "DC"
      )
      
      # Replace state names with abbreviations and remove those without a match
      states_abbr <- states[states %in% names(state_mapping)]
      states_abbr <- state_mapping[states_abbr]
      if (length(states) > 10){
        Survey_State <- paste(states_abbr, collapse = ", ")
      } else {
        Survey_State <- paste(states, collapse = ", ")
      }
    } else{
      Survey_State <- states
    }
  } else {
    Survey_State <- "No State"
  }
  
  
  if ("Current year" %in% colnames(demos)){
    Survey_Year <- demos$`Current year`[1]
  } else{
    Survey_Year <- "No Year"
  }

  
  if (nrow(demos) < 100){
  min_freq_number <- round(nrow(demos)*0.2)
  } else{
    min_freq_number <- 25
  }

  
  # Function to identify columns to keep, always preserving ID column
  filter_low_diversity_cols <- function(df, min_freq = min_freq_number, min_unique_vals = 2, id_col = "ID") {
    # Ensure ID column exists
    if (!id_col %in% names(df)) {
      stop(sprintf("ID column '%s' not found in dataframe", id_col))
    }
    
    # Process each column except ID
    cols_to_keep <- sapply(names(df), function(colname) {
      # Always keep ID column
      if (colname == id_col) {
        return(TRUE)
      }
      
      # Get value frequencies for current column
      col <- df[[colname]]
      val_freq <- table(col)
      
      # Keep only values that appear at least min_freq times
      frequent_vals <- val_freq[val_freq >= min_freq]
      
      # Count number of remaining unique values
      num_unique <- length(frequent_vals)
      
      # Return TRUE if we have at least min_unique_vals values remaining
      return(num_unique >= min_unique_vals)
    })
    
    # Return filtered dataframe
    return(df[, cols_to_keep, drop = FALSE])
  }
  

  
  if("age" %in% colnames(demos)){
    demos$age <- as.numeric(demos$age)
    demos$age <- cut(demos$age, 
                     breaks = c(17, 35, 55, 75, 100), 
                     labels = c("Adult: 18-35 years", 
                                "Middle-aged: 36-55 years",
                                "Senior: 56-75 years",
                                "Veteran: over 76 years"),
                     include.lowest = TRUE,
                     right = TRUE)
  }
  
  if("household_income" %in% colnames(demos)){
    demos$household_income <- as.numeric(demos$household_income)
    demos$household_income <- cut(demos$household_income, 
                     breaks = c(0, 50000, 100000, 200000, 500000, 20000000), 
                     labels = c("Economical Households: less than $50k", 
                                "Moderate Income Families: from $50k to $100k",
                                "Prosperous Households: from $100k to $200k",
                                "Wealthy Estates: from $200k to 500k",
                                "Wealth Apex: more than $500k"),
                     include.lowest = FALSE,
                     right = FALSE)
  }
  
    
    
  
  if("education_level" %in% colnames(demos)){
    demos$education_level <- ifelse(demos$education_level %in% c("Masters", "PhD"), "Advanced Degree",
                                    (ifelse(demos$education_level %in% c("Bachelors"), "Bachelor's Degree",
                                            (ifelse(demos$education_level %in% c("High School Diploma", "High School but no diploma", "Less than high school"), "High School or Equivalent",
                                                    (ifelse(demos$education_level %in% c("Associates", "Some College"), "Some College or Associate's", 
                                                            demos$education_level)))))))
    
  }
  
  if("racial_group" %in% colnames(demos)){
    demos$racial_group <- ifelse(demos$racial_group %in% c("Native Hawaiian/Pacific Islander", "American Indian/Alaska Native",
                                                           "Other race"), "Other", 
                                                            demos$racial_group)
    
  }
  
  if("census_division" %in% colnames(demos)){
    demos$census_division <- ifelse(demos$census_division %in% c("New England", "Middle Atlantic"), "Northeast",
                                    (ifelse(demos$census_division %in% c("East North Central", "West North Central"), "Midwest",
                                            (ifelse(demos$census_division %in% c("South Atlantic", "East South Central", "West South Central"), "South",
                                                    (ifelse(demos$census_division %in% c("Mountain", "Pacific"), "West", 
                                                            demos$census_division)))))))
    
  }
  
  if("marital_status" %in% colnames(demos)){
    demos$marital_status<- ifelse(demos$marital_status %in% c("Married, spouse present", "Married, spouse absent"), "Currently Married",
                                    (ifelse(demos$marital_status %in% c("Widowed", "Divorced", "Separated"), "Formerly Married",
                                            (ifelse(demos$marital_status %in% c("Never married/single"), "Never Married",
                                                            demos$marital_status)))))
    
  }
  
  if("speaks_english" %in% colnames(demos)){
    demos$speaks_english <- ifelse(demos$speaks_english %in% c("Yes, speaks only English", "Yes, speaks very well", "Yes, speaks well"), "High English Proficiency",
                                  (ifelse(demos$speaks_english %in% c("Yes, but not well"), "Limited English Proficiency",
                                          (ifelse(demos$speaks_english %in% c("Does not speak English"), "Non-English Speaker",
                                                  demos$speaks_english)))))
    
  }
  
  if("number_of_children" %in% colnames(demos)){
    demos$number_of_children <- ifelse(demos$number_of_children %in% c("0"), "Childless",
                                   (ifelse(demos$number_of_children %in% c("1", "2"), "Few: less than 3 children",
                                           (ifelse(demos$number_of_children %in% c("3", "4", "4+"), "Multiple: 3 and more children",
                                                   demos$number_of_children)))))
    
  }
  
  
  
  
  
  
  demos <- filter_low_diversity_cols(demos)
  
  }
  
  # Removing demographics where we have less than 2 unique levels
  cols_to_keep <- sapply(demos, function(x) length(unique(x)) > 1)
  
  # Check if removing columns would leave more than 2
  if (sum(cols_to_keep) > 1) {
    demos <- demos[, cols_to_keep]
  } else {
    demos <- demos
  }
  
  Demo_Data_Complete <- demos

  # Getting choice variable and transforming it into binary
  data_complete <- data_raw

  data_complete$choice <-  match(data_complete$chosen_choice_letter, LETTERS)

  data_complete$choice <- ifelse(data_complete$choice == data_complete$Alt,
    1, 0
  )

  data_complete <- data_complete %>% arrange(ID, QES, Alt)
  
  data_complete_orig <- data_complete
  
  # Modified get_mode function
  get_single_mode <- function(x) {
    ux <- unique(x)
    ux <- ux[ux != 0]
    modes <- ux[which.max(tabulate(match(x, ux)))]
    
    # If there's more than one mode, randomly select one
    if (length(modes) > 1) {
      return(sample(modes, 1))
    } else {
      return(modes)
    }
  }
  
  if (attr_json$null_levels_included == FALSE){
  # most common levels per attribute
  common_levs_data <- as.data.frame(lapply(data_complete[,7:ncol(data_complete)-2], get_single_mode))
  # if there is price, take the middle quantile (40%-60%)
  if(length(attr_continuouis) > 0){
    
    remove_elements_by_prefix <- function(input_vector, prefixes) {
      # Create a pattern that matches any of the prefixes
      # escape special characters in prefixes and combine with OR operator
      pattern <- paste0("^(", 
                        paste(gsub("([.|()\\[])", "\\\\\\1", prefixes), 
                              collapse="|"), 
                        ")")
      
      # Keep only elements that DON'T match the pattern
      result <- input_vector[!grepl(pattern, input_vector)]
      
      return(result)
    }
    
    att_c <- foreach(a = c(attr_continuouis), .combine = "c") %dopar% {
      a <- colnames(survey)[6+a]
    }
    
    common_levs_init <- paste0(colnames(common_levs_data), "_", common_levs_data[1,])
    
    common_levs_discrete <- remove_elements_by_prefix(common_levs_init, paste0(att_c, "_"))
    
    # Initialize empty vector to store all common levels
    all_common_levs <- c()
    
    
    for(i in c(attr_continuouis)){
      Variable_ID <- att %>% filter(attribute_id %in% i)
      # Create common_levs for this iteration
      current_common_levs <- c(common_levs_discrete, 
                               paste0( colnames(survey)[[6+i]], "_", Variable_ID$level_ids[3]))
      
      # Append to our running vector of all common levels
      all_common_levs <- c(all_common_levs, current_common_levs)
      
    }
    common_levs <- unique(all_common_levs)
    
  }else{
  common_levs <- paste0(colnames(common_levs_data), "_", common_levs_data[1,])
  }
  } else {
    name <- colnames(data_complete[,7:ncol(data_complete)-2])
    filtered_att <- att[att$level_text == "Not available", ]
    result_n <- filtered_att[!duplicated(filtered_att$attribute_text), ]
    cod <- result_n$level_ids[which(result_n$level_text == "Not available")]
    common_levs <- paste0(name, "_", cod)
  }
  
  
  
  
  # Getting independent variables
  var <- ncol(data_complete[, c(5:(ncol(data_complete) - 2))])

  # Setting independent variables as type 'factor'
  data_complete[ colnames(data_complete[, c(5:(ncol(data_complete) - 2))])] <-
    lapply(data_complete[colnames(data_complete[, c(5:(ncol(data_complete) - 2))])], factor)


  # When there is no price attribute
  data_complete_wide <- cbind(
      fastDummies::dummy_cols(data_complete[, c(5:(ncol(data_complete) - 2))])
      [, -c(1:var)], data_complete[, c("ID", "Choice_ID", "Alt", "choice")]
    )

    data_wide_full <- data_complete_wide

    independent_var <- colnames(data_complete[, c(5:(ncol(data_complete) - 2))])
    
    att$new_coded <- paste(c((rep(independent_var, 
                                  as.numeric(table(factor(att$attribute_text, levels = unique(att$attribute_text))))))), att$level_ids,
                           sep = "_")
    

    att <- att %>% filter(!(level_ids %in% remove_levels))

    data_complete <- data_wide_full[, c(
      att$new_coded, "ID", "Choice_ID", "Alt",
      "choice"
    )]

  # formula for clm function
  independent_var <- colnames(data_complete[, c(1:(ncol(data_complete) - 4))])
  independent_var_without_base <- independent_var[!independent_var %in% common_levs]
  formula_elements  <- c(independent_var_without_base)
  formula <- as.formula(paste("choice", paste(
    paste(c(formula_elements, "Intercept"),
      collapse = " + "
    ),
    "+ strata(Choice_ID)"
  ),
  sep = "~"
  ))

  data_complete <- data_complete %>% select(ID,Choice_ID,Alt,choice, everything())
  data_complete$Intercept <- rep(c(rep(0, each = (n_alt-1)), 1),nrow(data_complete)/n_alt)
  
  # clm model
  model <- clogit(formula, data_complete)

  # Standard error, p-value, coefficients
  model_results <- summary(model)
  r_sq <- round(performance::r2(model)$R2_Nagelkerke,2)
  names(r_sq) <- NULL
  p_value <- model_results$coefficients[, 5]
  
  significance <- data.frame(
    new_coded = names(p_value), p_value = p_value,
    significance = ifelse(p_value <= 0.05,
                          "Yes", "No"
    )
  )
  
  rownames(significance) <- NULL
  
  
  att_lev <- att %>% select(attribute_text, new_coded)
  

  att <- as.data.frame(att)
  
  att_orig <- att
  
  att$part_worth <- c(model_results$coefficients[, 1][match(
    att$new_coded,
    names(model_results$coefficients[, 1])
  )])
  
  att$se <- c(model_results$coefficients[, 3][match(
    att$new_coded,
    names(model_results$coefficients[, 3])
  )])
  
  att[is.na(att)] <- 0
  
  
  # Add significance estimates to parthworth values table
  merged_data <- merge(att, significance, by = "new_coded", all.x = TRUE)
  
  merged_data <- merged_data %>% arrange(level_ids)
  
  sign_s <- ifelse(model_results$coefficients[nrow(model_results$coefficients),5] <= 0.05, "Yes", "No")
  
  merged_data_s <- rbind(merged_data, c("Intercept", (max(merged_data$attribute_id)+1), "Intercept",  (nrow(merged_data)+1), "Intercept", 
                                        ifelse(is.na(model_results$coefficients[nrow(model_results$coefficients),1]), 0, model_results$coefficients[nrow(model_results$coefficients),1]),
                                        ifelse(is.na(model_results$coefficients[nrow(model_results$coefficients),3]), 0, model_results$coefficients[nrow(model_results$coefficients),3]),
                                        ifelse(is.na(model_results$coefficients[nrow(model_results$coefficients),5]), 0, model_results$coefficients[nrow(model_results$coefficients),5]),
                                        sign_s))
  
  
  merged_data$std_error <- round(2*as.numeric(merged_data$se)*exp(as.numeric(merged_data$part_worth))/(1+exp(as.numeric(merged_data$part_worth)))^2,2)
  
  merged_data$part_worth <- round(2*((exp(as.numeric(merged_data$part_worth))/(1+exp(as.numeric(merged_data$part_worth)))-0.5)),2)
  
  merged_data_s$std_error <- round(2*as.numeric(merged_data_s$se)*exp(as.numeric(merged_data_s$part_worth))/(1+exp(as.numeric(merged_data_s$part_worth)))^2,2)
  
  
  merged_data_s$AMCE <- round(2*((exp(as.numeric(merged_data_s$part_worth))/(1+exp(as.numeric(merged_data_s$part_worth)))-0.5)),2)
  
  
  merged_data_s <- merged_data_s %>% select(attribute_id, attribute_text, level_ids, level_text,part_worth, se, p_value, significance, AMCE, std_error)
  
  merged_data_s[is.na(merged_data_s)] <- "Base Level"
  merged_data_s[nrow(merged_data_s), c(8:9)] <- 0
  merged_data_s$p_value <- ifelse(merged_data_s$p_value == "Base Level", 1, round(as.numeric(merged_data_s$p_value),2))

  
  merged_data_s$AMCE_tstat <- ifelse(merged_data_s$std_error == "0", 0, 
                                     round((as.numeric(merged_data_s$AMCE)/as.numeric(merged_data_s$std_error)), 2))
  
  
  merged_data_s_p <- merged_data_s
  merged_data_s_p$lower_bound <- ifelse((round(merged_data_s_p$AMCE-(1.96*merged_data_s_p$std_error), 2)) <(-1),-1,
                                        round((merged_data_s_p$AMCE-(1.96*merged_data_s_p$std_error)),2))
  
  merged_data_s_p$upper_bound <- ifelse((round(merged_data_s_p$AMCE+(1.96*merged_data_s_p$std_error), 2)) > (1), 1,
                                        round((merged_data_s_p$AMCE+(1.96*merged_data_s_p$std_error)),2))
  
  merged_data_s_p <- merged_data_s_p %>% filter(attribute_text != "Intercept")
  merged_data_s_p$AMCE <- as.numeric(merged_data_s_p$AMCE)
  
 merged_data_s_p$formatted_part_worth <- ifelse(merged_data_s_p$significance == "Base Level", "Base Level", ifelse(merged_data_s_p$AMCE < 0, 
                                                                                                                      paste0("(", round(merged_data_s_p$AMCE,2), ")"),
                                                                                                                      round(merged_data_s_p$AMCE,2)))
 rownames(merged_data_s_p) <- NULL

 att_complete <- att

 att_complete <- att_complete %>% select(attribute_text, level_text, part_worth)

  ### Graph 2 GPT prompts

  data_b <- merged_data_s_p %>% filter(level_text != "Not available")


  cases_b <- paste(paste0("Feature: ", data_b$attribute_text),
    paste0("Level: ", paste0(
      data_b$level_text, "(",
      round(data_b$AMCE, 2), ")"
    )),
    sep = "\n"
  )

  text_in_2 <- paste(base::readLines(exp2_path), collapse = "\n")
 

  q_2 <- paste0(
    "I have a conjoint study aimed at the following question: ",
    paste0('"', question, '"'), 
    ", and the survey participants were given the following instruction: ",
    paste0('"', dependent_variable, '"'), 
    ", below are feature level importance results:"
  )

  prompt_2 <- paste0(
    "Experiment Results:", "\n",
    q_2, "\n",
    paste0(do.call(paste, c(as.list(cases_b), sep = "\n"))), "\n",
    "Answer the study question  in form of causal insights based on feature level importance results and the survey instruction."
  )
  
  q_amce <- paste0(
    "Experiment:", "\n",
    "I have a conjoint study aimed at the following question: ",
    paste0('"', question, '"'), 
    ", the survey participants were given the following instruction: ",
    paste0('"', dependent_variable, '".'),  "\n",
    "Suggest a title that will capture the topic of the survey based on the question and instruction, and will match the purpose of the graph."
  )
  
  text_in_amce <- paste(base::readLines(amce_title_path), collapse = "\n")

  # Utility calculation
  utility_data <- merged_data_s_p %>% filter(significance != "Base Level") %>%
    group_by(attribute_text) %>%
    dplyr::summarise(avg = mean(abs(AMCE)))
  
  coefs <- as.numeric(c(merged_data_s_p$part_worth, merged_data_s$part_worth[nrow(merged_data_s)]))
  
  importance <- sapply(seq_along(unique(merged_data_s_p$attribute_text)), function(i) {
    # Ensure a complete copy of data for each iteration
    merged_data_s_p1 <- merged_data_s_p
    merged_data_s_p1$new_coded <- colnames(data_complete)[5:(ncol(data_complete)-1)]
    
    # Filter for specific attribute
    att_l <- merged_data_s_p1 %>% 
      filter(attribute_text == unique(merged_data_s_p1$attribute_text)[i])
    
    # Subset data
    data_complete_new <- data_complete[, 5:(ncol(data_complete))]
    data_complete_0 <- data_complete_new
    
    # Zero out the columns for this attribute
    data_complete_0[, att_l$new_coded] <- 0
    
    # Find max and min attributes
    max_att <- att_l[which.max(att_l$part_worth), "new_coded"]
    min_att <- att_l[which.min(att_l$part_worth), "new_coded"]
    
    # Max scenario
    data_complete_max <- data_complete_0
    data_complete_max[, max_att] <- 1
    m_multiply_max <- data_complete_max * rep(unlist(coefs), each = nrow(data_complete_max))
    utility_max <- rowSums(m_multiply_max)
    reshaped_matrix_max <- as.data.frame(matrix(utility_max, 
                                                nrow = (nrow(data_complete_max)/n_alt), 
                                                byrow = TRUE))
    r_max <- exp(reshaped_matrix_max)
    prob_max <- r_max[, 1:(n_alt-1)]
    
    # Min scenario
    data_complete_min <- data_complete_0
    data_complete_min[, min_att] <- 1
    m_multiply_min <- data_complete_min * rep(unlist(coefs), each = nrow(data_complete_min))
    utility_min <- rowSums(m_multiply_min)
    reshaped_matrix_min <- as.data.frame(matrix(utility_min, 
                                                nrow = (nrow(data_complete_min)/n_alt), 
                                                byrow = TRUE))
    r_min <- exp(reshaped_matrix_min)
    prob_min <- r_min[, 1:(n_alt-1)]
    
    # Calculate difference
    prob_dif <- prob_max - prob_min
    Coef_Dif <- mean(unlist(prob_dif))
    
    return(Coef_Dif)
  })
  
  
  
  utility_data$importance <- importance
  
  utility_data$importance <- round(utility_data$importance/sum(utility_data$importance),2)
  
  # In case of NaN values
  utility_data$importance[is.na(utility_data$importance)] <- 0
  utility_data$attribute_text <- unique(merged_data_s_p$attribute_text)
  
  utility_data$attribute_text <- trimws(sub("\\s*\\(ln\\(.*", "", utility_data$attribute_text))
  
  utility_data <- utility_data %>%
    dplyr::select(-avg) %>%
    as.data.frame() %>% arrange(desc(importance))

  ### Graph 1 GPT prompts
  data_u <- utility_data
  data_u$perc <- paste0(round(data_u$importance * 100), "%")
  data_u$input <- paste0(data_u$attribute_text, " (", data_u$perc, ")")
  text_in_1 <- paste(base::readLines(exp1_path), collapse = "\n")
 
  q_1 <- paste0(
    "I have a conjoint study aimed at the following question: ",
    paste0('"', question, '"'),
    ", the survey participants were given the following instruction: ",
    paste0('"', dependent_variable, '"'),
    ", below are the results of relative importance of features:"
  )

  prompt_1 <- paste0(
    "Experiment Results:", "\n",
    q_1, "\n",
    paste0(do.call(paste, c(as.list(data_u$input), sep = "; "))),
    "\n", "Answer the study question in form of causal insights based on feature importance results and the survey instruction."
  )
  
  data_price <- att %>%
    filter(attribute_text %in% c("Price")) %>% filter(level_text != "Not available")
  data_price$level_text <- gsub(",", "", data_price$level_text)
  
  
  # Getting number of numeric values in the "Price" attribute
  if (nrow(data_price > 0)) {
    price <- foreach(
      i = 1:nrow(data_price), .combine = "c",
      .packages = c("stringr")
    ) %dopar% {
      a <- mean(as.numeric(str_extract_all(
        data_price[i, ]$level_text,
        "\\(?[0-9,.]+\\)?"
      )[[1]]))
      a
    }
  } else {
    price <- 0
  }
  

if(length(attr_json$monetary_attributes) > 0){
  if (any(grepl("Price", attr_json$monetary_attributes, ignore.case = TRUE) | 
          grepl("\\$", attr_json$monetary_attributes))) {
    atr <- att_complete %>% filter(attribute_text == 
                                     attr_json$monetary_attributes[which(grepl("price", attr_json$monetary_attributes, 
                                                                               ignore.case = TRUE) | 
                                                                           grepl("\\$", attr_json$monetary_attributes))[1]])
   if (!grepl("%", atr$level_text[1])){
    price_attribute <- "Price Attribute"
  } else{
    price_attribute <- "No Price Attribute"
  } 
   } else{
    price_attribute <- "No Price Attribute"
  }
} else {
  price_attribute <- "No Price Attribute"
}
  
  
  if ((sum(att$attribute_text %in% c("Price")) > 0) && ((sum(nrow(data_price) - length(price[!is.na(price)]))) < 1)) {
    price_attribute <- "Price Attribute"
  } else {
    price_attribute <- price_attribute
  }
  
  if (price_attribute == "Price Attribute") {
    if (length(attr_json$monetary_attributes) > 0) {
      # Check for either "price" or "$" in the monetary attributes
      price_match <- grepl("price", attr_json$monetary_attributes, ignore.case = TRUE) | 
        grepl("\\$", attr_json$monetary_attributes)
      
      if (any(price_match) && 
          !is.null(attr_json$monetary_attributes[which(price_match)[1]]) && 
          !is.na(attr_json$monetary_attributes[which(price_match)[1]])) {
        price_titles <- attr_json$monetary_attributes[which(price_match)[1]]
      } else {
        price_titles <- "Price"
      }
    } else {
      price_titles <- "Price"
    }
  } else {
    price_titles <- "No Price Attribute"
  }
  
  
  if (price_attribute == "Price Attribute"){
    price_lev <- att %>% filter (attribute_text == price_titles)
    euro <- c("Euros", "€", "EUR")
    pound <- c("GBP", "£")
    dollar <- c("USD", "$", "dollars")
    if (grepl(paste(euro, collapse="|"), price_lev$level_text[1])){
      currency = "€"
    } else if (grepl(paste(pound, collapse="|"), price_lev$level_text[1])){
      currency = "£"
    }else if (grepl(paste(dollar, collapse="|"), price_lev$level_text[1])){
      currency = "$"
    }else {
      currency = ""
    }
  } else {
    currency <- "No Price Attribute"
  }
  
  if (price_attribute == "Price Attribute"){
    price_lev <- att %>% filter (attribute_text == price_titles)
    million <- c("Million", "million", "mln")
    thousand <- c("Thousand", "thousand")
    if (grepl(paste(million, collapse="|"), price_lev$level_text[1])){
      estimate = " million"
    } else if (grepl(paste(thousand, collapse="|"), price_lev$level_text[1])){
      estimate = " thousand"
    } else{
      estimate = ""
    }
  }
  
  
  
  

# WTP calculation if there is a price attribute
  if (price_attribute == "Price Attribute"){
    merged_data_s_p$part_worth <- as.numeric(merged_data_s_p$part_worth)
    
    merged_data_s_p$level_ids <- as.numeric(merged_data_s_p$level_ids)
    data_coef_wtp <- merged_data_s_p %>%
      filter(attribute_text %in% price_titles) %>% filter(level_text != "Not available") %>% arrange(level_ids)
    
    # Get Baseline Price
    data_mix <- data_survey[6:(ncol(data_survey)-1)]
    
    price_data <- data_coef_wtp
    
    price <- foreach(
      i = 1:nrow(price_data), .combine = "c",
      .packages = c("stringr")
    ) %dopar% {
      a <- mean(as.numeric(str_extract_all(
        price_data[i, ]$level_text,
        "\\(?[0-9,.]+\\)?"
      )[[1]]))
      a
    }
    
    price_data$price <- price
    
    price_mapping <- setNames(price_data$price, price_data$level_ids)
    
    # Replace price levels in data_levels with actual prices
    data_levels <- data_mix[rowSums(data_mix != 0) > 0, ]
    
    # Replace price levels in data_levels with actual prices

    column_name <- price_titles[1]
    
    if (column_name %in% colnames (data_levels)) {
    data_levels[[column_name]] <- price_mapping[as.character(data_levels[[column_name]])]
    } else{
      column_name <- colnames(data_levels)[as.numeric(price_data$attribute_id[1])+1]
      data_levels[[column_name]] <- price_mapping[as.character(data_levels[[column_name]])]
    }

    
    # Initialize an empty list to store results
    results <- list()
    
    # Process each column except Price
    columns_to_process <- setdiff(names(data_levels), column_name)
    
    for (col in columns_to_process) {
      # Get unique values in the column (excluding zeros)
      unique_values <- unique(data_levels[[col]])
      unique_values <- unique_values[unique_values != 0]
      
      # For each unique value, calculate average price
      avg_prices <- sapply(unique_values, function(val) {
        subset_data <- data_levels[data_levels[[col]] == val, ]
        mean(subset_data[[column_name]], na.rm = TRUE)
      })
      
      # Create a data frame with the results
      col_result <- data.frame(
        level_ids = unique_values,
        baseline_price = round(avg_prices,2)
      )
      
      # Add to results list
      results[[col]] <- col_result
    }
    
    # Combine all results
    final_results <- do.call(rbind, results)
   
    
    
    extract_number <- function(text_vector) {
      # Initialize results vector
      results <- numeric(length(text_vector))
      
      for (i in seq_along(text_vector)) {
        # Extract all numbers from the string
        numbers <- as.numeric(unlist(regmatches(
          text_vector[i],
          gregexpr("\\d+(?:\\.\\d+)?", text_vector[i], perl = TRUE)
        )))
        
        # Handle different cases
        if (length(numbers) == 0) {
          # No numbers found
          results[i] <- NA
        } else if (length(numbers) > 1) {
          # Multiple numbers found - take the second one
          results[i] <- numbers[2]
        } else {
          # Single number found
          results[i] <- numbers[1]
        }
      }
      
      return(results)
    }
    
    quintiles <- extract_number(data_coef_wtp$level_text)
    
    price_coef <- (data_coef_wtp$part_worth[1] - data_coef_wtp$part_worth[nrow(data_coef_wtp)])/(quintiles[length(quintiles)] - quintiles[1])
    
    
  
      data_wtp <- merged_data_s_p %>%
      filter(!attribute_text %in% price_titles)

    data_wtp$wtp <- round(data_wtp$part_worth/(price_coef),2)

    data_wtp$formatted_wtp <- ifelse(data_wtp$significance == "Base Level", "Base Level", (ifelse(data_wtp$wtp < 0,
                                      paste0("(-", currency, formatC(-data_wtp$wtp,
                                                            format = "f", digits = 2,
                                                            big.mark = ","
                                      ), estimate, ")"),
                                      paste0(currency, formatC(data_wtp$wtp, format = "f", digits = 2, big.mark = ","), estimate))))
    

    data_wtp <- data_wtp %>% dplyr::select(attribute_text, level_text, wtp, level_ids, formatted_wtp)
    
    data_wtp <- merge(data_wtp, final_results, all.x = T)

  } else {
    data_wtp <- "no data"
  }

  ### Graph 3 GPT prompts
if (price_attribute == "Price Attribute"){

    data_b <- data_wtp 
    
    data_b$formatted_wtp <- gsub("[()]", "", data_b$formatted_wtp)
    
    data_b <- data_b %>% filter(level_text != "Not available")

    cases_b <- paste(paste0("Feature: ", data_b$attribute_text),
                     paste0("Level: ",  data_b$level_text, "(", data_b$formatted_wtp, ")"),
                     sep = "\n"
    )
    

    text_in_3 <- paste(base::readLines(exp3_path), collapse = "\n")
  
    
    q_3 <- paste0(
      "I have a conjoint study aimed at the following question: ",
      paste0('"', question, '"'), 
      ", and the survey participants were given the following instruction: ",
      paste0('"', dependent_variable, '"'), 
      ", below are willingness-to-pay results for feature levels:"
    
      )

    prompt_3 <- paste0(
      "Experiment Results:", "\n",
      q_3, "\n",
      paste0(do.call(paste, c(as.list(cases_b), sep = "\n"))),
      "\n", "Answer the study question  in form of causal insights based on willingness-to-pay results and the survey instruction."
    )
  } else {
    prompt_3 <- "no data"
  }
  
  
  create_chat_completion <- function(model, temperature, messages, api_key, endpoint, 
                                     api_version = "2024-12-01-preview", max_retries = 3) {
    
    url <- paste0(endpoint, "/openai/deployments/", model, "/chat/completions?api-version=", api_version)
    
    body <- list(
      messages = messages,
      temperature = temperature
    )
    
    for (attempt in 1:max_retries) {
      # Make the API request
      response <- tryCatch({
        POST(
          url = url,
          add_headers("api-key" = api_key, 
                      "Content-Type" = "application/json"),
          body = toJSON(body, auto_unbox = TRUE),
          encode = "json"
        )
      }, error = function(e) {
        return(NULL)
      })
      
      # Check if request was successful
      if (!is.null(response) && status_code(response) == 200) {
        content <- content(response, "parsed")
        if (!is.null(content$choices) && length(content$choices) > 0) {
          return(content)
        }
      }
      
      # If we get here, the request failed or returned empty
      # Wait with exponential backoff before retrying
      if (attempt < max_retries) {
        wait_time <- 2^attempt  # Exponential backoff: 2, 4, 8... seconds
        Sys.sleep(wait_time)
      }
    }
    
    # Return empty response after all retries failed
    return(list(choices = list(list(message = list(content = " ")))))
  }
  
  
  # Your main code with the modifications
  if (price_attribute == "Price Attribute"){
    tab_1_prompts <- c(prompt_1, prompt_2, prompt_3)
    contents <- c(text_in_1, text_in_2, text_in_3)
    tab_1_prompt_output <- foreach(
      i = 1:length(tab_1_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "model_name", "r_path")
    ) %dopar% {
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini" #"gpt-4ov2"  # Update this to match your deployed model name
      
      
      answer <- tryCatch(
        {
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = contents[i]
              ),
              list(
                "role" = "user",
                "content" = tab_1_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          " "
        }
      )
      answer
    }
    if (is.na(tab_1_prompt_output[1]) || (is.null(tab_1_prompt_output[1])) || (tab_1_prompt_output[1] == " ")){
      utility_int <- ""
    } else {
      utility_int <- paste("How to interpret:", tab_1_prompt_output[1])
    }
    
    if (is.na(tab_1_prompt_output[2]) || (is.null(tab_1_prompt_output[2])) || (tab_1_prompt_output[2] == " ")){
      part_worths_int <- ""
    } else {
      part_worths_int <- paste("How to interpret:", tab_1_prompt_output[2])
    }
    
    if (is.na(tab_1_prompt_output[3]) || (is.null(tab_1_prompt_output[3])) || (tab_1_prompt_output[3] == " ")){
      data_wtp_int <- ""
    } else {
      data_wtp_int <- paste("How to interpret:", tab_1_prompt_output[3])
    }
   
  } else {
    tab_1_prompts <- c(prompt_1, prompt_2)
    contents <- c(text_in_1, text_in_2)
    tab_1_prompt_output <- foreach(
      i = 1:length(tab_1_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "model_name", "r_path")
    ) %dopar% {
      
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
        
      answer <- tryCatch(
        {
          
          
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = contents[i]
              ),
              list(
                "role" = "user",
                "content" = tab_1_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          " "
        }
      )
      answer
    }
    if (is.na(tab_1_prompt_output[1]) || (is.null(tab_1_prompt_output[1])) || (tab_1_prompt_output[1] == " ")){
      utility_int <- ""
    } else {
      utility_int <- paste("How to interpret:", tab_1_prompt_output[1])
    }
    
    if (is.na(tab_1_prompt_output[2]) || (is.null(tab_1_prompt_output[2])) || (tab_1_prompt_output[2] == " ")){
      part_worths_int <- ""
    } else {
      part_worths_int <- paste("How to interpret:", tab_1_prompt_output[2])
    }
    
    data_wtp_int <- " no data"
  }
  
 

# GPT insights 1 prompts
  
  # The main code block with Azure OpenAI implementation
  if (price_attribute == "Price Attribute"){
    sum_text <- paste(substring(part_worths_int, 19),
                      substring(utility_int, 19), substring(data_wtp_int, 19),
                      sep = "\n"
    )
    
    Ins_1_system <- paste(base::readLines(ins_1_path), collapse = "\n")
    
    Ans_1_system <- paste(base::readLines(ans_1_path), collapse = "\n")
    
    
    aa <- paste0("Experiment Results:", "\n",
                 paste0(
                   "I have a conjoint study aimed at the following question: ",
                   paste0('"', question, '"'),
                   ", and the survey participants were given the following instruction: ",
                   paste0('"', dependent_variable, '"'), 
                   ", below are analysis results:"
                 ), "\n",
                 sum_text, "\n",
                 "Answer the study question in 4-5 short bullet points of actionable business recommendations based on the survey results and the survey instruction."
    )
    
    aa_2 <- paste0("Experiment Results:", "\n",
                   paste0(
                     "I have a conjoint study aimed at the following question: ",
                     paste0('"', question, '"'),
                     ", and the survey participants were given the following instruction: ",
                     paste0('"', dependent_variable, '"'), 
                     ", below are analysis results:"
                   ), "\n",
                   sum_text, "\n",
                   "Answer the study question based on the survey results and the survey instruction."
    )
    
    contents <- c(Ins_1_system, Ans_1_system, text_in_amce)
    tab_1_prompts <- c(aa, aa_2, q_amce)
    
    tab_1_output <- foreach(
      i = 1:length(tab_1_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "r_path")
    ) %dopar% {
      
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
      
      answer <- tryCatch(
        {
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = contents[i]
              ),
              list(
                "role" = "user",
                "content" = tab_1_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          " "
        }
      )
      answer
    }
    
    ins_1 <-  tab_1_output[1]
    ans_1 <-  tab_1_output[2]
    amce_graph_title <- tab_1_output[3]
  } else {
    sum_text <- paste(substring(part_worths_int, 19), substring(utility_int, 19),
                      sep = "\n"
    )
    
    Ins_1_system <- paste(base::readLines(ins_1_path), collapse = "\n")
    
    Ans_1_system <- paste(base::readLines(ans_1_path), collapse = "\n")
    
    
    aa <- paste0("Experiment Results:", "\n",
                 paste0(
                   "I have a conjoint study aimed at the following question: ",
                   paste0('"', question, '"'),
                   ", and the survey participants were given the following instruction: ",
                   paste0('"', dependent_variable, '"'), 
                   ", below are analysis results:"
                 ), "\n",
                 sum_text, "\n",
                 "Answer the study question in 4-5 short bullet points of actionable business recommendations based on the survey results and the survey instruction."
    )
    
    aa_2 <- paste0("Experiment Results:", "\n",
                   paste0(
                     "I have a conjoint study aimed at the following question: ",
                     paste0('"', question, '"'),
                     ", and the survey participants were given the following instruction: ",
                     paste0('"', dependent_variable, '"'), 
                     ", below are analysis results:"
                   ), "\n",
                   sum_text, "\n",
                   "Answer the study question based on the survey results and the survey instruction.."
    )
    
    
    contents <- c(Ins_1_system, Ans_1_system, text_in_amce)
    tab_1_prompts <- c(aa, aa_2, q_amce)
    
    tab_1_output <- foreach(
      i = 1:length(tab_1_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "r_path")
    ) %dopar% {
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
      
      answer <- tryCatch(
        {
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = contents[i]
              ),
              list(
                "role" = "user",
                "content" = tab_1_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          " "
        }
      )
      answer
    }
    
    ins_1 <-  tab_1_output[1]
    ans_1 <-  tab_1_output[2]
    amce_graph_title <- tab_1_output[3]
  }
 

   if (price_attribute == "Price Attribute"){
    estimate <- estimate
  } else {
    estimate <- "No Price Attribute"
  }
  
  if (price_attribute == "Price Attribute"){
    quintiles <- quintiles
  } else {
    quintiles <- "No Price Attribute"
  }
  
  
  if(length(attr_json$realworld_products) == 0 | nrow(as.data.frame(attr_json$realworld_products)) <= 1){
    generated_products <- "No Real Product"
  } else{
    products <- as.data.frame(attr_json$realworld_products)
    real_products <- as.data.frame(t(products))
    # If there are more than 5 products, only take the first 5
    if(ncol(real_products)>5){
      real_products <- real_products[,1:5]
    }
    real_products <- real_products[, colSums(is.na(real_products)) == 0]
    if(ncol(real_products)< 2){
      generated_products <- "No Real Product"
    } else{
    
    colnames(real_products) <- paste0("Prod", c(1:5))[1:ncol(real_products)]
    real_products$attribute_text <- rownames(real_products)
    rownames(real_products) <- NULL
    real_products <- merge(real_products, attr_type, all.y = T)
    real_products <- real_products %>% select(attribute_id, attribute_text, type, everything())
    attribute_list <- att[c(2,4)]
    
    if(anyNA(real_products)){
      generated_products <- "No Real Product"
    } else{
      
    
    # real_products_df <- real_products
    # attribute_list_df <- attribute_list
    # col <- "Prod1"
    # i <- 5
    # brand_real <- brand_real
    # brand_exp <- brand_exp$level_text
    
    transform_products <- function(real_products_df, attribute_list_df) {
      # Get all column names starting with "Prod"
      prod_cols <- grep("^Prod", names(real_products_df), value = TRUE)
      
      # For each Prod column
      for (col in prod_cols) {
        # Process each row
        for (i in 1:nrow(real_products_df)) {
        
        if (real_products_df$attribute_text[i] == "Brand"){
         brand_real <- real_products_df[i, col]
         brand_exp <- att_complete %>% filter(attribute_text == "Brand")
         
         find_best_match <- function(brand_real, brand_exp, threshold = 0.6) {
           # Calculate string similarity for each option
           similarities <- sapply(brand_exp, function(exp) {
             # Use Jaro-Winkler distance as it works well for brand names
             1 - stringdist(tolower(brand_real), tolower(exp), method = "jw")
           })
           
           # Find the most similar one and its similarity score
           best_match_idx <- which.max(similarities)
           best_match <- brand_exp[best_match_idx]
           best_score <- similarities[best_match_idx]
           
           # Return the match only if similarity is above threshold
           if (best_score > threshold) {
             return(best_match)
           } else {
             return(brand_real)
           }
         }
         
         brand_new <- find_best_match(brand_real, brand_exp$level_text)
         
         real_products_df[i, col] <- brand_new
        }
          
          # Only transform if type is Continuous
          if (real_products_df$type[i] == "Continuous" | real_products_df$attribute_text[i] %in% price_titles) {
            # Get current value and extract numeric part
            current_val <- real_products_df[i, col]
            
            process_value <- function(current_val) {
              # Check if the string contains a hyphen (indicating a range)
              if (grepl("-", current_val)) {
                # Split the string by hyphen
                parts <- strsplit(current_val, "-")[[1]]
                
                # Extract numeric values from each part
                first_val <- as.numeric(gsub("[^0-9.]", "", parts[1]))
                second_val <- as.numeric(gsub("[^0-9.]", "", parts[2]))
                
                # Return the average
                return((first_val + second_val) / 2)
              } else {
                # If not a range, just return the numeric value as before
                return(as.numeric(gsub("[^0-9.]", "", current_val)))
              }
            }
            
            numeric_val <- process_value(current_val)
            
            # Get the attribute text for current row
            curr_attribute <- real_products_df$attribute_text[i]
            
            # Filter attribute_list for current attribute
            ranges <- attribute_list_df[attribute_list_df$attribute_text == curr_attribute, ]
            
            if (is.na(numeric_val)){
              real_products_df[i, col] <- real_products_df[i, col]
            } else {
            
            # Find matching range and get level_text
            for (j in 1:nrow(ranges)) {
              # Extract range from level_text
              range_str <- ranges$level_text[j]
              range_nums <-  as.numeric(unlist(regmatches(range_str, gregexpr("[0-9]+(\\.[0-9]+)?", range_str))))
              
              # Check if value falls in range
              if (length(range_nums) == 2 && 
                  numeric_val >= range_nums[1] && 
                  numeric_val <= range_nums[2]) {
                real_products_df[i, col] <- ranges$level_text[j]
                break
              } else if (length(range_nums) == 1) {
                if ((grepl("Less than", range_str) && numeric_val < range_nums) || (numeric_val < range_nums)) {
                  real_products_df[i, col] <- ranges$level_text[j]
                  break
                } else if (grepl("More than", range_str) && numeric_val > range_nums) {
                  real_products_df[i, col] <- ranges$level_text[j]
                  break
                }
              }
            }
            }
          }
        }
      }
      real_products_df <- real_products_df %>% arrange(attribute_id) %>% select(-attribute_text, -type, -attribute_id)

      return(real_products_df)
    }
    
    generated_products <- transform_products(real_products, attribute_list)
    
    }}}
  
  if((class(generated_products) != "character") && (any(att$level_text == "Not available"))){
    common_levs_data <- as.data.frame(lapply(data_complete_orig[,7:ncol(data_complete_orig)-2], get_single_mode))
    
    if(length(attr_continuouis) > 0){
      
      remove_elements_by_prefix <- function(input_vector, prefixes) {
        # Create a pattern that matches any of the prefixes
        # escape special characters in prefixes and combine with OR operator
        pattern <- paste0("^(", 
                          paste(gsub("([.|()\\[])", "\\\\\\1", prefixes), 
                                collapse="|"), 
                          ")")
        
        # Keep only elements that DON'T match the pattern
        result <- input_vector[!grepl(pattern, input_vector)]
        
        return(result)
      }
      
      att_c <- foreach(a = c(attr_continuouis), .combine = "c") %dopar% {
        a <- colnames(survey)[6+a]
      }
      
      common_levs_init <- paste0(colnames(common_levs_data), "_", common_levs_data[1,])
      
      common_levs_discrete <- remove_elements_by_prefix(common_levs_init, paste0(att_c, "_"))
      
      # Initialize empty vector to store all common levels
      all_common_levs <- c()
      
      
      for(i in c(attr_continuouis)){
        Variable_ID <- att_orig %>% filter(attribute_id %in% i)
        # Create common_levs for this iteration
        current_common_levs <- c(common_levs_discrete, 
                                 paste0( colnames(survey)[[6+i]], "_", Variable_ID$level_ids[3]))
        
        # Append to our running vector of all common levels
        all_common_levs <- c(all_common_levs, current_common_levs)
        
      }
      common_levs <- unique(all_common_levs)
      
    }else{
      common_levs <- paste0(colnames(common_levs_data), "_", common_levs_data[1,])
    }
    
    ### Not available:
    name <- colnames(data_complete_orig[,7:ncol(data_complete_orig)-2])
    cod <- att_orig$level_ids[which(att_orig$level_text == "Not available")]
    not_available_levels <- paste0(name, "_", cod)
    
    
    # Getting independent variables
    var <- ncol(data_complete_orig[, c(5:(ncol(data_complete_orig) - 2))])
    
    # Setting independent variables as type 'factor'
    data_complete_orig[ colnames(data_complete_orig[, c(5:(ncol(data_complete_orig) - 2))])] <-
      lapply(data_complete_orig[colnames(data_complete_orig[, c(5:(ncol(data_complete_orig) - 2))])], factor)
    
    
    # When there is no price attribute
    data_complete_wide <- cbind(
      fastDummies::dummy_cols(data_complete_orig[, c(5:(ncol(data_complete_orig) - 2))])
      [, -c(1:var)], data_complete_orig[, c("ID", "Choice_ID", "Alt", "choice")]
    )
    
    data_wide_full <- data_complete_wide
    
    independent_var <- colnames(data_complete_orig[, c(5:(ncol(data_complete_orig) - 2))])
    
    att_orig$new_coded <- paste(c((rep(independent_var, 
                                  as.numeric(table(factor(att_orig$attribute_text, levels = unique(att_orig$attribute_text))))))), att_orig$level_ids,
                           sep = "_")
    
    
    att_orig <- att_orig %>% filter(!(level_ids %in% remove_levels))
    
    
    data_complete_orig <- data_wide_full[, c(
      att_orig$new_coded, "ID", "Choice_ID", "Alt",
      "choice"
    )]
    
    # formula for clm function
    independent_var <- colnames(data_complete_orig[, c(1:(ncol(data_complete_orig) - 4))])
    independent_var_without_base <- independent_var[!independent_var %in% common_levs]
    
    #### New Method
    
    constraits <- ifelse(c(independent_var_without_base, "Intercept")  %in% not_available_levels, -1, 0)  # -1 orig
    params <- rep(0.1, length(constraits))
    
    params_names <- c(independent_var_without_base, "Intercept")
    
    names(params) <- params_names
    
    mnl_loglik <- function(params, data, constraits, parameterized) {
      # Extract dimensions
      n_alt <- length(unique(data$Alt))
      n_obs <- length(unique(data$ID))
      
      if (parameterized == 1){
        params[constraits == 1]  <- exp(params[constraits == 1])
        params[constraits == -1] <- -exp(params[constraits == -1])
        params[constraits == 10] <- 1/(1+exp(-params[constraits == 10]))
        params[constraits == 10] <- log(params[constraits == 10])
        
      }
      
      #params[constraits == 10] <- log(params[constraits == 10])
      
      # Assuming a generic format: utility = X * beta
      X <- as.matrix(data[, params_names])  # predictors named X1, X2, etc.
      beta <- matrix(params, ncol = 1)
      
      # Compute utilities
      utility <- X %*% beta
      
      utility_mat <- as.data.frame(matrix(utility, nrow = (nrow(data)/n_alt), byrow = TRUE))
      
      # Reshape into a matrix of (n_alt x n_obs)
      #utility_mat <- matrix(utility, ncol = n_obs)
      
      # Compute choice probabilities using softmax per choice set
      exp_util <- exp(utility_mat)
      prob_mat <- exp_util / rowSums(exp_util)
      
      # Indicator matrix: 1 if alt was chosen, 0 otherwise
      chosen <- as.data.frame(matrix(data$choice, nrow = (nrow(data)/n_alt), byrow = TRUE))
      
      # Log-likelihood
      log_likelihood <- sum(log(prob_mat[chosen == 1]))
      
      return(log_likelihood)
    }
    
    data_complete_orig <- data_complete_orig %>% select(ID,Choice_ID,Alt,choice, everything())
    data_complete_orig$Intercept <- rep(c(rep(0, each = (n_alt-1)), 1),nrow(data_complete_orig)/n_alt)
    
    
    
    # model
    model <- maxLik::maxLik(logLik = mnl_loglik, data = data_complete_orig,constraits = constraits, start = params, method = "BFGS", parameterized = 1,
                            control=list(reltol=2.718^(-5), gradtol=10^(-5), iterlim = 500))
    
    
    # Standard error, p-value, coefficients
    model_results <- summary(model)
    
    params <- model_results$estimate[,1]
    
    params[constraits == 1]  <- exp(params[constraits == 1])
    params[constraits == -1] <- -exp(params[constraits == -1])
    params[constraits == 10] <- 1/(1+exp(-params[constraits == 10]))
    params[constraits == 10] <- log(params[constraits == 10])
    
    
    
    model <- maxLik::maxLik(logLik = mnl_loglik, data = data_complete_orig,constraits = constraits, start = params, method = "BFGS", parameterized = 0,
                            control=list(reltol=2.718^(-5), gradtol=10^(-5), iterlim = 0))
    
    
    model_results <- summary(model)
    
    att_orig$part_worth <- c(model_results$estimate[, 1][match(
      att_orig$new_coded,
      names(model_results$estimate[, 1])
    )])
    
    market_share_est <- att_orig %>% select(attribute_text, level_text, part_worth)
    
    market_share_est <- rbind(market_share_est, c("Intercept", "Intercept", model_results$estimate[nrow(model_results$estimate), 1]))
    
    market_share_est[is.na(market_share_est)] <- 0
    
  } else{
    market_share_est <- "No Data"
  }
  
  
  if(class(generated_products) == "character"){
    market_share_suggestion <- "No Real Product"
  } else{
    replace_missing <- function(value) {
      if (!(value %in% market_share_est$level_text)) {
        return("Not available")
      } else {
        return(value)
      }
    }
    
    # Apply the function to each element in the dataframe
    for (i in 1:ncol(generated_products)) {
      for (j in 1:nrow(generated_products)) {
        generated_products[j, i] <- replace_missing(generated_products[j, i])
      }
    }
  
    Products <- generated_products
    n_p <- ncol(Products)
    att_complete <- att_complete %>% filter (level_text != "Not available")
    Ind <- market_share_est
    prod_names <- paste("Scenario", c(1:5), sep = " ")[1:ncol(Products)]
    market_share_data <- market_share_est
    
    at_products <- unique(market_share_data$attribute_text)
    at_products <- at_products[at_products != "Intercept"]
    Preferences <- gather(Products, Scenario, level_text)
    Preferences$attribute_text <- rep(at_products, length(unique(Preferences$Scenario)))
    Preferences <- rbind(Preferences, c("Intercept", "Intercept", "Intercept"))
    
    coef <- market_share_data %>% dplyr::select(part_worth, attribute_text, level_text)
    
    coef$part_worth <- as.numeric(coef$part_worth)
    
    d1 <- merge(Preferences, coef, by = c("level_text", "attribute_text"), all = T) %>% tidyr::drop_na() %>% 
      dplyr::select(-attribute_text,-level_text)
    
    m_share <- d1 %>% group_by(Scenario) %>% summarise(utility = sum(part_worth)) %>% as.data.frame()
    
    m_share$share <- round(exp(m_share$utility)/sum(exp(m_share$utility)),6)
    
    names_prod <- c("Opt Out",
                    "Scenario 1", "Scenario 2", "Scenario 3",
                    "Scenario 4", "Scenario 5"
    )
    
    m_share$Scenario <- names_prod[1:nrow(m_share)]
    m_share <- m_share %>% dplyr::select(-utility)
    download_data5 <- m_share
    
    
    data_complete_sugg <- foreach(
      g = 1:length(prod_names),
      .packages = c("dplyr", "tidyr", "foreach"), .export = c("att_complete", "Ind")
    ) %dopar% {
      prod <- paste0("Prod", extract_numeric(prod_names[g]))
      attr <- att_complete %>%
        select(attribute_text) %>%
        unique() %>%
        pull()
      
      data_attr <- foreach(
        t = 1:length(attr),
        .packages = c("dplyr", "foreach", "tidyr"), .export = c("att_complete", "Ind")
      ) %dopar% {
        att <- which(attr == attr[t])
        data <- gather(Products, Scenario, level_text)
        at_products <- unique(Ind$attribute_text)
        at_products <- at_products[at_products != "Intercept"]
        data$attribute_text <- rep(at_products, length(unique(data$Scenario)))
        data <- rbind(data, c("Intercept", "Intercept", "Intercept"))
        
        
        coef <- att_complete %>%
          dplyr::select(part_worth, attribute_text, level_text)
        
        sel_group <- att_complete %>%
          dplyr::select(part_worth, level_text, attribute_text) %>%
          group_by(attribute_text) %>%
          top_n(2, part_worth)
        
        levels_1 <- att_complete %>%
          filter(attribute_text == attr[t]) %>%
          dplyr::select(level_text) %>%
          pull()
        
        levels <- intersect(levels_1, sel_group$level_text)
        
        data_o <- gather(Products, Scenario, level_text)
        at_products <- unique(Ind$attribute_text)
        at_products <- at_products[at_products != "Intercept"]
        data_o$attribute_text <- rep(at_products, length(unique(data_o$Scenario)))
        data_o <- rbind(data_o, c("Intercept", "Intercept", "Intercept"))
        
        
        
        m_share_att <- foreach(i = 1:length(levels), .packages = c("dplyr"),
                               .export = c("Ind", "data_o")) %dopar% {
                                 previous_level <- data_o[data_o$Scenario == prod, ][att, ]$level_text
                                 data[data$Scenario == prod, ][att, ]$level_text <- levels[i]
                                 
                                 coef <- Ind %>% dplyr::select (part_worth, level_text, attribute_text)
                                 coef$part_worth <- as.numeric(coef$part_worth)
                                 
                                 d1 <- merge(data, coef, by = c("level_text", "attribute_text"), all = TRUE) %>%
                                   tidyr::drop_na() %>%
                                   dplyr::select(-level_text, -attribute_text)
                                 
                                 m_share <- d1 %>% group_by(Scenario) %>% summarise(utility = sum(part_worth)) %>% as.data.frame()
                                 
                                 m_share$share <- round(exp(m_share$utility)/sum(exp(m_share$utility)),6)
                                 
                                 m_share$previous_level <- previous_level
                                 
                                 names_prod <- c("Opt Out",
                                                 "Scenario 1", "Scenario 2", "Scenario 3",
                                                 "Scenario 4", "Scenario 5"
                                 )
                                 
                                 m_share$Scenario <- names_prod[1:nrow(m_share)]
                                 m_share <- m_share %>% dplyr::select(-utility)
                                 m_share$level_text <- levels[i]
                                 m_share
                               }
        m_share <- do.call(rbind.data.frame, m_share_att)
        m_share$level_text <- factor(m_share$level_text, levels = levels)
        m_share$order <- c(1:nrow(m_share))
        m_share <- m_share %>% dplyr::select(-order)
        m_share$attribute <- attr[[t]]
        m_share
      }
      dat <- do.call(rbind.data.frame, data_attr)
      dat <- dat %>% filter(Scenario == paste0("Scenario", " ", g))
      dat
    }
    data <- do.call(rbind.data.frame, data_complete_sugg)
    data_rec_price <- data
    
    # Suggestions 
    data <- data_rec_price %>% spread(Scenario, share)
    names(data)[1:3] <- c("Previous Level", "Attribute Level", "Attribute")
    data <- data %>% dplyr::select(Attribute, everything())
    
    suggestions <- foreach(f = 1:length(prod_names),
      .packages = c("dplyr", "tidyr", "foreach"), .export = c("att_complete", "Ind", "prod_names", "data", "price_titles", "Products", 
                                                              "download_data5", "str_extract_all", "currency", "real_products")
    ) %dopar% {
    selected_index <- as.numeric(gsub("Scenario ([0-9]+).*", "\\1", prod_names[f]))
    product <- which(colnames(data) == paste0("Scenario ", selected_index))
    data_1 <- data[c(1, 2, 3, product)]
    names(data_1)[4] <- "Scenario"
    cond <- length(unique(unique(data_1$Attribute) %in% price_titles))
    if (cond == 2) {
      products <- Products
      names_prod <- c(
        "Scenario 1", "Scenario 2", "Scenario 3",
        "Scenario 4", "Scenario 5"
      )
      products$Scenario <- names_prod[1:nrow(products)]
      Preferences <- gather(products, Scenario, level_text)
      
      att <- att_complete
      att_prices <- att %>%
        filter(attribute_text %in% price_titles) %>%
        dplyr::select(level_text) %>%
        pull()
      
      small_data <- as.data.frame(Preferences) %>% filter(level_text %in% att_prices)
      small_data$Scenario <- names_prod[1:nrow(small_data)]
      names(small_data)[2] <- "level_text_2"
      small_data <- small_data %>% filter(Scenario == paste0("Scenario ", selected_index))
      small_data$level_text_2 <- gsub(",", "", small_data$level_text_2)
      price <- mean(as.numeric(str_extract_all(small_data$level_text_2, "\\(?[0-9,.]+\\)?")[[1]]))
      
      data_1$Scenario <- as.numeric(data_1$Scenario)
      Pref_share <- download_data5 %>%
        filter(Scenario == paste0("Scenario ", selected_index))
      data_1$original_share <- Pref_share$share
      data_1$diff <- round(data_1$Scenario - data_1$original_share, 3)
      data_1$price <- price
      c_pr <- c()
      for (t in 1:nrow(data_1)) {
        if (data_1[t, 1] %in% price_titles) {
          a <- gsub(",", "", data_1[t, 2])
          c_p <- mean(as.numeric(str_extract_all(a, "\\(?[0-9,.]+\\)?")[[1]]))
        } else {
          c_p <- price
        }
        c_pr <- c(c_pr, c_p)
      }
      data_1$con <- c_pr
      data_1$price_n <- c_pr
      
      data_1$rev_d <- ((data_1$Scenario * data_1$price_n) -
                         (data_1$original_share * data_1$price)) * 1000
      
      nDigits <- function(x) nchar(trunc(abs(x)))
      data_1$Revenue_M <- ifelse(nDigits(round(data_1$rev_d)) >= 7,
                                 paste(format(round(data_1$rev_d / 1e6, 1), trim = TRUE), "M"),
                                 (ifelse(nDigits(round(data_1$rev_d)) >= 3,
                                         paste(format(round(data_1$rev_d / 1e3, 1), trim = TRUE), "k"),
                                         round(data_1$rev_d)
                                 ))
      )
      
      data_1$Revenue_M <- gsub(" ", "", data_1$Revenue_M, fixed = TRUE)
      data_1$sign <- ifelse(data_1$rev_d > 0, "increase", "decrease")
      
      data_1 <- data_1 %>%
        filter(rev_d > 0) %>%
        filter(!Attribute %in% c("Brand", price_titles)) %>%
        arrange(desc(rev_d))
      data_sel <- head(data_1, 5)
      
      if (nrow(data_sel) > 0) {
        data_sel <- data_sel
        data_sel$suggestion <-  paste0(
          "When changing ", "'", data_sel[, 1], "'",  " from ", "'", data_sel[, 2], "'",
          " to ", "'", data_sel[, 3], "'", ":")
        
        data_sel$market_share <-  paste0("+", round(data_sel[, 6] * 100, 2), "% in expected share")
        data_sel$revenue <-  paste0("+", currency, data_sel[, 11], " in revenue")
        data_sel <- data_sel[,(ncol(data_sel)-2):ncol(data_sel)]
        
      } else {
        data_sel <- "Optimal levels are already selected."
      }
    } else {
      data_1$Scenario <- as.numeric(data_1$Scenario)
      Pref_share <- download_data5 %>%
        filter(Scenario == paste0("Scenario ", selected_index))
      data_1$original_share <- Pref_share$share
      data_1$diff <- round(data_1$Scenario - data_1$original_share, 3)
      
      data_1 <- data_1 %>%
        filter(diff > 0) %>%
        filter(!Attribute %in% c("Brand", price_titles)) %>%
        arrange(desc(Scenario))
      data_sel <- head(data_1, 5)
      if (nrow(data_sel) > 0) {
        data_sel$suggestion <-  paste0(
          "When changing ", "'", data_sel[, 1], "'",  " from ", "'", data_sel[, 2], "'",
          " to ", "'", data_sel[, 3], "'", ":")
        
        data_sel$market_share <-  paste0("+", round(data_sel[, 6] * 100, 2), "% in expected share")
        data_sel <- data_sel[,(ncol(data_sel)-1):ncol(data_sel)]
      } else {
        data_sel <- "Optimal levels are already selected."
      }
    }
    
    }
    if("Brand" %in% real_products$attribute_text){
    brand <- real_products %>% filter(attribute_text == "Brand")
    names(suggestions) <- paste0(prod_names, " (", brand[, 4:ncol(brand)], ")")
    } else{
      names(suggestions) <- paste0(prod_names)
    }
      
      market_share_suggestion <- suggestions
  }
  
  
  
  

  return(list(AMCE_Data = merged_data_s_p, PartWorths_int = part_worths_int, AMCE_graph_title = amce_graph_title,
    Utility = utility_data, Utility_int = utility_int, att = att, att_complete = att_complete,
    Data_complete = data_complete, Data_persona_id = Demo_Data_Complete,
    n_resp = n_resp, n_responses = n_responses, currency = currency, estimate = estimate, order_att = order_att, dependent_variable = dependent_variable,
    none_sel = none_sel, question = question, formula_elements =  formula_elements,
    Data_WTP = data_wtp,  Data_WTP_int = data_wtp_int, Ins_1 = ins_1, Ans_1 = ans_1,
    price_titles = price_titles, price_attribute = price_attribute, quintiles =  quintiles,
    merged_data_s = merged_data_s, r_sq = r_sq,
    generated_products = generated_products, Survey_Country = Survey_Country, Survey_State = Survey_State, Survey_Year = Survey_Year,
    Market_Share_Suggestion = market_share_suggestion, market_share_est = market_share_est, Persona_Type = Persona_Type
  ))
}

#' Error Handling for Attributes and Surveys
#'
#' @description This function handles potential errors in attributes and surveys  related to
#' insufficient number of respondents or tasks per respondent. It calculates AMCE data per respondent and
#' recovers respondents trait data based on encoded data.
#'
#' @param attributes A JSON object containing attribute information (levels, codings).
#' @param survey A dataframe containing survey data.
#'
#' @return A list containing partworth estimates (per respondent), demographic characteristics data (per respondent) and
#' precalculated data to be used in upcoming functions
#'
#' @examples
#' \dontrun{
#' error_handling(attributes = your_attr_data, survey = your_survey_data)
#' }
#' @export


#### Error Handling_1
error_handling_1 <- function(attributes, survey) {
  # Getting all precalculated data
  pre_calculated <- utility_estimation(attributes = attributes, survey = survey)
  data_complete <- pre_calculated$Data_complete
  demo_data <- pre_calculated$Data_persona_id
  att <- pre_calculated$att
  order_att <- pre_calculated$order_att
 
  formula_elements <- pre_calculated$formula_elements
  formula <- as.formula(paste("choice", paste(
    paste(c(formula_elements, "Intercept"),
          collapse = " + "
    ),
    "+ strata(Choice_ID)"
  ),
  sep = "~"
  ))



  # Calculating estimates per person/ID
  part_worths_ind <- foreach(
    i = 1:length(unique(demo_data$ID)),
    .packages = c("dplyr", "survival")
  ) %dopar% {
    tryCatch(
      {
        # Getting survey data per person/ID
        data <- data_complete %>% filter(ID == unique(demo_data$ID)[i])
        

        # clogit model
        model <- clogit(formula, data)

        # p-value, coefficients
        model_results <- summary(model)
        p_value <- model_results$coefficients[, 5]
        
        att_lev <- att %>% select(attribute_text, new_coded)
        

        # Partworths calculation
        att$part_worth <- c(model_results$coefficients[, 1][match(
          att$new_coded,
          names(model_results$coefficients[, 1])
        )])
        
        att$ID <- unique(demo_data$ID)[i]
        
        # Getting structured data for partworths
        merged_data <- att

        part_worths_data <- merged_data %>%
          group_by(attribute_text) %>%
          mutate(across(part_worth, ~ if_else(is.na(.x), 0, .x))) %>%
          ungroup() %>%
          as.data.frame() %>%
          select(attribute_text, level_text, part_worth, ID)
        
        part_worths_data$part_worth <- round(as.numeric(part_worths_data$part_worth),5)
        
        if(nrow( merge(part_worths_data, order_att, all.x = TRUE)) > nrow(part_worths_data)){
        part_worths_data$row <- order_att$row
          
        } else{
        part_worths_data <- merge(part_worths_data, order_att, all.x = TRUE)
        }
        
        part_worths_data <- rbind(part_worths_data, c("Intercept","Intercept", 
                                                      model_results$coefficients[nrow(model_results$coefficients),c(1)],
                                                      part_worths_data$ID[1], as.character(max(as.numeric(part_worths_data$row))+1)))
        
        part_worths_data$part_worth <- round(as.numeric(part_worths_data$part_worth),5)
       
        # Removing NULL levels for further analysis
        part_worths_data
      },
      error = function(e) {
      }
    )
  }


  part_worths_ind[sapply(part_worths_ind, is.null)] <- NULL

 # Identifying respondents with insufficient/poor data
 del1 <- foreach(a = 1:length(part_worths_ind), .combine = "c") %dopar% {
  if (max(colSums(is.na(part_worths_ind[[a]]))) > 0) {
    del <- a
     } else {
      del <- 0
    }
    del
  }
 
  l_del <- del1[del1 != 0]
 
  # Removing personas with insufficient/poor data
   if (length(l_del) > 0) {
     part_worths_ind <- part_worths_ind[-l_del]
  } else {
    part_worths_ind <- part_worths_ind
  }

  return(list(
    PartWorths_Ind = part_worths_ind,
    Utility_Estimation_calc = pre_calculated
  ))
}




#' Utility Estimation Function for Smaller Respondent Groups
#'
#' @param attributes A JSON object containing attribute information (levels, codings).
#' @param survey A dataframe containing survey data.
#'
#'
#' @description This function takes two main parameters which are attribute information and survey data.
#' The function performs grouping respondents into 2-3 clusters (PCA analysis and Cluster analysis),
#' grouping respondents based on their demographic belonging and estimating preferences per each group. Once done, the results
#' are used for furhter AI-aided interpretations and explanations
#'
#' @return A list containing mindset analysis (AMCE data, utility data and willingness to pay data (if price attribute is available),
#' AI-aided interpretations of the results), trait analysis (AMCE data and AI-aided interpretations
#' of the results) for each demographic group.
#'
#' @examples
#' \dontrun{
#' utility_estimation(attributes = your_attr_data, survey = your_survey_data)
#' }
#' @export

# utility_estimation_ind
utility_estimation_ind <- function(attributes, survey) {
  pre_calculated <- error_handling_1(attributes = attributes, survey = survey)
  part_worths_ind <- pre_calculated$PartWorths_Ind
  
  demos1 <- pre_calculated$Utility_Estimation_calc$Data_persona_id
  currency <- pre_calculated$Utility_Estimation_calc$currency
  estimate <- pre_calculated$Utility_Estimation_calc$estimate
  data_complete <- pre_calculated$Utility_Estimation_calc$Data_complete
  att <- pre_calculated$Utility_Estimation_calc$att
  price_attribute <- pre_calculated$Utility_Estimation_calc$price_attribute
  question <- pre_calculated$Utility_Estimation_calc$question
  dependent_variable <- pre_calculated$Utility_Estimation_calc$dependent_variable
  
  attribute_levels_base <- pre_calculated$Utility_Estimation_calc$AMCE_Data %>% filter (significance == "Base Level")

  
  price_titles <- pre_calculated$Utility_Estimation_calc$price_titles
  quintiles <- pre_calculated$Utility_Estimation_calc$quintiles
  formula_elements <- pre_calculated$Utility_Estimation_calc$formula_elements

  formula <- as.formula(paste("choice", paste(
    paste(c(formula_elements, "Intercept"),
          collapse = " + "
    ),
    "+ strata(Choice_ID)"
  ),
  sep = "~"
  ))

  # K-Means based on AMCEs
  part_worths_wide_format_1 <- do.call(rbind.data.frame, part_worths_ind) %>%
    dplyr::select(attribute_text, level_text, part_worth, ID, row) %>% filter(level_text != "Not available")

  part_worths_wide_format_1$level_unique <-
    paste(part_worths_wide_format_1$level_text, part_worths_wide_format_1$row,
      sep = "_"
    )
  
  part_worths_wide_format <- part_worths_wide_format_1 %>%
    dplyr::select(level_unique, part_worth, ID) %>%
    pivot_wider(names_from = level_unique, values_from = part_worth) %>%
    as.data.frame()

  # Find constant columns
  dat <- part_worths_wide_format_1 %>%
    dplyr::select(level_unique, part_worth, ID) %>%
    pivot_wider(names_from = ID, values_from = part_worth) %>%
    as.data.frame()

  dat_subset <- dat[-1]
  constant_columns <- sapply(dat_subset, function(x) length(unique(x)) == 1)
  dat_cleaned <- dat_subset[, !constant_columns]
  columns_to_remove <- which(constant_columns)

  if (length(columns_to_remove) > 0) {
    part_worths_wide_format <- part_worths_wide_format[-c(columns_to_remove), ]
  } else {
    part_worths_wide_format <- part_worths_wide_format
  }
  
  #dat_cleaned <- as.data.frame(lapply(dat_cleaned, as.numeric))
  pca_res <- prcomp(dat_cleaned, scale. = TRUE)$rotation[, 1:2]
  pca_imp <- summary(prcomp(dat_cleaned, scale. = TRUE))$importance[2, 1:2]

  part_worths_wide_format$mindset2 <-  kmeans(as.data.frame(pca_res),2)$cluster
  part_worths_wide_format$mindset3 <- kmeans(as.data.frame(pca_res),3)$cluster

  demos <- demos1
  # Adding mindsets and traits to the data set
  part_worth_mindset <- part_worths_wide_format %>%
    dplyr::select(ID, mindset2, mindset3)
  pca_data <- cbind(part_worth_mindset, pca_res)
  mindsets_size <- part_worth_mindset
  demos <- demos %>% filter(!(ID %in% as.numeric(columns_to_remove)))
  add_on <- merge(part_worth_mindset, demos, by = "ID")
  
  # Model by Trait levels and Mindsets
  dem_cl_partworths <- foreach(
    i = colnames(add_on)[-1],
    .packages = c("dplyr", "survival", "foreach")) %dopar% {
    dem_cl_partworths_sig <- foreach(
      j = unique(add_on[, i]),
      .combine = rbind
    ) %dopar% {
      data <- data_complete %>% filter(ID %in% add_on$ID[add_on[, i] == j])
      
      # multinom model
      model <- clogit(formula, data)
      # Model Output
      model_results <- summary(model)

      p_value <- model_results$coefficients[, 5]


      # Partworths calculation
      att$part_worth <- c(model_results$coefficients[, 1][match(
        att$new_coded,
        names(model_results$coefficients[, 1])
      )])
      
      att$se <- c(model_results$coefficients[, 3][match(
        att$new_coded,
        names(model_results$coefficients[, 3])
      )])
      
      att[is.na(att)] <- 0


      merged_data <- att
      
      att_lev <- att %>% select(attribute_text, new_coded)
      
      merged_data[, c("part_worth")][is.na(merged_data[, c("part_worth")])] <- 0
      merged_data[, i] <- j
      merged_data$row <- 1:nrow(merged_data)
      
      
      merged_data$AMCE <- round(2*((exp(as.numeric(merged_data$part_worth))/(1+exp(as.numeric(merged_data$part_worth)))-0.5)),2)
      merged_data$std_error <- round(2*as.numeric(merged_data$se)*exp(as.numeric(merged_data$part_worth))/(1+exp(as.numeric(merged_data$part_worth)))^2,2)
      merged_data$lower_bound <- ifelse((round( merged_data$AMCE-(1.96* merged_data$std_error), 2)) <(-1),-1,
                                             round(( merged_data$AMCE-(1.96* merged_data$std_error)),2))
      
      merged_data$upper_bound <-  ifelse((round( merged_data$AMCE+(1.96* merged_data$std_error), 2)) >(1),1,
                                              round((merged_data$AMCE+(1.96* merged_data$std_error)),2))
      
      merged_data
      
    }
    dem_cl_partworths_sig <- dem_cl_partworths_sig %>%
      dplyr::arrange(attribute_text, level_text)
    dem_cl_partworths_sig <- dem_cl_partworths_sig[, c(
      "attribute_text",
      "level_text", i, "row",
      "AMCE", "lower_bound", "upper_bound", "part_worth"
    )]
    names(dem_cl_partworths_sig)[3] <- gsub(".", " ",  names(dem_cl_partworths_sig)[3], fixed=TRUE)
    
    dem_cl_partworths_sig["AMCE"][is.na(dem_cl_partworths_sig["AMCE"])] <- 0
    dem_cl_partworths_sig
    }
  
  

  names(dem_cl_partworths) <- colnames(add_on)[-1]

  # Dataset based on traits and mindsets
  part_worths_dem <- dem_cl_partworths[names(demos)[-1]]
  
  #Update name
  names(part_worths_dem) <- gsub(".", " ", names(part_worths_dem), fixed=TRUE)
  names(part_worths_dem) <- gsub("_", " ", names(part_worths_dem), fixed=TRUE)
  
  names(part_worths_dem) <- tools::toTitleCase(names(part_worths_dem))

  part_worths_dem2 <- part_worths_dem
  for (i in 1:length(part_worths_dem2)){
    names(part_worths_dem2[[i]])[3] <- "trait"
    part_worths_dem2[[i]]$trait_group <- names(part_worths_dem2)[i]
  }
  
  part_worths_dem_full <- as.data.frame(do.call(
    rbind.data.frame,
    part_worths_dem2
  )) %>%
    dplyr::select(-row)
  rownames(part_worths_dem_full) <- NULL
  part_worths_dem_full <- part_worths_dem_full %>% select(trait_group, trait, attribute_text, level_text, AMCE, lower_bound, upper_bound)
  part_worths_dem_full$trait_group <- gsub(".", " ", part_worths_dem_full$trait_group, fixed=TRUE)


  title <- foreach(
    i = 1:length(part_worths_dem),
    .packages = c("dplyr")
  ) %dopar% {
    trait_title <- paste0(
      toupper(substr(names(part_worths_dem[[i]])[3], 1, 1)),
      substr(
        names(part_worths_dem[[i]])[3], 2,
        nchar(names(part_worths_dem[[i]])[3])
      )
    )
    trait_1 <- gsub(".", " ", trait_title, fixed=TRUE)
    trait_2 <-gsub("_", " ", trait_1, fixed=TRUE)
    tools::toTitleCase(trait_2)
  }
  
  dem_levels <- foreach(
    i = 1:length(part_worths_dem), .packages = c("dplyr", "openai"),
    .export = c("r_path", "exp_t_path", "question")
  ) %dopar% {
    d1 <- part_worths_dem[[i]]
    names(d1)[3] <- "trait"
    sort(unique(d1$trait))
  }
  
  # Define the Azure OpenAI function first
  
  create_chat_completion <- function(model, temperature, messages, api_key, endpoint, 
                                     api_version = "2024-12-01-preview", max_retries = 3) {
    
    url <- paste0(endpoint, "/openai/deployments/", model, "/chat/completions?api-version=", api_version)
    
    body <- list(
      messages = messages,
      temperature = temperature
    )
    
    for (attempt in 1:max_retries) {
      # Make the API request
      response <- tryCatch({
        POST(
          url = url,
          add_headers("api-key" = api_key, 
                      "Content-Type" = "application/json"),
          body = toJSON(body, auto_unbox = TRUE),
          encode = "json"
        )
      }, error = function(e) {
        return(NULL)
      })
      
      # Check if request was successful
      if (!is.null(response) && status_code(response) == 200) {
        content <- content(response, "parsed")
        if (!is.null(content$choices) && length(content$choices) > 0) {
          return(content)
        }
      }
      
      # If we get here, the request failed or returned empty
      # Wait with exponential backoff before retrying
      if (attempt < max_retries) {
        wait_time <- 15 ^attempt  # Exponential backoff: 2, 4, 8... seconds
        Sys.sleep(wait_time)
      }
    }
    # Return empty response after all retries failed
    return(list(choices = list(list(message = list(content = " ")))))
  }
  
  
  dem_l <- min(length(part_worths_dem),5)
  
  #### Dem Prompts
  #### Dem Prompts
# Initialize empty list to store results
  dem_graph_int <- foreach(
    i = 1:length(part_worths_dem), .packages = c("httr", "jsonlite"),
    .export = c("api_key", "endpoint", "r_path", "exp_t_path", "question", "dependent_variable")
  ) %dopar% {
    d1 <- part_worths_dem[[i]]
    d1$trait_group <- gsub(".", " ", names(d1)[3], fixed=TRUE)
    names(d1)[3] <- "trait"
    
    data <- d1 %>%
      arrange(desc(AMCE)) %>%
      group_by(trait) %>%
      slice(1:10)
    
    cases_b <- paste(data$trait_group,data$trait, paste(data$attribute_text,
                                                        paste0(
                                                          data$level_text, "(",
                                                          round(data$AMCE, 2), ")"
                                                        ),
                                                        sep = ":"
    ), sep = ":")
    
    text_in <- paste(base::readLines(exp_t_path), collapse = "\n")
    
    aa <- paste0("Experiment Results:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ",  paste0('"', dependent_variable, '"'), 
      ", below are 10 of the most preferred levels per trait based on the feature level importance results:"), "\n",
      paste0(do.call(paste, c(as.list(cases_b), sep = "; "))), "\n",
      "Answer the study question based on feature level importance results per trait and the survey instruction."
    )
    
    Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
    Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
    model_name <- "gpt-4o-mini" #"gpt-4ov2"  # Update this to match your deployed model name
    
    answer <- tryCatch(
      {
        response <- create_chat_completion(
          model = model_name,
          temperature = 0,
          messages = list(
            list(
              "role" = "system",
              "content" = text_in
            ),
            list(
              "role" = "user",
              "content" = aa
            )
          ),
          api_key = api_key,
          endpoint = endpoint
        )
        response$choices[[1]]$message$content
      },
      error = function(e) {
        # Return empty string in case of an error
        " "
      }
    )
    answer
  }
  
  
  
  
  
  

  
  

  part_worths_mindsets <- dem_cl_partworths[grepl(
    "mindset",
    names(dem_cl_partworths)
  )]


  # Dataset based on mindsets
  utility_mindsets <- foreach(i = 1:2, .packages = c("dplyr"), .export =  c("attribute_levels_base")) %dopar% {
    mindset <- paste0("mindset", i+1)
    utility_data <- part_worths_mindsets[[i]] %>%
      dplyr::select(attribute_text, level_text, part_worth, mindset) %>%
      filter(!(level_text %in% attribute_levels_base$level_text)) %>%
      group_by(across(c(1, ncol(.)))) %>%
      dplyr::summarise (max = max(part_worth),
                       min = min(part_worth)) %>% as.data.frame()
    
    
    utility_data$importance <- utility_data$max - utility_data$min
    
    utility_data <-  utility_data %>% group_by(across(c(2))) %>% 
      dplyr::mutate(sum = sum(importance))
    
    utility_data$Importance <- round(utility_data$importance /
                                       utility_data$sum, 2)
    
    utility_data$Importance[is.na(utility_data$Importance)] <- 0
    
    utility_data <- utility_data %>%
      select(-max, -min, -importance, -sum) %>%
      as.data.frame()
    
    utility_data
    
  }
  names(utility_mindsets) <- names(part_worths_mindsets)

  # Set  key
  text_in <- paste(base::readLines(prompt_input_path), collapse = "\n")
  
  
  # Define the Azure OpenAI function first
  
  create_chat_completion <- function(model, temperature, messages, api_key, endpoint, 
                                     api_version = "2024-12-01-preview", max_retries = 5) {
    
    url <- paste0(endpoint, "/openai/deployments/", model, "/chat/completions?api-version=", api_version)
    
    body <- list(
      messages = messages,
      temperature = temperature
    )
    
    for (attempt in 1:max_retries) {
      # Add sleep before making API request to implement rate limiting
      Sys.sleep(1)
      
      # Make the API request
      response <- tryCatch({
        POST(
          url = url,
          add_headers("api-key" = api_key, 
                      "Content-Type" = "application/json"),
          body = toJSON(body, auto_unbox = TRUE),
          encode = "json"
        )
      }, error = function(e) {
        return(NULL)
      })
      
      # Check if request was successful
      if (!is.null(response) && status_code(response) == 200) {
        content <- content(response, "parsed")
        if (!is.null(content$choices) && length(content$choices) > 0) {
          return(content)
        }
      }
      
      # If we get here, the request failed or returned empty
      # Wait with exponential backoff before retrying
      if (attempt < max_retries) {
        wait_time <- 30#^attempt  # Exponential backoff: 15, 30, 45... seconds
        Sys.sleep(wait_time)
      }
    }
    
    # Return empty response after all retries failed
    return(list(choices = list(list(message = list(content = " ")))))
  }
  
  # First part - for k=1
  mindset_ai_1 <- {
    k <- 1
    mindset <- part_worths_mindsets[[k]]
    mindset_a <- utility_mindsets[[k]]
    colnames(mindset)[3] <- "mindset"
    colnames(mindset_a)[2] <- "mindset"
    
    # Set environment variables outside of any loops
    Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
    Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
    model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
    
    sentence_att_l <- c()
    # Process for i=1
    i <- 1
    # First identify attributes where mindset i has maximum Importance
    max_attributes <- mindset_a %>%
      group_by(attribute_text) %>%
      summarize(max_mindset = which.max(Importance)) %>%
      filter(max_mindset == i) %>%
      pull(attribute_text)
    
    # If no attributes have max importance for this mindset
    if(length(max_attributes) == 0) {
      # Get the single attribute with highest importance for this mindset
      highest_for_mindset <- mindset_a %>%
        filter(mindset == i) %>%
        arrange(desc(Importance)) %>%
        slice(1) %>%
        pull(attribute_text)
      
      max_attributes <- highest_for_mindset
    }
    
    # Then filter data for current mindset and keep only those attributes
    data_filtered <- mindset_a %>% 
      filter(mindset == i, attribute_text %in% max_attributes)
    
    # Sort by importance and format
    result <- data_filtered %>%
      arrange(desc(Importance))
    
    # Format as before
    cases_b <- paste0(
      result$attribute_text, " (",
      100 * result$Importance, "%)"
    )
    
    # Create final string
    sent1 <- paste0(do.call(paste, c(as.list(cases_b), sep = "; ")))
    sentence_att_l[1] <- sent1
    
    # Process for i=2
    i <- 2
    # First identify attributes where mindset i has maximum Importance
    max_attributes <- mindset_a %>%
      group_by(attribute_text) %>%
      summarize(max_mindset = which.max(Importance)) %>%
      filter(max_mindset == i) %>%
      pull(attribute_text)
    
    # If no attributes have max importance for this mindset
    if(length(max_attributes) == 0) {
      # Get the single attribute with highest importance for this mindset
      highest_for_mindset <- mindset_a %>%
        filter(mindset == i) %>%
        arrange(desc(Importance)) %>%
        slice(1) %>%
        pull(attribute_text)
      
      max_attributes <- highest_for_mindset
    }
    
    # Then filter data for current mindset and keep only those attributes
    data_filtered <- mindset_a %>% 
      filter(mindset == i, attribute_text %in% max_attributes)
    
    # Sort by importance and format
    result <- data_filtered %>%
      arrange(desc(Importance))
    
    # Format as before
    cases_b <- paste0(
      result$attribute_text, " (",
      100 * result$Importance, "%)"
    )
    
    # Create final string
    sent2 <- paste0(do.call(paste, c(as.list(cases_b), sep = "; ")))
    sentence_att_l[2] <- sent2
    
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features based on the results of relative importance of features per mindset:")
    )
    
    aa <- paste(q, paste(
      paste(paste0("1.Mindset 1:", sentence_att_l[1]), sep = ","),
      paste(paste0("2.Mindset 2:", sentence_att_l[2]), sep = ","),
      sep = "\n"), "Create a short title for each mindset that bests describes it based on feature importance results and the study question.", sep = "\n"
    )
    
    # Add sleep before API call
    Sys.sleep(5)
    
    # Get response from Azure OpenAI
    answer <- tryCatch(
      {
        response <- create_chat_completion(
          model = model_name,
          temperature = 0,
          messages = list(
            list(
              "role" = "system",
              "content" = text_in
            ),
            list(
              "role" = "user",
              "content" = aa
            )
          ),
          api_key = api_key,
          endpoint = endpoint
        )
        response$choices[[1]]$message$content
      },
      error = function(e) {
        # Return empty string instead of an error
        NULL
      }
    )
    cl_un <- sort(unique(mindset$mindset))
    
    if (is.null(answer)) {
      desc <- c("Cluster 1", "Cluster 2", "Cluster 3")[1:length(cl_un)]
    } else {
      desc <- c()
      # For t=1
      t <- cl_un[1]
      pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
      pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
      des <- sub(pattern_next, "", sub(
        pattern, "",
        answer
      ))
      if (grepl("\n", des, fixed = TRUE)) {
        des <- substr(des, 1, nchar(des) - 1)
      } else {
        des <- des
      }
      desc[1] <- des
      
      # For t=2
      t <- cl_un[2]
      pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
      pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
      des <- sub(pattern_next, "", sub(
        pattern, "",
        answer
      ))
      if (grepl("\n", des, fixed = TRUE)) {
        des <- substr(des, 1, nchar(des) - 1)
      } else {
        des <- des
      }
      desc[2] <- des
    }
    cl <- c(sort(unique(mindset$mindset)))
    nam <- as.data.frame(cbind(cl, desc))
    if (nrow(nam[duplicated(nam$desc), ]) > 0) {
      nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)] <-
        paste(nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)], 2)
    } else {
      nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)] <-
        nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)]
    }
    names(nam) <- c("mindset", "desc")
    nam
  }
  
  # Second part - for k=2 
  mindset_ai_2 <- {
    k <- 2
    mindset <- part_worths_mindsets[[k]]
    mindset_a <- utility_mindsets[[k]]
    colnames(mindset)[3] <- "mindset"
    colnames(mindset_a)[2] <- "mindset"
    
    # Set environment variables outside of any loops
    Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
    Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
    model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
    
    sentence_att_l <- c()
    # Process for i=1
    i <- 1
    # First identify attributes where mindset i has maximum Importance
    max_attributes <- mindset_a %>%
      group_by(attribute_text) %>%
      summarize(max_mindset = which.max(Importance)) %>%
      filter(max_mindset == i) %>%
      pull(attribute_text)
    
    # If no attributes have max importance for this mindset
    if(length(max_attributes) == 0) {
      # Get the single attribute with highest importance for this mindset
      highest_for_mindset <- mindset_a %>%
        filter(mindset == i) %>%
        arrange(desc(Importance)) %>%
        slice(1) %>%
        pull(attribute_text)
      
      max_attributes <- highest_for_mindset
    }
    
    # Then filter data for current mindset and keep only those attributes
    data_filtered <- mindset_a %>% 
      filter(mindset == i, attribute_text %in% max_attributes)
    
    # Sort by importance and format
    result <- data_filtered %>%
      arrange(desc(Importance))
    
    # Format as before
    cases_b <- paste0(
      result$attribute_text, " (",
      100 * result$Importance, "%)"
    )
    
    # Create final string
    sent1 <- paste0(do.call(paste, c(as.list(cases_b), sep = "; ")))
    sentence_att_l[1] <- sent1
    
    # Process for i=2
    i <- 2
    # First identify attributes where mindset i has maximum Importance
    max_attributes <- mindset_a %>%
      group_by(attribute_text) %>%
      summarize(max_mindset = which.max(Importance)) %>%
      filter(max_mindset == i) %>%
      pull(attribute_text)
    
    # If no attributes have max importance for this mindset
    if(length(max_attributes) == 0) {
      # Get the single attribute with highest importance for this mindset
      highest_for_mindset <- mindset_a %>%
        filter(mindset == i) %>%
        arrange(desc(Importance)) %>%
        slice(1) %>%
        pull(attribute_text)
      
      max_attributes <- highest_for_mindset
    }
    
    # Then filter data for current mindset and keep only those attributes
    data_filtered <- mindset_a %>% 
      filter(mindset == i, attribute_text %in% max_attributes)
    
    # Sort by importance and format
    result <- data_filtered %>%
      arrange(desc(Importance))
    
    # Format as before
    cases_b <- paste0(
      result$attribute_text, " (",
      100 * result$Importance, "%)"
    )
    
    # Create final string
    sent2 <- paste0(do.call(paste, c(as.list(cases_b), sep = "; ")))
    sentence_att_l[2] <- sent2
    
    # Process for i=3
    i <- 3
    # First identify attributes where mindset i has maximum Importance
    max_attributes <- mindset_a %>%
      group_by(attribute_text) %>%
      summarize(max_mindset = which.max(Importance)) %>%
      filter(max_mindset == i) %>%
      pull(attribute_text)
    
    # If no attributes have max importance for this mindset
    if(length(max_attributes) == 0) {
      # Get the single attribute with highest importance for this mindset
      highest_for_mindset <- mindset_a %>%
        filter(mindset == i) %>%
        arrange(desc(Importance)) %>%
        slice(1) %>%
        pull(attribute_text)
      
      max_attributes <- highest_for_mindset
    }
    
    # Then filter data for current mindset and keep only those attributes
    data_filtered <- mindset_a %>% 
      filter(mindset == i, attribute_text %in% max_attributes)
    
    # Sort by importance and format
    result <- data_filtered %>%
      arrange(desc(Importance))
    
    # Format as before
    cases_b <- paste0(
      result$attribute_text, " (",
      100 * result$Importance, "%)"
    )
    
    # Create final string
    sent3 <- paste0(do.call(paste, c(as.list(cases_b), sep = "; ")))
    sentence_att_l[3] <- sent3
    
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features based on the results of relative importance of features per mindset:")
    )
    
    aa <- paste(q, paste(
      paste(paste0("1.Mindset 1:", sentence_att_l[1]), sep = ","),
      paste(paste0("2.Mindset 2:", sentence_att_l[2]), sep = ","),
      paste(paste0("3.Mindset 3:", sentence_att_l[3]), sep = ","),
      sep = "\n"), "Create a short title for each mindset that bests describes it based on feature importance results and the study question.", sep = "\n"
    )
    
    # Add sleep before API call
    Sys.sleep(10)  # Longer sleep for second API call
    
    # Get response from Azure OpenAI
    answer <- tryCatch(
      {
        response <- create_chat_completion(
          model = model_name,
          temperature = 0,
          messages = list(
            list(
              "role" = "system",
              "content" = text_in
            ),
            list(
              "role" = "user",
              "content" = aa
            )
          ),
          api_key = api_key,
          endpoint = endpoint
        )
        response$choices[[1]]$message$content
      },
      error = function(e) {
        # Return empty string instead of an error
        NULL
      }
    )
    cl_un <- sort(unique(mindset$mindset))
    
    if (is.null(answer)) {
      desc <- c("Cluster 1", "Cluster 2", "Cluster 3")[1:length(cl_un)]
    } else {
      desc <- c()
      # For t=1
      t <- cl_un[1]
      pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
      pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
      des <- sub(pattern_next, "", sub(
        pattern, "",
        answer
      ))
      if (grepl("\n", des, fixed = TRUE)) {
        des <- substr(des, 1, nchar(des) - 1)
      } else {
        des <- des
      }
      desc[1] <- des
      
      # For t=2
      t <- cl_un[2]
      pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
      pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
      des <- sub(pattern_next, "", sub(
        pattern, "",
        answer
      ))
      if (grepl("\n", des, fixed = TRUE)) {
        des <- substr(des, 1, nchar(des) - 1)
      } else {
        des <- des
      }
      desc[2] <- des
      
      # For t=3
      t <- cl_un[3]
      pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
      pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
      des <- sub(pattern_next, "", sub(
        pattern, "",
        answer
      ))
      if (grepl("\n", des, fixed = TRUE)) {
        des <- substr(des, 1, nchar(des) - 1)
      } else {
        des <- des
      }
      desc[3] <- des
    }
    cl <- c(sort(unique(mindset$mindset)))
    nam <- as.data.frame(cbind(cl, desc))
    if (nrow(nam[duplicated(nam$desc), ]) > 0) {
      nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)] <-
        paste(nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)], 2)
    } else {
      nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)] <-
        nam$desc[as.numeric(nam[duplicated(nam$desc), ]$cl)]
    }
    names(nam) <- c("mindset", "desc")
    nam
  }
  
  # Store the results in a list
  mindset_ai <- list(mindset_ai_1, mindset_ai_2)
  names(mindset_ai) <- names(add_on)[2:3]
  
  

  mindset_tab <- foreach(k = 1:2) %dopar% {
    mindset <- paste0("mindset", (k + 1))
    data <- utility_mindsets[[mindset]]
    names(data)[2] <- "mindset"
    data$mindset <- paste0("Mindset ", data$mindset)
    mindset_size <- mindsets_size %>% dplyr::select(mindset)
    names(mindset_size) <- "mindset"
    mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)


    mindset_size <- mindset_size %>%
      group_by(mindset) %>%
      dplyr::summarise(n = n()) %>%
      dplyr::mutate(freq = (100 * n / sum(n)))

    data <- merge(data, mindset_size, all.x = TRUE)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)

    data$desc1 <- paste0(data$desc, " (", round(data$freq, 2), "%)")


    nn <- data %>%
      dplyr::select(desc, freq) %>%
      unique()
    nam <- merge(nam, nn, all.x = TRUE)
    nam$desc_full <- paste0(nam$desc, " (", round(nam$freq, 2), "%)")
    nam <- nam %>% arrange(mindset)

    descr <- foreach(
      i = 1:length(unique(nam$mindset)),
      .combine = "c"
    ) %dopar% {
      descrip <- paste0(nam$mindset[i], ": ", nam$desc_full[i])
      descrip
    }
    descr
  }

  mindset_levels <- foreach(k = 1:2) %dopar% {
    mindset <- paste0("mindset", (k + 1))
    data <- utility_mindsets[[mindset]]
    names(data)[2] <- "mindset"
    data$mindset <- paste0("Mindset ", data$mindset)
    mindset_size <- mindsets_size %>% dplyr::select(mindset)
    names(mindset_size) <- "mindset"
    mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)


    mindset_size <- mindset_size %>%
      group_by(mindset) %>%
      dplyr::summarise(n = n()) %>%
      mutate(freq = (100 * n / sum(n)))

    data <- merge(data, mindset_size, all.x = TRUE)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)

    data$desc1 <- paste0(data$desc, " (", round(data$freq, 2), "%)")


    nn <- data %>%
      dplyr::select(desc, freq) %>%
      unique()
    nam <- merge(nam, nn, all.x = TRUE)
    nam$desc_full <- paste0(nam$desc, " (", round(nam$freq, 2), "%)")
    nam <- nam %>% arrange(mindset)

    descr <- foreach(
      i = 1:length(unique(nam$mindset)),
      .combine = "c"
    ) %dopar% {
      descrip <- nam$desc_full[i]
      descrip
    }
    descr
  }

  mindset_dem <- foreach(k = 1:2) %dopar% {
    data <- add_on
    names(data)[4:ncol(data)] <- gsub(".", " ",  names(data)[4:ncol(data)], fixed=TRUE)
    names(data)[4:ncol(data)] <- gsub("_", " ",  names(data)[4:ncol(data)], fixed=TRUE)
    names(data)[4:ncol(data)] <- tools::toTitleCase((names(data)[4:ncol(data)]))
    
    
    mindset_selected <- paste0("mindset", (k + 1))
    mindset <- data[, mindset_selected]
    demos <- c(names(part_worths_dem))
    
    # If demos has length 1
    if(length(demos) == 1) {
      data_comb <- data.frame(data[, demos])
      colnames(data_comb) <- demos
    } else {
      data_comb <- as.data.frame(data[, c(demos)])
    }
    
    data_comb$mindset <- mindset
    data_comb$ID <- data$ID
    data_comb$mindset <- paste0("Mindset ", data_comb$mindset)
    #names(data_comb)[1] <- demos

    part_worths_dem <- part_worths_dem

    graphs <- foreach(i = 1:length(demos), .packages = c("dplyr")) %dopar% {
      data_graph <- data_comb %>% dplyr::select(mindset, demos[i], ID)
      title <- names(data_graph)[2]
      names(data_graph)[2] <- "dem"
      data_graph$dem_new <- gsub(".", " ", title, fixed=TRUE)
      
      dd <- data_graph %>%
        group_by(mindset, dem) %>%
        dplyr::summarise(n = n()) %>%
        dplyr::mutate(freq = n / sum(n))
      
      dd$freq <- round(dd$freq, 2)

      data_graph <- merge(data_graph, dd, all.x = TRUE) %>%
        dplyr::select(mindset, dem, dem_new, freq, n) %>%
        unique()

    }
    a <- do.call(rbind.data.frame, graphs)
    data <- as.data.frame(a)

    mindset <- paste0("mindset", (k + 1))
    mindset_ai <- mindset_ai
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)
    data <- merge(data, nam, all.x = TRUE)
    data_u <- data %>% select(dem,dem_new) %>% distinct()
    comb <- as.data.frame(do.call("rbind", replicate(k+1, data_u, simplify = FALSE)))
    comb$mindset <- rep(unique(data$mindset), each = nrow(data_u))
    
    aa <- merge(comb, data, all.x = TRUE)
    bb <- aa %>%
      group_by(mindset, desc) %>%
      dplyr::summarise(n = n())
    bb <- bb %>% filter(!is.na(desc))
    cc <- aa %>%
      group_by(dem, dem_new) %>%
      dplyr::summarise(n = n())
    cc <- cc %>% filter(!is.na(dem))

    dd <- left_join(aa[, c(
      "mindset", "freq", "n",
      "dem", "dem_new"
    )], bb[, 1:2], by = "mindset")
    dd$freq[is.na(dd$freq)] <- 0
    dd$n[is.na(dd$n)] <- 0

    data <- dd

    data$mindset <- factor(data$mindset, levels = c(
      "Mindset 1", "Mindset 2",
      "Mindset 3"
    )[1:(k + 1)])
    data <- data %>% distinct()
    data <- data %>% dplyr::arrange(mindset, dem_new)
    data
  }


  ### Graph  GPT Interpretation
  text_in_mind_dem <- paste(base::readLines(exp8_path), collapse = "\n")

  mindset_dem_prompt <- foreach(
    k = 1:2, .packages = c("openai"),
    .export = c("r_path", "exp8_path")
  ) %dopar% {
    data <- mindset_dem[[k]][, c(4, 2:3, 6,5, 1)]
    names(data) <- c(
      "trait", "frequency", "sample_size", "mindset",
      "trait_group", "m_group"
    )
    data$frequency <- round(data$frequency, 2)


    data <- data %>%
      arrange(desc(sample_size)) %>%
      group_by(mindset, trait_group) %>%
      slice(which.max(sample_size)) %>% arrange(m_group)
    cases_b <- paste(data$mindset, data$trait_group,data$trait,sep = ":")

    m_d_prompt <- paste0("Experiment Results:", "\n",
    "I have conjoint study data, where I grouped all participants into 2-3 mindsets based on their preferences. The below paragraph presents the most dominant trait levels per mindset:", "\n",
      paste0(do.call(paste, c(as.list(cases_b), sep = "; "))), "\n",
      "For each mindset describe the trait profile."
    )

    m_d_prompt
  }

  mindset_utility <- foreach(k = 1:2) %dopar% {
    utility_mindset1 <- utility_mindsets
    mindset <- paste0("mindset", (k + 1))
    data <- utility_mindset1[[mindset]]
    names(data)[2] <- "mindset"
    data_ut <- data
    data_ut$mindset <- paste0("Mindset ", data_ut$mindset)
    mindset_size <- mindsets_size %>% dplyr::select(mindset)
    names(mindset_size) <- "mindset"
    mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)

    data <- merge(data_ut, nam, all.x = TRUE)



    data <- data %>% dplyr::select(desc, attribute_text, Importance)

    mindset_size <- mindsets_size %>% dplyr::select(mindset)
    names(mindset_size) <- "mindset"
    mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)

    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)


    mindset_size <- mindset_size %>%
      group_by(mindset) %>%
      dplyr::summarise(n = n()) %>%
      mutate(freq = (100 * n / sum(n)))

    data <- merge(data, mindset_size, all.x = TRUE)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)

    data$desc1 <- paste0(data$desc, " (", round(data$freq, 2), "%)")
    data$importance <- round(as.numeric(data$Importance),2)
    data <-data %>% select(desc, attribute_text, importance, desc1)
    data$formatted_importance <- paste(data$attribute_text,
                                       paste0("(", round(data$importance * 100), "%)"),
                                       sep = " ")
    data
  }

  ### Graph GPT Interpretation
  text_in_mind_ut <- paste(base::readLines(exp11_path), collapse = "\n")


  mindset_utility_prompt <- foreach(
    k = 1:2, .packages = c("openai"),
    .export = c("r_path", "exp11_path")
  ) %dopar% {
    data <- mindset_utility[[k]]
    data$Importance <- round(data$importance * 100)
    
    data <- merge(data,mindset_ai[[k]])

    data <- data %>%
      arrange(desc(Importance)) %>%
      group_by(desc) %>% arrange(mindset)

    cases_b <- paste(data$desc, paste0(
      data$attribute_text, " (",
      data$Importance, "%)"
    ), sep = ":")

    m_ut_prompt <- paste0("Experiment Results:", "\n", paste0(
    "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
    ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
    ", below are the results of relative importance of features per mindset:"), "\n",
      paste0(do.call(paste, c(as.list(cases_b), sep = "; "))), "\n",
      "Answer the study question based on feature importance results and the survey instruction."
    )

    m_ut_prompt
  }

  mindset_partworth <- foreach(k = 1:2) %dopar% {
    mindset <- paste0("mindset", (k + 1))
    data <- part_worths_mindsets[[mindset]]
    names(data)[3] <- "mindset"
    data$mindset <- paste0("Mindset ", data$mindset)
    mindset_size <- mindsets_size %>% dplyr::select(mindset)
    names(mindset_size) <- "mindset"
    mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
    nam_list <- mindset_ai
    nam <- nam_list[[mindset]]
    nam$mindset <- paste0("Mindset ", nam$mindset)


    mindset_size <- mindset_size %>%
      group_by(mindset) %>%
      dplyr::summarise(n = n()) %>%
      mutate(freq = (100 * n / sum(n)))

    data <- merge(data, mindset_size, all.x = TRUE)
    data <- merge(data, nam, all.x = TRUE)

    data$desc <- factor(data$desc, levels = nam$desc)

    data$mindset <- paste0(data$desc, " (", round(data$freq, 2), "%)")

    nn <- data %>%
      dplyr::select(desc, freq) %>%
      unique()
    nam <- merge(nam, nn, all.x = TRUE)
    nam$desc_full <- paste0(nam$desc, " (", round(nam$freq, 2), "%)")

    nam <- nam %>% arrange(mindset)
    data$mindset <- factor(data$mindset, levels = nam$desc_full)
    data$mindset <- as.character(data$mindset)
    data %>% select(-n, -freq, -part_worth)
  }

  ### Graph GPT Interpretation
  text_in_mind_p <- paste(base::readLines(exp9_path), collapse = "\n")


  mindset_partworth_prompt <- foreach(
    k = 1:2, .packages = c("openai"),
    .export = c("r_path", "exp9_path")
  ) %dopar% {
    data <- mindset_partworth[[k]]
    
    mindset_ai2 <- mindset_ai[[k]]
    colnames(mindset_ai2)[1] <- "m_group"
    
    data <- merge(data,mindset_ai2)

    data <- data %>%
      arrange(desc(AMCE)) %>%
      group_by(mindset) %>%
      slice(1:10) %>% arrange(m_group)
    
    cases_b <- paste(data$desc, paste(data$attribute_text,
                                                        paste0(
                                                          data$level_text, "(",
                                                          round(data$AMCE, 2), ")"
                                                        ),
                                                        sep = ":"
    ), sep = ":")


    m_part_prompt <- paste0("Experiment Results:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ",  paste0('"', dependent_variable, '"'), 
      ", below are 10 of the most preferred levels per mindset based on the feature level importance results:"), "\n",
      paste0(do.call(paste, c(as.list(cases_b), sep = "; "))), "\n",
      "Answer the study question based on feature level importance results per mindset and the survey instruction."
    )


    m_part_prompt
  }


  if (price_attribute == "Price Attribute"){
    mindset_wtp <- foreach(k = 1:2, .export =  c("price_titles","attribute_levels_base")) %dopar% {
      part_worths_mindset1 <- part_worths_mindsets
      mindset <- paste0("mindset", (k + 1))
      download_data111 <- part_worths_mindset1[[mindset]]
      names(download_data111)[3] <- "mindset"
      nam_list <- mindset_ai

      nam1 <- mindset_ai[[mindset]]
      order <- factor(unique(nam1$desc), levels = nam1$desc)

      wtp_mindset <- foreach(
        i = 1:length(order), .export = c("price_titles","attribute_levels_base"),
        .packages = c("dplyr")
      ) %dopar% {
        tryCatch(
          {
            nam <- nam_list[[mindset]] %>% filter(desc == order[i])

            download_data11 <- download_data111 %>% filter(mindset == nam$mindset)
            download_data11 <- download_data11 %>% dplyr::select(-mindset)
            
            
            data_wtp <- download_data11 %>%
              filter(attribute_text %in% price_titles) %>% filter(level_text != "Not available") %>% arrange(row)
            data_wtp$price_perc <- quintiles
            
            price_coef <- (data_wtp$part_worth[1] - data_wtp$part_worth[nrow(data_wtp)])/(quintiles[length(quintiles)] - quintiles[1])

            data_1 <- download_data11 %>%
              filter(!attribute_text %in% price_titles)

            data_1$wtp <- round(data_1$part_worth/(price_coef),2)

            data_2  <- data_1 %>% dplyr::select(attribute_text, level_text, wtp, row)

            data_2$mindset <- order[i]
            data_2

          },
          error = function(e) {
          }
        )
      }

      aa <- as.data.frame(do.call(rbind.data.frame, wtp_mindset))
      to_drop <- unique(aa[rowSums(is.na(aa)) > 0, ]$mindset)
      data <- aa %>% filter(!mindset %in% to_drop)

      mindset <- paste0("mindset", (k + 1))
      mindset_size <- mindsets_size %>% dplyr::select(mindset)
      names(mindset_size) <- "mindset"
      mindset_size$mindset <- paste0("Mindset ", mindset_size$mindset)
      nam_list <- mindset_ai
      nam <- nam_list[[mindset]]
      nam$mindset <- paste0("Mindset ", nam$mindset)


      mindset_size <- mindset_size %>%
        group_by(mindset) %>%
        dplyr::summarise(n = n()) %>%
        mutate(freq = (100 * n / sum(n)))

      data_n <- merge(mindset_size, nam, all.x = TRUE) %>% select(-mindset)
      names(data_n)[3] <- "mindset"
      data <- merge(data, data_n, all.x = TRUE)

      data$desc <- factor(data$mindset, levels = nam$desc)

      data$mindset <- paste0(data$desc, " (", round(data$freq, 2), "%)")

      nn <- data %>%
        dplyr::select(desc, freq) %>%
        unique()
      nam <- merge(nam, nn, all.x = TRUE)
      nam$desc_full <- paste0(nam$desc, " (", round(nam$freq, 2), "%)")

      nam <- nam %>% arrange(mindset)
      data$mindset <- factor(data$mindset, levels = nam$desc_full)
      data$level_text <- stringr::str_wrap(data$level_text, 80)
      data <- arrange(data, row)
      data$level_text <- as.factor(data$level_text)
      data$level_text <- reorder(data$level_text, data$row)
      data %>% select(-n,-freq)
    }
  } else {
    mindset_wtp <- "no data"
  }

  ### Graph GPT Interpretation

 if (price_attribute == "Price Attribute"){
    text_in_mind_wtp <- paste(base::readLines(exp10_path), collapse = "\n")


    mindset_wtp_prompt <- foreach(
      k = 1:2, .packages = c("openai"),
      .export = c("r_path", "exp10_path")
    ) %dopar% {
      data <- mindset_wtp[[k]]
      data$wtp <- round(data$wtp, 2)
      data <- data %>%
        arrange(desc(wtp)) %>%
        group_by(mindset) %>%
        slice(1:10)

      
      cases_b <- paste(data$desc, paste(data$attribute_text,
                                        paste0(
                                          data$level_text,
                                          " (", currency, data$wtp, estimate, ")"
                                        ),
                                        sep = ":"
      ), sep = ":")

      m_wtp_prompt <- paste0("Experiment Results:", "\n", paste0(
    "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
    ", and the survey participants were given the following instruction: ",  paste0('"', dependent_variable, '"'),
    ", below are 10 of the most preferred levels per mindset based on willingness-to-pay results:"), "\n",
    paste0(do.call(paste, c(as.list(cases_b), sep = "; "))), "\n",
    "Answer the study question based on willingness-to-pay results per mindset and the survey instruction."
      )

      m_wtp_prompt
    }
  } else {
    mindset_wtp_prompt <- "no data"
  }

  # Graph 2-5 GPT Interpretation
  
  if (price_attribute == "Price Attribute"){
    tab_4_prompts <- c(unlist(c(
      mindset_utility_prompt, mindset_partworth_prompt,
      mindset_wtp_prompt, mindset_dem_prompt
    )))
    tab_4_prompt_session <- c(text_in_mind_ut, text_in_mind_ut, text_in_mind_p, text_in_mind_p, text_in_mind_wtp, text_in_mind_wtp, text_in_mind_dem, text_in_mind_dem)
    
    tab_4_prompt_output <- foreach(
      i = 1:length(tab_4_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "r_path", "tab_4_prompts", "tab_4_prompt_session")
    ) %dopar% {
      
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
      
      answer <- tryCatch(
        {
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = tab_4_prompt_session[i]
              ),
              list(
                "role" = "user",
                "content" = tab_4_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          # Return empty string in case of an error
          " "
        }
      )
      answer
    }
    
    if (is.na(tab_4_prompt_output[1]) || (is.null(tab_4_prompt_output[1])) || (tab_4_prompt_output[1] == " ")){
      ut_1 <- ""
    } else {
      ut_1 <- paste("How to interpret:", tab_4_prompt_output[1])
    }
    if (is.na(tab_4_prompt_output[2]) || (is.null(tab_4_prompt_output[2])) || (tab_4_prompt_output[2] == " ")){
      ut_2 <- ""
    } else {
      ut_2 <- paste("How to interpret:", tab_4_prompt_output[2])
    }
    mindset_utility_int <- list(ut_1, ut_2)
    
    if (is.na(tab_4_prompt_output[3]) || (is.null(tab_4_prompt_output[3])) || (tab_4_prompt_output[3] == " ")){
      p_1 <- ""
    } else {
      p_1 <- paste("How to interpret:", tab_4_prompt_output[3])
    }
    if (is.na(tab_4_prompt_output[4]) || (is.null(tab_4_prompt_output[4])) || (tab_4_prompt_output[4] == " ")){
      p_2 <- ""
    } else {
      p_2 <- paste("How to interpret:", tab_4_prompt_output[4])
    }
    mindset_partworth_int <- list(p_1, p_2)
    
    if (is.na(tab_4_prompt_output[5]) || (is.null(tab_4_prompt_output[5])) || (tab_4_prompt_output[5] == " ")){
      w_1 <- ""
    } else {
      w_1 <- paste("How to interpret:", tab_4_prompt_output[5])
    }
    if (is.na(tab_4_prompt_output[6]) || (is.null(tab_4_prompt_output[6])) || (tab_4_prompt_output[6] == " ")){
      w_2 <- ""
    } else {
      w_2 <- paste("How to interpret:", tab_4_prompt_output[6])
    }
    mindset_wtp_int <- list(w_1, w_2)
    
    if (is.na(tab_4_prompt_output[7]) || (is.null(tab_4_prompt_output[7])) || (tab_4_prompt_output[7] == " ")){
      d_1 <- ""
    } else {
      d_1 <- paste("How to interpret:", tab_4_prompt_output[7])
    }
    if (is.na(tab_4_prompt_output[8]) || (is.null(tab_4_prompt_output[8])) || (tab_4_prompt_output[8] == " ")){
      d_2 <- ""
    } else {
      d_2 <- paste("How to interpret:", tab_4_prompt_output[8])
    }
    mindset_dem_int <- list(d_1, d_2)
  } else {
    tab_4_prompts <- c(unlist(c(
      mindset_utility_prompt, mindset_partworth_prompt,
      mindset_dem_prompt
    )))
    
    tab_4_prompt_session <- c(text_in_mind_ut, text_in_mind_ut, text_in_mind_p, text_in_mind_p, text_in_mind_dem, text_in_mind_dem)
    
    tab_4_prompt_output <- foreach(
      i = 1:length(tab_4_prompts), .combine = "c",
      .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "r_path", "tab_4_prompts", "tab_4_prompt_session")
    ) %dopar% {
      
      Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
      Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
      model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
      
      
      answer <- tryCatch(
        {
          response <- create_chat_completion(
            model = model_name,
            temperature = 0,
            messages = list(
              list(
                "role" = "system",
                "content" = tab_4_prompt_session[i]),
              list(
                "role" = "user",
                "content" = tab_4_prompts[i]
              )
            ),
            api_key = api_key,
            endpoint = endpoint
          )
          response$choices[[1]]$message$content
        },
        error = function(e) {
          # Return empty string in case of an error
          " "
        }
      )
      answer
    }
    
    if (is.na(tab_4_prompt_output[1]) || (is.null(tab_4_prompt_output[1])) || (tab_4_prompt_output[1] == " ")){
      ut_1 <- ""
    } else {
      ut_1 <- paste("How to interpret:", tab_4_prompt_output[1])
    }
    if (is.na(tab_4_prompt_output[2]) || (is.null(tab_4_prompt_output[2])) || (tab_4_prompt_output[2] == " ")){
      ut_2 <- ""
    } else {
      ut_2 <- paste("How to interpret:", tab_4_prompt_output[2])
    }
    mindset_utility_int <- list(ut_1, ut_2)
    
    if (is.na(tab_4_prompt_output[3]) || (is.null(tab_4_prompt_output[3])) || (tab_4_prompt_output[3] == " ")){
      p_1 <- ""
    } else {
      p_1 <- paste("How to interpret:", tab_4_prompt_output[3])
    }
    if (is.na(tab_4_prompt_output[4]) || (is.null(tab_4_prompt_output[4])) || (tab_4_prompt_output[4] == " ")){
      p_2 <- ""
    } else {
      p_2 <- paste("How to interpret:", tab_4_prompt_output[4])
    }
    mindset_partworth_int <- list(p_1, p_2)
    
    mindset_wtp_int <- "no data"
    
    
    if (is.na(tab_4_prompt_output[5]) || (is.null(tab_4_prompt_output[5])) || (tab_4_prompt_output[5] == " ")){
      d_1 <- ""
    } else {
      d_1 <- paste("How to interpret:", tab_4_prompt_output[5])
    }
    if (is.na(tab_4_prompt_output[6]) || (is.null(tab_4_prompt_output[6])) || (tab_4_prompt_output[6] == " ")){
      d_2 <- ""
    } else {
      d_2 <- paste("How to interpret:", tab_4_prompt_output[6])
    }
    mindset_dem_int <- list(d_1, d_2)
  }

  
  
  ### Tab 3 summary
  Ins_3_system <- paste(base::readLines(ins_3_path), collapse = "\n")
  
  
  ins_3_data <- gsub("How to interpret: ", "", paste(unlist(dem_graph_int), collapse = "\n"))
  ins_3_prompt <- paste0("Experiment Results:", "\n",
                         paste0(
                           "I have a conjoint study aimed at the following question: ",
                           paste0('"', question, '"'),
                           ", and the survey participants were given the following instruction: ",
                           paste0('"', dependent_variable, '"'), 
                           ", below are analysis results based on traits: "
                         ), "\n",
                         ins_3_data, "\n",
                         "Answer the study question in 5-9 short bullet points of actionable business recommendations based on the survey results per trait and the survey instruction."
  )
  
  


  ### Tab 4 summary

  Ins_4_system <- paste(base::readLines(ins_4_path), collapse = "\n")
 

if (price_attribute == "Price Attribute"){
    ins_4_prompt <- foreach(
      k = 1:2, .packages = c("openai"),
      .export = c("r_path", "question")
    ) %dopar% {
      sum_text <- paste(substring(mindset_dem_int[[k]], 19),
        substring(mindset_partworth_int[[k]], 19),
        substring(mindset_wtp_int[[k]], 19),
        substring(mindset_utility_int[[k]], 19),
        sep = "\n"
      )

      aa <- paste0("Experiment Results:", "\n",
                   paste0(
                     "I have a conjoint study aimed at the following question: ",
                     paste0('"', question, '"'),
                     ", and the survey participants were given the following instruction: ",
                     paste0('"', dependent_variable, '"'), 
                     ", below are analysis results based on mindsets: "
                   ), "\n",
                   sum_text, "\n",
                   "Answer the study question in bullet points of actionable business recommendations based on the survey results per mindset and the survey instruction."
      )
        

    }
  } else {
    ins_4_prompt <- foreach(
      k = 1:2, .packages = c("openai"),
      .export = c("r_path", "question")
    ) %dopar% {
      sum_text <- paste(substring(mindset_dem_int[[k]], 19),
        substring(mindset_partworth_int[[k]], 19),
        substring(mindset_utility_int[[k]], 19),
        sep = ""
      )

    aa <- paste0("Experiment Results:", "\n",
    paste0(
      "I have a conjoint study aimed at the following question: ",
      paste0('"', question, '"'),
      ", below are analysis results based on mindset"
    ), "\n",
      sum_text, "\n",
      "Write 4-5 short bullet points of useful suggestions and strategies based on key insights that can be derived based on above results."
    )

    }
  }
  ### Tab 5 Content

  titles <- c("mindset2", "mindset3")

  # Set API key
  Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
  Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
  model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
  

  sentence <- foreach(
    t = 1:2, .packages =
      c("dplyr", "doParallel", "foreach")
  ) %dopar% {
    mindset <- mindset_partworth[[t]]
    sent <- foreach(
      a = unique(mindset$mindset), .combine = "c",
      .packages = c("dplyr")
    ) %dopar% {
      data_filtered <- mindset %>% filter(mindset == a)
      result <- data_filtered %>%
        arrange(desc(AMCE)) %>%
        head(10)
      sent_ind <- paste(result$attribute_text,
                                         result$level_text,
                                         sep = ":")
      sent_ind2 <- paste0(do.call(paste, c(as.list(sent_ind),
                                           sep = "; "
      )))
      
      sent_ind2
      
      
    }
    sent
  }


  text_in_q1 <- paste(base::readLines(prompt_input_q1_path), collapse = "\n")

  q1_prompt <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]
    
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features per mindset in decreasing order:", "\n")
    )
    

    if (a == 1) {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[1]][1]),
        paste("2.Mindset 2:", sentence[[1]][2]),
        sep = "\n"),
        "\n", "Describe each mindset based on their preferences."))
    } else {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[2]][1]),
        paste("2.Mindset 2:", sentence[[2]][2]),
        paste("3.Mindset 3:", sentence[[2]][3]),
        sep = "\n"),
        "\n", "Describe each mindset based on their preferences."))

    }
  }


  text_in_q2 <- paste(base::readLines(prompt_input_q2_path), collapse = "\n")

  q2_prompt <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]
    
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features per mindset in decreasing order:", "\n")
    )
    
    
    if (a == 1) {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[1]][1]),
              paste("2.Mindset 2:", sentence[[1]][2]),
              sep = "\n"),
        "\n", "Describe the attractiveness of each mindset as a target audience based on their preferences."))
    } else {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[2]][1]),
              paste("2.Mindset 2:", sentence[[2]][2]),
              paste("3.Mindset 3:", sentence[[2]][3]),
              sep = "\n"),
        "\n", "Describe the attractiveness of each mindset as a target audience based on their preferences."))
      
    }
  }



  text_in_q3 <- paste(base::readLines(prompt_input_q3_path), collapse = "\n")

  q3_prompt <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]
    
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features per mindset in decreasing order:", "\n")
    )
    
    if (a == 1) {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[1]][1]),
              paste("2.Mindset 2:", sentence[[1]][2]),
              sep = "\n"),
        "\n", "Describe the unattractiveness of each mindset as a target audience based on their preferences."))
    } else {
      q1_p <- paste(q, paste(
        paste(paste("1.Mindset 1:", sentence[[2]][1]),
              paste("2.Mindset 2:", sentence[[2]][2]),
              paste("3.Mindset 3:", sentence[[2]][3]),
              sep = "\n"),
        "\n", "Describe the unattractiveness of each mindset as a target audience based on their preferences."))
      
    }

  }


  text_in_q4 <- paste(base::readLines(prompt_input_q4_path), collapse = "\n")

  q4_prompt <- foreach(k = 1:2, .packages = c("stringr", "foreach")) %dopar% {
    mindset <-  mindset_partworth[[k]]
    sentence <- foreach(
      i = unique(mindset$mindset), .combine = "c",
      .packages = "foreach"
    ) %dopar% {
      data_filtered <- mindset %>% filter(mindset == i)
      result <- data_filtered %>%
        arrange(desc(AMCE)) %>%
        head(10)
      
      sent_ind <- paste(result$attribute_text,
                        result$level_text,
                        sep = ":")
      sent_ind2 <- paste0(do.call(paste, c(as.list(sent_ind),
                                           sep = "; "
      )))
      sent_ind2
    }
    aa <- c()
    q <- paste0("Mindsets:", "\n", paste0(
      "I have a conjoint study aimed at the following question: ", paste0('"', question, '"'),
      ", and the survey participants were given the following instruction: ", paste0('"', dependent_variable, '"'),
      ", below are the most preferred features per mindset in decreasing order:", "\n")
    )
    
    
    if (k == 1) {
      aa[1] <- paste(q, paste(
        paste("Mindset:", sentence[1]),
        "\n","Generate  messages that will interest the following mindset based on their preferences."
      ))
      aa[2] <- paste(q, paste(
        paste("Mindset:", sentence[2]),
        "\n", "Generate  messages that will interest the following mindset based on their preferences."
      ))
    } else {
      aa[1] <- paste(q, paste(
        paste0("Mindset:", sentence[1]),
        "\n", "Generate  messages that will interest the following mindset based on their preferences."
      ))
      aa[2] <- paste(q, paste(
        paste("Mindset:", sentence[2]),
        "\n",  "Generate  messages that will interest the following mindset based on their preferences."
      ))

      aa[3] <- paste(q, paste(
        paste("Mindset:", sentence[3]),
        "\n", "Generate  messages that will interest the following mindset based on their preferences."
      ))
    }

    aa
  }
  
  create_chat_completion <- function(model, temperature, messages, api_key, endpoint, 
                                     api_version = "2024-12-01-preview", max_retries = 3) {
    
    url <- paste0(endpoint, "/openai/deployments/", model, "/chat/completions?api-version=", api_version)
    
    body <- list(
      messages = messages,
      temperature = temperature
    )
    
    for (attempt in 1:max_retries) {
      # Make the API request
      response <- tryCatch({
        POST(
          url = url,
          add_headers("api-key" = api_key, 
                      "Content-Type" = "application/json"),
          body = toJSON(body, auto_unbox = TRUE),
          encode = "json"
        )
      }, error = function(e) {
        return(NULL)
      })
      
      # Check if request was successful
      if (!is.null(response) && status_code(response) == 200) {
        content <- content(response, "parsed")
        if (!is.null(content$choices) && length(content$choices) > 0) {
          return(content)
        }
      }
      
      # If we get here, the request failed or returned empty
      # Wait with exponential backoff before retrying
      if (attempt < max_retries) {
        wait_time <- 15 ^attempt  # Exponential backoff: 2, 4, 8... seconds
        Sys.sleep(wait_time)
      }
    }
    
    # Return empty response after all retries failed
    return(list(choices = list(list(message = list(content = " ")))))
  }


  tab_5_prompts <- c(unlist(c(
    ins_3_prompt, ins_4_prompt, q1_prompt, q2_prompt,
    q3_prompt, q4_prompt
  )))

  tab_5_prompt_output <- foreach(
    i = 1:length(tab_5_prompts), .combine = "c",
    .packages = c("httr", "jsonlite"), .export = c("api_key", "endpoint", "r_path")
  ) %dopar% {
    
    Sys.setenv("AZURE_OPENAI_API_KEY1" = api_key)
    Sys.setenv("AZURE_OPENAI_ENDPOINT" = endpoint)
    model_name <- "gpt-4o-mini"  # Update this to match your deployed model name
    

    content_1 <- "You are a data analyst skilled in deriving causal conclusions from results of statistical analysis. Your focus is to create insights that correlate with the study question and study results."
    contents <- c(Ins_3_system,  Ins_4_system,  Ins_4_system,
                  text_in_q1, text_in_q1,
                  text_in_q2, text_in_q2,
                  text_in_q3, text_in_q3,
                  text_in_q4, text_in_q4, text_in_q4, text_in_q4, text_in_q4)
    
    
    answer <- tryCatch(
      {
        response <- create_chat_completion(
          model = model_name,
          temperature = 0,
          messages = list(
            list(
              "role" = "system",
              "content" = contents[i]
            ),
            list(
              "role" = "user",
              "content" = tab_5_prompts[i]
            )
          ),
          api_key = api_key,
          endpoint = endpoint
        )
        response$choices[[1]]$message$content
      },
      error = function(e) {
        # Return empty string in case of an error
        " "
      }
    )
    answer
  }
    

  # Tab 3 summary output

  ins_3 <- tab_5_prompt_output[1]

  # Tab 4 summary output

  ins_4_2 <- tab_5_prompt_output[2]
  ins_4_3 <- tab_5_prompt_output[3]
  ins_4 <- list(ins_4_2, ins_4_3)





  q1 <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]


    cl_un <- sort(unique(mindset$mindset))

    if (is.null(tab_5_prompt_output[a + 3])) {
      desc <- c(" ", " ", " ")[1:length(cl_un)]
    } else {
      desc <- foreach(t = cl_un, .combine = "c") %dopar% {
        pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
        pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
        des <- sub(pattern_next, "", sub(
          pattern, "",
          tab_5_prompt_output[a + 3]
        ))
        if (grepl("\n", des, fixed = TRUE)) {
          des <- substr(des, 1, nchar(des) - 2)
        } else {
          des <- des
        }
        stringr::str_remove(des, "\\.$")
      }
    }

    cl <- c(sort(unique(mindset$mindset)))

    nam <- as.data.frame(cbind(cl, desc))
    names(nam) <- c("mindset", "desc")
    nam
  }


  names(q1) <- titles


  q2 <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]


    cl_un <- sort(unique(mindset$mindset))
    if (is.null(tab_5_prompt_output[a + 5])) {
      desc <- c(" ", " ", " ")[1:length(cl_un)]
    } else {
      desc <- foreach(t = cl_un, .combine = "c") %dopar% {
        pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
        pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
        des <- sub(pattern_next, "", sub(
          pattern, "",
          tab_5_prompt_output[a + 5]
        ))
        if (grepl("\n", des, fixed = TRUE)) {
          des <- substr(des, 1, nchar(des) - 2)
        } else {
          des <- des
        }
        stringr::str_remove(des, "\\.$")
      }
    }



    cl <- c(sort(unique(mindset$mindset)))

    nam <- as.data.frame(cbind(cl, desc))
    names(nam) <- c("mindset", "desc")
    nam
  }

  names(q2) <- titles

  q3 <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    mindset <- part_worths_mindsets[[a]]


    cl_un <- sort(unique(mindset$mindset))
    if (is.null(tab_5_prompt_output[a + 7])) {
      desc <- c(" ", " ", " ")[1:length(cl_un)]
    } else {
      desc <- foreach(t = cl_un, .combine = "c") %dopar% {
        pattern <- paste0(".*", paste("Output", t), "\\.", sep = "")
        pattern_next <- paste0("\\", paste("Output", t + 1), ".*", sep = "")
        des <- sub(pattern_next, "", sub(
          pattern, "",
          tab_5_prompt_output[a + 7]
        ))
        if (grepl("\n", des, fixed = TRUE)) {
          des <- substr(des, 1, nchar(des) - 2)
        } else {
          des <- des
        }
        stringr::str_remove(des, "\\.$")
      }
    }

    cl <- c(sort(unique(mindset$mindset)))

    nam <- as.data.frame(cbind(cl, desc))
    names(nam) <- c("mindset", "desc")
    nam
  }
  names(q3) <- titles

  q4 <- foreach(
    a = 1:2, .packages = c("foreach", "openai"),
    .export = c("r_path")
  ) %dopar% {
    if (a == 1) {
      aa <- tab_5_prompt_output[10:11]
    } else {
      aa <- tab_5_prompt_output[12:14]
    }

    if (is.null(aa)) {
      desc <- c(rep(" ", a + 1))
    } else {
      desc <- foreach(
        r = 1:length(aa), .packages = c("openai"),
        .combine = "c"
      ) %dopar% {
        text <- substring(sub("Messages:\n", "", str_remove(aa[r], "^\\s")), 4)
        gsub("\n\n", "\n", text)
      }
    }

    if (length(aa) > 0) {
      cl <- c(1:length(aa))
    } else {
      cl <- 1:(a + 1)
    }


    nam <- as.data.frame(cbind(cl, desc))
    names(nam) <- c("mindset", "desc")
    nam
  }

  return(list(
    PartWorths_Dem = part_worths_dem, Dem_Title = title, Dem_Int = dem_graph_int, PartWorths_Dem_Full = part_worths_dem_full, dem_levels = dem_levels,
    mindset_ai = mindset_ai, mindset_levels = mindset_levels, mindset_tab = mindset_tab, 
    mindset_dem = mindset_dem, mindset_dem_int = mindset_dem_int, 
    mindset_utility = mindset_utility, mindset_utility_int = mindset_utility_int,
    mindset_partworth = mindset_partworth, mindset_partworth_int = mindset_partworth_int, 
    mindset_wtp = mindset_wtp, mindset_wtp_int = mindset_wtp_int, 
    Ins_3 = ins_3, Ins_4 = ins_4, MarketingQuestions = list(Q1 = q1, Q2 = q2, Q3 = q3, Q4 = q4),
    error_handling_calc = pre_calculated
  ))
}

# AMCE Estimates for Human Baseline
AMCE_estimation_hb <- function(survey, attributes, coefs) {
  
  # Import data with attribute information (levels, codings)
  attr_json <- attributes
  
  # Extracting attribute names and codings
  attrs <- data.frame(
    attribute_id = as.numeric(names(
      unlist(attr_json$attributes_and_levels_lookup$get_attribute_text)
    )),
    attribute_text = unlist(
      attr_json$attributes_and_levels_lookup$get_attribute_text
    )
  )
  
  # Number of levels per each attribute
  length <- data.frame(
    attribute_id = as.numeric(names(
      unlist(attr_json$attributes_and_levels_lookup$get_attribute_text)
    )),
    rep = sapply(attr_json$attributes_and_levels_lookup$get_level_ids, length)
  )
  
  a <- as.data.frame(left_join(attrs, length, by = "attribute_id"))
  
  attributes <- as.data.frame(lapply(a, rep, a$rep))
  
  # Extracting attribute levels
  attrs_lev <- data.frame(
    level_ids = unlist(attr_json$attributes_and_levels_lookup$get_level_ids),
    level_text = unlist(attr_json$attributes_and_levels_lookup$get_level_text)
  )
  
  # Matching attribute names and levels
  att <- cbind(attributes, attrs_lev)
  att <- as.data.frame(att)
  
  # Getting the order of attributes and levels (to be further used for visualization)
  order_att <- with(att, {
    dplyr::select(att, attribute_text, level_ids, level_text)
  })
  names(order_att)[2] <- "row"
  
  # Loading survey data
  data_survey <- survey
  
  names(data_survey)[4] <- "Alt"
  
  n_alt <- length(unique(data_survey$Alt))
  
  # Feature levels present in attribute list but not selected by the respondents
  levels_present <- data_survey[, (which(
    colnames(data_survey) == "chosen_choice_letter"
  ) + 1):ncol(data_survey)]
  remove_levels <- setdiff(
    c(unique(unlist(att$level_ids))),
    as.numeric(unique(unlist(levels_present)))[(c(as.numeric(unique(unlist(levels_present)))) != 0)]
  )
  
  # Removing unnecessary columns
  data_survey$X <- NULL
  
  # Identifying unique users
  data_survey$Choice_ID <- c(rep((1:(nrow(data_survey)/n_alt)), each = n_alt))
  names(data_survey)[3] <- "QES"
  data_survey <- data_survey %>% dplyr::select(ID, QES, everything())
  
  
  # Getting choice variable and transforming it into binary
  data_complete <- data_survey
  
  data_complete$choice <- ifelse(data_complete$chosen_choice_letter == "A", 1,
                                 ifelse(data_complete$chosen_choice_letter == "B", 2, (ifelse(data_complete$chosen_choice_letter == "C", 3,
                                                                                              (ifelse(data_complete$chosen_choice_letter == "D", 4, (ifelse(data_complete$chosen_choice_letter == "E", 5, 6))))))))
  
  data_complete$choice <- ifelse(data_complete$choice == data_complete$Alt,
                                 1, 0
  )
  
  
  data_complete <- data_complete %>% arrange(ID, QES, Alt)
  
  coefs_precalc <- coefs
  common_levs_data <- coefs_precalc %>% filter(err == 0 & coef == 0)
  c_data <- att %>% filter(level_text %in% common_levs_data$varname)
  c_data$attribute_text <- gsub(" ", ".", c_data$attribute_text)
  c_data$attribute_text <- gsub("[[:punct:]]", ".",  c_data$attribute_text)
  
  common_levs <- paste0(c_data$attribute_text, "_", c_data$level_ids)
  
  # Getting independent variables
  var <- ncol(data_complete[, c(6:(ncol(data_complete) - 2))])
  
  # Setting independent variables as type 'factor'
  data_complete[ colnames(data_complete[, c(6:(ncol(data_complete) - 2))])] <-
    lapply(data_complete[colnames(data_complete[, c(6:(ncol(data_complete) - 2))])], factor)
  
  att_original <- order_att %>% select(-row)
  
  
  # When there is no price attribute
  data_complete_wide <- cbind(
    fastDummies::dummy_cols(data_complete[, c(6:(ncol(data_complete) - 2))])
    [, -c(1:var)], data_complete[, c("ID", "Choice_ID", "Alt", "choice")]
  )
  
  data_wide_full <- data_complete_wide
  
  independent_var <- colnames(data_complete[, c(6:(ncol(data_complete) - 2))])
  att$new_coded <- paste(c((rep(independent_var, a$rep))), att$level_ids,
                         sep = "_"
  )
  
  att <- att %>% filter(!(level_ids %in% remove_levels))
  
  data_complete <- data_wide_full[, c(
    att$new_coded, "ID", "Choice_ID", "Alt",
    "choice"
  )]
  
  # formula for clm function
  independent_var <- colnames(data_complete[, c(1:(ncol(data_complete) - 4))])
  independent_var_without_base <- independent_var[!independent_var %in% common_levs]
  formula_elements  <- c(independent_var_without_base)
  formula <- as.formula(paste("choice", paste(
    paste(c(formula_elements, "Intercept"),
          collapse = " + "
    ),
    "+ strata(Choice_ID)"
  ),
  sep = "~"
  ))
  
  data_complete <- data_complete %>% select(ID,Choice_ID,Alt,choice, everything())
  data_complete$Intercept <- rep(c(rep(0, each = (n_alt-1)), 1),nrow(data_complete)/n_alt)
  
  # clm model
  model <- clogit(formula, data_complete)
  
  # Standard error, p-value, coefficients
  model_results <- summary(model)
  r_sq <- performance::r2(model)$R2_Nagelkerke
  names(r_sq) <- NULL
  p_value <- model_results$coefficients[, 5]
  
  significance <- data.frame(
    new_coded = names(p_value), p_value = p_value,
    significance = ifelse(p_value <= 0.05,
                          "Yes", "No"
    )
  )
  
  rownames(significance) <- NULL
  
  
  att_lev <- att %>% select(attribute_text, new_coded)
  
  
  att <- as.data.frame(att)
  att$part_worth <- c(model_results$coefficients[, 1][match(
    att$new_coded,
    names(model_results$coefficients[, 1])
  )])
  
  att$se <- c(model_results$coefficients[, 3][match(
    att$new_coded,
    names(model_results$coefficients[, 3])
  )])
  
  att[is.na(att)] <- 0
  
  
  # Add significance estimates to parthworth values table
  merged_data <- merge(att, significance, by = "new_coded", all.x = TRUE)
  
  merged_data <- merged_data %>% arrange(level_ids)
  
  sign_s <- ifelse(model_results$coefficients[nrow(model_results$coefficients),5] <= 0.05, "Yes", "No")
  merged_data_s <- rbind(merged_data, c("Intercept", (max(merged_data$attribute_id)+1), "Intercept", 1, (nrow(merged_data)+1), "Intercept", model_results$coefficients[nrow(model_results$coefficients),c(1, 3, 5)], sign_s))
  
  
  
  merged_data_s$AMCE <- round(2*((exp(as.numeric(merged_data_s$part_worth))/(1+exp(as.numeric(merged_data_s$part_worth)))-0.5)),4)
  
  merged_data_s$std_error <-  round(2*as.numeric(merged_data_s$se)*exp(as.numeric(merged_data_s$part_worth))/(1+exp(as.numeric(merged_data_s$part_worth)))^2,2)
  
  merged_data_s <- merged_data_s %>% select(attribute_id, attribute_text, level_ids, level_text,part_worth, se, p_value, significance, AMCE, std_error)
  
  merged_data_s[is.na(merged_data_s)] <- "Base Level"
  merged_data_s[nrow(merged_data_s), c(9:10)] <- 0
  merged_data_s$p_value <- ifelse(merged_data_s$p_value == "Base Level", 1, round(as.numeric(merged_data_s$p_value),2))
  
  merged_data_s$AMCE_tstat <- ifelse(merged_data_s$std_error == "0", 0, 
                                     round((as.numeric(merged_data_s$AMCE)/as.numeric(merged_data_s$std_error)), 2))
  
  
  

  
  
  merged_data_s$`Reported part_worth` <-  c(as.numeric(coefs_precalc$coef), model_results$coefficients[nrow(model_results$coefficients)])
  merged_data_s$`Reported significance` <- ifelse(merged_data_s$level_text =="Intercept", merged_data_s$significance,
                                                  (ifelse(merged_data_s$significance!= "Base Level", 
                                                          ifelse((abs(merged_data_s$`Reported part_worth`)/coefs$err) >= 1.95 , "Yes", "No"), "Base Level")))
  
  
  
  
  merged_data_s$`Reported AMCE` <- round(2*((exp(as.numeric(merged_data_s$`Reported part_worth`))/(1+exp(as.numeric(merged_data_s$`Reported part_worth`)))-0.5)),4)
  
  se_precalc <- c(as.numeric(coefs_precalc$err), model_results$coefficients[nrow(model_results$coefficients), 3])
  merged_data_s$`Reported std_error` <- round(2*as.numeric(se_precalc)*exp(as.numeric(merged_data_s$`Reported part_worth`))/(1+exp(as.numeric(merged_data_s$`Reported part_worth`)))^2,2)
  
  
  merged_data_s$`Reported AMCE_tstat` <- ifelse(merged_data_s$`Reported std_error` == 0, 0, 
                                                round((as.numeric(merged_data_s$`Reported AMCE`)/as.numeric(merged_data_s$`Reported std_error`)), 2))
  
  
  
  names(merged_data_s)[c(5:11)] <- paste0("Calculated ", names(merged_data_s)[c(5:11)])
  
  merged_data_s <- merged_data_s %>% select(c(1:4), 12, c(5:8), 13, 9, everything())
  
  merged_data_s_no_base <- merged_data_s %>% filter(`Calculated significance` != "Base Level") %>% filter(level_text != "Intercept")
  
  # Coverage Probability
  
  merged_data_s_no_base$lower <- ifelse((as.numeric(merged_data_s_no_base$`Calculated AMCE`) - 1.96 * as.numeric(merged_data_s_no_base$`Calculated std_error`)) < (-1), -1,
    (as.numeric(merged_data_s_no_base$`Calculated AMCE`) - 1.96 * as.numeric(merged_data_s_no_base$`Calculated std_error`)))
  
  merged_data_s_no_base$upper <- ifelse((as.numeric(merged_data_s_no_base$`Calculated AMCE`) + 1.96 * as.numeric(merged_data_s_no_base$`Calculated std_error`)) > 1, 1 ,
                                        (as.numeric(merged_data_s_no_base$`Calculated AMCE`) + 1.96 * as.numeric(merged_data_s_no_base$`Calculated std_error`)))
  
  merged_data_s_no_base$in_range <- merged_data_s_no_base$lower <= as.numeric(merged_data_s_no_base$`Reported AMCE`) & as.numeric(merged_data_s_no_base$`Reported AMCE`) <= merged_data_s_no_base$upper
  
  cov_prob <- round(mean(merged_data_s_no_base$in_range),4)
  
  spearman_rho <- round(cor(as.numeric(merged_data_s_no_base$`Reported part_worth`), as.numeric(merged_data_s_no_base$`Calculated part_worth`), method="spearman"),4)
  
  observed <- as.numeric(merged_data_s_no_base$`Reported AMCE`)
  predicted <- as.numeric(merged_data_s_no_base$`Calculated AMCE`)
  n <- nrow(merged_data_s_no_base)
  
  mape <- mean((abs(observed-predicted)/abs(observed))*100)
  
  merged_data_s$`Calculated part_worth` <- round(as.numeric(merged_data_s$`Calculated part_worth`),4)
  
  merged_data_s$`Calculated se` <- round(as.numeric(merged_data_s$`Calculated se`),4)
  
  merged_data_s$`Reported AMCE` <- round(as.numeric(merged_data_s$`Reported AMCE`),2)
  merged_data_s$`Calculated AMCE` <- round(as.numeric(merged_data_s$`Calculated AMCE`),2)
  
  return(list(AMCE_Results = list(AMCE_Results = merged_data_s, R_Squared = r_sq, R_Spearman = spearman_rho, 
              MAPE = mape, Coverage_Probability = cov_prob)))
  
}



dashboard_cal <- function(survey, attributes) {
  all_estimations <- utility_estimation_ind(attributes, survey)
  AMCE_Results <-  all_estimations$error_handling_calc$Utility_Estimation_calc$merged_data_s %>% filter(level_text != "Not available")
  Attribute_Importance <-  all_estimations$error_handling_calc$Utility_Estimation_calc$Utility
  R_Squared <-  all_estimations$error_handling_calc$Utility_Estimation_calc$r_sq
  AMCE_Title <-  all_estimations$error_handling_calc$Utility_Estimation_calc$AMCE_graph_title
  
  price_titles <-  all_estimations$error_handling_calc$Utility_Estimation_calc$price_titles
  
  p_div <-  all_estimations$error_handling_calc$Utility_Estimation_calc$p_div
  p_paste <- ifelse (p_div == 1, " (ln(Price))", paste0(" (ln(Price/",p_div, "))"))
  
  # function 1 
  all_estimations$error_handling_calc$Utility_Estimation_calc$att <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$Data_complete <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$Data_persona_id <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$formula_elements <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$max_price <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$p_div <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$order_att <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$quintiles <- NULL
  all_estimations$error_handling_calc$Utility_Estimation_calc$estimate <- NULL
  all_estimations$error_handling_calc$PartWorths_Ind <- NULL
  all_estimations$error_handling_calc$attribute_levels_base <- NULL
  
 
  return(list(
    Ind_Est_2 = all_estimations,
    AMCE_Results = list(AMCE_Results = AMCE_Results, Attribute_Importance = Attribute_Importance, R_Squared = R_Squared, 
                        AMCE_Title = AMCE_Title)
  ))
}


if (exp_type == TRUE){
  Data_Output <- AMCE_estimation_hb(csv, json, coefs_precalc)
  ##### Output/ Object #####
  jsonlite::write_json(Data_Output, output_AMCE)
  jsonlite::write_json("Empty Data", output_path_dashboard)
} else{
  Data_Output <- dashboard_cal(csv, json)
  ##### Output/ Object #####
  jsonlite::write_json(Data_Output[-2], output_path_dashboard)
  jsonlite::write_json(Data_Output[-1], output_AMCE)
}






