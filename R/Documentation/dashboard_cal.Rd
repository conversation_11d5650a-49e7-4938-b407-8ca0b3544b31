% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/dashboard_cal.R
\name{dashboard_cal}
\alias{dashboard_cal}
\title{Loading Experiment Entire Analysis Results}
\usage{
dashboard_cal(survey, attributes)
}
\arguments{
\item{survey}{A dataframe containing survey results data.}

\item{attributes}{A JSON object containing attribute information (levels, codings) and experiment description.}
}
\value{
A single list object containing entire analytics data generated based on function inputs
}
\description{
This function takes two main parameters which are attribute information and survey data.
The function returns entire analytics results in a single list
}
\examples{
\dontrun{
dashboard_cal(attributes = your_attr_data, survey = your_survey_data)
}
}
