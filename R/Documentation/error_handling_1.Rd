% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/error_handling_1.R
\name{error_handling_1}
\alias{error_handling_1}
\title{Error Handling for Attributes and Surveys}
\usage{
error_handling_1(attributes, survey)
}
\arguments{
\item{attributes}{A JSON object containing attribute information (levels, codings) and experiment description.}

\item{survey}{A dataframe containing survey results data.}
}
\value{
A list containing partworth estimates (per respondent), demographic characteristics data (per respondent) and
precalculated data to be used in upcoming functions
}
\description{
This function handles potential errors in analysis  related to
insufficient number of respondents or small number of tasks per respondent. It calculates partworths data per respondent and
recovers respondents trait data based on encodings found in the JSON file.
}
\examples{
\dontrun{
error_handling(attributes = your_attr_data, survey = your_survey_data)
}
}
