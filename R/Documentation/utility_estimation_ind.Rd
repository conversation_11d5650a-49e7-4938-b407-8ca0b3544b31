% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/utility_estimation_ind.R
\name{utility_estimation_ind}
\alias{utility_estimation_ind}
\title{Utility Estimation Function for Smaller Respondent Groups}
\usage{
utility_estimation_ind(attributes, survey)
}
\arguments{
\item{attributes}{A JSON object containing attribute information (levels, codings) and experiment description.}

\item{survey}{A dataframe containing survey results data.}
}
\value{
A list containing mindset analysis (partworths data, utility data and willingness to pay data (if price attribute is available),
AI-aided interpretations of the results), trait analysis (partworths data and AI-aided interpretations
of the results) for each demographic group.
}
\description{
This function takes two main parameters which are attribute information and survey data.
The function performs grouping respondents into 2-3 clusters (PCA analysis and Cluster analysis),
grouping respondents based on their demographic belonging and estimating preferences per each group. Once done, the results
are used for furhter AI-aided interpretations and explanations
}
\examples{
\dontrun{
utility_estimation(attributes = your_attr_data, survey = your_survey_data)
}
}
