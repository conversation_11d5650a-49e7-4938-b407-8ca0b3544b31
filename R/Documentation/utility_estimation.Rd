% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/utility_estimation.R
\name{utility_estimation}
\alias{utility_estimation}
\title{Utility Estimation Function}
\usage{
utility_estimation(attributes, survey)
}
\arguments{
\item{attributes}{A JSON object containing attribute information (levels, codings) and experiment description.}

\item{survey}{A dataframe containing survey results data.}
}
\value{
A list containing partworths data, utility data and willingness to pay data (if price attribute is available),
AI-aided interpretations of the results, and precalculated data to be used in upcoming functions
}
\description{
This function takes two main parameters which are attribute information and survey data.
The function performs several steps to estimate the utility of each attribute. The steps involve
loading attribute data and survey data, extracting encoded attribute names and levels, data manipulation and
data cleaning steps, calculating partworths and utilities, etc.
The output is a list object containing partworths and utility data, as well as other information related
to the conducted conjoint analysis.
}
\examples{
\dontrun{
utility_estimation(attributes = your_attr_data, survey = your_survey_data)
}
}
