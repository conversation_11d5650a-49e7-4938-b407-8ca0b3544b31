FROM python:3.11-bullseye

WORKDIR /usr/src/app

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1
# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

RUN apt-get update && \
  echo "deb https://cloud.r-project.org/bin/linux/debian bullseye-cran40/" >> /etc/apt/sources.list && \
  apt-key adv --keyserver keyserver.ubuntu.com --recv-key '95C0FAF38DB3CCAD0C080A7BDC78B2DDEABC47B7' && \
  apt-get update && \
  apt-get install -y --no-install-recommends r-base r-base-core r-recommended r-base-dev && \
  apt-get install -y 

RUN R -e "install.packages('mclust', repos='https://cloud.r-project.org/')"           || { echo "Installation of 'mclust' package failed" ; exit 1; }
RUN R -e "install.packages('openxlsx', repos='https://cloud.r-project.org/')"         || { echo "Installation of 'openxlsx' package failed" ; exit 1; }
RUN R -e "install.packages('doParallel', repos='https://cloud.r-project.org/')"       || { echo "Installation of 'doParallel' package failed" ; exit 1; }
RUN R -e "install.packages('data.table', repos='https://cloud.r-project.org/')"       || { echo "Installation of 'data.table' package failed" ; exit 1; }
RUN R -e "install.packages('altair', repos='https://cloud.r-project.org/')"           || { echo "Installation of 'altair' package failed" ; exit 1; }
RUN R -e "install.packages('foreach', repos='https://cloud.r-project.org/')"          || { echo "Installation of 'foreach' package failed" ; exit 1; }
RUN R -e "install.packages('vegawidget', repos='https://cloud.r-project.org/')"       || { echo "Installation of 'vegawidget' package failed" ; exit 1; }
RUN R -e "install.packages('RcppRoll', repos='https://cloud.r-project.org/')"         || { echo "Installation of 'RcppRoll' package failed" ; exit 1; }
RUN R -e "install.packages('tidyr', repos='https://cloud.r-project.org/')"            || { echo "Installation of 'tidyr' package failed" ; exit 1; }
RUN R -e "install.packages('dplyr', repos='https://cloud.r-project.org/')"            || { echo "Installation of 'dplyr' package failed" ; exit 1; }
RUN R -e "install.packages('plyr', repos='https://cloud.r-project.org/')"             || { echo "Installation of 'plyr' package failed" ; exit 1; }
RUN R -e "install.packages('stringr', repos='https://cloud.r-project.org/')"          || { echo "Installation of 'stringr' package failed" ; exit 1; }
RUN R -e "install.packages('formattable', repos='https://cloud.r-project.org/')"      || { echo "Installation of 'formattable' package failed" ; exit 1; }
RUN R -e "install.packages('survival', repos='https://cloud.r-project.org/')"         || { echo "Installation of 'survival' package failed" ; exit 1; }
RUN R -e "install.packages('magrittr', repos='https://cloud.r-project.org/')"         || { echo "Installation of 'magrittr' package failed" ; exit 1; }
RUN R -e "install.packages('fontawesome', repos='https://cloud.r-project.org/')"      || { echo "Installation of 'fontawesome' package failed" ; exit 1; }
RUN R -e "install.packages('openai', repos='https://cloud.r-project.org/')"           || { echo "Installation of 'openai' package failed" ; exit 1; }
RUN R -e "install.packages('digest', repos='https://cloud.r-project.org/')"           || { echo "Installation of 'digest' package failed" ; exit 1; }
RUN R -e "install.packages('fastDummies', repos='https://cloud.r-project.org/')"      || { echo "Installation of 'fastDummies' package failed" ; exit 1; }
RUN R -e "install.packages('DT', repos='https://cloud.r-project.org/')"               || { echo "Installation of 'DT' package failed" ; exit 1; }
RUN R -e "install.packages('jsonlite', repos='https://cloud.r-project.org/')"         || { echo "Installation of 'jsonlite' package failed" ; exit 1; }
RUN R -e "install.packages('zoo', repos='https://cloud.r-project.org/')"              || { echo "Installation of 'zoo' package failed" ; exit 1; }
RUN R -e "install.packages('stringi', repos='https://cloud.r-project.org/')"          || { echo "Installation of 'stringi' package failed" ; exit 1; }
RUN R -e "install.packages('performance', repos='https://cloud.r-project.org/')"      || { echo "Installation of 'performance' package failed" ; exit 1; }
RUN R -e "install.packages('httr', repos='https://cloud.r-project.org/')"             || { echo "Installation of 'httr' package failed" ; exit 1; }
RUN R -e "install.packages('maxLik', repos='https://cloud.r-project.org/')"           || { echo "Installation of 'maxLik' package failed" ; exit 1; }

#RUN R -e "install.packages('shinydashboard', repos='https://cloud.r-project.org/')"   || { echo "Installation of 'shinydashboard' package failed" ; exit 1; }
#RUN R -e "install.packages('shinyWidgets', repos='https://cloud.r-project.org/')"     || { echo "Installation of 'shinyWidgets' package failed" ; exit 1; }
#RUN R -e "install.packages('shinyjs', repos='https://cloud.r-project.org/')"          || { echo "Installation of 'shinyjs' package failed" ; exit 1; }
#RUN R -e "install.packages('shinyBS', repos='https://cloud.r-project.org/')"          || { echo "Installation of 'shinyBS' package failed" ; exit 1; }
#RUN R -e "install.packages('shinycssloaders', repos='https://cloud.r-project.org/')"  || { echo "Installation of 'shinycssloaders' package failed" ; exit 1; }

RUN apt-get update && apt-get install -y apt-transport-https ca-certificates curl gnupg && \
  curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | gpg --dearmor -o /usr/share/keyrings/doppler-archive-keyring.gpg && \
  echo "deb [signed-by=/usr/share/keyrings/doppler-archive-keyring.gpg] https://packages.doppler.com/public/cli/deb/debian any-version main" | tee /etc/apt/sources.list.d/doppler-cli.list && \
  apt-get update && \
  apt-get -y install doppler

RUN pip install --upgrade pip --no-cache-dir poetry
COPY ./pyproject.toml .
COPY ./poetry.lock .

RUN poetry config virtualenvs.create false
RUN poetry install --no-root --only main