site_name: Rehoboam Documentation
site_description: Documentation for the Rehoboam API
theme:
  name: material
  language: en
  features:
    - content.tabs.link
    - content.code.annotate
    - content.code.copy
    - announce.dismiss
    - navigation.tabs
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.preview
    - navigation.instant.progress
    - navigation.path
    - navigation.sections
    - navigation.top
    - navigation.tracking
    - search.suggest
    - toc.follow
  palette:
    - scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to light mode

markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:mermaid2.fence_mermaid_custom
  - admonition
  - footnotes
  - attr_list
  - md_in_html
  - codehilite
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg

nav:
  - Home: index.md
  - Getting Started:
      - Remote Development: getting-started/remote-development.md
      - Prerequisites: getting-started/prerequisites.md
      - Installation: getting-started/installation.md
      - Running the App: getting-started/running-the-app.md
  - Architecture:
      - Overview: architecture/overview.md
      - Authentication: architecture/authentication.md
      - Infrastructure: architecture/infrastructure.md
  - Contributing:
      - Guidelines: contributing/guidelines.md
      - Development Workflow: contributing/development-workflow.md
  #  - Testing:
  #    - Overview: testing/overview.md
  #    - Unit Testing: testing/unit-testing.md
  #    - Integration Testing: testing/integration-testing.md
  #    - E2E Testing: testing/e2e-testing.md
  #    - Test Coverage: testing/coverage.md
  - Deployment:
      - Environments: deployment/environments.md
  #    - CI/CD Pipeline: deployment/ci-cd.md
      - Release Process: deployment/release-process.md
  - How Tos:
      - Create a Doppler Token: how-tos/get-doppler-token.md
      - Get Access Token: how-tos/get-access-token.md

plugins:
  - search:
      language: en
      highlight: true
  - mermaid2:
      javascript: https://unpkg.com/mermaid@10.4.0/dist/mermaid.esm.min.mjs
  - minify:
      minify_html: true
