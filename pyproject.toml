[tool.poetry]
package-mode = false
name = "backend"
version = "1.0.0"
description = ""
authors = ["<PERSON> <j<PERSON><PERSON><PERSON>@gmail.com>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pydantic = "2.9.0"
uvicorn = "^0.30.5"
openai = "^1.38.0"
doit = "^0.36.0"
tomli = "^2.0.1"
rich = "^12.6.0"
firebase-admin = "^6.0.1"
wandb = "^0.18.3"
uuid6 = "^2022.10.25"
numpy = "1.26.4"
backoff = "^2.2.1"
pandas = "^1.5.2"
requests = "^2.28.2"
statsmodels = "^0.13.5"
matplotlib = "3.7.2"
seaborn = "^0.12.2"
scikit-learn = "^1.2.0"
absl-py = "1.3.0"
gitpython = "^3.1.43"
google-api-core = "^2.21.0"
google-api-python-client = "2.72.0"
google-auth-httplib2 = "0.1.0"
google-auth = "^2.35.0"
google-cloud-core = "2.3.2"
google-cloud-firestore = "2.9.0"
openpyxl = "3.1.0"
pyyaml = "6.0"
scipy = "^1.14.0"
types-pytz = "2022.7.0.0"
typing-extensions = "^4.12.2"
pnglatex = "1.1"
click = "^8.1.3"
unidecode = "^1.3.6"
ecs-logging = "2.0.0"
PyJWT = { version = "2.7.0", extras = ["crypto"] }
aiohttp = "^3.11.16"
aiosignal = "1.3.1"
alabaster = "0.7.13"
anyio = "3.6.2"
appdirs = "1.4.4"
async-timeout = "4.0.2"
attrs = "23.1.0"
babel = "2.12.1"
cachecontrol = "0.12.11"
cachetools = "5.3.0"
certifi = "^2025.1.31"
cffi = "1.15.1"
charset-normalizer = "3.1.0"
cloudpickle = "2.2.1"
colorama = "0.4.6"
commonmark = "0.9.1"
contourpy = "1.0.7"
cryptography = "^44.0.2"
cycler = "0.11.0"
dataclasses-json = "0.5.7"
docker-pycreds = "0.4.0"
docutils = "0.19"
et-xmlfile = "1.1.0"
fonttools = "^4.57.0"
frozenlist = "1.3.3"
future = "0.18.3"
gitdb = "4.0.10"
google-crc32c = "1.5.0"
googleapis-common-protos = "1.59.0"
greenlet = "2.0.2"
grpcio-status = "1.54.2"
grpcio = "^1.71.0"
h11 = "0.14.0"
httplib2 = "0.22.0"
httptools = "0.5.0"
idna = "^3.10"
imagesize = "1.4.1"
importlib-metadata = "6.6.0"
jinja2 = "^3.1.6"
joblib = "1.2.0"
kiwisolver = "1.4.4"
markupsafe = "2.1.2"
marshmallow-enum = "1.5.1"
marshmallow = "3.19.0"
multidict = "6.0.4"
mypy-extensions = "1.0.0"
packaging = "^23.2"
pathtools = "0.1.2"
patsy = "0.5.3"
pillow = "^11.1.0"
proto-plus = ">=1.22.3"
protobuf = "4.23.1"
psutil = "5.9.5"
pyasn1-modules = "0.3.0"
pyasn1 = "0.5.0"
pycparser = "2.21"
pygments = "^2.18.0"
pyjwt = "2.7.0"
pyparsing = "3.0.9"
python-dateutil = "2.8.2"
python-dotenv = "1.0.0"
pytz = "2023.3"
rsa = "4.9"
sentry-sdk = "1.21.1"
setproctitle = "1.3.2"
setuptools = "^78.1.0"
six = "1.16.0"
smmap = "5.0.0"
sniffio = "1.3.0"
snowballstemmer = "2.2.0"
sqlalchemy = "1.4.48"
starlette = "<0.38.0"
tenacity = "8.2.2"
threadpoolctl = "3.1.0"
tqdm = "4.65.0"
typing-inspect = "0.8.0"
uritemplate = "4.1.1"
uvloop = "0.17.0"
watchfiles = "0.19.0"
websockets = "11.0.3"
yarl = "^1.18.3"
zipp = "^3.21.0"
camel-converter = "^3.0.0"
rfc3986 = { version = "1.5.0", extras = ["idna2008"] }
celery = "^5.2.7"
redis = "^4.5.5"
flower = "^1.2.0"
gunicorn = "^23.0.0"
yarg = "^0.1.9"
docopt = "^0.6.2"
pipreqs = "^0.4.13"
doppler-env = "^0.3.1"
trove-classifiers = "2023.11.14"
stripe = "^7.7.0"
cohere = "^5.6.2"
novu = "^1.14.0"
sse-starlette = "^2.1.2"
fastapi-cache2 = {extras = ["redis"], version = "^0.2.1"}
circuitbreaker = "^2.0.0"
fastapi = "^0.112.0"
pydantic-settings = "^2.4.0"
spinners = "^0.0.24"
yaspin = "^3.0.2"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.23.8"
pytest-mock = "^3.14.0"
loguru = "^0.7.2"
click-didyoumean = "^0.3.1"
langsmith = "^0.1.144"
memory-profiler = "^0.61.0"
pylogit = {git = "https://github.com/aviyashchin/pylogit.git"}
langchain = "0.3.4"
langchain-core = "^0.3.12"
langchain-openai = "^0.3.14"
langchain-anthropic = "^0.2.3"
langchain-community = "^0.3.3"
langchain-cohere = "^0.3.1"
httpx = "^0.27.2"
langchain-google-genai = "^2.0.1"
google-cloud-storage = "^2.18.2"
langchain-google-vertexai = "^2.0.7"
langchain-mistralai = "^0.2.0"
pymupdf = "^1.24.14"
python-multipart = "^0.0.20"
faiss-cpu = "^1.9.0.post1"
urllib3 = "^2.3.0"
pandera = "^0.22.1"
asyncpg = "^0.30.0"
alembic = "^1.14.0"
psycopg2-binary = "^2.9.10"
gpt-researcher = "^0.10.11"
langgraph = "^0.3.1"
supabase = "^2.15.0"
reportlab = "^4.4.0"
boto3 = "^1.38.7"



[tool.poetry.group.test.dependencies]
pyproject-flake8 = "^6.0.0.post1"
pytest = "^7.2.0"
coverage = "^7.2.1"
tox = "^4.4.7"
tox-gh-actions = "^3.1.0"
black = "^23.1.0"
isort = "^5.12.0"


[tool.poetry.group.dev.dependencies]
mkdocs-material = "^9.5.47"
mkdocs-minify-plugin = "^0.8.0"
mkdocs-mermaid2-plugin = "^1.2.1"
pymdown-extensions = "^10.13"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.flake8]
max-line-length = 88
max-complexity = 10
extend-ignore = "E203"
exclude = ["docs", "wandb", ".*/data", ".venv"]

[tool.black]
line_length = 88
preview = true

[tool.isort]
profile = "black"
multi_line_output = 3
known_third_party = ["wandb"]
