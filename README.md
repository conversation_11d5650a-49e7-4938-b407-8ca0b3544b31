# Rehoboam

💕SuperEgo API empowers consulting and product teams to design and run cost-effective, ethical, and causal experiments using generative AI for experimental design. 🚀👨‍🔬⚗️


## Table of Contents

- [Getting Started](#getting-started)
- [Architecture](#architecture)
- [Contributing](#contributing)
- [Deployment](#deployment)
- [How Tos](#how-tos)
- [Contributing](#contributing)
- [Troubleshooting](#troubleshooting)

## Getting Started

Follow these steps to set up the project locally:

### Prerequisites
- Python 3.11 or later
- Poetry (for dependency management): [Installation instructions](https://python-poetry.org/docs/#installation)
- Doppler (for managing secrets): [Installation instructions](https://docs.doppler.com/docs/enclave-installation)
- Docker (for containerized development): [Installation instructions](https://docs.docker.com/get-docker/)
- Recommended IDE: VSCode with the Python extension installed. [Installation instructions](https://code.visualstudio.com/)


### Installation

To set up the project locally, you have two options:
1. Run the project without Docker
2. Run the project with <PERSON><PERSON> (recommended)

### Running the project without Docker

1. Clone the repository:

```bash
<NAME_EMAIL>:Subconscious-ai/rehoboam.git
```

2. Navigate to the project directory:

```bash
cd rehoboam
```

3. Activate the virtual environment:

```bash
poetry shell
```

4. Install the project dependencies:

```bash
poetry install
```

5. Create a `.env` file in the root directory of the project. The file must contain the following environment variables:

```bash
DOPPLER_TOKEN=<add_key>
DEBUG=True
ENVIRONMENT=local
```

NOTE: Get the `DOPPLER_TOKEN` from the project owner.

6. Run the following command to start the application:

```bash
doppler run -- uvicorn rehoboam.main:app --host 0.0.0.0 --reload --port 8080 --timeout-keep-alive 60
```

7. The API will be available at `localhost:8080` and the Swagger documentation at `localhost:8080/docs`.

## For Development Running (Without Docker):

1. Add `DOPPLER_TOKEN` and run the following script:

```
export DOPPLER_TOKEN=<add_key> DEBUG=True && doppler run -- sh -c "pip install debugpy -t ./tmp && python ./tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 -m uvicorn app.main:app --host 0.0.0.0 --reload --port 8080 --timeout-keep-alive 60"
```

2. Attach a debugger to port `5678` to start the App service
   Run the task `Debug Python Without Docker` from `.vscode/launch.json`

- Api is at `localhost:8080`
- Docs is at `localhost:8080/docs`

# With Docker

## Basic Running (with Docker):

```
docker-compose -f docker-compose.yml up -d
```

- Api is at `localhost:8080`
- Docs is at `localhost:8080/docs`
- Worker dashboard is at `localhost:5555`

## To view the logs:

- Use the command `docker ps` to obtain a list of containers. The main app is contained in the `backend-app` image
- Use the command `docker logs <container_id>` to view the logs for that container

## For Development (with Docker):

1. Run the following command:

```
docker-compose -f docker-compose.debug.yml up
```

2. Attach a debugger to port `5678` to start the App service. You can do this by running the task "Debug Python" from `.vscode/launch.json`.

- Attach a debugger to port `5678` to start the App service
- Api is at `localhost:8080`
- Docs is at `localhost:8080/docs`
- Worker dashboard is at `localhost:5555`


## Deploying to production

Any releases tagged in the format `vx.x.x` will be deployed to production using the Cloud Build pipeline.

To create a release:

- Navigate to the main page of the [repository](https://github.com/Subconscious-ai/rehoboam).
- To the right of the list of files, click Releases.
- At the top of the page, click Draft a new release.
- select the Choose a tag dropdown menu and create a new tag (e.g. v1.2.2).
- If you're ready to publicize your release, click Publish release. To work on the release later, click Save draft. You can then view your published or draft releases in the releases feed for your repository. For more information, see [Viewing your repository's releases and tags](https://docs.github.com/en/repositories/releasing-projects-on-github/viewing-your-repositorys-releases-and-tags).

## High level process is the following:

- CI/CD Pipeline downloads the latest docker build and uses for cache
- Everything that can be used from the cache is used, the rest is rebuilt by Docker
- The image gets pushed to the repository
- CI/CD pipelines tells Kubernetes the pull the new image and replace the old instances. This process takes time because it replaces instances one by one so as not to have any downtime.
- Then the CI/CD pipeline will perform health checks until one the two happens: The instances are all online or 20 minutes has elapsed since the first Health check


Join our Discord here: https://discord.gg/3bgj4ZhABz 
