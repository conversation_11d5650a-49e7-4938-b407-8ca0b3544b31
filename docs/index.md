# Rehoboam Documentation

💕SuperEgo API empowers consulting and product teams to design and run cost-effective, ethical, and causal experiments using generative AI for experimental design. 🚀👨‍🔬⚗️

## Getting Started

Get up and running with <PERSON><PERSON>boam:

- [Remote Development](getting-started/remote-development.md)
- [Prerequisites](getting-started/prerequisites.md)
- [Installation](getting-started/installation.md)
- [Running the App](getting-started/running-the-app.md)

## Architecture
Understand the system architecture and components:

- [System Overview](architecture/overview.md)
- [Authentication & Authorization](architecture/authentication.md)
- [Infrastructure](architecture/infrastructure.md)
- [Database](architecture/database.md)
- [Experiment Flow](architecture/experiment-flow.md)

## How Tos

Step-by-step guides for common tasks:

- [Create Doppler Token](how-tos/get-doppler-token.md)
- [Get Access Token](how-tos/get-access-token.md)

## Contributing
Learn how to contribute to the project:

- [Guidelines](contributing/guidelines.md)
- [Development Workflow](contributing/development-workflow.md)

## Deployment

Learn about our deployment environments and processes:

- [Environments](deployment/environments.md)
- [Release Process](deployment/release-process.md)

## Getting Help

If you need help, you can:

1. Check the [How-Tos](#how-tos) section for common tasks
2. Search this documentation using the search bar at the top of the page
3. Contact the team on [Subconscious AI Discord](https://discord.gg/3bgj4ZhABz){target="_blank"} and ask for help.

## Quick Links

- API Documentation: [http://localhost:8080/docs](http://localhost:8080/docs){target="_blank"}
- Source Code: [GitHub Repository](https://github.com/Subconscious-ai/rehoboam){target="_blank"}
- Discord: [Subconscious AI Discord](https://discord.gg/3bgj4ZhABz){target="_blank"}
