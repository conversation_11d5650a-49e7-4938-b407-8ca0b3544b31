Before you begin setting up the Rehoboam backend, ensure you have the following
prerequisites installed on your system.

To install the prerequisites on Ubuntu, Debian, or Darwin (macOS) systems, follow the
steps in [this section](#installation-and-verification). If, for some reason, the
automatic process doesn't work, you can install the prerequisites manually using
the links provided [below](#required-software).

Alternatively, you can proceed with the full setup by following the
[installation steps](installation.md), which will automatically check
for and install any missing prerequisites.

### Required Software
   1. **Python 3.11+**
      - The project uses Python 3.11+
      - If you're on Windows, we recommend using
        [WSL2](https://docs.microsoft.com/en-us/windows/wsl/install)
        for a better development experience
      - Verify installation:
        ```bash
        python3 --version
        ```

   2. **Poetry**
      - Primary package manager for dependency management
      - Manual installation:
        [https://python-poetry.org/docs/](https://python-poetry.org/docs/)
      - Verify installation:
        ```bash
        poetry --version
        ```

   3. **Doppler CLI**
      - Required for managing secrets and environment variables
      - Manual Installation:
        [Doppler Installation Guide](https://docs.doppler.com/docs/install-cli)
      - Verify installation:
        ```bash
        doppler --version
        ```

   4. **Git**
      - Required for version control and repository access
      - Manual installation:
        [Git Installation Guide](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git)
      - Verify installation:
        ```bash
        git --version
        ```

   5. **R**
      - Programming language and environment designed for statistical computing,
      data analysis, and visualization.
      - Manual installation:
         - Ubuntu:
            [official CRAN installation](https://cran.r-project.org/bin/linux/ubuntu/fullREADME.html)
         - Debian:
            [official CRAN installation](https://cran.r-project.org/bin/linux/debian/)
         - macOS:
            [install with Homebrew](https://formulae.brew.sh/formula/r)
         - Windows:
            [official CRAN website](https://cran.r-project.org/){target="_blank"}.
      - Verify installation:
        ```bash
        R --version
        ```


### Optional Software

   1. **Docker**
      - Optional but recommended for containerized development
      - Installation: [Docker Installation Guide](https://docs.docker.com/engine/install/)
      - If using WSL2, ensure Docker engine is enabled for WSL

   2. **WSL2 (Windows Systems Only)**
      ```powershell
      # In PowerShell (Administrator)
      wsl --install

      # Set WSL2 as default version
      wsl --set-default-version 2
      ```

### Development Environment

   1. **IDE Setup**
      - Recommended: [VS Code](https://code.visualstudio.com/){target="_blank"} or
        [PyCharm](https://www.jetbrains.com/pycharm/){target="_blank"}
      - Required VS Code extensions:
        - [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python){target="_blank"}
        - [Remote Development](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack){target="_blank"}

   2. **System Requirements**
      - Minimum 4GB RAM
      - 10GB free disk space
      - Internet connection for API access

### Access Requirements

   1. **GitHub Access**
      - Access to the Rehoboam repository
      - GitHub personal access token for installation

   2. **Doppler Token**
      - Required for accessing secrets
      - Must be obtained from the development team


### Installation and Verification

To install the prerequisites or verify their presence on Ubuntu, Debian, or Darwin
(macOS) systems, simply run the following:

```bash
# Clone the repository
git clone https://github.com/Subconscious-ai/rehoboam.git

# Change directory to the backend
cd rehoboam

# Check prerequisites; install Poetry and R
make prerequisites
```

### Next Steps

After ensuring all prerequisites are met:

1. Test your setup:
   ```bash
   # Navigate to the project
   cd rehoboam

   # Test Poetry
   poetry run python -c "print('Setup successful!')"
   ```

2. Continue to [Installation Steps](installation.md)
3. Configure [Authentication](../architecture/authentication.md)

### Troubleshooting

If you encounter issues during setup:

1. **Poetry Installation Issues**
   ```bash
   # Uninstall Poetry
   curl -sSL https://install.python-poetry.org | python3 - --uninstall

   # Reinstall with modified settings
   curl -sSL https://install.python-poetry.org > poetry_install.py

   # Edit poetry_install.py - change symlink to True on line 317
   python3 poetry_install.py
   ```

2. **WSL2 Issues**
      - Ensure virtualization is enabled in BIOS
      - Update Windows to the latest version
      - Run Windows PowerShell as Administrator

3. **Common Python Issues**
      - Use [pyenv](https://github.com/pyenv/pyenv){target="_blank"} to manage
        multiple Python versions
      - Ensure Python 3.11+ is set as the default version

4. Also refer to the [Troubleshooting Guide](/how-tos/troubleshooting) for additional help.
