The recommended way to set up your development environment is by using our remote
development machine (remote-dev-machine). This guide will walk you through the process.

```bash
# Clone the repository
git clone https://github.com/Subconscious-ai/rehoboam.git

# Change directory to the backend
cd rehoboam

# Full installation (prerequisites + Poetry, R packages)
make setup
```

### Configure Environment Variables

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Add your Doppler token to the `.env` file:
   ```bash
    DOPPLER_TOKEN=<YOUR_KEY>  # Add your Doppler token here
   ```

### Activate the Environment

You can activate the poetry environment by running the following command from the
project directory:
```bash
source scripts/activate_env.sh
```
If you'd like to do this with a single command from anywhere (*e.g.*, `activate-env`),
add the following lines to your `~/.bashrc` or `~/.zshrc` (replacing `PATH-TO-REPO`):
```bash
REHOBOAM_PATH="PATH-TO-REPO"
alias activate-env="source $REHOBOAM_PATH/scripts/activate_env.sh"
```
After sourcing your `~/.bashrc` or `~/.zshrc`, you can simply run `activate-env`
in your terminal. If you'd like this to run automatically at startup, add the
following line after the previous ones in your `bashrc` or `zshrc`:
```
activate-env
```
This will run at startup, which is specially useful for
[remote development](remote-development.md).

<!-- on Windows Powershell, use ` Invoke-Expression (poetry env activate)` instead -->
<!-- of `eval $(poetry env activate)`. -->

### IDE Setup

#### VS Code
1. Install Python extension
2. Select Python interpreter from Poetry environment
3. Configure debugging settings in `.vscode/launch.json`

#### PyCharm
1. Go to PyCharm -> Preferences
2. Navigate to Project: backend -> Python Interpreter
3. Add interpreter (Poetry Environment)
4. Configure run configuration for main.py

### Next Steps

After completing the installation:

1. Continue to [Remote Development](remote-development.md)
