### 4. Run the Application

#### Without Docker

After activating the environment, you can run the application from the project folder using:
```bash
make startup
```

!!! note2
    If the port `8080` is already in use, you can change it to another port by changing the `--port` argument to the script.
    `OR` you can use port `0` to automatically assign a free port.

##### Development with Debugger
1. Install the `debugpy` package:
```bash
pip install debugpy -t ./tmp
```

2. Run the application with the debugger:
```bash
./scripts/startup.sh --debug
```
And you can connect the debugger to port `5678`.


#### With Docker (currently unavailable)

Basic Running:
```bash
docker compose -f docker-compose.yml up -d
```

Development with Debugger:
```bash
docker compose -f docker-compose.debug.yml up
```

### 5. Access Points

- API: [http://localhost:8080](http://localhost:8080)
- Documentation: [http://localhost:8080/docs](http://localhost:8080/docs)
- Worker Dashboard: [http://localhost:5555](http://localhost:5555)  (Docker only)

### Next Steps

After completing the installation and starting the app:

   1. Verify your setup:
      - Check API access at [http://localhost:8080](http://localhost:8080)
      - Review API documentation at [http://localhost:8080/docs](http://localhost:8080/docs)
      - Test running a basic experiment

   2. Review Architecture and Infrastructure:
      - [System Overview](../architecture/overview.md)
      - [Authentication & Authorization](../architecture/authentication.md)
      - [Infrastructure](../architecture/infrastructure.md)
