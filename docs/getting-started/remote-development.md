The recommended way to set up your development environment is by using our remote
development machine (`remote-dev-machine`). This guide will walk you through the
process.

## 1. Set Up SSH Access

### Get Your Local SSH Public Key

```bash
# View existing public key
cat $HOME/.ssh/id_rsa.pub

# Or generate new key if needed
ssh-keygen -t rsa -f $HOME/.ssh/id_rsa
```

### Add Your Key to GCP

1. Go to [remote-dev-machine](https://console.cloud.google.com/compute/instancesDetail/zones/us-west1-b/instances/remote-dev-machine?project=subconsciousai-dev){target="_blank"}
2. Click "Edit" at the top
3. Scroll down to "SSH Keys"
4. Click "Add Item"
5. Paste your public key
6. Click "Save"

## 2. Connect to the Remote Machine

### Prerequisite: Set Up SSH Configuration

1. Create the SSH config file

      For macOS/Linux:
      ```bash
      vim ~/.ssh/config          # macOS/Linux
      ```
      For Windows (PowerShell):
      ```bash
      notepad $HOME/.ssh/config  # Windows
      ```

2. Add SSH configuration
      ```
      Host remote-dev-machine
         User USERNAME
         Hostname ************
         LocalForward 8080 localhost:8080
      ```

### SSH Via Terminal

1. Clone the repo:
   ```bash
    # Clone the repository
   git clone https://github.com/Subconscious-ai/rehoboam.git

   # Change directory to the backend
   cd rehoboam
   ```

2. Connect:
   ```bash
   make remote-dev
   ```
   If you'd like to do this with a single command from anywhere (*e.g.*, `remote-dev`),
   add the following lines to your `~/.bashrc` or `~/.zshrc` (replacing `PATH-TO-REPO`):
   ```bash
   REHOBOAM_PATH="PATH-TO-REPO"
   alias remote-dev="make -C $REHOBOAM_PATH remote-dev"
   ```
   After sourcing your `~/.bashrc` or `~/.zshrc`, you can simply type `remote-dev`.


### SSH VS Code

1. Install "Remote - SSH" extension
2. Connect:
   - Click green button (><) in bottom-left corner
   - Select "Connect to Host..."
   - Choose "remote-dev-machine"

## 3. Set Up GitHub Access

1. Generate SSH key on remote machine:
   ```bash
   # Connect to the remote machine first
   remote-dev

   # Generate SSH key
   ssh-keygen -t rsa -f ~/.ssh/id_rsa

   # View public key
   cat ~/.ssh/id_rsa.pub
   ```

2. Add key to GitHub:
   - Go to [GitHub SSH Settings](https://github.com/settings/keys){target="_blank"}
   - Click "New SSH key"
   - Give it a title (e.g., "remote-dev-machine")
   - Paste the public key
   - Click "Add SSH key"

## 4. Project Setup on Remote Machine

Please follow the [Installation Guide](./installation.md).

<!-- with these additional notes: -->
<!-- # Configure Poetry to create virtual environment in project directory -->
<!-- poetry config virtualenvs.in-project true -->

## 5. VS Code Extensions

Install these recommended extensions in your VS Code Remote session:

  - [Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python){target="_blank"}
  - [GitLens](https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens){target="_blank"}
  - [Docker](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker){target="_blank"}

## Troubleshooting

### Common Issues

1. SSH Connection Failed:
   ```bash
   # Check if you can ping the machine
   ping ************

   # Verify your SSH key is correct
   ssh -v USERNAME@************
   ```

!!! note
    Replace `USERNAME` with your actual username.

2. GitHub Authentication:
   ```bash
   # Test GitHub SSH connection
   ssh -T **************
   ```

3. Poetry Issues:
   ```bash
   # Verify Poetry installation
   poetry --version

   # If Poetry not found
   export PATH="/home/<USER>/.local/bin:$PATH"
   ```

## Next Steps

1. Continue to [Prerequisites](prerequisites.md)
2. Set up your development environment following the
   [Installation Guide](installation.md)
