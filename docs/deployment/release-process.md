# Release Process

This document outlines the standard release process for all components of our system: Rehoboam (backend API), Holodeck (frontend), and <PERSON>oi<PERSON>-<PERSON><PERSON><PERSON> (analytics).

## Release Schedule

We follow a regular release schedule to ensure consistent and predictable deployments:

1. **Weekly Test Releases**: Every week, we push changes from the `develop` branch to the `test` branch for testing.
2. **Bi-weekly Production Releases**: Every two weeks, we push validated changes from the `test` branch to the `production` branch.

## Branch Strategy

We maintain three primary branches across all repositories:

- **`develop`**: Active development branch where all feature branches are merged
- **`test`**: Testing environment branch that receives weekly releases from `develop`
- **`production`**: Production environment branch that receives bi-weekly releases from `test`

## Release Process Overview

### 1. Weekly Test Release

Every Monday, we release from `develop` to `test` to begin the testing cycle.

### 2. Bi-weekly Production Release

Every other Monday, we release from `test` to `production` after completing a full testing cycle.

## Detailed Release Steps

### Preparing for a Release

   1. **Code Freeze**:
      - Announce code freeze for the target branch 24 hours before the planned release
      - Only critical bug fixes should be merged during this period

   2. **Pre-release Checks**:
      - Ensure all CI/CD pipelines are passing
      - Verify that all required features for the release are merged
      - Check that documentation is up-to-date

### Releasing to Test Environment

#### Step 1: Create a Release Branch

```bash
# Ensure you're on the develop branch with the latest changes
git checkout develop
git pull origin develop

# Create a release branch
git checkout -b release/test-YYYY-MM-DD
```

#### Step 2: Create a Pull Request to Test

1. Push the release branch to GitHub:
   ```bash
   git push origin release/test-YYYY-MM-DD
   ```

2. Go to the GitHub repository and create a new pull request:
   - Base branch: `test`
   - Compare branch: `release/test-YYYY-MM-DD`
   - Title: `Release to Test: YYYY-MM-DD`
   - Description: Include a summary of the changes and any notable features or fixes

3. Request reviews from the appropriate team members

#### Step 3: Review and Merge

1. Address any feedback from reviewers
2. Once approved, merge the pull request using the "Squash and merge" option
3. Delete the release branch after merging

#### Step 4: Verify Deployment

1. Monitor the CI/CD pipeline to ensure successful deployment to the test environment
2. Perform smoke tests to verify basic functionality
3. Notify the QA team that the test environment has been updated

### Releasing to Production Environment

#### Step 1: Create a Release Branch from Test

```bash
# Ensure you're on the test branch with the latest changes
git checkout test
git pull origin test

# Create a production release branch
git checkout -b release/prod-YYYY-MM-DD
```

#### Step 2: Create a Pull Request to Production

1. Push the release branch to GitHub:
   ```bash
   git push origin release/prod-YYYY-MM-DD
   ```

2. Go to the GitHub repository and create a new pull request:
   - Base branch: `production`
   - Compare branch: `release/prod-YYYY-MM-DD`
   - Title: `Release to Production: YYYY-MM-DD`
   - Description: Include a comprehensive changelog of all features, improvements, and bug fixes

3. Request reviews from the appropriate team members and project stakeholders

#### Step 3: Review and Merge

1. Address any feedback from reviewers
2. Once approved, merge the pull request using the "Squash and merge" option
3. Delete the release branch after merging

#### Step 4: Create a GitHub Release

1. Go to the "Releases" section in the GitHub repository
2. Click "Draft a new release"
3. Tag version: `v1.x.y` (following semantic versioning)
4. Target: `production` branch
5. Release title: `Release v1.x.y`
6. Description: Include the full changelog from the pull request
7. Click "Publish release"

#### Step 5: Verify Production Deployment

1. Monitor the CI/CD pipeline to ensure successful deployment to production
2. Perform smoke tests to verify basic functionality
3. Monitor application metrics and error rates for any issues

## Repository-Specific Release Instructions

### Rehoboam (Backend API)

#### Additional Pre-release Checks

1. Verify all API tests are passing
2. Check database migration scripts
3. Ensure API documentation is up-to-date

#### Post-deployment Steps

1. Verify API health endpoints
2. Run integration tests against the deployed API
3. Check logs for any unexpected errors

### Holodeck (Frontend)

#### Additional Pre-release Checks

1. Verify all UI tests are passing
2. Ensure compatibility with the latest API version
3. Check for any UI/UX issues

#### Post-deployment Steps

1. Verify the application loads correctly in all supported browsers
2. Test critical user flows
3. Check for any console errors

### Voight-Kampff (Analytics)

#### Additional Pre-release Checks

1. Verify all analytics models are functioning correctly
2. Ensure data processing pipelines are working
3. Check for any performance issues

#### Post-deployment Steps

1. Verify data processing jobs are running
2. Check sample reports for accuracy
3. Monitor resource usage

## Rollback Procedure

If issues are detected after deployment, follow these steps to roll back:

   1. **Identify the Issue**:
      - Determine the severity and impact
      - Decide if a rollback is necessary

   2. **Create a Rollback Pull Request**:
      - Create a new branch from the previous stable release
      - Create a pull request to the affected environment branch
      - Mark as "URGENT: Rollback"

   3. **Expedite Review and Merge**:
      - Request immediate reviews
      - Merge as soon as approved

   4. **Verify Rollback**:
      - Confirm the rollback has resolved the issue
      - Notify all stakeholders

   5. **Post-mortem**:
      - Schedule a post-mortem meeting
      - Document the issue, impact, and resolution
      - Create tickets to address the root cause

## Communication

### Pre-release Communication

1. Announce the upcoming release schedule to the team
2. Provide a list of features and improvements

### During Release

1. Notify the team when the release process begins
3. Announce the completion of the release to the team

### Post-release Communication

1. Write a release summary and share it with the team
2. Highlight new features and improvements
