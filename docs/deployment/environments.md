# Environments

Our system exists in three environments. Each environment contains an instance of each of our services. All images are stored in the shared Artifact Registry.

## Deployment Process

### Platform:
[Google Cloud Build](https://console.cloud.google.com/cloud-build/builds?referrer=search&authuser=4&project=subconsciousai-shared)

### Project Group:
subconsciousai

### Dev Deployment:
**Automatic**  
Any changes to the `dev` branch are automatically deployed in GCP. This can be monitored further in Cloud Build.

### Production Deployment:
- From the GitHub repository page, select **"Releases"** on the right-hand side.
- Draft a new release and ensure you're targeting the correct branch (typically `dev`).
- Monitor the release on Cloud Build.  
  **Typical build time:** 5 minutes.

---

## Environments Overview

### 1. subconsciousai-dev
- **Aggregate Logs:** [View Logs](https://cloudlogging.app.goo.gl/nFJ11jjAQ4RMeJMZ7)
- **Holodeck:** [App (Dev)](https://app.dev.subconscious.ai/)  
  Deployed automatically through a Cloud Build Trigger on merge to the `main` branch.
- **Rehoboam:** [API Docs (Dev)](https://api.dev.subconscious.ai/docs)  
  Deployed automatically through a Cloud Build Trigger on merge to the `develop` branch.
- **Voight-Kampff:** [Analytics (Dev)](https://analytics.dev.subconscious.ai/)  
  Deployed automatically through a Cloud Build Trigger on merge to the `dev` branch.

---

### 2. subconsciousai-test
- **Aggregate Logs:** [View Logs](https://cloudlogging.app.goo.gl/AP4qp8Ar6k7QD2LRA)
- **Holodeck:** [App (Test)](https://app.test.subconscious.ai/)  
  Deployed automatically through a Cloud Build Trigger on merge to the `main` branch.
- **Rehoboam:** [API Docs (Test)](https://api.test.subconscious.ai/docs)  
  Deployed automatically through a Cloud Build Trigger on merge to the `develop` branch.
- **Voight-Kampff:** [Analytics (Test)](https://analytics.test.subconscious.ai/)  
  Deployed automatically through a Cloud Build Trigger on merge to the `dev` branch.

---

### 3. subconsciousai-prod
- **Aggregate Logs:** [View Logs](https://cloudlogging.app.goo.gl/rGcrKkrvNAUZkhDY8)
- **Holodeck:** [App (Prod)](https://app.subconscious.ai/)  
  Deployed through a Cloud Build Trigger when a release is made with tag `v.*`.
- **Rehoboam:** [API Docs (Prod)](https://api.subconscious.ai/docs)  
  Deployed through a Cloud Build Trigger when a release is made with tag `v.*`.
- **Voight-Kampff:** [Analytics (Prod)](https://analytics.subconscious.ai/)  
  Deployed through a Cloud Build Trigger when a release is made with tag `v.*`.
