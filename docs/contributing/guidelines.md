We use [black](https://github.com/psf/black){target="_blank"} and [isort](https://pycqa.github.io/isort/){target="_blank"} to ensure quality formatting for our code before merging new submissions. 
You can ensure your new PR is meeting code standards with the following commands:

## Black
Go to the root directory (backend) and enter into the terminal:
```zsh
black .
```


## isort
Go to the root directory (backend) and enter into the terminal:
```zsh 
isort .
```
