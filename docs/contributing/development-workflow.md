# Development Workflow

This guide outlines the recommended workflow for contributing to the Subconscious.ai backend API.

## Getting Started

Before you begin development, make sure you have:

1. Set up your local environment following the [installation guide](../getting-started/installation.md)
2. Familiarized yourself with the [project architecture](../architecture/overview.md)
3. Joined the [Subconscious AI Discord](https://discord.gg/3bgj4ZhABz) for support

## Development Process

### 1. Issue Tracking

All development work should be tied to an issue in [our issue tracker](https://github.com/orgs/Subconscious-ai/projects/6):

1. Check existing issues to see if your task is already tracked
2. If not, ask your manager to create a new issue describing the feature, bug, or improvement
3. Request to assign the issue to you

### 2. Branching Strategy

We follow a feature branch workflow:

```bash
# Ensure your develop branch is up to date
git checkout develop
git pull origin develop

# Create a new feature branch
git checkout -b feature/your-feature-name
# For bug fixes
git checkout -b fix/bug-description
# For documentation
git checkout -b docs/description
```

### 3. Development

When working on your feature:

1. Make small, focused commits with clear messages
2. Follow the [coding guidelines](guidelines.md)
3. Write tests for new functionality
4. Update documentation as needed

#### Code Style

We use automated tools to maintain code quality:

- **Python**: Black and isort for formatting

Run these tools before committing:

```bash
# For Python code
black .
isort .
```

### 4. Testing

All code should be tested before submission:

```bash
# Run Python tests
pytest
```

### 5. Pull Requests

When your feature is ready:

1. Push your branch to the remote repository
   ```bash
   git push origin feature/your-feature-name
   ```

2. Create a pull request (PR) with the following information:
   - Clear title describing the change
   - Reference to the issue it addresses (e.g., "Fixes #123")
   - Brief description of the changes
   - Any notes on testing or deployment considerations

3. Request a review from appropriate team members

### 6. Code Review

During the code review process:

1. Address all reviewer comments
2. Make requested changes in new commits
3. Push updates to the same branch
4. Respond to comments to indicate when they've been addressed

### 7. Merging

1. Ensure CI checks pass
2. Merge your PR into the `develop` branch
3. Delete the feature branch after successful merge


## Best Practices

- Keep PRs focused and reasonably sized
- Write clear commit messages explaining the "why" not just the "what"
- Document new features or API changes
- Update tests when modifying existing functionality
- Regularly sync your branch with `develop` to avoid merge conflicts
