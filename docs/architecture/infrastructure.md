# Infrastructure

This document outlines the infrastructure architecture for the Subconscious.ai.

## Overview

Below is a high-level architecture diagram of the infrastructure:

```mermaid
graph TD
    Client[Client Applications] --> API[API Service]
    API --> Cache[Redis Cache]
    API --> DB[PostgreSQL Database]
    API --> Queue[Task Queue]
    Queue --> Workers[Worker Nodes]
    Workers --> LLM[LLMs]
    Workers --> Analytics[Analytics Engine]
    
    subgraph Data Layer
        DB
    end
    
    subgraph Compute Layer
        API
        Workers
        Analytics
    end
    
    subgraph External Services
        LLM
    end
```

## Core Components

### Compute Resources

   1. **API Service**
      - FastAPI application serving the REST API

   2. **Worker Nodes**
      - Process asynchronous tasks from the queue
      - Handle experiment execution

   3. **Analytics Engine**
      - R-based statistical analysis
      - Experiment result processing
      - Data visualization generation

### Data Storage

   1. **PostgreSQL Database**
      - Primary relational database
      - Stores populations data fro Ipums

   2. **Redis Cache**
      - Task queue for asynchronous processing


### External Services

   1. **LLM Service**
      - LLMs from OpenAI, Anthropic, VertexAI are supported.

   2. **Monitoring**
      - Application monitoring with Sentry


## Deployment Architecture

Backend API is Google Cloud Platform (GCP) Virtual Machine (VM) using linux system services.

## Environment Configuration

### Development

- **Purpose**: Feature development and testing

### Staging

- **Purpose**: Pre-production validation and integration testing

### Production

- **Purpose**: Live system serving end users


