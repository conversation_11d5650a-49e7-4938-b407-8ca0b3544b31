# Authentication & Authorization

## Overview

The Rehoboam backend uses Auth0 for authentication and authorization. The system implements a secure token-based authentication flow with role-based access control to ensure that only authorized users can access specific resources and operations.

## Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Auth0
    participant Backend
    participant APIs
    
    User->>Frontend: Click Sign In
    Frontend->>Auth0: Redirect to Auth0
    Auth0-->>User: Present login interface
    User->>Auth0: Enter credentials
    Auth0-->>Frontend: Return Access Token & ID Token
    Frontend->>Backend: API Request + Access Token
    Backend->>Auth0: Validate Token
    Auth0-->>Backend: Token Valid + User Claims & Roles
    Backend->>APIs: Authorized Request
    APIs-->>User: Response
```

## Implementation Details

### Auth0 Configuration

Our apps uses Auth<PERSON> as the identity provider with the following configuration:

1. **Application Type**: Single Page Application (SPA) for the frontend, Machine To Machine (M2M) for Rehoboam - API access to Auth0.
2. **Connections**: Username/password database and social logins (Google)
3. **Actions**: Custom rules for role assignment and token enrichment

### Token Types

The system uses two types of tokens:

1. **ID Token**: Contains user identity information (profile data)
2. **Access Token**: Used to access protected resources (API endpoints)


### Role-Based Access Control (RBAC)

Rehoboam implements RBAC to control access to different parts of the system:

1. **Customer**: Access to all endpoints
2. **Employee**: Access to all endpoints
3. **Free User**: Access to all endpoints but limited to running 2 experiments.


To get access tokens, see the [Get Access Token](../how-tos/get-access-token.md) guide.