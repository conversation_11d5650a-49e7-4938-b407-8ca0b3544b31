# Architecture Overview

## High-Level Architecture

```mermaid
graph TD
    Client[Client Applications] --> API[FastAPI API]
    API <--> Auth[Auth0 Authentication]
    API --> Core[Controller]

    Core --> LLM[LLM Prompt Executor]
    Core --> Analytics[R Analytics]
    Core <--> WANDB[Weights & Biases]

    API <--> Queue[Celery Task Queue]
    Queue <--> Worker[Worker Nodes]
    Worker --> Core

    LLM --> OpenAI[OpenAI API]
    LLM --> Anthropic[Anthropic API]
    LLM --> Perplexity[Perplexity API]

    Queue <--> Redis[Redis]

    subgraph External Services
        OpenAI
        Anthropic
        Perplexity
        WANDB
    end
```

## Project Structure

The Rehoboam project is organized into several key components:

### Backend (FastAPI)

The backend is built with FastAPI and follows a modular structure:

```
app/
├── api/                   # API Layer
│   ├── router.py          # Main API Router
│   ├── v1/                # API version 1
│   │   ├── endpoints/     # API endpoints (experiments, human_baselines, etc.)
│   │   ├── helpers/       # Helper functions for API endpoints
│   │   ├── schemas/       # Request/response schemas
│   │   └── router.py      # V1 router configuration
│   └── v2/                # API version 2
├── auth/                  # Authentication components
├── core/                  # Core Business Logic
│   ├── controller.py      # Main experiment controller
│   ├── experiment_config.py # Experiment configuration
│   ├── global_config.py   # Global application settings
│   ├── db/                # Database models
│   └── utils/             # Utility functions
├── llm_prompt/            # LLM Integration
│   ├── prompt_executor.py # Handles LLM API interactions
│   ├── prompt_builder.py  # Constructs prompts from templates
│   ├── prompt_templates.py # Template definitions
│   └── llm_settings.py    # LLM configuration settings
└── main.py                # Application entry point
```

### Infrastructure Components

The application is deployed using Docker containers with the following services:

1. **App**: FastAPI application serving the REST API
2. **Worker**: Celery worker processing asynchronous tasks
3. **Redis**: Used as a message broker for Celery
4. **Dashboard**: Flower dashboard for monitoring Celery tasks


!!! Note
    We're currently not using docker!

### Key Technologies

Rehoboam leverages several key technologies:

- **FastAPI**: Web framework for building the API
- **Celery**: Distributed task queue for asynchronous processing
- **Redis**: In-memory data store for message brokering
- **Auth0**: Authentication and authorization
- **LangChain**: Framework for LLM application development
- **R**: Statistical analysis and modeling
- **Weights & Biases**: Experiment tracking and visualization
- **Docker**: Containerization for deployment

## Data Flow

The system follows a structured flow for experiment execution:

```mermaid
sequenceDiagram
    participant Client
    participant API as FastAPI API
    participant Queue as Celery Queue
    participant Worker as Worker Node
    participant Core as Core Controller
    participant LLM as LLM Service
    participant Analytics as R Analytics
    participant WANDB as Weights & Biases

    Client->>API: Submit Experiment Request
    API->>Queue: Queue Experiment Task
    Queue->>Worker: Process Task
    Worker->>Core: Execute Experiment

    Core->>LLM: Generate Synthetic Population
    LLM-->>Core: Return Population Data

    Core->>LLM: Execute Survey Tasks
    LLM-->>Core: Return Survey Responses

    Core->>Analytics: Process Results
    Analytics-->>Core: Return Statistical Analysis

    Core->>WANDB: Store Results & Visualizations

    Client->>API: Request Results
    API->>WANDB: Fetch Results
    WANDB-->>API: Return Results Data
    API->>Client: Return Experiment Results
```

## Security Model

The system implements several security measures:

- **Authentication**: Auth0-based authentication for API access
- **Authorization**: Role-based access control for different operations
- **Environment Isolation**: Separate environments for development, staging, and production
