# Experiment Flow

## Overview

The Rehoboam experiment system implements a discrete choice survey workflow using synthetic populations and LLM-based responses. This document outlines the architecture and flow of experiments through the system based on the actual implementation.

```mermaid
graph TD
    Client[Client Application] --> API[FastAPI API Layer]
    API --> Controller[Controller]
    Controller --> Config[Experiment Config]
    Controller --> Queue[Celery Task Queue]
    Queue --> Worker[Worker Node]
    Worker --> Core[Core Engine]
    Core --> LLM[LLM Service]
    Core --> Analytics[R Analytics]
    Core --> WANDB[Weights & Biases]
    
    subgraph Execution
        Core
        LLM
        Analytics
    end
```

## Experiment Lifecycle

### 1. Experiment Initialization

```mermaid
graph LR
    API[API Request] --> Validation[Request Validation]
    Validation --> Config[Experiment Config]
    Config --> Queue[Celery Queue]
    
    subgraph Experiment Parameters
        Random[random_seed]
        Model[model_name/model_type]
        Size[experiment_size]
        Attributes[number_of_attributes]
        Levels[number_of_levels]
        Personas[personas]
        DV[respondent_dependent_variable]
        Where[where_preamble]
        When[when_preamble]
    end
```

The experiment begins when a user submits a request through the API. Based on the actual codebase, the key parameters include:

- **random_seed**: Controls randomization for reproducibility
- **model_name/model_type**: LLM model to use (e.g., GPT-4, Claude)
- **experiment_size**: Controls the number of respondents
- **number_of_attributes**: Number of product/service attributes to test
- **number_of_levels**: Number of variations for each attribute
- **personas**: Synthetic population characteristics
- **respondent_dependent_variable**: The question posed to survey participants
- **where_preamble**: Geographic context (e.g., "United States")
- **when_preamble**: Temporal context (e.g., "2023")

### 2. Execution Pipeline

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Controller
    participant Queue
    participant Worker
    participant Core
    participant Personas
    participant LLM
    participant Analytics
    participant WANDB
    
    Client->>API: POST /api/v1/experiments
    API->>Controller: Process Request
    Controller->>Queue: Queue Experiment Task
    Queue->>Worker: Process Task
    Worker->>Core: Execute Experiment
    
    Core->>Personas: get_personas()
    alt Pre-defined personas
        Personas->>Core: Use provided personas
    else Generate personas
        Personas->>LLM: Generate synthetic population
        LLM-->>Personas: Return population data
        Personas->>Core: Return population
    end
    
    Core->>Core: run_survey()
    loop For each persona and task
        Core->>LLM: Execute survey task
        LLM-->>Core: Return response
    end
    
    Core->>Analytics: run_analytics()
    Analytics->>Core: Return statistical results
    
    Core->>WANDB: push_survey_details_to_storage_objects()
    WANDB-->>Core: Confirm storage
    
    Core->>Worker: Complete task
    Worker->>Queue: Update task status
    
    Client->>API: GET /api/v1/experiments/{id}
    API->>WANDB: Fetch results
    WANDB-->>API: Return results
    API->>Client: Return experiment results
```

## Key Components

### 1. Controller (`app/core/controller.py`)

The controller is the central orchestrator for experiment execution:

```python
def execute(expr_conf: ExperimentConfig):
    """
    Main execution function for experiments
    """
    # Get personas
    get_personas(expr_conf)
    
    # Run survey
    run_survey(expr_conf)
    
    # Run analytics
    run_analytics(expr_conf)
    
    # Store results
    push_survey_details_to_storage_objects(expr_conf)
```

Key functions in the controller include:
- **get_personas()**: Retrieves or generates synthetic population
- **run_survey()**: Executes the discrete choice survey
- **run_analytics()**: Processes survey results
- **push_survey_details_to_storage_objects()**: Stores results in WANDB

### 2. Experiment Configuration (`app/core/experiment_config.py`)

The `ExperimentConfig` class manages all experiment parameters and state:

```python
class ExperimentConfig:
    # Experiment parameters
    random_seed: int
    model_name: str
    model_type: ModelType
    experiment_size: int
    number_of_attributes: int
    number_of_levels: int
    respondent_dependent_variable: str
    where_preamble: str
    when_preamble: str
    
    # Runtime state
    survey_results: pd.DataFrame
    design_matrix: pd.DataFrame
    mappings: dict
    wandb_run: WandbAPI
    
    # Methods for experiment execution
    def get_settings_dict(self) -> dict:
        # Returns experiment settings as a dictionary
        
    def update_settings(self, **kwargs):
        # Updates experiment settings
```

### 3. LLM Integration (`app/llm_prompt/`)

The LLM integration is handled through several components:

```mermaid
graph TD
    Executor[prompt_executor.py] --> Builder[prompt_builder.py]
    Builder --> Templates[prompt_templates.py]
    Executor --> Settings[llm_settings.py]
    Executor --> LLMs[LLM APIs]
    
    subgraph LLM Integration
        Executor
        Builder
        Templates
        Settings
    end
```

#### Prompt Executor (`app/llm_prompt/prompt_executor.py`)

The `LCELPromptExecutor` class handles interactions with language models:

```python
class LCELPromptExecutor(BaseModel):
    llm_model: LLMModel = LLMModel.GPT4
    _model_settings: dict = None
    
    def execute(self, prompt, args, output_object, description):
        # Execute a single prompt
        
    def batch_execute(self, prompts, args, output_object, description):
        # Execute multiple prompts in batch
```

The executor supports multiple LLM providers:
- OpenAI (GPT-3.5, GPT-4)
- Anthropic (Claude)
- Perplexity
- Google (Gemini, PaLM)
- Azure OpenAI

### 4. Population Management (`app/personas/`)

The population management module handles synthetic population generation:

```mermaid
graph TD
    Input[Population Parameters] --> Generator[Population Generator]
    Generator --> Traits[Trait Assignment]
    Traits --> Storage[Population Storage]
    
    subgraph Population Generation
        Generator
        Traits
    end
```

Population generation involves:
1. **Trait Definition**: Defining demographic and psychographic traits
2. **Trait Assignment**: Assigning traits to synthetic individuals
3. **Population Storage**: Persisting population data for experiment use

### 5. Analytics Processing (`app/modelling/`)

The analytics engine processes survey results using R for statistical analysis:

```mermaid
graph TD
    Results[Survey Results] --> Cleaning[Data Cleaning]
    Cleaning --> CLM[CLM Model]
    Cleaning --> OLS[OLS Model]
    CLM --> Visualization[Visualization]
    OLS --> Visualization
    
    subgraph R Analytics
        Cleaning
        CLM
        OLS
        Visualization
    end
```

Analytics processing includes:
1. **Data Cleaning**: Filtering and preparing raw responses
2. **Statistical Analysis**: Applying conjoint analysis techniques
3. **Model Generation**: Creating utility models and preference simulations
4. **Visualization**: Generating charts and dashboards

### 6. Result Storage (Weights & Biases)

All experiment data is stored in Weights & Biases (WANDB):

```mermaid
graph TD
    Config[Experiment Config] --> WANDB[WANDB Storage]
    Population[Population Data] --> WANDB
    Tasks[Task Definitions] --> WANDB
    Responses[LLM Responses] --> WANDB
    Analytics[Analytics Results] --> WANDB
    Visualizations[Visualizations] --> WANDB
```

## Human Baseline Experiments

Human baseline experiments serve as a validation mechanism:

```mermaid
graph TD
    HB[Human Baseline Data] --> Trans[Data Transcription]
    Trans --> Rep[Experiment Replication]
    Rep --> Compare[Result Comparison]
    Compare --> Measure[Accuracy Measurement]
```

The process involves:
1. **Data Transcription**: Converting human study data to system format
2. **Experiment Replication**: Running equivalent LLM-based experiments
3. **Result Comparison**: Comparing human and LLM responses
4. **Accuracy Measurement**: Quantifying system accuracy against human baselines

## Parallel Processing

Experiments leverage Celery for parallel processing:

```mermaid
graph TD
    Queue[Celery Queue] --> Workers[Worker Pool]
    Workers --> Tasks[Task Execution]
    Tasks --> Batching[Request Batching]
    Tasks --> Threading[Parallel Threading]
    
    subgraph Parallel Processing
        Workers
        Tasks
        Batching
        Threading
    end
```

Parallel processing includes:
1. **Celery Queue**: Redis-based queue for asynchronous processing
2. **Worker Pool**: Multiple worker processes handling tasks
3. **Request Batching**: Combining multiple prompts in single API calls
4. **Parallel Threading**: Concurrent execution of independent tasks

## Error Handling

The system implements robust error handling:

```mermaid
graph TD
    Error[Error Detection] --> Retry[Backoff Retry Logic]
    Retry --> Fallback[Fallback Mechanisms]
    Fallback --> Logging[Error Logging]
    Logging --> Sentry[Sentry Error Tracking]
```

Error handling includes:
1. **Backoff Retry Logic**: Automatic retries with exponential backoff
2. **Fallback Mechanisms**: Alternative processing paths
3. **Error Logging**: Detailed error tracking
4. **Sentry Integration**: Error monitoring and alerting

## Example Experiment Flow

Here's a complete example of an experiment flow based on the actual implementation:

1. **User submits experiment request**:
   ```json
   {
     "random_seed": 42,
     "model_name": "gpt-4",
     "experiment_size": 20,
     "number_of_attributes": 4,
     "number_of_levels": 3,
     "respondent_dependent_variable": "Which car would you prefer to purchase?",
     "where_preamble": "United States",
     "when_preamble": "2023"
   }
   ```

2. **System creates ExperimentConfig**:
   ```python
   expr_conf = ExperimentConfig(
       random_seed=42,
       model_name="gpt-4",
       experiment_size=20,
       number_of_attributes=4,
       number_of_levels=3,
       respondent_dependent_variable="Which car would you prefer to purchase?",
       where_preamble="United States",
       when_preamble="2023"
   )
   ```

3. **System generates attributes and levels**:
   ```
   Attribute: Price
   Levels: $20,000, $30,000, $40,000
   
   Attribute: Fuel Type
   Levels: Gasoline, Hybrid, Electric
   
   Attribute: Size
   Levels: Compact, Mid-size, SUV
   
   Attribute: Brand
   Levels: Toyota, Ford, Tesla
   ```

4. **System creates synthetic population**:
   ```
   Persona 1: 35-year-old male, software engineer, urban resident, high income
   Persona 2: 42-year-old female, teacher, suburban resident, middle income
   ...
   ```

5. **System generates choice tasks**:
   ```
   Task 1:
   Option A: $30,000 Hybrid Mid-size Toyota
   Option B: $40,000 Electric Compact Tesla
   
   Task 2:
   Option A: $20,000 Gasoline Compact Ford
   Option B: $30,000 Hybrid SUV Toyota
   ...
   ```

6. **LLM executes survey with personas**:
   ```
   Prompt:
   Imagine you are a human with the following characteristics:
   I am a 35-year-old male software engineer living in an urban area with high income.
   
   You are participating in a survey about cars.
   Which car would you prefer to purchase?
   
   Option A: $30,000 Hybrid Mid-size Toyota
   Option B: $40,000 Electric Compact Tesla
   
   Response:
   Option B
   ```

7. **System analyzes results**:
   - Attribute importance: Price (40%), Fuel Type (25%), Size (20%), Brand (15%)
   - Level utilities: Electric (+0.8), SUV (+0.5), Tesla (+0.3), etc.

8. **Results stored in WANDB**:
   - Experiment configuration
   - Population data
   - Survey responses
   - Statistical models
   - Visualizations 