from openai import RateLimitError


class CountedRateLimitError(RateLimitError):
    counter = 0

    def __init__(self, message=None):
        CountedRateLimitError.counter += 1
        super().__init__(message)

    @classmethod
    def reset_counter(cls):
        cls.counter = 0


class BackoffException(Exception):
    counter = 0

    def __init__(self, message=""):
        BackoffException.counter += 1
        super().__init__(f"Backoff Exception: Query was throttled. {message}")

    @classmethod
    def reset_counter(cls):
        cls.counter = 0


class RateLimitException(Exception):
    counter = 0

    def __init__(self, message=""):
        RateLimitException.counter += 1
        super().__init__(f"Rate Limit Exception: Rate Limit Exceeded. {message}")

    @classmethod
    def reset_counter(cls):
        cls.counter = 0


class ParseException(Exception):
    counter = 0

    def __init__(self, message=""):
        ParseException.counter += 1
        super().__init__(f"Parse Exception: Response Parsing Failed. {message}")

    @classmethod
    def reset_counter(cls):
        cls.counter = 0
