import json
import os
import time
from typing import Any, Dict, List, Optional, Tuple, Union

import backoff
from langsmith import Client
from langsmith.utils import LangSmithError

from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)


class LangSmithTraceManager:
    """
    A class to manage LangSmith traces, including extracting run data and sharing runs.
    """

    def __init__(self):
        """
        Initialize the LangSmithTraceManager.
        """
        self.client = Client(api_key=os.getenv("LANGCHAIN_API_KEY"))

    def get_runs(self, project_name: str, **kwargs) -> List[Any]:

        try:
            projects = self.client.list_projects()
            project_exists = any(p.name == project_name for p in projects)

            if not project_exists:
                try:
                    self.client.create_project(project_name)
                except Exception as e:
                    if "already exists" not in str(e):
                        raise

            runs = list(self.client.list_runs(project_name=project_name, **kwargs))
            return runs
        except Exception as e:
            raise

    def calculate_total_usage(self) -> Dict[str, Union[int, float]]:
        """
        Calculate the total usage across all runs.

        Returns:
            Dict[str, float]: A dictionary containing the total usage statistics.
        """
        total_token_count = {
            "token_count": 0,
            "cost_amount": 0.0,
        }

        return total_token_count

    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        giveup=lambda e: "not found" not in str(e).lower(),
    )
    def extract_run_data(
        self, project_name: str, **kwargs
    ) -> Tuple[List[Dict[str, Any]], Dict[str, float]]:
        """
        Extract data from runs in a LangSmith project.

        Args:
            project_name (str): The name of the project to extract data from.
            **kwargs: Additional arguments to filter the runs (see get_runs method for details).

        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, float]]: A tuple containing a list of dictionaries with extracted run data and a dictionary with total token count.
        """
        try:
            runs = self.get_runs(project_name, **kwargs)
        except Exception as e:
            runs = {}
        total_token_count = self.calculate_total_usage()

        if runs:
            for run in runs:
                try:
                    self._update_total_token_count(total_token_count, run)
                except Exception as e:
                    logger.error(
                        f"Error processing run {run.id}: {str(e)} for total token and"
                        " cost extraction",
                        exc_info=True,
                    )

        logger.info(f"Total token count: {total_token_count}")
        return total_token_count

    def _update_total_token_count(self, total_token_count: Dict[str, float], run: Any):
        """Update the total token count and log any zero values."""
        token_cost_data = {
            "token_count": int(run.token_count if run.token_count else 0),
            "cost_amount": float(run.cost_amount if run.cost_amount else 0),
        }

        if token_cost_data["token_count"] == 0 and token_cost_data["cost_amount"] == 0:
            logger.warning(
                f"{run.run_type.upper()} Run {run.id} has zero values for both"
                " token_count and cost_amount"
            )
        elif token_cost_data["token_count"] == 0:
            logger.warning(
                f"{run.run_type.upper()} Run {run.id} has zero token_count but"
                f" non-zero cost_amount: {token_cost_data['cost_amount']}"
            )
        elif token_cost_data["cost_amount"] == 0:
            logger.warning(
                f"{run.run_type.upper()} Run {run.id} has zero cost_amount but non-zero"
                f" token_count: {token_cost_data['token_count']}"
            )

        for key in total_token_count:
            total_token_count[key] += float(token_cost_data.get(key, 0.0))

    def save_to_json(self, data: List[Dict[str, Any]], filename: str):
        """
        Save data to a JSON file.

        Args:
            data (List[Dict[str, Any]]): The data to save.
            filename (str): The name of the file to save the data to.
        """
        try:
            with open(filename, "w") as f:
                json.dump(data, f, indent=2)
            logger.info(f"Data saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving data to {filename}: {str(e)}", exc_info=True)
            raise

    def share_run_with_retry(
        self, run_id: str, max_retries: int = 3, delay: int = 5
    ) -> Optional[str]:
        """
        Share a run with retry mechanism.

        Args:
            run_id (str): The ID of the run to share.
            max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.
            delay (int, optional): Delay between retry attempts in seconds. Defaults to 5.

        Returns:
            Optional[str]: The shareable URL if successful, None otherwise.
        """
        for attempt in range(max_retries):
            try:
                try:
                    self.client.unshare_run(run_id)
                    # print(f"Unshared run: {run_id}")
                except LangSmithError as e:
                    if "Run is not shared" in str(e):
                        logger.debug(f"Run {run_id} was not previously shared.")
                    else:
                        logger.warning(f"Error unsharing run {run_id}: {e}")

                shared_url = self.client.share_run(run_id)
                return shared_url
            except LangSmithError as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"Error sharing run {run_id} (attempt {attempt + 1}): {e}"
                    )
                    logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                else:
                    logger.error(
                        f"Failed to share run {run_id} after"
                        f" {max_retries} attempts: {e}",
                        exc_info=True,
                    )
                    return None

    def share_all_runs(self, project_name: str, **kwargs) -> Dict[str, str]:
        """
        Share all runs in a project and return shareable links.

        Args:
            project_name (str): The name of the project to share runs from.
            **kwargs: Additional arguments to filter the runs (see get_runs method for details).

        Returns:
            Dict[str, str]: A dictionary mapping run IDs to their shareable links.
        """
        try:
            runs = self.get_runs(project_name, **kwargs)
        except Exception as e:
            runs = {}
        shared_links = {}
        for idx, run in enumerate(runs):
            shared_url = self.share_run_with_retry(run.id)
            if shared_url:
                shared_links[str(run.id)] = shared_url
            else:
                logger.warning(f"Failed to share run {run.id}")
        logger.info(f"Shared {len(shared_links)} runs from project '{project_name}'")
        return shared_links
