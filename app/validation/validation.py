import os
from pathlib import Path
from typing import Optional

import numpy as np
import pandas as pd
import wandb
from pydantic import BaseModel
from tqdm import tqdm

from app.core.global_config import settings


class ValidationMetrics(BaseModel):
    randomness_alpha: Optional[float] = 0.01
    llm_parse_error_threshold: Optional[float] = 0.1
    task_consistency_threshold: Optional[float] = 0.4
    task_failure_rate_threshold: Optional[float] = 0.6
    vif_factor_threshold: Optional[float] = 5
    residual_normality_alpha: Optional[float] = 0.001
    spearman_r_threshold: Optional[float] = 0.2
    spearman_r_alpha: Optional[float] = 0.05
    non_respondent_percent_threshold: Optional[float] = 0.6
    outlier_threshold: Optional[float] = 10

    def data_generation_metrics(
        self, llm_parse_error: float, task_consistency: float, task_failure_rate: float
    ):
        if (
            llm_parse_error < self.llm_parse_error_threshold
            and task_consistency > self.task_consistency_threshold
            and task_failure_rate < self.task_failure_rate_threshold
        ):
            return True
        return False

    def dependent_variable_metric(self, a_count: int, b_count: int, c_count: int):
        total_responses = a_count + b_count + c_count
        if (c_count / total_responses) > self.non_respondent_percent_threshold:
            return False
        return True

    def inference_metric(self, survey_data_path):
        survey_data = pd.read_csv(survey_data_path)
        survey_data = survey_data.drop(
            survey_data[survey_data["chosen_choice_letter"] == "C"].index
        )
        levels = len(survey_data.columns[7:].unique())
        if survey_data.shape[0] > (levels + 1):
            return True
        return False

    def persona_clustering_metric(self, survey_data_path):
        survey_data = pd.read_csv(survey_data_path)
        survey_data = survey_data.drop(
            survey_data[survey_data["chosen_choice_letter"] == "C"].index
        )
        levels = len(np.unique(survey_data.iloc[:, 6:]))
        personas = np.unique(survey_data["persona_id"])
        return (
            np.sum([
                survey_data[survey_data["persona_id"] == persona].shape[0]
                > (levels + 1)
                for persona in personas
            ])
            >= 5
        )

    def randomness_metrics(self, bartels_pvalue: float, wolfowitz_pvalue: float):
        if (
            bartels_pvalue > self.randomness_alpha
            or wolfowitz_pvalue > self.randomness_alpha
        ):
            return True
        return False

    def model_metrics(
        self, vif_factor: float, residual_normality_pvalue: float, outliers: float
    ):
        if (
            vif_factor > self.vif_factor_threshold
            or residual_normality_pvalue < self.residual_normality_alpha
            or outliers > self.outlier_threshold
        ):
            return False
        return True

    def human_baseline_metric(self, human_baseline_pvalue: float):
        return human_baseline_pvalue < self.spearman_r_alpha


class Validator(ValidationMetrics):
    def __init__(self, entity, project):
        super().__init__()
        self.entity = entity
        self.project = project
        self.root = f"{self.entity}/{self.project}"
        self.api = wandb.Api(timeout=None)

    def validate(self):
        database = self.fetch_database()

        for run_id in tqdm(database["wandb_id"].dropna(), desc="Flagging Runs"):
            run = database[database.wandb_id == run_id]

            if self.check_run(run_id):
                self.validate_internal_consistency(run, run_id)
                self.validate_randomness(run, run_id)
                self.validate_survey_data(run_id)

                if not self.validate_run(run_id):
                    self.update_tag("Success", run_id)

    def validation_summary(self, batch):
        database = self.fetch_database()
        runs = database["wandb_id"].dropna()
        wandb.init(
            entity="why-earth", project="validation", name=f"validation_batch_{batch}"
        )

        icc_failure_count = 0
        survey_data_failure_count = 0
        persona_data_failure_count = 0
        randomness_test_failure_count = 0
        success_count = 0

        for run_id in tqdm(runs, desc="Flagging Runs"):
            run = wandb.Api(timeout=None).run(f"why-earth/subconscious-ai/{run_id}")

            icc_failure_count += "Internal Consistency Failure" in run.tags
            survey_data_failure_count += (
                "Failure: Not Enough A and B Responses" in run.tags
            )
            persona_data_failure_count += "Persona Data Failure" in run.tags
            randomness_test_failure_count += "Randomness Test Failure" in run.tags
            success_count += "Success" in run.tags

        total_runs = runs.shape[0]

        p_icc_failure = icc_failure_count / total_runs
        p_survey_failure = survey_data_failure_count / total_runs
        p_persona_failure = persona_data_failure_count / total_runs
        p_randomness_failure = randomness_test_failure_count / total_runs
        p_success = success_count / total_runs

        wandb.log({
            "Percent Internal Consistency Failure": p_icc_failure,
            "Percent Survey Failure": p_survey_failure,
            "Percent Persona Failure": p_survey_failure,
            "Percent Randomness Failure": p_randomness_failure,
            "Percent Success": p_success,
        })

        wandb.run.summary.update({
            "Percent Internal Consistency Failure": p_icc_failure,
            "Percent Survey Failure": p_survey_failure,
            "Percent Persona Failure": p_persona_failure,
            "Percent Randomness Failure": p_randomness_failure,
            "Percent Success": p_success,
        })

        wandb.finish()

    def fetch_database(self):
        runs = self.api.runs(self.root)
        summary_list, config_list, name_list = list(), list(), list()

        for run in tqdm(runs, desc="Loading Database"):
            summary_list.append(run.summary._json_dict)
            config_list.append(
                {k: v for k, v in run.config.items() if not k.startswith("_")}
            )
            name_list.append(run.name)

        summary_df = pd.DataFrame.from_records(summary_list)
        config_df = pd.DataFrame.from_records(config_list)
        name_df = pd.DataFrame({"name": name_list})

        database = pd.concat([name_df, config_df, summary_df], axis=1)

        return database

    def check_run(self, run_id):
        run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
        run_tags = run.tags
        return "human_baseline" not in run_tags

    def validate_run(self, run_id):
        run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
        run_tags = run.tags
        failure_tags = (
            "Internal Consistency Failure",
            "Failure: Not Enough A and B Responses",
            "Persona Data Failure",
            "Randomness Test Failure",
        )
        return set(failure_tags) & set(run_tags)

    def update_tag(self, tag, run_id):
        run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
        run.tags.append(str(tag))
        run.update()

    def validate_internal_consistency(self, run, run_id):
        if not self.data_generation_metrics(
            run["Response Parse Error Rate"].values,
            run["Internally Consistent Metric"].values,
            run["Internal Task Failure Rate Metric"].values,
        ):
            self.update_tag(tag="Internal Consistency Failure", run_id=run_id)
            return False

    def validate_survey_data(self, run_id):
        try:
            run = wandb.init()
            survey_data = run.use_artifact(
                f"{self.entity}/{self.project}/experiment_survey_results_{run_id}:v0"
            )
            survey_data.download()
            artifact_dir = settings.VALIDATION_ARTIFACTS_DIRECTORY / Path(
                f"experiment_survey_results_{run_id}-v0"
            )
            for file in os.listdir(artifact_dir):
                if f"experiment_survey_results_{run_id}" not in file:
                    continue
                if ".csv" not in file:
                    survey_data_path = Path(f"{file}.csv")
                    os.rename(
                        artifact_dir / Path(file), artifact_dir / survey_data_path
                    )
                else:
                    survey_data_path = Path(file)
                survey_data_path = artifact_dir / survey_data_path

                if not self.inference_metric(survey_data_path):
                    self.update_tag(
                        tag="Failure: Not Enough A and B Responses", run_id=run_id
                    )

        except wandb.errors.CommError as e:
            print(f"Wandb artifact missing: {e}")
            self.update_tag(tag="Missing Data", run_id=run_id)

        except Exception as e:
            print(f"Unexpected error: {e}")

    def validate_randomness(self, run, run_id):
        if not self.randomness_metrics(
            run["Bartels Rank: Noise Metric"].values,
            run["Wald Wolfowitz: Noise Metric"].values,
        ):
            self.update_tag(tag="Randomness Test Failure", run_id=run_id)

    def validate_human_baseline(self, run, run_id):
        if not self.human_baseline_metric(run["Spearman P-Value"].values):
            self.update_tag(tag="Human Baseline Failure", run_id=run_id)
