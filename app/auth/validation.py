from typing import <PERSON><PERSON><PERSON><PERSON>

from fastapi import Depends
from starlette.requests import Request as StarletteRequest

from app.auth.constants import R<PERSON>ES_KEY
from app.auth.exceptions import (
    AccessDeniedException,
    BadCredentialsException,
    PermissionDeniedException,
    RequiresAuthenticationException,
)
from app.auth.jwt import JsonWebToken


class AuthorizationHeaderElements(NamedTuple):
    authorization_scheme: str
    bearer_token: str
    are_valid: bool


def get_authorization_header_elements(
    authorization_header: str,
) -> AuthorizationHeaderElements:
    try:
        authorization_scheme, bearer_token = authorization_header.split()
    except ValueError:
        raise BadCredentialsException
    else:
        valid = authorization_scheme.lower() == "bearer" and bool(bearer_token.strip())
        return AuthorizationHeaderElements(authorization_scheme, bearer_token, valid)


def get_bearer_token(request: StarletteRequest) -> str:
    authorization_header = request.headers.get("Authorization")
    if authorization_header:
        authorization_header_elements = get_authorization_header_elements(
            authorization_header
        )
        if authorization_header_elements.are_valid:
            return authorization_header_elements.bearer_token
        else:
            raise BadCredentialsException
    else:
        raise RequiresAuthenticationException


def validate_token(token: str = Depends(get_bearer_token)):
    return JsonWebToken(token).validate()


class PermissionsValidator:
    def __init__(self, required_permissions: list[str]):
        self.required_permissions = required_permissions

    def __call__(self, token: str = Depends(validate_token)):
        token_permissions = token.get("permissions")
        token_permissions_set = set(token_permissions)
        required_permissions_set = set(self.required_permissions)

        if not required_permissions_set.issubset(token_permissions_set):
            raise PermissionDeniedException


class RoleAuthorization:
    def __init__(self, required_roles=[]):
        self.required_roles = required_roles

    def __call__(self, token: dict = Depends(validate_token)):
        user_roles = token.get(ROLES_KEY, [])
        if any(role in user_roles for role in self.required_roles):
            return True
        else:
            raise AccessDeniedException
