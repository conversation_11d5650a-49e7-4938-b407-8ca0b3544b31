CAUSAL_WORDS = [
    "causality",
    "effect",
    "cause",
    "consequence",
    "result",
    "implication",
    "outcome",
    "correlation",
    "association",
    "influence",
    "impact",
    "reaction",
    "trigger",
    "stimulus",
    "conduct",
    "action",
    "relation",
    "chain_reaction",
    "reciprocity",
    "interdependence",
    "dependency",
    "catalyst",
    "provocation",
    "inducement",
    "motivation",
    "causation",
    "generation",
    "product",
    "creation",
    "sequence",
    "succession",
    "series",
    "follow_up",
    "conclusion",
    "end_result",
    "connection",
    "coherence",
    "link",
    "transition",
    "inference",
    "antecedent",
    "postulate",
    "precedent",
    "derivation",
    "outgrowth",
    "aftermath",
    "aftereffect",
    "ramification",
    "side_effect",
    "response",
    "backlash",
    "repercussion",
    "echo",
    "feedback",
    "outcome",
    "contingency",
    "condition",
    "context",
    "circumstance",
    "situation",
    "environment",
    "setting",
    "stage",
    "ground",
    "base",
    "foundation",
    "precursor",
    "origin",
    "source",
    "root",
    "core",
    "basis",
    "driver",
    "instigator",
    "inciter",
    "exciter",
    "agitator",
    "motivator",
    "igniter",
    "accelerator",
    "amplifier",
    "multiplier",
    "facilitator",
    "affect",
    "change",
    "alteration",
    "modification",
    "variation",
    "transformation",
    "evolution",
    "progression",
    "development",
    "growth",
    "advancement",
    "shift",
    "swing",
    "turn",
    "tilt",
    "pivot",
    "rotation",
    "revolution",
    "mutation",
    "metamorphosis",
    "conversion",
    "inversion",
    "reversal",
    "modulation",
    "adjustment",
    "regulation",
    "control",
    "governance",
    "direction",
    "guidance",
    "supervision",
    "management",
    "determination",
    "decision",
    "resolution",
    "fix",
    "choice",
    "option",
    "preference",
    "preferment",
    "selection",
    "election",
    "appointment",
    "designation",
    "delegation",
    "authorization",
    "permission",
    "sanction",
    "approval",
    "endorsement",
    "support",
    "back_up",
    "encouragement",
    "promotion",
    "boost",
    "push",
    "aid",
    "help",
    "assistance",
    "benefit",
    "advantage",
    "gain",
    "profit",
    "reward",
    "return",
    "yield",
    "outcome",
    "fruit",
    "product",
    "consequence",
    "fallout",
    "sequel",
    "successor",
    "descendant",
    "heir",
    "scion",
    "progeny",
    "offspring",
    "issue",
    "spawn",
    "outcome",
    "event",
    "incident",
    "occurrence",
    "episode",
    "happening",
    "experience",
    "affair",
    "matter",
    "thing",
    "fact",
    "reality",
    "truth",
    "actualization",
    "manifestation",
    "realization",
    "embodiment",
    "expression",
    "exhibition",
    "presentation",
    "demonstration",
    "display",
    "exposure",
    "revelation",
    "disclosure",
    "unveiling",
    "exposition",
    "clarification",
    "explanation",
    "elucidation",
    "interpretation",
    "translation",
    "decoding",
    "decipherment",
    "understanding",
    "comprehension",
    "perception",
    "insight",
    "appreciation",
    "recognition",
    "acknowledgment",
    "realization",
    "discovery",
    "detection",
    "find",
    "unearth",
    "excavation",
    "dig",
    "search",
    "quest",
    "hunt",
    "pursuit",
    "chase",
    "race",
    "competition",
    "contest",
    "rivalry",
    "conflict",
    "battle",
    "fight",
    "war",
    "confrontation",
    "clash",
    "collision",
    "impact",
    "hit",
    "strike",
    "blow",
    "shock",
    "jolt",
    "jar",
    "thrust",
    "push",
    "pull",
    "attraction",
    "draw",
    "lure",
    "allure",
    "temptation",
    "seduction",
    "invitation",
    "offer",
    "proposal",
    "suggestion",
    "idea",
    "concept",
    "notion",
    "thought",
    "belief",
    "opinion",
    "view",
    "perspective",
    "angle",
    "standpoint",
    "position",
    "stance",
    "attitude",
    "approach",
    "method",
    "way",
    "style",
    "fashion",
    "mode",
    "manner",
    "means",
    "mechanism",
    "system",
    "procedure",
    "process",
    "operation",
    "function",
    "activity",
    "action",
    "behavior",
    "conduct",
    "performance",
    "execution",
    "implementation",
    "enforcement",
    "compliance",
    "adherence",
    "observance",
    "respect",
    "honor",
]


capitals = {
    "Afghanistan": "Kabul",
    "Albania": "Tirana",
    "Algeria": "Algiers",
    "Andorra": "Andorra La Vella",
    "Angola": "Luanda",
    "Antigua & Barbuda": "Saint John's",
    "Argentina": "Buenos Aires",
    "Armenia": "Yerevan",
    "Australia": "Canberra",
    "Austria": "Vienna",
    "Azerbaijan": "Baku",
    "Bahamas": "Nassau",
    "Bahrain": "Manama",
    "Bangladesh": "Dhaka",
    "Barbados": "Bridgetown",
    "Belarus": "Minsk",
    "Belgium": "Brussels",
    "Belize": "Belmopan",
    "Benin": "Porto-Novo",
    "Bhutan": "Thimphu",
    "Bolivia": "Sucre",
    "Bosnia & Herzegovina": "Sarajevo",
    "Botswana": "Gaborone",
    "Brazil": "Brasilia",
    "Brunei": "Bandar Seri Begawan",
    "Bulgaria": "Sofia",
    "Burkina Faso": "Ouagadougou",
    "Burundi": "Bujumbura",
    "Cabo Verde": "Praia",
    "Cambodia": "Phnom Penh",
    "Cameroon": "Yaounde",
    "Canada": "Ottawa",
    "Central African Republic": "Bangui",
    "Chad": "N'djamena",
    "Chile": "Santiago",
    "China": "Beijing",
    "Colombia": "Bogota",
    "Comoros": "Moroni",
    "Congo": "Kinshasa",
    "Costa Rica": "San Jose",
    "Cote D'ivoire": "Yamoussoukro",
    "Croatia": "Zagreb",
    "Cuba": "Havana",
    "Cyprus": "Nicosia",
    "Czech Republic": "Prague",
    "Denmark": "Copenhagen",
    "Djibouti": "Djibouti",
    "Dominica": "Roseau",
    "Dominican Republic": "Santo Domingo",
    "Ecuador": "Quito",
    "Egypt": "Cairo",
    "El Salvador": "San Salvador",
    "Equatorial Guinea": "Malabo",
    "Eritrea": "Asmara",
    "Estonia": "Tallinn",
    "Eswatini": "Mbabane",
    "Ethiopia": "Addis Ababa",
    "Micronesia": "Palikir",
    "Fiji": "Suva",
    "Finland": "Helsinki",
    "France": "Paris",
    "Gabon": "Libreville",
    "Gambia": "Banjul",
    "Georgia": "Tbilisi",
    "Germany": "Berlin",
    "Ghana": "Accra",
    "Greece": "Athens",
    "Grenada": "Saint George's",
    "Guatemala": "Guatemala City",
    "Guinea": "Conakry",
    "Guinea-Bissau": "Bissau",
    "Guyana": "Georgetown",
    "Haiti": "Port-Au-Prince",
    "Honduras": "Tegucigalpa",
    "Hungary": "Budapest",
    "Iceland": "Reykjavik",
    "India": "New Delhi",
    "Indonesia": "Jakarta",
    "Iran": "Tehran",
    "Iraq": "Baghdad",
    "Ireland": "Dublin",
    "Israel": "Jerusalem",
    "Italy": "Rome",
    "Jamaica": "Kingston",
    "Japan": "Tokyo",
    "Jordan": "Amman",
    "Kazakhstan": "Astana",
    "Kenya": "Nairobi",
    "Kiribati": "South Tarawa",
    "Kosovo": "Pristina",
    "Kuwait": "Kuwait City",
    "Kyrgyzstan": "Bishkek",
    "Laos": "Vientiane",
    "Latvia": "Riga",
    "Lebanon": "Beirut",
    "Lesotho": "Maseru",
    "Liberia": "Monrovia",
    "Libya": "Tripoli",
    "Liechtenstein": "Vaduz",
    "Lithuania": "Vilnius",
    "Luxembourg": "Luxembourg",
    "Madagascar": "Antananarivo",
    "Malawi": "Lilongwe",
    "Malaysia": "Kuala Lumpur",
    "Maldives": "Male",
    "Mali": "Bamako",
    "Malta": "Valletta",
    "Marshall Islands": "Majuro",
    "Mauritania": "Nouakchott",
    "Mauritius": "Port Louis",
    "Mexico": "Mexico City",
    "Moldova": "Chisinau",
    "Monaco": "Monaco",
    "Mongolia": "Ulaanbaatar",
    "Montenegro": "Podgorica",
    "Morocco": "Rabat",
    "Mozambique": "Maputo",
    "Myanmar": "Nay Pyi Taw",
    "Namibia": "Windhoek",
    "Nauru": "Yaren District",
    "Nepal": "Kathmandu",
    "Netherlands": "Amsterdam",
    "New Zealand": "Wellington",
    "Nicaragua": "Managua",
    "Niger": "Niamey",
    "Nigeria": "Abuja",
    "North Korea": "Pyongyang",
    "North Macedonia": "Skopje",
    "Norway": "Oslo",
    "Oman": "Muscat",
    "Pakistan": "Islamabad",
    "Palau": "Ngerulmud",
    "Palestine": "Jerusalem",
    "Panama": "Panama City",
    "Papua New Guinea": "Port Moresby",
    "Paraguay": "Asuncion",
    "Peru": "Lima",
    "Philippines": "Manila",
    "Poland": "Warsaw",
    "Portugal": "Lisbon",
    "Qatar": "Doha",
    "Romania": "Bucharest",
    "Russia": "Moscow",
    "Rwanda": "Kigali",
    "Saint Kitts & Nevis": "Basseterre",
    "Saint Lucia": "Castries",
    "Saint Vincent & The Grenadines": "Kingstown",
    "Samoa": "Apia",
    "San Marino": "San Marino",
    "Sao Tome & Principe": "Sao Tome",
    "Saudi Arabia": "Riyadh",
    "Senegal": "Dakar",
    "Serbia": "Belgrade",
    "Seychelles": "Victoria",
    "Sierra Leone": "Freetown",
    "Singapore": "Singapore",
    "Slovakia": "Bratislava",
    "Slovenia": "Ljubljana",
    "Solomon Islands": "Honiara",
    "Somalia": "Mogadishu",
    "South Africa": "Pretoria",
    "South Korea": "Seoul",
    "South Sudan": "Juba",
    "Spain": "Madrid",
    "Sri Lanka": "Kotte",
    "Sudan": "Khartoum",
    "Suriname": "Paramaribo",
    "Sweden": "Stockholm",
    "Switzerland": "Bern",
    "Syria": "Damascus",
    "Tajikistan": "Dushanbe",
    "Tanzania": "Dodoma",
    "Thailand": "Bangkok",
    "Timor-Leste": "Dili",
    "Togo": "Lome",
    "Tonga": "Nuku'alofa",
    "Trinidad & Tobago": "Port Of Spain",
    "Tunisia": "Tunis",
    "Turkey": "Ankara",
    "Turkmenistan": "Ashgabat",
    "Tuvalu": "Funafuti",
    "Uganda": "Kampala",
    "Ukraine": "Kyiv",
    "United Arab Emirates": "Abu Dhabi",
    "United Kingdom": "London",
    "United States": "Washington D.C.",
    "Uruguay": "Montevideo",
    "Uzbekistan": "Tashkent",
    "Vanuatu": "Port Vila",
    "Vatican City": "Vatican City",
    "Venezuela": "Caracas",
    "Vietnam": "Hanoi",
    "Yemen": "Sana'a",
    "Zambia": "Lusaka",
    "Zimbabwe": "Harare",
}

numerical = ["age", "household_income"]

categorical = [
    "education_level",
    "gender",
    "racial_group",
    "number_of_children",
]

order_of_execution = [
    "gender",
    "age",
    "number_of_children",
    "education_level",
    "household_income",
    "racial_group",
]

order_of_display = [
    "age",
    "household_income",
    "education_level",
    "gender",
    "number_of_children",
    "racial_group",
]
INCOME_LOWER_THRESHOLD = 0
INCOME_UPPER_THRESHOLD = 3_000_000
AGE_LOWER_THRESHOLD = 18
AGE_UPPER_THRESHOLD = 100
context_message = (
    "The input does not contain enough detail to generate actionable, domain-specific"
    " suggestions. Please clarify the object of study (e.g., product, service,"
    " treatment), the type of decision or preference being evaluated, and any relevant"
    " constraints, features, or stakeholders involved."
)
