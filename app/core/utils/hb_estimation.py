import ast
import calendar
import itertools
import json
import sys
import time
import warnings
from copy import deepcopy
from datetime import datetime
from math import *

import numpy as np
import pandas as pd
import statsmodels.tools.numdiff as ndd
from numpy.linalg import cholesky, cond, det, multi_dot
from scipy.optimize import fmin_bfgs
from scipy.stats import norm, spearmanr, t
from statsmodels.sandbox.distributions.multivariate import mvstdnormcdf
from tabulate import tabulate

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.simplefilter(action="ignore", category=pd.errors.PerformanceWarning)
np.set_printoptions(formatter={"all": lambda x: str(x)})


def pprint_df(dframe):
    print(tabulate(dframe, headers="keys", tablefmt="psql", showindex=False))


def delete_last_line():
    sys.stdout.write("\x1b[1A")
    sys.stdout.write("\x1b[2K")


def Get_date():
    currentDay = datetime.now().day
    currentMonth = datetime.now().month
    currentYear = datetime.now().year
    month_eng = calendar.month_name[currentMonth]
    return month_eng + "_" + str(currentDay) + "_" + str(currentYear)


def find_key(dictionary, value):
    for key, val in dictionary.items():
        if val == value:
            return key
    return None


def pd_inv(a):
    n = a.shape[0]
    I = np.identity(n)
    check1 = np.isfinite(cond(a))
    det1 = det(a)
    check2 = det1 > 0.01
    check3 = is_pos_def(a)
    return check1 & check2 & check3


# Function to check if all the eigen values a matrix are positive
def is_pos_def(x):
    return np.all(np.linalg.eigvals(x) > 0)


# Function to obtain quasi-random draws from a Halton sequence
def HaltonSequence(n, dim):
    prim = np.array([
        2,
        3,
        5,
        7,
        11,
        13,
        17,
        19,
        23,
        29,
        31,
        37,
        41,
        43,
        47,
        53,
        59,
        61,
        67,
        71,
        73,
        79,
        83,
        89,
        97,
        101,
        103,
        107,
        109,
        113,
        127,
        131,
        137,
        139,
        149,
        151,
        157,
        163,
        167,
        173,
        179,
        181,
        191,
        193,
        197,
        199,
        211,
        223,
        227,
        229,
        233,
        239,
        241,
        251,
        257,
        263,
        269,
        271,
        277,
        281,
        283,
        293,
        307,
        311,
        313,
        317,
        331,
        337,
        347,
        349,
        353,
        359,
        367,
        373,
        379,
        383,
        389,
        397,
        401,
        409,
        419,
        421,
        431,
        433,
        439,
        443,
        449,
        457,
        461,
        463,
        467,
        479,
        487,
        491,
        499,
        503,
        509,
        521,
        523,
        541,
    ])
    prim = prim[:, np.newaxis]
    hs = np.zeros((n, dim))
    for idim in range(dim):
        b = prim[idim, 0]
        hs[:, idim] = halton(n, b)

    return hs[10:n, :]


# Halton sequence sub function.
def halton(n, s):
    k = floor(log(n + 1) / log(s))
    phi = np.zeros((1, 1))
    i = 1
    count = 0
    while i <= k:
        count = count + 1
        x = phi
        j = 1
        while j < s:
            y = phi + (j / s**i)
            x = np.vstack((x, y))
            j += 1

        phi = x
        i += 1

    x = phi
    j = 1
    while (j < s) and (len(x) < (n + 1)):
        y = phi + (j / s**i)
        x = np.vstack((x, y))
        j += 1

    return x[1 : (n + 1), 0]


# GHK simulator to calculate multivariate normal cdf of dimension 3 or more
def cdfmvnGHK(a, r, s):
    a = np.multiply(a, (a < 5.7)) + 5.7 * (a >= 5.7)
    a = np.multiply(a, (a > -5.7)) - 5.7 * (a <= -5.7)

    nintegdim = a.shape[1]
    if sys.getsizeof(s) < 50:
        np.random.seed(s)
    else:
        np.random.set_state(s)

    rnum = np.random.random((1, 1))
    ss = np.random.get_state()
    s = ss

    startRow = ceil(rnum * (_halt_maxdraws - _halt_numdraws - 1))
    uniRands = allHaltDraws[startRow : startRow + _halt_numdraws, 0 : nintegdim - 1]
    chol_r = cholesky(r)
    chol_r = chol_r.T
    ghkArr = np.zeros((nrep, nintegdim))
    etaArr = np.zeros((nrep, (nintegdim - 1)))
    temp = norm.cdf(a[0, 0] / chol_r[0, 0]) * np.ones((nrep, 1))
    ghkArr[:, 0] = temp[:, 0]
    del temp

    for iintegdim_main in range(1, nintegdim):
        iintegdim = iintegdim_main - 1
        temp1 = uniRands[:, iintegdim]
        temp2 = ghkArr[:, iintegdim]
        temp1 = temp1[:, np.newaxis]
        temp2 = temp2[:, np.newaxis]

        temp3 = np.multiply(temp1, temp2)
        temp4 = cdfni(temp3)
        temp4 = temp4[:, np.newaxis]
        etaArr[:, iintegdim] = temp4[:, 0]
        del temp1, temp2, temp3, temp4

        ghkElem = a[0, iintegdim + 1] * np.ones((nrep, 1))
        ghkElem1 = 0
        for jintegdim in range(iintegdim_main):
            temp = chol_r[jintegdim, iintegdim + 1] * etaArr[:, jintegdim]
            temp = temp[:, np.newaxis]
            ghkElem1 = ghkElem1 - temp
            del temp

        ghkElem1 = ghkElem1 + ghkElem
        temp1 = ghkElem1 / (chol_r[(iintegdim + 1), (iintegdim + 1)])
        temp2 = cdfn(temp1)
        temp2 = temp2[:, np.newaxis]
        ghkArr[:, iintegdim + 1] = temp2[:, 0]
        del temp1, temp2

    probab = ghkArr[:, 0]
    probab = probab[:, np.newaxis]
    for iintegdim in range(1, nintegdim):
        temp = ghkArr[:, iintegdim]
        temp = temp[:, np.newaxis]
        probab = np.multiply(probab, temp)
        del temp

    probab = np.mean(probab, axis=0)[0]
    return (probab, s)


# Procedure to obtain standard normal distribution CDF
def cdfn(a):
    return norm.cdf(a[:, 0])


# Procedure to obtain inverse of univariate normal distribution CDF
def cdfni(a):
    return norm.ppf(a[:, 0])


# Procedure to obtain bi-variate normal distribution CDF
def cdfbvn(a1, a2, r):
    low_x = -np.inf * np.ones(2)
    up_x = np.array([a1, a2])
    return mvstdnormcdf(low_x, up_x, r, abseps=1e-6)


# Function to convert a covariance matrix into a correlation matrix
def corrvc(S):
    temp = np.diag(S)
    temp = temp[:, np.newaxis]
    D = temp**0.5
    Dcol = np.kron(np.ones((1, S.shape[0])), D)
    Drow = np.kron(np.ones((S.shape[0], 1)), D.T)
    DF = np.divide(S, Dcol)
    DF = np.divide(DF, Drow)
    return diagrv(DF)


# Function to obtain Cholesky decomposition of a matrix
def chol(r):
    return cholesky(r)


# Function to put 1's on a diagonal of a matrix
def diagrv(a):
    for i in range(a.shape[0]):
        a[i, i] = 1.0
    return a


# FUnction to expnad a vector into a symmetric matrix
def xpnd(r):
    d = int((-1 + sqrt(1 + 8 * len(r))) / 2)
    xp = np.zeros((d, d), dtype=float)
    count = 0
    xp[0, 0] = r[0, 0]
    for i in range(1, d):
        for j in range(i + 1):
            count = count + 1
            xp[i, j] = r[count, 0]
            xp[j, i] = r[count, 0]
    return xp


# Function to vectorize  a symmetric matrix into unique elements
def vech(r):
    drow = r.shape[0]
    d = int(drow * (drow + 1) * 0.5)
    xp = np.zeros((d, 1))
    xp[0, 0] = r[0, 0]
    count = 0
    for i in range(1, drow):
        for j in range(i + 1):
            count = count + 1
            xp[count, 0] = r[i, j]
    return xp


# Function to obtain upper traingular elements of a matrix
def upmat(r):
    drow = r.shape[0]
    xp = np.zeros((drow, drow))
    for i in range(drow):
        for j in range(i, drow):
            xp[i, j] = r[i, j]
    return xp


# FUnction to obtain lower triangular elements of a matrix
def lowmat(r):
    drow = r.shape[0]
    xp = np.zeros((drow, drow))
    xp[0, 0] = r[0, 0]
    for i in range(1, drow):
        for j in range(i + 1):
            xp[i, j] = r[i, j]
    return xp


def lpr(parm_estimated, idx_estimated, idx_fixed, parm_fixed, dta, mnp_model):
    x = reconstruct(parm_estimated, idx_estimated, idx_fixed, parm_fixed)
    atemp_array = lprT(x, dta, mnp_model)

    if Parametrized == 1:
        return -np.mean(atemp_array, axis=0)

    if Parametrized == 0:
        return -atemp_array


def lprT(parm, dta, mnp_model):
    upper_limit = 1e-05  # Any value of CDF below this limit is capped at this value
    nind_num = dta.shape[0]
    nc = (dta["Alt"].unique()[0]).astype(int)

    smallb = parm[0:nvarma]
    if mnp_model == True:
        Psi_diff = 0.5 * np.eye(nc - 1) + 0.5 * np.ones((nc - 1, nc - 1))
        Psi = np.zeros((nc, nc))
        Psi[1:nc, 1:nc] = Psi_diff

    v1 = (np.kron(np.ones((nc, 1)), smallb)) * (dta.loc[:, ivgenva].values.T)
    Utility = np.empty(shape=(nind_num, nc), dtype=float)
    for i in range(nc):
        j = i + 1
        Utility[:, i] = np.sum(v1[(j - 1) * nvarma : (j * nvarma), :], axis=0)
    del v1

    All_Chosen = dta.loc[:, altchm].values.astype(int)

    if mnp_model == True:
        iden_matrix = np.eye(nc - 1)
        one_negative = -1 * np.ones((nc - 1, 1))
        seednext = MACMLS[0]
        Likelihood = np.zeros((nind_num, 1))

        for i in range(nind_num):

            u_temp = Utility[i, :]
            UY = u_temp[:, np.newaxis]
            Full_error = Psi

            if All_Chosen[i, 0] == 1:
                temp1 = np.hstack((one_negative, iden_matrix))
            elif All_Chosen[i, 0] == nc:
                temp1 = np.hstack((iden_matrix, one_negative))
            else:
                ch = int(All_Chosen[i, 0])
                t1 = iden_matrix[:, 0 : ch - 1]
                t2 = iden_matrix[:, ch - 1 : nc - 1]
                temp1 = np.hstack((t1, one_negative, t2))
            M_big = temp1

            B_Tild = multi_dot([M_big, UY])
            Error_Tild = multi_dot([M_big, Full_error, M_big.T])

            mean_gu = -B_Tild
            var_gu = Error_Tild

            om = np.diag(var_gu)
            om = om[:, np.newaxis]
            om = om**0.5
            mean_gu_final = np.divide(mean_gu.T, om.T)
            var_gu_final = corrvc(var_gu)

            if nc - 1 == 2:
                p2 = cdfbvn(
                    mean_gu_final[0, 0], mean_gu_final[0, 1], var_gu_final[0, 1]
                )
            elif nc == 2:
                p2 = cdfn(mean_gu_final)
            else:
                seed20 = seednext
                p2, sss = cdfmvnGHK(mean_gu_final, var_gu_final, seed20)
                seednext = sss

            p2 = max(p2, upper_limit)
            Likelihood[i, 0] = np.log(p2)
    else:
        Utility_exp = np.exp(Utility)
        Utility_sum = np.sum(Utility_exp, axis=1)
        Prob = Utility_exp / Utility_sum[:, np.newaxis]
        Prob = Prob * All_Chosen
        Prob = np.sum(Prob, axis=1)
        try:
            temp = Prob.shape[1]
        except:
            Prob = Prob[:, np.newaxis]

        Prob = (Prob < upper_limit) * upper_limit + (Prob >= upper_limit) * Prob
        Likelihood = np.log(Prob)

    return Likelihood


def lgd_NM(parm_estimated, idx_estimated, idx_fixed, parm_fixed, dta, mnp_model):
    eps = np.sqrt(np.finfo(np.float64).eps)
    return ndd.approx_fprime(
        parm_estimated,
        lpr,
        eps,
        args=(idx_estimated, idx_fixed, parm_fixed, dta, mnp_model),
        centered=False,
    )


def reconstruct(parm_estimated, idx_estimated, idx_fixed, parm_fixed):
    total_parm = parm_estimated.shape[0] + parm_fixed.shape[0]
    beta_reconstructed = np.empty(shape=(total_parm, 1), dtype=float)

    beta_reconstructed[idx_estimated] = parm_estimated
    beta_reconstructed[idx_fixed] = parm_fixed
    return beta_reconstructed


# A function to obtain AMC estimates
def Get_Elasticity(data_elt, parm, mnp_model):
    nc = (data_elt["Alt"].unique()[0]).astype(int)
    nind_num = data_elt.shape[0]
    smallb = parm[0:nvarma]
    if mnp_model == True:
        Psi_diff = 0.5 * np.eye(nc - 1) + 0.5 * np.ones((nc - 1, nc - 1))
        Psi = np.zeros((nc, nc))
        Psi[1:nc, 1:nc] = Psi_diff

    v1 = (np.kron(np.ones((nc, 1)), smallb)) * (data_elt.loc[:, ivgenva].values.T)
    Utility = np.empty(shape=(nind_num, nc), dtype=float)
    for i in range(nc):
        j = i + 1
        Utility[:, i] = np.sum(v1[(j - 1) * nvarma : (j * nvarma), :], axis=0)
    del v1

    if mnp_model == True:
        iden_matrix = np.eye(nc - 1)
        one_negative = -1 * np.ones((nc - 1, 1))
        seednext = MACMLS[0]
        Likelihood = np.zeros((nind_num, nc))

        for i in range(nind_num):
            for j in range(nc):

                u_temp = Utility[i, :]
                UY = u_temp[:, np.newaxis]
                Full_error = Psi
                curr_chosen = j + 1

                if curr_chosen == 1:
                    temp1 = np.hstack((one_negative, iden_matrix))
                elif curr_chosen == nc:
                    temp1 = np.hstack((iden_matrix, one_negative))
                else:
                    ch = int(curr_chosen)
                    t1 = iden_matrix[:, 0 : ch - 1]
                    t2 = iden_matrix[:, ch - 1 : nc - 1]
                    temp1 = np.hstack((t1, one_negative, t2))
                M_big = temp1

                B_Tild = multi_dot([M_big, UY])
                Error_Tild = multi_dot([M_big, Full_error, M_big.T])

                BU_Tild = B_Tild
                Error_U_Tild = Error_Tild

                mean_gu = -BU_Tild
                var_gu = Error_U_Tild

                om = np.diag(var_gu)
                om = om[:, np.newaxis]
                om = om**0.5
                mean_gu_final = np.divide(mean_gu.T, om.T)
                var_gu_final = corrvc(var_gu)

                if nc - 1 == 2:
                    p2 = cdfbvn(
                        mean_gu_final[0, 0], mean_gu_final[0, 1], var_gu_final[0, 1]
                    )
                elif nc == 2:
                    p2 = cdfn(mean_gu_final)
                else:
                    seed20 = seednext
                    p2, sss = cdfmvnGHK(mean_gu_final, var_gu_final, seed20)
                    seednext = sss
                Likelihood[i, j] = p2
    else:
        Utility_exp = np.exp(Utility)
        Utility_sum = np.sum(Utility_exp, axis=1)
        try:
            temp = Utility_sum.shape[1]
        except:
            Utility_sum = Utility_sum[:, np.newaxis]
        Prob = Utility_exp / Utility_sum
        try:
            temp = Prob.shape[1]
        except:
            Prob = Prob[:, np.newaxis]
        Likelihood = Prob

    return Likelihood


def convert_to_dict(lst):
    demo_list = ast.literal_eval(lst)
    demo_dict = {}
    for item in demo_list:
        key, value = item.split(": ", 1)  # split on first occurrence only
        demo_dict[key] = value

    return demo_dict


def Quartile_cols(df, cols):
    df[cols] = df[cols].astype(int)
    q1 = df[cols].quantile(0.25)
    q2 = df[cols].quantile(0.50)
    q3 = df[cols].quantile(0.75)

    q1 = int(round(q1, 0))
    q2 = int(round(q2, 0))
    q3 = int(round(q3, 0))

    df[f"{cols}_{q1}"] = df[cols].apply(lambda x: 1 if x <= q1 else 0)
    df[f"{cols}_{q2}"] = df[cols].apply(lambda x: 1 if q1 < x <= q2 else 0)
    df[f"{cols}_{q3}"] = df[cols].apply(lambda x: 1 if q2 < x <= q3 else 0)
    df[f"{cols}_{q3}M"] = df[cols].apply(lambda x: 1 if x > q3 else 0)

    col_names = [f"{cols}_{q1}", f"{cols}_{q2}", f"{cols}_{q3}", f"{cols}_{q3}M"]

    return df, col_names


def unique_except_most_frequent(arr):
    values, counts = np.unique(arr, return_counts=True)
    most_frequent = values[counts.argmax()]
    levels_without_most_frequent = np.sort(values[values != most_frequent])
    levels_without_most_frequent = levels_without_most_frequent.tolist()
    return levels_without_most_frequent


def data_long_to_wide(data, feature_names):
    uni_id = np.sort(data["ID"].unique())
    task_id = np.sort(data["Task"].unique())
    num_alt = int(data["Alts"].max())
    num_att = len(feature_names)

    new_feature_names = []
    for i in range(num_alt):
        new_feature_names.extend(
            col_names + "_" + str(i + 1) for col_names in feature_names
        )

    All_new_cols = ["PID", "TID", "Persona", "Chosen"] + new_feature_names
    final_df = pd.DataFrame(columns=All_new_cols)
    count = -1
    for individual_num in uni_id:
        temp_df = data[data["ID"] == individual_num]
        for task_num in task_id:
            curr_data = temp_df[temp_df["Task"] == task_num]
            feature_values = curr_data[feature_names].values
            feature_values = np.reshape(feature_values, (1, num_alt * num_att))
            chosen_mask = curr_data["chosen_choice_letter"].values
            chosen_alt = ord(chosen_mask[0]) - 64
            count += 1

            final_df.loc[count, new_feature_names] = feature_values
            final_df.loc[count, "PID"] = individual_num
            final_df.loc[count, "TID"] = task_num
            final_df.loc[count, "Persona"] = curr_data["Persona"].values
            final_df.loc[count, "Chosen"] = chosen_alt

    final_df = final_df[All_new_cols]
    return final_df, new_feature_names


def callbackF(Xi):
    global Nfeval
    Summary_table = pd.DataFrame({"Parameter": Active_name, "Value": Xi})
    print(
        "================================================================================"
    )
    print()
    print("Iter_no: {0:1d}".format(Nfeval))
    print(
        tabulate(
            Summary_table,
            headers="keys",
            tablefmt="psql",
            floatfmt=(".0f", ".3f"),
            showindex=False,
        )
    )
    print()
    Nfeval += 1


def HB_Transcription_Check(HB_data):
    HB_data = HB_data[["group", "varname", "coef", "err"]]
    HB_data = HB_data.rename(
        columns={
            "group": "Attribute",
            "varname": "Levels",
            "coef": "Reported Value",
            "err": "Reported St-Err",
        }
    )
    HB_data.fillna(0, inplace=True)
    HB_data["Reported Value"] = HB_data["Reported Value"].astype(float)
    HB_data["Reported St-Err"] = HB_data["Reported St-Err"].astype(float)
    encoding_type = "Unknown"
    Attribute_Value_Sum = HB_data.groupby("Attribute")["Reported Value"].sum()
    Attribute_Value_Sum = Attribute_Value_Sum.to_list()
    if all(x == 0 for x in Attribute_Value_Sum) == True:
        encoding_type = "Effect_Coding"

    zero_coefs = (
        HB_data.groupby("Attribute")
        .apply(lambda x: x[x["Reported Value"] == 0]["Levels"].tolist())
        .reset_index()
    )
    zero_coefs.columns = ["Attribute", "Level_with_zero_coef"]

    # Check length of the list
    zero_coefs["List_length"] = zero_coefs["Level_with_zero_coef"].apply(
        lambda x: len(x)
    )
    if all(zero_coefs["List_length"].values == 1):
        encoding_type = "Dummy_Coding"

    Attribute_base = {}
    Missing_base = True
    if encoding_type != "Unknown":
        Missing_base = False
        Unique_attributes = HB_data["Attribute"].unique().tolist()
        if encoding_type == "Dummy_Coding":
            for i in Unique_attributes:
                temp = HB_data[HB_data["Attribute"] == i]
                base_level = temp[temp["Reported Value"] == 0]
                if base_level.shape[0] != 0:
                    base_level = base_level["Levels"].values[0]
                    Attribute_base[i] = base_level
                else:
                    Attribute_base[i] = "Missing_base"
                    Missing_base = True
        else:
            for i in Unique_attributes:
                temp = HB_data[HB_data["Attribute"] == i]
                base_level = temp[temp["Reported St-Err"] == 0]
                if base_level.shape[0] != 0:
                    base_level = base_level["Levels"].values[0]
                    Attribute_base[i] = base_level
                else:
                    Attribute_base[i] = "Missing_base"
                    Missing_base = True

    return encoding_type, Attribute_base, Missing_base


def Run_Model(
    experiment_mapping_file,
    experiment_survey_file,
    experiment_HB_file,
    Add_Personas=False,
    MNP_Model=True,
):
    global nind, nobs, Parametrized, altchm
    global ivgenva, nvarma, nvarma_global, ivgenvc, nvarmc
    global Nfeval, max_active, Active_name, MACMLS
    global _halt_numdraws, _halt_maxdraws, nrep, nrephalt, allHaltDraws

    Gradient_tol = 1e-04  # Gradient tolerance for BFGS
    Iteration_tol = 1000  # Maximum number of iterations for BFGS

    json_path = experiment_mapping_file
    data_path = experiment_survey_file
    HB_path = experiment_HB_file

    data = pd.read_csv(data_path, encoding="utf-8")
    HB_data = pd.read_csv(HB_path, encoding="utf-8")
    encoding_type, Attribute_base, Missing_base = HB_Transcription_Check(HB_data)

    data_dict = json.load(open(json_path))
    Opt_Out_Included = data_dict.get("opt_out_included", True)

    if Missing_base == False:
        HB_data = HB_data[["group", "varname", "coef", "err"]]
        HB_data = HB_data.rename(
            columns={
                "group": "Attribute",
                "varname": "Levels",
                "coef": "Reported Value",
                "err": "Reported St-Err",
            }
        )
        HB_data.fillna(0, inplace=True)
        HB_data["Reported Value"] = HB_data["Reported Value"].astype(float)
        HB_data["Reported St-Err"] = HB_data["Reported St-Err"].astype(float)

        attribute_names = data_dict["attributes_and_levels_lookup"][
            "get_attribute_text"
        ]
        attribute_levels = data_dict["attributes_and_levels_lookup"]["get_level_ids"]
        attribute_levels_description = data_dict["attributes_and_levels_lookup"][
            "get_level_text"
        ]
        attribute_levels_description[0] = "Not_Required"
        Unique_attributes = HB_data["Attribute"].unique().tolist()
        attribute_levels_description = {
            int(k): v for k, v in attribute_levels_description.items()
        }

        for att_num, att_name in enumerate(Unique_attributes):
            data[att_name] = data[att_name].map(attribute_levels_description)

        attribute_levels_grouped = {}
        for key, value in attribute_names.items():
            att_name = value
            value = attribute_levels[key]
            try:
                temp = [attribute_levels_description[i] for i in value]
            except:
                temp = [attribute_levels_description[str(i)] for i in value]

            for cols in temp:
                attribute_levels_grouped[cols] = att_name

        num_alt = int(data["Alts"].max())

        Main_data, alt_spe_attribute_name = data_long_to_wide(data, Unique_attributes)

        Main_data["uno"] = 1
        Main_data["sero"] = 0

        Attribute_Order = []

        if encoding_type == "Effect_Coding":
            for i in range(num_alt - 1):
                for icols in Unique_attributes:
                    col_name = f"{icols}_{i + 1}"

                    all_level = Main_data[col_name].unique().tolist()
                    all_level.remove(Attribute_base[icols])

                    curr_out = np.zeros((Main_data.shape[0], len(all_level)))
                    curr_all_values = Main_data[icols].values
                    curr_col_name = []

                    for index, ilevel in enumerate(all_level):
                        if ilevel not in Attribute_Order:
                            Attribute_Order.append(ilevel)
                        curr_col_name.append(f"{col_name}_{ilevel}")
                        curr_out[:, index] = (curr_all_values == ilevel).astype(int)

                    row_sums = np.sum(curr_out, axis=1)
                    zero_sum_mask = row_sums == 0
                    curr_out[zero_sum_mask, :] = -1

                    Main_data[curr_col_name] = curr_out
        elif encoding_type == "Dummy_Coding":
            for i in range(num_alt - 1):
                for icols in Unique_attributes:
                    col_name = f"{icols}_{i + 1}"

                    all_level = Main_data[col_name].unique().tolist()
                    all_level.remove(Attribute_base[icols])

                    curr_out = np.zeros((Main_data.shape[0], len(all_level)))
                    curr_all_values = Main_data[col_name].values
                    curr_col_name = []

                    for index, ilevel in enumerate(all_level):
                        if ilevel not in Attribute_Order:
                            Attribute_Order.append(ilevel)
                        curr_col_name.append(f"{col_name}_{ilevel}")
                        curr_out[:, index] = (curr_all_values == ilevel).astype(int)

                    Main_data[curr_col_name] = curr_out
        if Add_Personas == 1:
            big_list = []
            for i in range(Main_data.shape[0]):
                a = Main_data.loc[i, "Persona"]
                Pid = Main_data.loc[i, "PID"]
                Tid = Main_data.loc[i, "TID"]
                b = convert_to_dict(a[0])
                b["PID"] = Pid
                b["TID"] = Tid
                big_list.append(b)

            expanded_df = pd.DataFrame(big_list)
            expanded_df.sort_values(by=["PID", "TID"], inplace=True)
            expanded_df.drop(["PID", "TID"], axis=1, inplace=True)
            Main_data = pd.concat(
                [Main_data.drop(columns=["Persona"]), expanded_df], axis=1
            )
            persona_col_names = list(expanded_df.columns)

            drop_persona_cols = [
                "Number of Female Children in the household",
                "Number of Male Children in the household",
                "Number of Children Aged zero to five years old in the household",
                "Number of Children Aged six to ten years old in the household",
                "Number of Children Aged eleven to fifteen years old in the household",
                "Household Annual Income",
            ]

            for icols in drop_persona_cols:
                persona_col_names.remove(icols)

            Main_data["Children_Household"] = (
                Main_data["Number of Female Children in the household"].astype(int)
                + Main_data["Number of Male Children in the household"].astype(int)
                > 0
            ).astype(int)
            Main_data.drop(drop_persona_cols, axis=1, inplace=True)

            persona_col_estimate = []
            All_Persona_cols = []

            Main_data, age_col_names = Quartile_cols(Main_data, "Age")
            persona_col_estimate.extend(age_col_names[0:3])
            for i in age_col_names:
                All_Persona_cols.append([i, "Age"])
            persona_col_names.remove("Age")

            for icols in persona_col_names:
                temp = unique_except_most_frequent(Main_data[icols])
                all_uni_levels = Main_data[icols].unique().tolist()
                for levels in all_uni_levels:
                    All_Persona_cols.append([f"{icols}_{levels}", icols])
                for i in temp:
                    Main_data[f"{icols}_{i}"] = (Main_data[icols] == i).astype(int)
                    persona_col_estimate.append(f"{icols}_{i}")

        All_Specification = [[] for i in range(num_alt)]
        for icol in Attribute_Order:
            level_attribute_name = attribute_levels_grouped[icol]
            for i in range(num_alt - 1):
                All_Specification[i].append(f"{level_attribute_name}_{i + 1}_{icol}")
            All_Specification[num_alt - 1].append("sero")

        if Opt_Out_Included == True:
            for i in range(1, num_alt + 1):
                if i == num_alt:
                    All_Specification[i - 1].append("uno")
                else:
                    All_Specification[i - 1].append("sero")

        if Add_Personas == 1:
            for icols in persona_col_estimate:
                for i in range(1, num_alt + 1):
                    if i == num_alt:
                        All_Specification[i - 1].append("sero")
                    else:
                        All_Specification[i - 1].append(f"{icols}")

        ivgenva = list(itertools.chain.from_iterable(All_Specification))
        nvarma = len(
            All_Specification[0]
        )  # Number of variables in the utility specification
        nc = num_alt  # Number of alternatives
        nind = Main_data.shape[0]  # Number of observations
        nobs = nind

        max_gradTol = Gradient_tol
        max_iter = Iteration_tol

        print(f"Sample size      : {Main_data.shape[0]}")

        altchm = ["Chosen"]
        for icols in altchm:
            value_counts = Main_data[icols].value_counts().sort_index()
            df_value_counts = pd.DataFrame(value_counts)
            df_value_counts = df_value_counts.reset_index()
            df_value_counts.columns = ["Alternative", "Frequency"]
            print("Alternative shares")
            pprint_df(df_value_counts)
            print()

        if MNP_Model == False:
            altchm = []
            for ialt in range(1, nc + 1):
                Main_data[f"Alt_{ialt}"] = (Main_data["Chosen"] == ialt).astype(int)
                altchm.append(f"Alt_{ialt}")
        Main_data["Alt"] = nc

        All_parm = 0.1 * np.ones((nvarma, 1))

        if MNP_Model == True:
            MACMLS = [30000 + i for i in range(20)]
            _halt_numdraws = 200  # Number of halton draws
            nrep = _halt_numdraws
            nrephalt = nrep
            allHaltDraws = HaltonSequence(nobs * (nrep + 10), nc)
            _halt_maxdraws = allHaltDraws.shape[0]

        # ****************************************************************************************************************************************************************************************
        #                 Packing of all parameters in a single vector ( Do not change anything below this line)
        # *****************************************************************************************************************************************************************************************
        dgp_X = All_parm
        bb = dgp_X[:nvarma]

        max_active = np.ones((nvarma, 1))

        Num_Active_parm = np.sum(max_active == 1)
        idx_estimated = np.where(
            max_active == 1
        )  # Index of estimated variables in beta vector
        idx_fixed = np.where(max_active == 0)  # Index of fixed variables in beta vector

        idx_estimatedt = idx_estimated[0]
        idx_estimatedt = idx_estimatedt[:, np.newaxis]

        # Split the starting value vector into parameters that are estimated and fixed
        init_estimated = bb[idx_estimated]
        init_fixed = bb[idx_fixed]

        # Defining variable labels
        if Opt_Out_Included == True:
            Param_nam = Attribute_Order + ["Intercept"]
        else:
            Param_nam = Attribute_Order

        if Add_Personas == 1:
            Param_nam.extend(persona_col_estimate)

        Active_name = [
            Param_nam[idx_estimatedt[i, 0]]
            for i in range(int(np.sum(max_active, axis=0)[0]))
        ]

        Nfeval = 1
        Parametrized = 1

        print("Optimization has Started.............")
        start_time = time.time()
        [xopt, fopt, gopt, Bopt, func_calls, grad_calls, warnflg, allvecs] = fmin_bfgs(
            lpr,
            init_estimated,
            fprime=lgd_NM,
            args=(idx_estimated, idx_fixed, init_fixed, Main_data, MNP_Model),
            gtol=max_gradTol,
            epsilon=1.4901161193847656e-08,
            maxiter=max_iter,
            full_output=1,
            disp=1,
            retall=1,
            callback=callbackF,
        )
        xComplete = bb
        xComplete[idx_estimated] = xopt
        xComplete[idx_fixed] = init_fixed

        Parametrized = 0

        parm_estimated = xComplete[idx_estimated]
        parm_fixed = xComplete[idx_fixed]

        MNP_lpr = lpr(
            parm_estimated, idx_estimated, idx_fixed, parm_fixed, Main_data, MNP_Model
        )
        MNP_lgd = lgd_NM(
            parm_estimated, idx_estimated, idx_fixed, parm_fixed, Main_data, MNP_Model
        )

        Jacobian = multi_dot([MNP_lgd.T, MNP_lgd])
        Jacobian_invertable = pd_inv(Jacobian)

        IM_all = np.zeros((max_active.shape[0], 1))
        Pvalue_all = np.ones((max_active.shape[0], 1))
        T_stat_all = np.zeros((max_active.shape[0], 1))

        if Jacobian_invertable:
            IM = np.sqrt(np.diag(np.linalg.inv(Jacobian)))
            IM_all[idx_estimated] = IM

            T_stat = np.divide(xComplete[idx_estimated], IM)
            T_stat_all[idx_estimated] = T_stat

            Pvalue_mat = [
                t.sf(abs(T_stat[ic]), df=Main_data.shape[0]) * 2
                for ic in range(Num_Active_parm)
            ]
            Pvalue_all[idx_estimated] = Pvalue_mat

            end_time = time.time()
            total_time = (end_time - start_time) / 60

            Total_LL = -np.sum(MNP_lpr, axis=0)[0]
            print("Covariance matrix calculation Successful")
            Summary_table = pd.DataFrame({
                "Parameter": Param_nam,
                "Value": xComplete[:, 0],
                "St-Err": IM_all[:, 0],
                "P-value": Pvalue_all[:, 0],
                "T-value": T_stat_all[:, 0],
            })
            print("----------------Model Summary----------")
            print(f"Estimation time (mins) : {total_time:.2f}")
            print(f"Log-Likelihood         : {Total_LL:.2f}")
            print(
                tabulate(
                    Summary_table,
                    headers="keys",
                    tablefmt="psql",
                    floatfmt=(".0f", ".3f", ".3f", ".3f", ".2f"),
                    showindex=False,
                )
            )

        else:
            Total_LL = -np.sum(MNP_lpr, axis=0)[0]
            end_time = time.time()
            total_time = (end_time - start_time) / 60
            print("Covariance matrix calculation Failed")
            Summary_table = pd.DataFrame({
                "Parameter": Param_nam,
                "Value": xComplete[:, 0],
                "St-Err": IM_all[:, 0],
                "P-value": Pvalue_all[:, 0],
                "T-value": T_stat_all[:, 0],
            })
            print("----------------Model Summary----------")
            print(f"Estimation time (mins) : {total_time:.2f}")
            print(f"Log-Likelihood   : {Total_LL:.2f}")
            print(
                tabulate(
                    Summary_table,
                    headers="keys",
                    tablefmt="psql",
                    floatfmt=(".0f", ".3f", ".3f", ".3f", ".2f"),
                    showindex=False,
                )
            )

        attribute_levels_grouped_list = []
        for key, value in attribute_levels_grouped.items():
            attribute_levels_grouped_list.append([key, value])
        All_attribute_df = pd.DataFrame(
            attribute_levels_grouped_list, columns=["Levels", "Attribute"]
        )
        if Add_Personas == 1:
            All_Persona_cols_df = pd.DataFrame(
                All_Persona_cols, columns=["Levels", "Attribute"]
            )
            Combined_df = pd.concat(
                [All_attribute_df, All_Persona_cols_df], ignore_index=True
            )
            All_attribute_df = Combined_df

        All_attribute_df["Levels"] = All_attribute_df["Levels"].astype(str)
        All_attribute_df["Attribute"] = All_attribute_df["Attribute"].astype(str)

        Summary_table.rename(columns={"Parameter": "Levels"}, inplace=True)
        Join_tables = pd.merge(All_attribute_df, Summary_table, how="left", on="Levels")
        Join_tables = Join_tables[
            ["Attribute", "Levels", "Value", "St-Err", "P-value", "T-value"]
        ]
        Join_tables.fillna(
            {"Value": 0, "St-Err": 0, "T-value": 0, "P-value": 1.000}, inplace=True
        )
        Join_tables["Base"] = "No"
        if encoding_type == "Effect_Coding":
            for iunique in Unique_attributes:
                temp = Join_tables[Join_tables["Attribute"] == iunique]
                temp = temp["Value"].values
                temp_sum = np.sum(temp)
                Join_tables.loc[
                    (Join_tables["Attribute"] == iunique) & (Join_tables["Value"] == 0),
                    "Base",
                ] = "Yes"
                Join_tables.loc[
                    (Join_tables["Attribute"] == iunique) & (Join_tables["Value"] == 0),
                    "Value",
                ] = temp_sum
            if Add_Personas == 1:
                unique_persona_col_names = list(
                    All_Persona_cols_df["Attribute"].unique()
                )
                for iunique in unique_persona_col_names:
                    temp = Join_tables[Join_tables["Attribute"] == iunique]
                    temp = temp["Value"].values
                    temp_sum = np.sum(temp)
                    Join_tables.loc[
                        (Join_tables["Attribute"] == iunique)
                        & (Join_tables["Value"] == 0),
                        "Base",
                    ] = "Yes"
                    Join_tables.loc[
                        (Join_tables["Attribute"] == iunique)
                        & (Join_tables["Value"] == 0),
                        "Value",
                    ] = temp_sum
        else:
            for iunique in Unique_attributes:
                Join_tables.loc[
                    (Join_tables["Attribute"] == iunique) & (Join_tables["Value"] == 0),
                    "Base",
                ] = "Yes"
            if Add_Personas == 1:
                unique_persona_col_names = list(
                    All_Persona_cols_df["Attribute"].unique()
                )
                for iunique in unique_persona_col_names:
                    Join_tables.loc[
                        (Join_tables["Attribute"] == iunique)
                        & (Join_tables["Value"] == 0),
                        "Base",
                    ] = "Yes"

        Join_tables["Calculated_AMCE"] = np.round(
            2
            * (
                (
                    (np.exp(Join_tables["Value"].values))
                    / (1 + np.exp(Join_tables["Value"].values))
                )
                - 0.5
            ),
            2,
        )
        Join_tables["Calculated_Standard_Error"] = np.round(
            (2 * Join_tables["St-Err"].values * np.exp(Join_tables["Value"].values))
            / (1 + np.exp(Join_tables["Value"].values)) ** 2,
            2,
        )

        Join_tables = Join_tables.merge(HB_data, on=["Attribute", "Levels"], how="left")
        Join_tables.fillna(0, inplace=True)
        Join_tables["Reported_AMCE"] = np.round(
            2
            * (
                (
                    (np.exp(Join_tables["Reported Value"].values))
                    / (1 + np.exp(Join_tables["Reported Value"].values))
                )
                - 0.5
            ),
            2,
        )
        Join_tables["Reported_Standard_Error"] = np.round(
            (
                2
                * Join_tables["Reported St-Err"].values
                * np.exp(Join_tables["Reported Value"].values)
            )
            / (1 + np.exp(Join_tables["Reported Value"].values)) ** 2,
            2,
        )
        Join_tables = Join_tables[[
            "Attribute",
            "Levels",
            "Base",
            "Value",
            "St-Err",
            "P-value",
            "T-value",
            "Reported Value",
            "Reported St-Err",
            "Calculated_AMCE",
            "Calculated_Standard_Error",
            "Reported_AMCE",
            "Reported_Standard_Error",
        ]]

        Feature_table = deepcopy(Join_tables)
        column_values = Feature_table["Attribute"]
        filter_list = Unique_attributes
        mask = column_values.isin(filter_list)
        Feature_table = Feature_table[mask]
        Feature_table = Feature_table[Feature_table["Base"] == "No"]
        Spearmen_correlation_AMCE = spearmanr(
            Feature_table["Calculated_AMCE"].values,
            Feature_table["Reported_AMCE"].values,
        ).correlation
        Spearmen_correlation_Beta = spearmanr(
            Feature_table["Value"].values, Feature_table["Reported Value"].values
        ).correlation

        Num_Active_parm = int(len(Feature_table["Calculated_AMCE"].values))

        APE = []
        True_AMCE = Feature_table["Reported_AMCE"].values
        for v1, v2 in zip(
            Feature_table["Calculated_AMCE"].values,
            Feature_table["Reported_AMCE"].values,
        ):
            per_err = v1 - v2
            per_err = abs(per_err)
            APE.append(per_err)
        MAPE_AMCE = sum(APE) / len(APE)
        MAPE_AMCE = MAPE_AMCE / np.std(True_AMCE)
        MAPE_AMCE = round(MAPE_AMCE, 2)

        APE = []
        True_beta = Feature_table["Reported Value"].values
        for v1, v2 in zip(
            Feature_table["Value"].values, Feature_table["Reported Value"].values
        ):
            per_err = v1 - v2
            per_err = abs(per_err)
            APE.append(per_err)

        MAPE_Beta = sum(APE) / len(APE)
        MAPE_Beta = MAPE_Beta / np.std(True_beta)
        MAPE_Beta = round(MAPE_Beta, 2)

        Beta_LL = Feature_table["Value"].values - 1.96 * Feature_table["St-Err"].values
        Beta_UL = Feature_table["Value"].values + 1.96 * Feature_table["St-Err"].values

        LL_Beta_UL = np.hstack((
            Beta_LL[:, np.newaxis],
            Feature_table["Reported Value"].values[:, np.newaxis],
            Beta_UL[:, np.newaxis],
        ))

        Coverage_Prob = np.sum(
            (
                (LL_Beta_UL[:, 0] <= LL_Beta_UL[:, 1])
                & (LL_Beta_UL[:, 1] <= LL_Beta_UL[:, 2])
            ).astype(int)
        ) / len(Beta_LL)
        Coverage_Prob = round(Coverage_Prob * 100, 2)
        Calculated_Reported_Beta = np.hstack((
            Feature_table["Value"].values[:, np.newaxis],
            Feature_table["Reported Value"].values[:, np.newaxis],
        ))
        Compare_Sign = np.sum(
            (
                np.sign(Calculated_Reported_Beta[:, 0])
                == np.sign(Calculated_Reported_Beta[:, 1])
            ).astype(int)
        ) / len(Calculated_Reported_Beta)
        Compare_Sign = round(Compare_Sign * 100, 2)

        print(
            "Number of Active Parameters                                              "
            f"  : {Num_Active_parm}"
        )
        print(
            "Spearment Correlation for Feature Importance                             "
            f"  : {Spearmen_correlation_AMCE:.2f}"
        )
        print(
            "Spearment Correlation for Betas                                          "
            f"  : {Spearmen_correlation_Beta:.2f}"
        )
        print(
            "Standard Deviation Normalized Mean Absolute Error for Feature Importance "
            f"  : {MAPE_AMCE:.2f}"
        )
        print(
            "Standard Deviation Normalized Mean Absolute Error for Betas              "
            f"  : {MAPE_Beta:.2f}"
        )
        print(
            "Coverage Probability for Betas                                           "
            f"  : {Coverage_Prob:.2f}%"
        )
        print(
            "Percentage of Parameters with same Calculated & Reported Sign            "
            f"  : {Compare_Sign:.2f}%"
        )
    Orignal_Beta = np.zeros((len(Attribute_Order), 1))
    for level_num, att_level in enumerate(Attribute_Order):
        temp = Join_tables.loc[
            Join_tables["Levels"] == att_level, "Reported Value"
        ].values
        Orignal_Beta[level_num, 0] = temp

    Share_Calc = Get_Elasticity(Main_data, xComplete, MNP_Model)
    Share_Repo = Get_Elasticity(Main_data, Orignal_Beta, MNP_Model)

    Similarity_mat = matrix_similarity_measures(Share_Calc, Share_Repo)
    print(
        tabulate(
            Join_tables,
            headers="keys",
            tablefmt="psql",
            floatfmt=(
                ".0f",
                ".0f",
                ".0f",
                ".3f",
                ".2f",
                ".4f",
                ".2f",
                ".3f",
                ".3f",
                ".3f",
                ".3f",
                ".3f",
                ".3f",
            ),
            showindex=False,
        )
    )
    return (
        Join_tables,
        Spearmen_correlation_AMCE,
        Spearmen_correlation_Beta,
        MAPE_AMCE,
        MAPE_Beta,
        Coverage_Prob,
        Compare_Sign,
        Num_Active_parm,
        Similarity_mat,
    )


def matrix_similarity_measures(A, B):
    similarities = {}

    # 1. Frobenius Norm (normalized) : Good for overall structural similarity
    frob_norm = np.linalg.norm(A - B, "fro")
    max_possible_frob = np.sqrt(np.sum(np.maximum(A**2, B**2)))
    similarities["Frobenius_similarity"] = round(1 - (frob_norm / max_possible_frob), 3)

    # 2. Manhattan Distance (normalized) : Sensitive to all differences
    manhattan_dist = np.sum(np.abs(A - B))
    max_possible_manhattan = np.sum(np.maximum(np.abs(A), np.abs(B)))
    similarities["Manhattan_similarity"] = round(
        1 - (manhattan_dist / max_possible_manhattan), 3
    )

    # 6. Spectral Norm Similarity
    diff_spectral_norm = np.linalg.norm(A - B, ord=2)
    max_spectral_norm = max(np.linalg.norm(A, ord=2), np.linalg.norm(B, ord=2))
    similarities["Spectral_similarity"] = round(
        1 - (diff_spectral_norm / max_spectral_norm), 3
    )

    return similarities
