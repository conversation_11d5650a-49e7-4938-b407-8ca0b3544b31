import json
import os
from datetime import datetime
from typing import Dict

import firebase_admin
from firebase_admin import credentials, firestore
from supabase import Client, create_client

from app.core.global_config import settings
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)
supabase_client = create_client(settings.SUPABASE_URL, settings.SUPABASE_SERVICE_KEY)


def get_supabase_client() -> Client:
    return supabase_client


def check_database_connection():
    """Verify the Supabase database connection."""
    try:
        supabase_client.table("ipums").select("state").limit(1).execute()
        logger.info("Database connection successful.")
        return True
    except Exception as e:
        logger.error(f"Database connection failed. Error: {e}")
        raise


# TODO: refactor and use class for firebase connection. Use appropriate methods to the class.
def get_firebase_collection(coll_name: str):
    if not firebase_admin._apps:
        cred = credentials.Certificate(
            json.loads(os.getenv("FIREBASE_CONFIG_WHY_EARTH"))
        )
        firebase_admin.initialize_app(cred)
    firebase_client = firestore.client()
    return firebase_client.collection(coll_name)


def store_traits(traits_data: Dict):
    """
    Stores traits from the given dictionary into Firestore. The dictionary is expected to have a 'traits' key,
    which is a list of trait dictionaries.

    Args:
        traits_data (Dict): A dictionary containing a "traits" key associated with a list of trait dictionaries.
                            Example:
                            {
                                "traits": [
                                    {
                                        "long_description": "...",
                                        "measurement_type": "Nominal",
                                        "ordinal_rank": None,
                                        "set_type": "Technical Skills",
                                        "short_description": "Novice_Technical_Skills",
                                        "type": "work_skill"
                                    },
                                    ...
                                ]
                            }

    Returns:
        Dict: A dictionary containing the results of the Firestore write operations.
    """
    # Extract the traits list from the input dictionary
    traits = traits_data.get("traits", [])

    if not firebase_admin._apps:
        cred = credentials.Certificate(
            json.loads(os.getenv("FIREBASE_CONFIG_WHY_EARTH"))
        )
        firebase_admin.initialize_app(cred)
    firebase_client = firestore.client()

    collection = firebase_client.collection("traits_uploads")
    results = []

    # Generate a unique base upload_id prefix for all traits in this batch
    # This ensures traits uploaded at the same time have a related timestamp
    upload_prefix = datetime.utcnow().strftime("%Y%m%d%H%M%S")

    for index, trait in enumerate(traits):
        # Create a unique document ID for this trait
        doc_id = f"{upload_prefix}_{index}"
        doc_ref = collection.document(doc_id)

        # Add a timestamp for when this trait was uploaded
        trait["uploaded_at"] = datetime.utcnow().isoformat()

        try:
            doc_ref.set(trait)
            results.append({
                "trait_index": index,
                "message": "Trait uploaded successfully.",
                "document_path": doc_ref.path,
            })
        except Exception as e:
            results.append({
                "trait_index": index,
                "message": "Failed to upload trait.",
                "error": str(e),
            })

    return {"results": results}
