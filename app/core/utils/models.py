from typing import Literal

from pydantic import BaseModel


class SurveyTask(BaseModel):
    task_number: int
    persona_traits: str
    persona_id: int
    options: dict[str, list[str]]
    prompt_context: str
    year: str
    country: str
    study_subject: str
    why_prompt: str
    target_behavior: str
    optimization_context: str
    context: str
    dependent_variable: str
    none_choice_instruction: str
    wording_for_study_context: str
    response_format: str
    probabilistic_response_instructions: str
    survey_time_proportion: str
    guidance_section: str


class SurveyChoice(BaseModel):
    selected_option: Literal["A", "B", "C", "D", "E", "F", "G"]


class SurveyResponse(BaseModel):
    selected_option: list
    task_number: int
    persona_id: int
    decision_strategy: str | None = None
    llm_chosen: int
    reason: str


class SurveyChoiceResponse(BaseModel):
    selected_option: str | dict
    decision_strategy: Literal["C", "NC", "H", "I"]
    reason: str
    llm_choice: str | None = None
