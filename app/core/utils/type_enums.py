from enum import Enum


class PromptType(str, Enum):
    """
    Enum representing the type of prompts used in experiment designs.
    """

    GENERIC = "generic"  # no price, no brand
    SERVICE = "service"  # price, no brand
    PRODUCT = "product"  # price, brand
    EXPERIENCE = "experience"  # no price, no brand
    POLICY = "policy"  # no price, no brand


class LLMModel(str, Enum):
    """
    Enum representing the llm model types used in the experiments.
    """

    # OpenAI Models
    GPT3 = "gpt3"
    GPT4 = "gpt4"
    GPT3_INSTRUCT = "gpt3-instruct"
    GPTo1 = "gpto1"
    GPTo3 = "gpto3"
    # Azure Models
    AZURE_GPT4 = "azure-openai-gpt4"
    AZURE_GPT4O_MINI = "azure-openai-gpt4o-mini"
    AZURE_GPT35 = "azure-openai-gpt35"
    AZURE_GPTO3_MINI = "azure-openai-gpto3-mini"
    AZURE_COMPLETIONS = "azure-openai-completions"
    # GCP Models
    GCP_HAIKU = "gcp-haiku"
    GCP_GEMINIFLASH = "gcp-flash"
    # GCP_OPUS = "gcp-opus" # disabled: resource exhausted 429
    GCP_SONNET = "gcp-sonnet"
    GCP_GEMINI = "gcp-gemini"
    # GCP_LLAMA = "gcp-llama"
    # Cohere Models
    COHERE = "cohere"
    # Anthropic/claude Models
    OPUS = "opus"
    SONNET = "sonnet"
    HAIKU = "haiku"
    # Google Models
    GEMINI = "gemini"
    # Perplexity Models
    PPLXTY_CHAT_MODEL = "sonar"
    PPLXTY_ONLINE_MODEL = "llama-3.1-sonar-large-128k-online"


class ResearchReportType(Enum):
    ResearchReport = "research_report"
    ResourceReport = "resource_report"
    OutlineReport = "outline_report"
    CustomReport = "custom_report"
    DetailedReport = "detailed_report"
    SubtopicReport = "subtopic_report"


class ResearchToneType(Enum):
    Objective = "Objective (impartial and unbiased presentation of facts and findings)"
    Formal = (
        "Formal (adheres to academic standards with sophisticated language and"
        " structure)"
    )
    Analytical = (
        "Analytical (critical evaluation and detailed examination of data and theories)"
    )
    Persuasive = (
        "Persuasive (convincing the audience of a particular viewpoint or argument)"
    )
    Informative = (
        "Informative (providing clear and comprehensive information on a topic)"
    )
    Explanatory = "Explanatory (clarifying complex concepts and processes)"
    Descriptive = (
        "Descriptive (detailed depiction of phenomena, experiments, or case studies)"
    )
    Critical = (
        "Critical (judging the validity and relevance of the research and its"
        " conclusions)"
    )
    Comparative = (
        "Comparative (juxtaposing different theories, data, or methods to highlight"
        " differences and similarities)"
    )
    Speculative = (
        "Speculative (exploring hypotheses and potential implications or future"
        " research directions)"
    )
    Reflective = (
        "Reflective (considering the research process and personal insights or"
        " experiences)"
    )
    Narrative = (
        "Narrative (telling a story to illustrate research findings or methodologies)"
    )
    Humorous = (
        "Humorous (light-hearted and engaging, usually to make the content more"
        " relatable)"
    )
    Optimistic = "Optimistic (highlighting positive findings and potential benefits)"
    Pessimistic = (
        "Pessimistic (focusing on limitations, challenges, or negative outcomes)"
    )
