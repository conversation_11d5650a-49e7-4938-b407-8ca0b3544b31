import sys
from pathlib import Path

from loguru import logger
from pydantic_settings import BaseSettings

from app.core.global_config import settings


class LogConfig(BaseSettings):
    LEVEL: str = "INFO"
    CONSOLE_FORMAT: str = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SS}</green> | <level>{level: <4}</level> |"
        " <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> -"
        " <level>{message}</level>"
    )
    FILE_FORMAT: str = (
        "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} |"
        " {message}"
    )
    FILE_PATH: Path = Path(f"{settings.PROJECT_ROOT_DIRECTORY}/app/logs/app.log")
    ROTATION: str = "500 MB"
    RETENTION: str = "14 days"
    COMPRESSION: str = "gz"
    ENQUEUE: bool = True

    class Config:
        env_prefix = "LOG_"


class AppLogger:
    def __init__(self):
        self.config = LogConfig()
        self.setup()

    def setup(self):
        logger.remove()  # Remove default handler

        # Console handler
        logger.add(
            sys.stdout,
            format=self.config.CONSOLE_FORMAT,
            level=self.config.LEVEL,
            enqueue=self.config.ENQUEUE,
        )

        # File handler
        logger.add(
            self.config.FILE_PATH,
            format=self.config.FILE_FORMAT,
            level=self.config.LEVEL,
            rotation=self.config.ROTATION,
            retention=self.config.RETENTION,
            compression=self.config.COMPRESSION,
            enqueue=self.config.ENQUEUE,
        )

    def get_logger(self, name: str = None):
        return logger.bind(module=name) if name else logger


app_logger = AppLogger()
