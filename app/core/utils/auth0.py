import os
import time

import requests
from requests.exceptions import HTTPError


class Auth0:
    def __init__(self):
        self.auth0_domain = os.getenv("AUTH0_DOMAIN")
        self.auth0_audience = os.getenv("AUTH0_AUDIENCE")
        self.client_id = os.getenv("AUTH0_BACKEND_CLIENT_ID")
        self.client_secret = os.getenv("AUTH0_BACKEND_CLIENT_SECRET")
        self.management_api_token = None
        self.token_expires_at = None

    def get_management_api_token(self):
        """
        Retrieves and returns an Auth0 management API token. If an existing token is still valid,
        it returns the current token. Otherwise, it requests and stores a new token from Auth0.
        """
        if (
            self.management_api_token
            and self.token_expires_at
            and self.token_expires_at > time.time()
        ):
            return self.management_api_token

        url = f"https://{self.auth0_domain}/oauth/token"
        headers = {"content-type": "application/json"}
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "audience": self.auth0_audience,
            "grant_type": "client_credentials",
        }
        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            token_data = response.json()
            self.management_api_token = token_data["access_token"]
            self.token_expires_at = time.time() + token_data["expires_in"]
            return self.management_api_token
        except HTTPError as e:
            raise HTTPError(f"Error getting management API token: {e.response.text}")

    def assign_role(self, user_id, role_id):
        """
        Assigns a role to a user.
        """
        return self.update_role(user_id, role_id, "POST")

    def remove_role(self, user_id, role_id):
        """
        Removes a role from a user.
        """
        return self.update_role(user_id, role_id, "DELETE")

    def update_role(self, user_id, role_id, method):
        """
        Assigns or removes a role from a user.

        Allowed methods: "POST" (assign role), "DELETE" (remove role)
        """
        url = f"https://{self.auth0_domain}/api/v2/users/{user_id}/roles"
        management_api_token = self.get_management_api_token()
        headers = {
            "content-type": "application/json",
            "authorization": f"Bearer {management_api_token}",
        }
        payload = {"roles": [role_id]}
        try:
            response = requests.request(method, url, json=payload, headers=headers)
            response.raise_for_status()
            return response.ok
        except HTTPError as e:
            raise HTTPError(
                f"Error updating role for user {user_id}: {e.response.text}"
            )

    def get_role(self, user_id):
        """
        Retrieves the role of a user.
        """
        url = f"https://{self.auth0_domain}/api/v2/users/{user_id}/roles"
        management_api_token = self.get_management_api_token()
        headers = {
            "content-type": "application/json",
            "authorization": f"Bearer {management_api_token}",
        }
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except HTTPError as e:
            raise HTTPError(f"Error getting role for user {user_id}: {e.response.text}")

    def get_user_info(self, user_id):
        """
        Retrieves user information.
        """
        url = f"https://{self.auth0_domain}/api/v2/users/{user_id}"
        management_api_token = self.get_management_api_token()
        headers = {
            "content-type": "application/json",
            "authorization": f"Bearer {management_api_token}",
        }
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except HTTPError as e:
            raise HTTPError(
                f"Error getting user information for user {user_id}: {e.response.text}"
            )


auth0_client = Auth0()
