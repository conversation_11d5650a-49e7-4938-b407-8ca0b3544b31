import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

from app.core.global_config import settings
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)


s3_client = boto3.client(
    "s3",
    region_name=settings.AWS_REGION,
    aws_access_key_id=settings.AWS_S3_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
)


def download_file_from_s3(
    bucket_name: str, object_name: str, local_file_path: str
) -> None:
    """
    Download an image from S3 bucket to local file system.
    Args:
        bucket_name: name of the S3 bucket to download from.
        object_name: name of the object in S3 bucket to download
        local_file_path: local file path to save the downloaded image

    Returns: None
    """
    logger.info(
        f"Downloading {object_name} to {local_file_path} from {bucket_name} S3 bucket."
    )
    try:
        s3_client.download_file(bucket_name, object_name, local_file_path)
        logger.info(
            f"Image successfully downloaded from {bucket_name}/{object_name} to"
            f" {local_file_path}"
        )

    except FileNotFoundError:
        logger.error(f"File '{local_file_path}' not found.")
    except NoCredentialsError:
        logger.error("Credentials not available.")
    except PartialCredentialsError:
        logger.error("Incomplete credentials provided.")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
