import base64
from io import StringIO

import pandas as pd
import requests

from app.core.global_config import settings
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)


def get_transcription_csv_from_github(hb_folder: str):
    """Fetch the raw experiment definition JSON from GitHub."""
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/{settings.TRANSCRIPTIONS_PATH}/{hb_folder}"
    headers = {
        "Authorization": f"token {settings.GITHUB_PAT}",
        "Accept": "application/json",
    }

    response = requests.get(github_api_url, headers=headers)

    if response.status_code != 200:
        raise Exception(f"Failed to access GitHub folder: {hb_folder}")

    contents = response.json()
    for file_info in contents:
        if file_info["name"].endswith(".csv"):
            file_response = requests.get(file_info["download_url"], headers=headers)
            if file_response.status_code == 200:
                csv_data = StringIO(file_response.text)
                df = pd.read_csv(csv_data, index_col=0, encoding="utf-8")
                return df

    raise Exception(f"No CSV file (HB AMCE) found in GitHub folder: {hb_folder}")


def file_exists_in_github(file_name: str):
    """Check if a file exists in the GitHub folder."""
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/{settings.TRANSCRIPTIONS_PATH}/transcriptions_consolidated_results"

    headers = {
        "Authorization": f"token {settings.GITHUB_PAT}",
        "Accept": "application/json",
    }
    response = requests.get(github_api_url, headers=headers)
    if response.status_code == 200:
        for file_info in response.json():
            if file_info["name"] == f"{file_name}.csv":
                return True
    return False


def check_folder_exists_in_github(folder_name: str):
    """Check if a folder exists in the GitHub folder."""
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/{settings.TRANSCRIPTIONS_PATH}/{folder_name}"

    headers = {
        "Authorization": f"token {settings.GITHUB_PAT}",
        "Accept": "application/json",
    }
    response = requests.get(github_api_url, headers=headers)
    if response.status_code == 200:
        return True
    return False


def write_file_to_github(df: pd.DataFrame, file_name: str):
    """
    Create a new CSV file in the transcriptions_consolidated_results folder.
    """

    folder = "transcriptions_consolidated_results"
    filename = f"{file_name}.csv"
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/transcriptions/{folder}/{filename}"

    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {settings.GITHUB_PAT}",
        "X-GitHub-Api-Version": "2022-11-28",
    }
    data = {
        "message": f"Create {filename}",
        "commiter": {
            "name": "Subconscious AI API - Human Baselines",
            "email": "<EMAIL>",
        },
        "content": base64.b64encode(df.to_csv(index=False).encode("utf-8")).decode(
            "utf-8"
        ),
        "branch": "main",
    }

    try:
        response = requests.put(github_api_url, headers=headers, json=data)
        if response.status_code != 201:
            error_message = response.json().get("message", "")
            raise Exception(
                f"Failed to create file. Status code: {response.status_code}, "
                f"Response: {error_message}"
            )

        return {"status": "success", "message": f"File {filename} created in GitHub"}

    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to write file to GitHub: {str(e)}")
