import json
import os
import traceback
import uuid
from typing import <PERSON>ple

import wandb
from fastapi import HTTPException
from sentry_sdk import capture_exception

from app.api.v1.schemas.runs import CleanedRunResponse
from app.core.global_config import settings
from app.core.utils.common import create_dir, prettify_model_name
from app.core.utils.logging import app_logger

log = app_logger.get_logger(__name__)


class HumanBaselineDefinitionError(Exception):
    """Custom exception for HumanBaselineDefinitionError"""


class MissingHumanBaselineDefinition(HumanBaselineDefinitionError):
    """Custom Exception if HB definition is missing in WandbAPI"""


class WandbAPI:
    def __init__(self, project: str = settings.WANDB_PROJECT):
        self.api = wandb.Api(api_key=os.getenv("WANDB_API_KEY"), timeout=30)
        self.entity = settings.WANDB_ENTITY
        self.project = project

    def flush(self):
        self.api.flush()

    def get_wandb_runs(self, filters: dict = None) -> list[dict]:
        try:
            runs_dicts = []
            runs = self.api.runs(
                f"{self.entity}/{self.project}", filters=filters, order="-created_at"
            )

            for run in runs:
                runs_dicts.append({
                    "id": run.id,
                    "name": run.name,
                    "state": run.state,
                    "tags": run.tags,
                    "created_at": run.created_at,
                    "config": self.reformat_configs(run.config),
                })
            return runs_dicts
        except Exception as e:
            capture_exception()
            raise Exception(f"Error getting wandb runs: {e}")

    def get_all_user_runs(self, user_id: str) -> list[CleanedRunResponse]:
        try:
            filters = {"config.user": {"$eq": user_id}}
            runs = self.api.runs(
                f"{self.entity}/{self.project}", filters=filters, order="-created_at"
            )
            return [
                CleanedRunResponse(
                    id=run.id,
                    name=run.name,
                    state=(
                        "running"
                        if run.state == "running"
                        else (
                            run.state
                            if run.config.get("experiment_design", {}).get(
                                "r_squared", None
                            )
                            else "failed"
                        )
                    ),
                    created_at=run.created_at,
                    why_prompt=run.config.get("experiment_design", {}).get(
                        "why_prompt", None
                    )
                    or run.config.get("experiment_design", {}).get(
                        "experimentor_why_question_prompt", None
                    ),
                    is_private=run.config.get("experiment_design", {}).get(
                        "is_private", None
                    ),
                    r_squared=run.config.get("experiment_design", {}).get(
                        "r_squared", None
                    ),
                    sample_size=run.config.get("experiment_design", {}).get(
                        "sample_size", None
                    ),
                    tasks_per_respondent=run.config.get("experiment_design", {}).get(
                        "tasks_per_respondent", None
                    ),
                    total_number_of_tasks=run.config.get("experiment_design", {}).get(
                        "total_number_of_tasks", None
                    ),
                    expr_llm_model=prettify_model_name(
                        run.config.get("experiment_design", {}).get(
                            "expr_llm_model", None
                        )
                    ),
                    confidence_level=run.config.get("experiment_design", {}).get(
                        "confidence_level", None
                    ),
                    experiment_type=run.config.get("experiment_design", {}).get(
                        "experiment_type", "conjoint"
                    ),
                )
                for run in runs
                if user_id == run.config.get("user_id")
                or not run.config.get("is_private")
            ]
        except Exception as e:
            capture_exception()
            raise Exception(f"Error getting wandb runs: {e}")

    def merge_config_dicts(self, original: dict, update: dict):
        """
        Recursively merge two dictionaries, including their nested dictionaries.
        The values from 'update' will overwrite those in 'original'.

        God this is slow :/
        """
        for key, value in update.items():
            if isinstance(value, dict) and key in original:
                if isinstance(original[key], dict):
                    self.merge_config_dicts(original[key], value)
                else:
                    original[key] = value
            else:
                original[key] = value
        return original

    def update_configs(self, run_id: str, config_updates: dict) -> dict:
        """Given a run id and a dictionary of configuration elements, updates the config for that run and returns the formatted configs

        Arguments:
            run_id {str} -- a run_id
            config_updates {dict} -- a dictionary of the configuration elements to change
        Returns:
            a formatted dictionary of all the configs for the run (including those not updated)
        """
        try:
            run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
            updated_config = self.merge_config_dicts(
                self.reformat_configs(run.config), config_updates
            )
            run.config = updated_config
            run.update()
            log.info(f"Updated config for run {run_id}")
            return self.reformat_configs(run.config)
        except Exception as e:
            capture_exception()
            log.error(f"Error updating wandb config: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error updating wandb config: {e}",
            )

    def reformat_configs(self, configs):
        updated_configs = {}
        try:
            for key in configs:
                if (
                    isinstance(configs[key], dict)
                    and key.startswith("experiment_")
                    and "wandb_id" in configs[key]
                ):
                    configs[key]["id"] = f"{key}_{configs['wandb_id']}"
                    if (
                        "Experiment_Design" in configs[key]
                        and "personas" in configs[key]["Experiment_Design"]
                    ):
                        configs[key]["persona_keys"] = list(
                            configs[key]["Experiment_Design"]["personas"].keys()
                        )
                else:
                    updated_configs[key] = configs[key]

            return updated_configs
        except Exception as e:
            capture_exception()
            log.error(f"Error reformatting configs: {e}")
            return configs

    def get_wandb_run_details(self, run_id: str) -> dict | HTTPException:
        try:
            # First try the default project
            try:
                run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
            except Exception:
                # If not found in default project, search all projects
                run = None
                for project in self.api.projects(self.entity):
                    try:
                        run = self.api.run(f"{self.entity}/{project.name}/{run_id}")
                        break
                    except:
                        continue

                if not run:
                    raise Exception(f"Run {run_id} not found")

            summary = run.summary._json_dict
            configs = self.reformat_configs(run.config)
            files_list = []
            artifacts_list = []

            try:

                for artifact in run.logged_artifacts():
                    artifact_info = {
                        "name": artifact.name,
                        "type": artifact.type,  # Type of the artifact
                    }
                    artifacts_list.append(artifact_info)

            except wandb.errors.CommError as e:
                print(f"Communication error with W&B API: {e}")
            except Exception as e:
                print(f"An error occurred: {e}")

            for file in run.files():
                if file.name.endswith("wandb_manifest.json") or file.name.endswith(
                    ".yaml"
                ):
                    continue
                files_list.append(file.name)

            return {
                "summary": summary,
                "configs": configs,
                "files": files_list,
                "artifacts": artifacts_list,
                "run_state": run.state,
                "run_id": run.id,
                "run_name": run.name,
                "tags": run.tags,
                "start_time": run.created_at,
            }
        except Exception as e:
            capture_exception()
            log.error(f"Error getting wandb run details: {e}")
            raise HTTPException(
                status_code=404,
                detail=f"Error(get_wandb_run_details): {e}",
            )

    def get_wandb_run_file(self, run_id: str, file_name: str) -> str:
        try:
            # First try the default project
            try:
                run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
            except Exception:
                # If not found in default project, search all projects
                run = None
                for project in self.api.projects(self.entity):
                    try:
                        run = self.api.run(f"{self.entity}/{project.name}/{run_id}")
                        break
                    except:
                        continue

                if not run:
                    raise Exception(f"Run {run_id} not found")
            file = run.file(file_name)
            file.download("downloads", replace=True)
            file_path = f"downloads/{file_name}"
            log.info(f"Downloaded file {file_name} for run {run_id}")
            return file_path
        except Exception as e:
            capture_exception()
            log.error(f"Error getting wandb run file: {e}")

    def get_wandb_run_artifact(
        self, file_name: str
    ) -> Tuple[bool, str, str] | Tuple[bool, str]:
        try:
            """
            File Types: JSON, CSV, Images
            """
            # First try the default project
            try:
                artifact = self.api.artifact(
                    f"{self.entity}/{self.project}/{file_name}:v0"
                )
            except Exception:
                # If not found in default project, search all projects
                artifact = None
                for project in self.api.projects(self.entity):
                    try:
                        artifact = self.api.artifact(
                            f"{self.entity}/{project.name}/{file_name}:v0"
                        )
                        break
                    except:
                        continue

                if not artifact:
                    raise Exception(f"Artifact {file_name} not found")
            artifact_directory = artifact.download(
                root=f"{settings.EXPERIMENT_DATA_FOLDER_NAME}/{str(uuid.uuid4())}"
            )
            only_files = [
                f
                for f in os.listdir(artifact_directory)
                if os.path.isfile(f"{artifact_directory}/{f}")
            ]
            file_path = f"{artifact_directory}/{only_files[0]}"

            if "media" in os.listdir(artifact_directory):
                with open(file_path) as file:
                    file_data = json.load(file)
                    if "_type" in file_data and file_data["_type"] == "image-file":
                        file_path = os.path.join(artifact_directory, file_data["path"])
            return True, artifact_directory, file_path
        except Exception as e:
            capture_exception()
            log.error(f"Error getting wandb run artifact: {e}")
            return False, str(e)

    def get_wandb_experiment_definition(self, run_id: str) -> str:
        """
        Downloads the experiment definition artifact from WandB
        returns the file path of the downloaded artifact
        """
        try:
            file_name = f"experiment_definition_{run_id}"
            main_directory = f"{settings.EXPERIMENT_DATA_FOLDER_NAME}/{run_id}"
            artifact = self.api.artifact(
                f"why-earth/{settings.HUMAN_BASELINE_PROJECT}/{file_name}:v0"
            )
            create_dir(main_directory)
            artifact_directory = artifact.download(root=main_directory)
            log.info(f"Downloaded experiment definition for run {run_id}")
            return f"{artifact_directory}/{file_name}"

        except Exception as e:
            err_msg = (
                f"Error(wandb_experiment_definition): {e}\nTraceback:"
                f" {traceback.format_exc()}"
            )
            log.error(
                err_msg,
                extra={
                    "http.request.body.content": err_msg,
                    "labels": {
                        "wandb_run_id": f"{run_id}",
                    },
                },
            )
            capture_exception()
            raise MissingHumanBaselineDefinition(
                f"Human Baseline Definition with id: {run_id} is missing in WandbAPI"
            )

    def get_wandb_experiment_model_output(
        self,
        run_id: str,
        folder_name: str,
        model_name: str = "clm",
    ) -> str:
        """
        Downloads the experiment model output artifact
        based on run ID and experiment title from WANDB

        returns the file path of the downloaded artifact
        """

        try:
            extension = ".table.json"
            if model_name in ["clm", "ols"]:
                file_name = f"{model_name}_experiment_model_output_{run_id}"
                main_directory = f"{folder_name}/{run_id}"
                api = wandb.Api(timeout=None)
                artifact = api.artifact(
                    f"why-earth/{settings.HUMAN_BASELINE_PROJECT}/{file_name}:v0"
                )
                create_dir(main_directory)
                artifact_directory = artifact.download(root=main_directory)
                file_path = f"{artifact_directory}/{file_name}{extension}"
                return file_path
            else:
                raise Exception(
                    f"Model name '{model_name}' is invalid - valid options are 'clm'"
                    " and 'ols'"
                )
        except Exception as e:
            log.error(f"Error getting wandb experiment model output: {e}")
            capture_exception()

    def delete_wandb_run(self, run_id: str) -> Tuple[bool, str]:
        """
        Deletes a run from W&B.
        """
        try:
            run = self.api.run(f"{self.entity}/{self.project}/{run_id}")
            run.delete()
            log.info(f"Successfully deleted run {run_id}")
            return True, f"Run {run_id} deleted successfully"
        except Exception as e:
            error_msg = f"Error deleting run {run_id}: {str(e)}"
            log.error(error_msg)
            capture_exception()
            return False, error_msg
