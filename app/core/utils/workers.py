import json
import os
import time

from celery import Celery
from celery.exceptions import SoftTimeLimitExceeded
from kombu import Queue

from app.api.v1.helpers.experiments import ExperimentRunner
from app.api.v1.schemas.experiments import Experiment
from app.core.controller import execute
from app.core.experiment_config import ExperimentConfig
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)

celery = Celery(
    __name__,
    broker=os.getenv("CELERY_BROKER_URL", "redis://localhost:6379"),
    backend=os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379"),
    broker_connection_retry_on_startup=True,
)
celery.conf.task_queues = (
    Queue("high_priority", queue_arguments={"x-max-priority": 10}),
    Queue("default", queue_arguments={"x-max-priority": 5}),
)
celery.conf.task_default_queue = "default"


@celery.task(name="worker_ping")
def worker_ping():
    logger.info("Executing worker_ping task")
    time.sleep(10)
    return True


@celery.task(
    name="create_experiments",
    bind=True,
    max_retries=3,
    soft_time_limit=25 * 60,  # Stops experiment after 25 minutes
)
def create_celery_experiment(
    self, *, experiment_json: json, wandb_id: str, wandb_run_name: str, user_id: str
):
    logger.info(f"Starting experiment creation for wandb_id: {wandb_id}")
    experiment_conf = None
    try:
        # Experiment needed to be converted into json str for Celery queue - here, we convert it back
        experiment = Experiment.model_validate_json(json.dumps(experiment_json))

        experiment_conf = ExperimentConfig(
            experiment=experiment,
            use_api=True,
            use_batching=True,
        )

        tags = [
            f"exp_model_{experiment.expr_llm_model.value}",
            f"dv_model_{experiment.dv_llm_model.value}",
            f"lv_model_{experiment.levels_llm_model.value}",
        ]

        if experiment_conf.hb_run_id:
            experiment_conf.add_prefix_to_title("hb")
            tags.extend(["replication_study"])

        logger.info(f"Executing experiment for wandb_id: {wandb_id}")
        with ExperimentRunner(
            experiment_conf,
            user_id=user_id,
            tags=tags,
            wandb_id=wandb_id,
            wandb_name=wandb_run_name,
        ):
            execute(experiment_conf)

        return {"wandb_run_id": wandb_id, "wandb_run_name": wandb_run_name}

    except SoftTimeLimitExceeded:
        logger.error(
            f"Experiment with wandb_id {wandb_id} exceeded time limit "
            "(25 minutes) and was terminated"
        )
        if (
            experiment_conf
            and hasattr(experiment_conf, "wandb_run")
            and experiment_conf.wandb_run
        ):
            experiment_conf.wandb_run.mark_as_failed(reason="timeout")

        return {
            "wandb_run_id": wandb_id,
            "wandb_run_name": wandb_run_name,
        }
    except Exception as e:
        logger.error(
            f"Error in experiment creation for wandb_id {wandb_id}: {str(e)}",
            exc_info=True,
        )
        raise e


def enqueue_experiment_with_priority(
    experiment_json, wandb_id, wandb_run_name, user_id
):
    experiment_data = json.loads(experiment_json)
    queue_name = "default" if experiment_data.get("hb_run_id") else "high_priority"
    logger.info(f"Enqueueing experiment for wandb_id: {wandb_id} in {queue_name} queue")
    create_celery_experiment.apply_async(
        kwargs={
            "experiment_json": experiment_data,
            "wandb_id": wandb_id,
            "wandb_run_name": wandb_run_name,
            "user_id": user_id,
        },
        queue=queue_name,
        task_id=user_id + "_" + wandb_id,
    )


def get_queued_experiments_for_user(user_id):
    i = celery.control.inspect()

    reserved = i.reserved() or {}
    scheduled = i.scheduled() or {}

    user_experiments = []

    for worker_tasks in reserved.values():
        user_experiments.extend(
            task.get("kwargs", {})
            for task in worker_tasks
            if task.get("id", "").startswith(user_id)
        )
    for worker_tasks in scheduled.values():
        user_experiments.extend(
            task.get("kwargs", {})
            for task in worker_tasks
            if task.get("id", "").startswith(user_id)
        )

    user_experiments = sorted(
        user_experiments,
        key=lambda x: 0,  # Since we now only have kwargs, default to 0 for sorting
        reverse=True,
    )[:100]

    return user_experiments
