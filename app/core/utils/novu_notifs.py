import os

from novu.api import EventApi

from app.core.utils.logging import app_logger

log = app_logger.get_logger(__name__)

novu_api_url = os.getenv("NOVU_API_URL")
novu_api_key = os.getenv("NOVU_API_KEY")
event_api = EventApi(novu_api_url, novu_api_key)


async def trigger_novu_notification(subscriber_id, workflow_id, payload):
    try:
        trigger_response = event_api.trigger(
            name=workflow_id,
            recipients={
                "subscriberId": payload.get("email"),
                "email": payload.get("email"),
                "firstName": payload.get("firstname"),
                "lastName": payload.get("url"),
                "avatar": payload.get("prompt"),
            },
            payload=payload,
        )
        log.info("Notification sent successfully!")

        response_data = {
            "acknowledged": trigger_response.acknowledged,
            "status": trigger_response.status,
            "transaction_id": trigger_response.transaction_id,
        }

        return response_data

    except Exception as e:
        log.error(f"Error triggering notification: {e}")
        raise Exception("Error triggering notification")
