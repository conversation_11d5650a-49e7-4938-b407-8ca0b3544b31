import ast
import gc
import os
import sys
import time
from typing import Optional

import pandas as pd
import wandb
from langsmith import Client
from matplotlib import pyplot as plt
from pydantic import BaseModel
from requests import RequestException, post
from sentry_sdk import capture_exception
from wandb.sdk.wandb_run import Run

from app.core.global_config import settings
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)


class WandbRun(BaseModel):
    project: str = settings.WANDB_PROJECT
    entity: str = settings.WANDB_ENTITY
    config: dict = {}
    tags: Optional[list[str]] = []
    user_id: Optional[str] = ""
    id: Optional[str] = ""
    name: Optional[str] = ""
    run: Optional[Run] = None
    wandb_logged_artifacts_list: Optional[list] = []
    ls_client: Optional[Client] = None

    def __init__(self, **data):
        super().__init__(**data)
        logger.remove()
        logger.add(sys.stdout, level="INFO")
        self.initialize_run()

    def initialize_run(self):
        params = {
            "dir": f"{settings.EXPERIMENT_DATA_FOLDER_NAME}",
            "tags": self.tags,
            "project": self.project,
            "entity": self.entity,
            "config": self.config,
            "resume": "allow",
            "reinit": True,
        }
        if self.name:
            params["name"] = self.name
        if self.id:
            params["id"] = self.id

        max_reconnect_attempts = 3
        reconnect_attempt = 0

        while reconnect_attempt < max_reconnect_attempts:
            try:
                logger.info("Initializing WandB run...")
                self.run = wandb.init(**params)
                logger.info("WandB run initialized successfully.")
                self.ls_client = Client(api_key=os.getenv("LANGSMITH_API_KEY"))
                self.ls_client.create_project(project_name=self.id)
                logger.info(f"Langsmith project created successfully: {self.id}")
                break
            except Exception as e:
                reconnect_attempt += 1
                logger.error(
                    "WandB initialization failed (attempt"
                    f" {reconnect_attempt}/{max_reconnect_attempts}): {e}"
                )
                if reconnect_attempt < max_reconnect_attempts:
                    logger.warning("Waiting 10 seconds before trying to reconnect...")
                    time.sleep(10)
                else:
                    logger.critical("All reconnection attempts failed.")
                    capture_exception()
                    raise

        self.wandb_logged_artifacts_list = []
        self.run.config.update({"wandb_id": self.run.id, "wandb_name": self.run.name})
        self.name = self.run.name
        self.id = self.run.id

        self.send_webhook("started")

    def add_wandb_artifact(self, main_dir: str, artifact_name: str):
        self.wandb_logged_artifacts_list.append({f"{main_dir}": f"{artifact_name}"})

    def update_config(self, body: dict):
        self.run.config.update(body, allow_val_change=True)

    def finish_run(self):
        try:
            self.run.finish(exit_code=0, quiet=False)
            logger.info("Wandb run finished successfully.")
            self.send_webhook("finished")
        finally:
            gc.collect()

    def mark_as_failed(self, reason: str = "timeout"):
        """
        Mark an experiment as failed with a specific reason.

        Args:
            reason (str): The reason for failure (default: "timeout")
        """
        try:
            if self.run._is_finished:
                logger.info("Run is already finished.")
                return
            self.add_tags([f"{reason}_failure", "Invalid"])

            self.wandb_log({"failure_reason": reason})

            self.run.summary.update({"status": "failed", "failure_reason": reason})

            self.run.finish(exit_code=0, quiet=False)
            logger.info(f"Wandb run marked as failed due to {reason}.")

            self.send_webhook("failed")
        except Exception as e:
            logger.error(f"Error marking run as failed: {e}")
            capture_exception()
        finally:
            gc.collect()

    def wandb_log(self, log_dict: dict):
        logger.info(f"Logging {len(log_dict)} items to WandB")
        try:
            self.run.log(log_dict)
            for key in list(log_dict.keys()):
                logger.info(f"Logged {key}")
        except Exception as e:
            logger.error(f"Unexpected error during WandB logging: {e}")
            capture_exception()
            raise

    def wandb_log_artifact(
        self,
        file_path: str,
        file_type: str,
        artifact_main_directory: str,
        artifact_name: str,
        wandb_log_name: str = "",
        do_wandb_log: bool = True,
        file_extension: str = "",
    ):
        """
        General function for logging artifacts to WANDB - e.g. mapping files, experiment definition.
        Saves as an artifact for the experiment in WANDB

        The function reads the file based on "file_path" & "file_type" and uploads to the
        specified "artifact_main_directory" with the "artifact_name"

        do_wandb_log: If true it would log that artifact as well with wandb_log using "wandb_log_name" as key
        """
        if os.path.exists(file_path) and file_type in ["csv", "image", "any"]:
            results_artifact = wandb.Artifact(
                artifact_name, type=artifact_main_directory
            )
            results_obj = None
            try:
                if file_type == "csv":
                    with open(file_path, "r") as file:
                        results_csv = pd.read_csv(file)
                    results_obj = wandb.Table(dataframe=results_csv)
                    results_artifact.add(results_obj, artifact_name)
                elif file_type == "image":
                    with open(file_path, "rb") as file:
                        img = plt.imread(file)
                    results_obj = wandb.Image(img)
                    results_artifact.add(results_obj, artifact_name)
                elif file_type == "any":
                    results_artifact.add_file(
                        local_path=file_path, name=f"{artifact_name}{file_extension}"
                    )

                self.run.log_artifact(results_artifact)
                if do_wandb_log and results_obj:
                    self.wandb_log({f"{wandb_log_name}": results_obj})

                logger.info(
                    f"Logged {file_path} as {artifact_main_directory}/{artifact_name}"
                )
                self.wandb_logged_artifacts_list.append(
                    {f"{artifact_main_directory}": f"{artifact_name}"}
                )
                return True
            except Exception as e:
                logger.error(f"Error logging artifact: {e}")
                capture_exception()
                return False
            finally:
                results_artifact = None  # Help garbage collection
                gc.collect()
        else:
            logger.warning(f"File doesn't exist or invalid file type: {file_path}")
            return False

    def create_validity_tag(self):
        """Tags a run as invalid if it fails any of the validity checks."""
        if any("failure" in tag.lower() for tag in self.run.tags):
            self.run.tags += ("Invalid",)

    def add_tags(self, tag_list=None):
        if tag_list is None:
            tag_list = []
        for tag in tag_list:
            self.run.tags = self.run.tags + (tag,)

    def to_dict(self):
        """
        For serializing WandB Run
        """
        return {
            "id": self.id,
            "name": self.name,
            "project": self.project,
            "entity": self.entity,
            "user_id": self.user_id,
        }

    def send_webhook(self, state):
        """
        Send a webhook when the run is finished.
        """
        webhook_url = settings.WEBHOOK_URL
        run_config = {key: str(value) for key, value in self.run.config.items()}
        experiment_design_str = run_config.get("experiment_design", "{}")
        try:
            experiment_design = ast.literal_eval(experiment_design_str)
        except (ValueError, SyntaxError):
            experiment_design = {}

        data = {
            "run_id": self.run.id,
            "user_id": self.user_id,
            "run_name": self.run.name,
            "state": state,  # Can be "started", "finished", "failed"
            "project": self.run.project,
            "entity": self.run.entity,
            "wandb_logged_artifacts": self.wandb_logged_artifacts_list,
            "tags": self.run.tags,
            "config": run_config,
            "url": f"{os.getenv('AUTH0_BASE_URL')}insights/{self.run.id}",
            "why_prompt": experiment_design.get("why_prompt", ""),
        }

        if state == "finished":
            data["url"] = f"{os.getenv('AUTH0_BASE_URL')}insights/{self.run.id}"
        try:
            logger.info(f"Sending webhook to {webhook_url} with data: {data}")
            response = post(webhook_url, json=data)
            response.raise_for_status()
            logger.info(f"Webhook sent successfully! Response: {response.text}")
        except RequestException as e:
            logger.error(f"Error sending webhook: {e}")
            capture_exception()

    class Config:
        arbitrary_types_allowed = True
