import base64
import calendar
import os
import re
import shutil
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from functools import wraps
from multiprocessing import cpu_count
from typing import Any, Callable

import tiktoken
from fastapi.responses import JSONResponse
from openai import AzureOpenAI
from sentry_sdk import capture_exception
from yaspin import yaspin

from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)


def log(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            logger.info(
                f"Starting {func.__name__}\n\nInput({func.__name__}):"
                f" Args={args}\nKwargs={kwargs}"
            )
            result = func(*args, **kwargs)
            if "get_runs" not in func.__name__:
                logger.info(
                    f"Finished {func.__name__}\n\nOutput({func.__name__}):"
                    f" {result.body if type(result) is JSONResponse else result}"
                )
            else:
                logger.info(
                    f"Finished {func.__name__}\n\nOutput({func.__name__}): {result}"
                )
            return result

        except Exception as e:
            logger.error(f"Exception Error: {e}")
            raise e

    return wrapper


def str_keys(d: dict) -> dict:
    return {str(k): v for (k, v) in d.items()}


def get_value_from_list(array, key):
    for v in array:
        if v[0] == key:
            return v[1]


def is_reference_level(s: str):
    """
    checks if a string is a reference level
    """
    return bool(re.match(r"^Reference\d+$", s))


def create_dir(path: str):
    """
    creates directory if it doesn't already exist
    """
    try:
        if not os.path.exists(path):
            os.makedirs(path)
            print(f"Created: {path}")

    except Exception as e:
        print(f"Error Creating: {path} -> {e}")


def delete_local_dir(base_path: str, directory_name: str):
    """
    Used to clear local directories created in app repo if the use_local_storage is true
    """
    logger.info(f"Deleting local directory: {directory_name}")
    d_path = os.path.join(base_path, directory_name)
    if os.path.exists(d_path) and os.path.isdir(d_path):
        shutil.rmtree(d_path, ignore_errors=True)
        logger.info(f"Deleted: {d_path}")
    else:
        logger.warning(f"Does not Exist: {d_path}")


def delete_local_artifacts(artifact_path: str):
    """
    Deletes the local artifact from the local directory
    And its parent folder.
    """
    if os.path.isfile(artifact_path):
        os.remove(artifact_path)
        logger.info(f"Deleted artifact: {artifact_path}")
        folder_path = os.path.dirname(artifact_path)

        if not os.listdir(folder_path):
            os.rmdir(folder_path)
            logger.info(f"Deleted folder: {folder_path}")
        else:
            logger.info(f"File deleted, but folder not empty: {folder_path}")
    else:
        logger.info("File not found.")


def delete_subfolders(base_path: str):
    """
    Deletes all subdirectories within the given base_path, but keeps the base_path intact.
    """
    logger.info(f"Deleting subdirectories in: {base_path}")
    if os.path.exists(base_path) and os.path.isdir(base_path):
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                try:
                    shutil.rmtree(item_path)
                    logger.info(f"Deleted subdirectory: {item_path}")
                except Exception as e:
                    logger.error(f"Error deleting {item_path}: {e}")
        logger.info("Subdirectory deletion complete.")
    else:
        logger.warning(f"Base path does not exist or is not a directory: {base_path}")


def cleanup_old_wandb_runs(wandb_dir: str, days: int = 1):
    """
    Delete W&B run folders that are older than the specified number of days.

    Args:
    wandb_dir (str): Path to the W&B directory containing run folders.
    days (int): Number of days to keep. Folders older than this will be deleted.
    """
    try:
        if not os.path.exists(wandb_dir):
            logger.warning(f"W&B directory does not exist: {wandb_dir}")
            return

        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days)

        for run_folder in os.listdir(wandb_dir):
            run_path = os.path.join(wandb_dir, run_folder)
            if not os.path.isdir(run_path):
                continue

            if not run_folder.startswith("run-"):
                continue

            # Get the folder's modification time
            mod_time = datetime.fromtimestamp(os.path.getmtime(run_path))

            if mod_time < cutoff_time:
                logger.info(f"Deleting old W&B run folder: {run_path}")
                try:
                    shutil.rmtree(run_path)
                except Exception as e:
                    logger.error(f"Failed to delete W&B run folder {run_path}: {e}")

        logger.info("Cleanup of old W&B run folders completed.")
    except Exception as e:
        logger.error(f"Error during cleanup of old W&B run folders: {e}")
        capture_exception()


def get_max_workers() -> int:
    """
    Returns 2/3 of the number of CPUs
    """
    return int(cpu_count() * (2 / 3))


def with_spinner(desc: str = "Processing") -> Callable:
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            with yaspin(text=desc, timer=True) as sp:
                if "sp" in func.__code__.co_varnames:
                    kwargs["sp"] = sp
                try:
                    result = func(*args, **kwargs)
                    sp.ok()
                    return result
                except Exception as e:
                    sp.fail()
                    raise e

        return wrapper

    return decorator


def get_date():
    currentDay = datetime.now().day
    currentMonth = datetime.now().month
    currentYear = datetime.now().year
    month_eng = calendar.month_name[currentMonth]
    return month_eng + "_" + str(currentDay) + "_" + str(currentYear)


def chunk_list(lst, size):
    """Yield successive chunks from a list."""
    for i in range(0, len(lst), size):
        yield lst[i : i + size]


def get_enum_value(e: Enum):
    """
    Returns a list of all values from an enum class.
    """
    return e.value if isinstance(e, Enum) else e


def prettify_model_name(model: str | None) -> str | None:
    if model is None:
        return

    model = model.lower().replace("_", "-")

    mapping = {
        "gcp-geminiflash": "Gemini 2.0 Flash",
        "gcp-gemini": "Gemini 2.0",
        "gcp-sonnet": "Claude 3.5 Sonnet",
        "gcp-haiku": "Claude 3.5 Haiku",
        "gcp-llama": "LLaMA3.2",
        "gpt4": "GPT-4",
        "gpt-4": "GPT-4",
        "gpt-4o": "GPT-4o",
        "gpt-4o-mini": "GPT-4o mini",
        "azure-openai-gpt4": "GPT-4",
        "azure-gpt4": "GPT-4",
        "azure-gpt4-batch": "GPT-4",
        "cohere": "Cohere",
        "o3-mini": "o3-mini",
        "o1": "o1",
        "gpt-4.5": "GPT-4.5",
    }

    normalized = re.sub(r"[^a-z0-9\-]", "", model.strip())

    return mapping.get(normalized, model.title())


def count_tokens(text: str, model: str = "gpt-4") -> int:
    """
    Counts the number of tokens in the input text for the specified model.

    Args:
        text (str): The input text.
        model (str): The model name (e.g., "gpt-3.5-turbo", "gpt-4").

    Returns:
        int: The number of tokens.
    """
    encoding = tiktoken.encoding_for_model(model)
    return len(encoding.encode(text))


def convert_image_to_base64(image_path: str) -> str:
    """
    Converts an image to a base64 string.

    Args:
        image_path (str): Path to the image file.

    Returns:
        str: Base64 encoded string of the image.
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def summarize_image(img_base64, prompt):
    """This function will leverage GPT 4 to summarize the base64 encoded images"""
    api_key = os.getenv("AZURE_OPENAI_API_KEY1")
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

    client = AzureOpenAI(
        api_version="2024-02-01", api_key=api_key, azure_endpoint=endpoint
    )
    try:
        logger.info(f"Summarizing image...")
        msg = client.chat.completions.create(
            model="gpt-4ov2",
            messages=[{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"},
                    },
                ],
            }],
        )
        logger.info(f"Image summarized successfully.")
        return msg.choices[0].message.content
    except Exception as e:
        logger.error(e)
        pass
