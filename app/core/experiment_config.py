import os
import re
from typing import Optional

import pandas as pd
from pandas import DataFrame
from pydantic import BaseModel, model_validator

from app.api.v1.schemas.experiments import Experiment
from app.api.v2.schemas.latent_variables import ScoringType
from app.core.global_config import settings
from app.core.utils.common import create_dir
from app.core.utils.wandb_runs import WandbRun
from app.llm_prompt.prompt_executor import LCELPromptExecutor
from app.validation.validation import ValidationMetrics


class ExperimentConfig(Experiment):
    """
    Wrapper containing internally-applicable objects and parameters related to the experimental execution
    which do not need to be set within the Experiment request body
    """

    validator: Optional[ValidationMetrics] = ValidationMetrics()
    prompt_executor: Optional[LCELPromptExecutor] = None
    wandb_run: Optional[WandbRun | None] = None
    mappings: Optional[dict] = {}
    experiment_section_status: Optional[list] = []
    folder_names: Optional[dict] = {}
    file_paths: Optional[dict] = {}
    survey_summary: Optional[dict] = {}
    survey_results: Optional[pd.DataFrame | None] = None
    survey_results_labeled: Optional[pd.DataFrame | None] = None
    paper_reported_coefficients: Optional[pd.DataFrame | None] = None
    langsmith_sharelinks: Optional[dict] = None
    langsmith_total_token_data: Optional[dict] = None
    generate_mappings: Optional[bool] = False
    design_matrix: pd.DataFrame | None = None
    extract_decision_strategy: Optional[bool] = True
    predict_unknown_traits: Optional[bool] = False
    research_report: Optional[dict] = {}
    none_choice_instruction: Optional[str] = ""
    scoring_type: ScoringType = ScoringType.SOFT
    concept_analytics: dict = {}

    def __init__(self, experiment: Experiment, **data):
        if not experiment.title:
            experiment.title = self.get_title(experiment)
        experiment_config = experiment.model_dump()
        experiment_config.update(data)
        prompt_executor = LCELPromptExecutor(llm_model=experiment.expr_llm_model)
        super().__init__(**experiment_config, prompt_executor=prompt_executor)

    def get_settings_dict(self):
        # We want the output dict to only contain the Experiment object fields
        exp_fields = Experiment.model_fields
        return {field: getattr(self, field) for field in exp_fields}

    @model_validator(mode="after")
    def instantiate_prompt_executor(cls, values):
        if not values.prompt_executor:
            values["prompt_executor"] = LCELPromptExecutor(
                llm_model=values.expr_llm_model
            )
        return values

    def get_title(self, expr: Experiment):
        """
        Create the experiment title if none exists
        returns the first 50 characters of title
        """
        title = str(re.sub("\W+", "_", expr.why_prompt))
        return title[:50]

    def to_dict(self):
        """
        Outputs the params that can be used to generate a matching experiment
        """
        return {
            k: v.model_dump() if isinstance(v, BaseModel) else v
            for k, v in self.__dict__.items()
            if k
            not in [
                "wandb_run",
                "validator",
                "prompt_executor",
                "mappings",
                "survey_results",
                "file_paths",
                "survey_summary",
                "folder_names",
            ]
        }

    def create_experiment_dir(self, wandb_run_name: str, experiment_type: str):
        """
        Creates folders for logging/storing the results on local system
        """
        if experiment_type == "conjoint":
            experiment_title = self.title
        else:
            experiment_title = self.concept_description

        if settings.USE_LOCAL_STORAGE:
            create_dir(f"{settings.EXPERIMENT_DATA_FOLDER_NAME}/tmp")

        # Creates all local folders needed for local logging
        main_name = f"{settings.EXPERIMENT_DATA_FOLDER_NAME}/tmp/{wandb_run_name}_{experiment_title}"
        if experiment_type == "conjoint":
            self.folder_names["experiment_name_data"] = main_name
            self.folder_names["survey_summary_data"] = f"{main_name}/survey_summary"
            self.folder_names["mappings_data"] = f"{main_name}/mappings"
            self.folder_names["survey_results_data"] = f"{main_name}/survey_results"
            self.folder_names["design_matrix"] = f"{main_name}/design_matrix"
            self.folder_names["survey_results_labeled"] = (
                f"{main_name}/survey_results_labeled"
            )
            self.folder_names["experiment_def_data"] = f"{main_name}/experiment_def"
            self.folder_names["ols_data"] = f"{main_name}/ols"
            self.folder_names["clm_data"] = f"{main_name}/clm"
            self.folder_names["experiment_beta_amce"] = f"{main_name}/betas_amce"
            self.folder_names["paper_reported_coefficients"] = (
                f"{main_name}/paper_reported_coefficients"
            )
            self.folder_names["research_report"] = f"{main_name}/research_report"
            self.folder_names["amce_graph"] = f"{main_name}/amce_graph"
            self.folder_names["amce_table"] = f"{main_name}/amce_table"
            self.folder_names["experiment_langsmith_sharelinks"] = (
                f"{main_name}/langsmith_sharelinks"
            )

            for _, name in self.folder_names.items():
                create_dir(name)

            self.file_paths["survey_summary"] = os.path.join(
                self.folder_names["survey_summary_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-survey_summary-{wandb_run_name}.json",
            )

            self.file_paths["mappings"] = os.path.join(
                self.folder_names["mappings_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-mappings-{wandb_run_name}.json",
            )

            self.file_paths["research_report"] = os.path.join(
                self.folder_names["research_report"],
                str(experiment_title.replace(" ", "_"))
                + f"-research_report-{wandb_run_name}.json",
            )

            self.file_paths["survey_results"] = os.path.join(
                self.folder_names["survey_results_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-survey_results-{wandb_run_name}.csv",
            )

            self.file_paths["design_matrix"] = os.path.join(
                self.folder_names["design_matrix"],
                str(experiment_title.replace(" ", "_"))
                + f"-design_matrix-{wandb_run_name}.csv",
            )

            self.file_paths["survey_results_labeled"] = os.path.join(
                self.folder_names["survey_results_labeled"],
                str(experiment_title.replace(" ", "_"))
                + f"-survey_results_labeled-{wandb_run_name}.csv",
            )

            self.file_paths["paper_reported_coefficients"] = os.path.join(
                self.folder_names["paper_reported_coefficients"],
                str(experiment_title.replace(" ", "_"))
                + f"-paper_reported_coefficients-{wandb_run_name}.csv",
            )

            self.file_paths["expr_def"] = os.path.join(
                self.folder_names["experiment_def_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-expr_def-{wandb_run_name}.json",
            )

            self.file_paths["experiment_beta_amce"] = os.path.join(
                self.folder_names["experiment_beta_amce"],
                str(experiment_title.replace(" ", "_"))
                + f"-experiment_beta_amce-{wandb_run_name}.json",
            )
            self.file_paths["amce_graph"] = os.path.join(
                self.folder_names["amce_graph"],
                f"amce_graph-{wandb_run_name}.png",
            )
            self.file_paths["langsmith_sharelinks"] = os.path.join(
                self.folder_names["experiment_langsmith_sharelinks"],
                str(experiment_title.replace(" ", "_"))
                + f"-langsmith_sharelinks-{wandb_run_name}.json",
            )

            if settings.USE_RUN_ANALYTICS:
                # CLM Paths
                self.file_paths["clm_model_coefficients"] = os.path.join(
                    self.folder_names["clm_data"],
                    str(experiment_title) + f"-clm_coefficients-{wandb_run_name}.csv",
                )

                self.file_paths["clm_importance_frame_summary.csv"] = os.path.join(
                    self.folder_names["clm_data"],
                    str(experiment_title)
                    + f"-clm_importance_frame-{wandb_run_name}.csv",
                )

                self.file_paths["clm_importance_frame_summary.json"] = os.path.join(
                    self.folder_names["clm_data"],
                    str(experiment_title)
                    + f"-clm_importance_frame-{wandb_run_name}.json",
                )

                self.file_paths["clm_model_summary"] = os.path.join(
                    self.folder_names["clm_data"],
                    str(experiment_title) + f"-model_summary-{wandb_run_name}.csv",
                )

                self.file_paths["ols_model_summary"] = os.path.join(
                    self.folder_names["ols_data"],
                    str(experiment_title) + f"-model_summary-{wandb_run_name}.csv",
                )

                self.file_paths["ols_coefficients"] = os.path.join(
                    self.folder_names["ols_data"],
                    str(experiment_title) + f"-ols_coefficients-{wandb_run_name}.csv",
                )

                self.file_paths["ols_parameter_summary"] = os.path.join(
                    self.folder_names["ols_data"],
                    str(experiment_title)
                    + f"-ols_parameter_summary-{wandb_run_name}.csv",
                )

                self.file_paths["ols_diagnostics_table"] = os.path.join(
                    self.folder_names["ols_data"],
                    str(experiment_title)
                    + f"-ols_diagnostics_table-{wandb_run_name}.csv",
                )

                if settings.STORE_PLOTS:
                    self.file_paths["clm_AMCE"] = os.path.join(
                        self.folder_names["clm_data"],
                        str(experiment_title)
                        + f"-clm_coefficients-{wandb_run_name}.png",
                    )

                    self.file_paths["clm_attribute_importance"] = os.path.join(
                        self.folder_names["clm_data"],
                        str(experiment_title)
                        + f"-clm_importance_frame-{wandb_run_name}.png",
                    )

                    self.file_paths["ols_AMCE"] = os.path.join(
                        self.folder_names["ols_data"],
                        str(experiment_title)
                        + f"-ols_coefficients-{wandb_run_name}.png",
                    )

                    self.file_paths["ols_attribute_importance"] = os.path.join(
                        self.folder_names["ols_data"],
                        str(experiment_title)
                        + f"-ols_parameter_summary-{wandb_run_name}.png",
                    )
        else:
            self.folder_names["mappings_data"] = f"{main_name}/mappings"
            self.folder_names["survey_results_data"] = f"{main_name}/survey_results"
            self.folder_names["experiment_beta_amce"] = f"{main_name}/betas_amce"
            self.folder_names["experiment_def_data"] = f"{main_name}/experiment_def"

            for _, name in self.folder_names.items():
                create_dir(name)

            self.file_paths["mappings"] = os.path.join(
                self.folder_names["mappings_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-mappings-{wandb_run_name}.json",
            )
            self.file_paths["expr_def"] = os.path.join(
                self.folder_names["experiment_def_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-expr_def-{wandb_run_name}.json",
            )
            self.file_paths["survey_results"] = os.path.join(
                self.folder_names["survey_results_data"],
                str(experiment_title.replace(" ", "_"))
                + f"-survey_results-{wandb_run_name}.csv",
            )
            self.file_paths["experiment_beta_amce"] = os.path.join(
                self.folder_names["experiment_beta_amce"],
                str(experiment_title.replace(" ", "_"))
                + f"-experiment_beta_amce-{wandb_run_name}.json",
            )

    def add_prefix_to_title(self, prefix: str):
        """
        Adds prefix to title for reruns and hb experiments
        """
        self.title = f"{prefix}_{self.title}"

    def set_dependent_variable(self, text: str):
        """
        Sets a respondent dependent variable in the experiment
        config
        """
        self.respondent_dependent_variable = text

    def set_experiment_results(
        self,
        mappings: dict,
        survey_results: DataFrame,
        survey_summary: dict,
        survey_results_labeled: DataFrame,
    ):
        self.mappings = mappings
        self.survey_results = survey_results
        self.survey_summary = survey_summary
        self.survey_results_labeled = survey_results_labeled

    def set_persona_values(
        self,
        all_combos: any,
        personas: dict[str, dict[int, tuple[str, str]]],
        persona_string_list: list[str],
    ):
        self.all_combinations_index = all_combos
        self.personas = personas
        self.personas_full_string_definition = persona_string_list

    def make_wandb_run(
        self,
        tags: list = [],
        user_id: str = None,
        id: str = None,
        name: str = None,
        is_hb_run: bool = False,
    ):
        """
        Creates the WANDB run associated with this experiment.
        All parameters optional and are only included if defined

        - tags (list of WandB tags),
        - user (UUID),
        - id (WandB ID),
        - name (WandB name).
        """
        if self.title.strip():
            tags.append(self.title)

        # Iterate through the function arguments and create a dict from the ones that have values passed
        args = locals()
        init_params = {
            name: value
            for name, value in args.items()
            if name not in ("self") and value is not None
        }

        # Define the initialization parameters for the run
        init_params.update({
            "project": (
                settings.WANDB_REPLICATION_PROJECT
                if is_hb_run
                else settings.WANDB_PROJECT
            ),
            "entity": settings.WANDB_ENTITY,
            "config": {},
        })

        if user_id:
            init_params["config"]["user"] = user_id

        self.wandb_run = WandbRun(**init_params)
