import asyncio
import json
import math
import os
import random
import textwrap
import threading
import traceback
from pathlib import Path
from typing import Any

import numpy as np
import pandas as pd
from billiard.exceptions import SoftTimeLimitExceeded
from matplotlib import pyplot as plt
from pydantic.json import pydantic_encoder
from scipy.stats import permutation_test
from scipy.stats.mstats import spearmanr
from sentry_sdk import capture_exception
from unidecode import unidecode

import app.core.core as c
from app.api.v1.helpers.dependent_variables import generate_dependent_variable
from app.api.v1.helpers.human_baselines import run_r_analytics
from app.api.v1.helpers.personas import generate_personas
from app.api.v1.helpers.research import get_research_report
from app.auth.constants import USA_NAMES
from app.core.experiment_config import ExperimentConfig
from app.core.global_config import settings
from app.core.utils.common import delete_local_dir, get_value_from_list, str_keys
from app.core.utils.get_human_baselines import get_transcription_csv_from_github
from app.core.utils.hb_estimation import Run_Model
from app.core.utils.logging import app_logger
from app.core.utils.wandb_api import WandbAPI
from app.exceptions import CountedRateLimitError
from app.langsmith.trace_processing import LangSmithTraceManager
from app.modelling.data_objects import RunnerConfig
from app.modelling.randomness_inspector import Randomness_Inspector as ri
from app.modelling.runner import ClmRunner, OlsRunner
from app.personas.population_trait_gen import create_personas_from_local

logger = app_logger.get_logger(__name__)


def push_survey_details_to_storage_objects(expr_conf: ExperimentConfig):
    """
    Exports survey data, mappings, and experiment definition
    details based upon indicated global storage options
    ie: to WandB or local storage
    """
    if expr_conf.experiment_type == "conjoint":
        logger.info(
            f"Pushing survey details to storage for experiment: {expr_conf.why_prompt}"
        )
    else:
        logger.info(
            "Pushing survey details for concept testing:"
            f" {expr_conf.concept_description}"
        )
    if expr_conf.experiment_type == "conjoint":
        if settings.USE_LOCAL_STORAGE:
            logger.info("Saving experiment config to local storage")
            try:
                with open(
                    expr_conf.file_paths["survey_summary"], "w", encoding="utf-8-sig"
                ) as f:
                    json.dump(expr_conf.survey_summary, f, ensure_ascii=False)
            except Exception:
                pass

            try:
                with open(expr_conf.file_paths["mappings"], "w", encoding="utf-8") as f:
                    json.dump(expr_conf.mappings, f, ensure_ascii=False)
            except Exception:
                pass

            try:
                expr_conf.survey_results.to_csv(
                    expr_conf.file_paths["survey_results"], index=False
                )
            except Exception:
                pass
            try:
                expr_conf.design_matrix.to_csv(
                    expr_conf.file_paths["design_matrix"], index=False
                )
            except Exception:
                pass
            try:
                expr_conf.survey_results_labeled.to_csv(
                    expr_conf.file_paths["survey_results_labeled"], index=False
                )
            except Exception:
                pass

            if expr_conf.is_hb_run:
                try:
                    expr_conf.paper_reported_coefficients.to_csv(
                        expr_conf.file_paths["paper_reported_coefficients"], index=False
                    )
                except Exception:
                    pass
            try:
                with open(
                    expr_conf.file_paths["expr_def"], "w", encoding="utf-8-sig"
                ) as f:
                    json.dump(
                        expr_conf.get_settings_dict(),
                        f,
                        default=pydantic_encoder,
                        ensure_ascii=False,
                    )
            except Exception:
                pass
            logger.info("Experiment config saved successfully")

        if settings.USE_WANDB_STORAGE:
            logger.info("Logging artifacts to WandB")

            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["survey_summary"],
                    file_type="any",
                    artifact_main_directory="survey_summary",
                    artifact_name=(
                        f"experiment_survey_summary_{expr_conf.wandb_run.name}"
                    ),
                    file_extension=".json",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["expr_def"],
                    file_type="any",
                    artifact_main_directory="experiment_definition",
                    artifact_name=f"experiment_definition_{expr_conf.wandb_run.name}",
                    file_extension=".json",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["mappings"],
                    file_type="any",
                    artifact_main_directory="experiment_mappings",
                    artifact_name=f"experiment_mappings_{expr_conf.wandb_run.name}",
                    file_extension=".json",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["survey_results"],
                    file_type="any",
                    artifact_main_directory="experiment_survey_results",
                    artifact_name=(
                        f"experiment_survey_results_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"Survey Results_{expr_conf.title}",
                    do_wandb_log=True,
                    file_extension=".csv",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["design_matrix"],
                    file_type="any",
                    artifact_main_directory="experiment_design_matrix",
                    artifact_name=(
                        f"experiment_design_matrix_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"Design Matrix_{expr_conf.title}",
                    do_wandb_log=True,
                    file_extension=".csv",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["survey_results_labeled"],
                    file_type="any",
                    artifact_main_directory="experiment_survey_results_labeled",
                    artifact_name=(
                        f"experiment_survey_results_labeled_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"Survey Results Labeled_{expr_conf.title}",
                    do_wandb_log=True,
                    file_extension=".csv",
                )
            except Exception:
                pass
            try:
                if expr_conf.is_hb_run:
                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["paper_reported_coefficients"],
                        file_type="any",
                        artifact_main_directory="paper_reported_coefficients",
                        artifact_name=(
                            f"hb_paper_reported_coefficients_{expr_conf.wandb_run.name}"
                        ),
                        wandb_log_name=f"Paper Reported Coefficients_{expr_conf.title}",
                        do_wandb_log=True,
                        file_extension=".csv",
                    )
            except Exception:
                pass

                average_seconds_per_task = expr_conf.mappings.get(
                    "average_seconds_per_task"
                )
                expr_conf.wandb_run.wandb_log({
                    "Average Seconds per Task": average_seconds_per_task,
                })

                logger.info(f"Average Seconds per Task: {average_seconds_per_task}")
                logger.info(
                    "Survey details successfully pushed to storage for experiment:"
                    f" {expr_conf.title}"
                )
    else:
        if settings.USE_LOCAL_STORAGE:
            logger.info("Saving concept testing config to local storage")

            try:
                with open(expr_conf.file_paths["mappings"], "w", encoding="utf-8") as f:
                    json.dump(expr_conf.mappings, f, ensure_ascii=False)
            except Exception:
                pass
            try:
                with open(
                    expr_conf.file_paths["experiment_beta_amce"], "w", encoding="utf-8"
                ) as f:
                    json.dump(expr_conf.concept_analytics, f, ensure_ascii=False)
            except Exception:
                pass
            try:
                expr_conf.survey_results.to_csv(
                    expr_conf.file_paths["survey_results"], index=False
                )
            except Exception:
                pass
            try:
                with open(
                    expr_conf.file_paths["expr_def"], "w", encoding="utf-8-sig"
                ) as f:
                    json.dump(
                        expr_conf.get_settings_dict(),
                        f,
                        default=pydantic_encoder,
                        ensure_ascii=False,
                    )
            except Exception:
                pass
            logger.info("concept testing config saved successfully")

        if settings.USE_WANDB_STORAGE:
            logger.info("Logging artifacts to WandB for concept testing")

            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["mappings"],
                    file_type="any",
                    artifact_main_directory="experiment_mappings",
                    artifact_name=f"experiment_mappings_{expr_conf.wandb_run.name}",
                    file_extension=".json",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["experiment_beta_amce"],
                    file_type="any",
                    artifact_main_directory="experiment_beta_amce",
                    artifact_name=f"experiment_beta_amce_{expr_conf.wandb_run.name}",
                    file_extension=".json",
                )
            except Exception:
                pass
            try:
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["survey_results"],
                    file_type="any",
                    artifact_main_directory="experiment_survey_results",
                    artifact_name=(
                        f"experiment_survey_results_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"Survey Results_{expr_conf.title}",
                    do_wandb_log=True,
                    file_extension=".csv",
                )
            except Exception:
                pass


def update_wandb_configs(expr_conf: ExperimentConfig):
    """
    Uploads experiment parameters via experiment definitions
    and mappings in order to populate WandB config file
    """
    logger.info(f"Updating WandB configs for experiment: {expr_conf.why_prompt}")

    try:
        experiment_params = reformat_experiment_params(expr_conf)
        expr_conf.wandb_run.update_config(experiment_params)
        logger.info(
            f"WandB configs updated successfully for experiment: {expr_conf.why_prompt}"
        )
    except Exception as e:
        logger.error(f"Error updating WandB configs: {e}", exc_info=True)
        capture_exception(e)


def reformat_experiment_params(expr_conf: ExperimentConfig):
    """
    Reformats the params dictionary to upload the data to WandB
    """

    experiment_information = {
        "get_attribute_text": str_keys(
            expr_conf.mappings.get("attributes_and_levels_lookup", {}).get(
                "get_attribute_text", {}
            )
        ),
        "get_attribute_id": str_keys(
            expr_conf.mappings.get("attributes_and_levels_lookup", {}).get(
                "get_attribute_id", {}
            )
        ),
        "get_level_ids": str_keys(
            expr_conf.mappings.get("attributes_and_levels_lookup", {}).get(
                "get_level_ids", {}
            )
        ),
        "get_level_text": str_keys(
            expr_conf.mappings.get("attributes_and_levels_lookup", {}).get(
                "get_level_text", {}
            )
        ),
    }
    model_design = expr_conf.prompt_executor.get_model_settings(
        expr_conf.expr_llm_model
    )
    model_design.pop("api_key", None)
    if "model" in model_design:
        model_design["model_name"] = model_design.pop("model")

    experiment_params = {
        "model_design": model_design,
        "artifacts": expr_conf.wandb_run.wandb_logged_artifacts_list,
        "experiment_status": expr_conf.experiment_section_status,
        "experiment_design": expr_conf.get_settings_dict(),
        "experiment_information": experiment_information,
    }
    return experiment_params


def get_personas(expr_conf: ExperimentConfig):
    """
    Creates personas based upon specified population traits and source (api, local)
    """
    logger.info(f"Generating personas for experiment: {expr_conf.title}")

    try:
        if expr_conf.use_api:  # for linear schemas use
            generated_personas = generate_personas(
                trait_keys=list(expr_conf.population_traits.model_dump().keys()),
                levels_per_trait=expr_conf.levels_per_trait,
            )
        else:
            generated_personas = create_personas_from_local(
                trait_keys=list(expr_conf.population_traits.model_dump().keys())
            )
        number_of_respondents = get_number_of_respondents(expr_conf)
        generated_personas = adjust_population_size(
            generated_personas, number_of_respondents
        )
        logger.info(
            f"Personas generated successfully for experiment: {expr_conf.title}"
        )
        return generated_personas
    except Exception as e:
        logger.error(f"Error generating personas: {e}", exc_info=True)
        capture_exception(e)
        return False, f"Error: {str(e)}\nTraceback: {traceback.format_exc()}"


def get_number_of_respondents(expr_conf: ExperimentConfig) -> int:
    """
    Calculates the number of respondents based on the number of tasks per respondent,
    number of levels, and number of options excluding the 'none' option.
    Formula:
        n_min = (500 * c) / (t * a)
        n_max = (1000 * c) / (t * a)
    where:
        n = number of respondents
        c (on main effect) = max(levels across all attributes)
        t = tasks per respondent
        a = 2 -> number of options (A and B (excluding 'neither'))
    """

    if expr_conf.number_of_respondents:
        return expr_conf.number_of_respondents

    max_levels = max([
        len(attribute[1])
        # attribute[1] is the list of levels for the attribute
        for attribute in expr_conf.pre_cooked_attributes_and_levels_lookup
    ])

    tasks_per_respondent = expr_conf.number_of_tasks_per_respondent
    number_of_options = 2

    n_min = math.ceil((500 * max_levels) / (tasks_per_respondent * number_of_options))
    n_max = math.ceil((1000 * max_levels) / (tasks_per_respondent * number_of_options))
    number_of_respondents = math.ceil((n_min + n_max) / 2)
    expr_conf.number_of_respondents = number_of_respondents
    return number_of_respondents


def adjust_population_size(population: tuple, number_of_respondents: int):
    if number_of_respondents == len(population[0]):
        return population
    selection = [
        random.randint(0, len(population[0]) - 1) for _ in range(number_of_respondents)
    ]
    index_combination = [population[0][i] for i in selection]
    full_persona_definition = [population[2][i] for i in selection]
    return index_combination, population[1], full_persona_definition


def run_survey(expr_conf: ExperimentConfig):
    """
    Functions as a controller for numerous experiment preparation functions. Performs setup for survey
    (e.g. level and attribute generation, dependent variable creation, persona creation),
    sets up storage for logging, and generates endpoints, and then runs DCE experiment
    """
    if expr_conf.experiment_type == "conjoint":
        logger.info(f"Running survey for experiment: {expr_conf.title}")
    else:
        logger.info(
            f"Running concept testing for experiment: {expr_conf.concept_description}"
        )

    expr_conf.wandb_run.add_tags([expr_conf.experiment_type])

    try:
        # Generating the dependent_variable_prompt (if no DV already exists).
        # if pre_cooked_attributes_and_levels_lookup exists, we expect the dependent variable as well.
        if expr_conf.experiment_type == "conjoint":
            if expr_conf.pre_cooked_attributes_and_levels_lookup:
                expr_conf.wandb_run.add_tags(["pre_cooked_attributes_and_levels"])

            if not expr_conf.hb_run_id and not expr_conf.hb_folder:
                if (
                    not expr_conf.pre_cooked_attributes_and_levels_lookup
                    or not expr_conf.respondent_dependent_variable
                ):
                    dependent_variable = generate_dependent_variable(
                        prompt_type=expr_conf.prompt_type,
                        why_prompt=expr_conf.why_prompt,
                        dv_llm_model=expr_conf.dv_llm_model,
                        response_type=expr_conf.response_type,
                    )
                    expr_conf.set_dependent_variable(dependent_variable)

        logger.info(f"Experiment parameters: {expr_conf.to_dict()}")

        if settings.USE_WANDB_STORAGE:
            expr_conf.create_experiment_dir(
                wandb_run_name=expr_conf.wandb_run.name,
                experiment_type=expr_conf.experiment_type,
            )
        else:
            expr_conf.create_experiment_dir(
                wandb_run_name="RUN",
                experiment_type=expr_conf.experiment_type,
            )

        if expr_conf.experiment_type == "conjoint":
            if expr_conf.is_hb_run and expr_conf.hb_folder:
                df = get_transcription_csv_from_github(hb_folder=expr_conf.hb_folder)
                expr_conf.wandb_run.wandb_log({"HB Folder ": expr_conf.hb_folder})
                expr_conf.wandb_run.wandb_log(
                    {"Opt-out Included ": expr_conf.add_neither_option}
                )
                expr_conf.paper_reported_coefficients = df
        if expr_conf.experiment_type == "conjoint":
            (
                mappings,
                survey_results,
                survey_summary,
                all_answers,
                survey_results_labeled,
                external_persona_provided,
            ) = c.dce_experiment(expr_conf)
            if len(survey_results) < 100:
                expr_conf.wandb_run.add_tags(["Failure: Not Enough Data"])
                return False, "Not Enough Data"
            logger.success("DCE experiment executed successfully")
            if mappings:
                expr_conf.set_experiment_results(
                    mappings, survey_results, survey_summary, survey_results_labeled
                )
                expr_conf.total_number_of_tasks = int(
                    survey_results.shape[0] / survey_results["Alts"].nunique()
                )

                alts = survey_results["Alts"].unique()
                logger.info(f"Number of Alternatives: {len(alts)}")
                expr_conf.wandb_run.wandb_log({"Number of Alternatives": len(alts)})
                for i in range(len(alts)):
                    logger.info(
                        f"# of {chr(65 + i)}'s: {all_answers.count(chr(65 + i))}"
                    )
                    expr_conf.wandb_run.wandb_log({
                        f"# of {chr(65 + i)}'s": all_answers.count(chr(65 + i)),
                    })
                expr_conf.wandb_run.wandb_log({
                    "Parse Error Count": expr_conf.mappings["parse_error_count"],
                })
                logger.info(
                    f"Parse Error Count: {expr_conf.mappings['parse_error_count']}"
                )

                push_survey_details_to_storage_objects(expr_conf)

                logger.success(
                    "Survey successfully completed for experiment"
                    f" {expr_conf.why_prompt}"
                )
                return True, "survey success", external_persona_provided
            else:
                logger.error(f"Error(run_experiment) in {expr_conf.title}: no mappings")
                return (
                    False,
                    f"Error(run_experiment) in {expr_conf.title}: no mappings",
                    external_persona_provided,
                )
        elif expr_conf.experiment_type == "concept_testing":
            (
                mappings,
                survey_results,
                survey_analytics,
                external_persona_provided,
            ) = c.Run_Statement(expr_conf)
            if mappings:
                expr_conf.wandb_run.wandb_log({"Experiment Mappings": mappings})
                expr_conf.wandb_run.wandb_log({"Survey Results": survey_results})
                expr_conf.wandb_run.wandb_log({"Survey Analytics": survey_analytics})
                expr_conf.wandb_run.wandb_log(
                    {"External Persona Provided": external_persona_provided}
                )
                expr_conf.concept_analytics = survey_analytics
                expr_conf.survey_results = survey_results
                expr_conf.mappings = mappings
                push_survey_details_to_storage_objects(expr_conf)
            return True, "survey success", external_persona_provided
    except SoftTimeLimitExceeded:
        capture_exception()
        raise SoftTimeLimitExceeded
    except Exception as e:
        logger.error(f"Error in survey: {e}", exc_info=True)
        capture_exception()
        if type(e) is CountedRateLimitError:
            expr_conf.wandb_run.wandb_log({
                "Rate Limit Error": CountedRateLimitError.counter,
            })
        return False, f"Error: {str(e)}\nTraceback: {traceback.format_exc()}", False


def run_randomness_inspector(expr_conf: ExperimentConfig):
    """
    Performs the randomness inspector test and logs them to WANDB
    """
    logger.info(f"Running randomness inspector for experiment: {expr_conf.title}")

    try:
        x = expr_conf.survey_results["chosen_choice_letter"].tolist()
        mapper = {"A": 0, "B": 1, "C": 2}
        x = [mapper[y] for y in x]

        metrics = ri(X=x).randonmness_test()

        if isinstance(metrics, tuple) and metrics[0] is None:
            return (
                False,
                f"Randomness Test Failure Experiment # {expr_conf.title}: {metrics[1]}",
            )

        else:
            if settings.USE_WANDB_STORAGE:
                wald_wolfowitz_p_value = metrics.loc[
                    metrics["Test"] == "Wald Wolfowitz"
                ]["p-value"].tolist()[0]
                bartels_p_value = metrics.loc[metrics["Test"] == "Bartels Rank"][
                    "p-value"
                ].tolist()[0]

                expr_conf.wandb_run.wandb_log({
                    "Wald Wolfowitz: Noise Metric": wald_wolfowitz_p_value,
                    "Bartels Rank: Noise Metric": bartels_p_value,
                })

                # If Randomness Test are successfully performed validated the results
                if not expr_conf.validator.randomness_metrics(
                    bartels_p_value, wald_wolfowitz_p_value
                ):
                    expr_conf.wandb_run.add_tags(["Randomness Test Failure"])

            logger.info(
                f"Randomness inspection completed for experiment: {expr_conf.title}"
            )
            return True, "randomness inspector success"

    except Exception as e:
        capture_exception()
        logger.error(f"Error in randomness inspection: {e}", exc_info=True)
        return False, f"Randomness Test Error: {str(e)}"


def run_clm(expr_conf: ExperimentConfig):
    """
    Runs CLM statistical analysis and then logs results to WandB
    """
    logger.info(f"Running CLM analysis for experiment: {expr_conf.title}")

    try:
        config = RunnerConfig(
            title=expr_conf.title,
            run_name=expr_conf.wandb_run.name,
            run_id=expr_conf.wandb_run.id,
            model="clm",
            output_dir=Path(expr_conf.folder_names["clm_data"]),
            survey_path=Path(expr_conf.file_paths["mappings"]),
            survey_setup_path=Path(expr_conf.file_paths["survey_results"]),
        )

        results = ClmRunner(config).run()
        if results:
            results.log(logger, config.run_id, config.run_name)
            expr_conf.confidence_level = get_confidence_level(results.rho2)

            if settings.USE_WANDB_STORAGE:
                expr_conf.wandb_run.wandb_log({
                    "CLM Pseudo R Squared": results.rho2,
                    "CLM # Significant Variables": results.num_sig_vars,
                    "CLM # of variables": results.num_vars,
                    "CLM F1 Statistic": results.f1,
                    "CLM Ratio of Significant Variables": (
                        results.num_sig_vars / results.num_vars
                    ),
                })

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["clm_model_coefficients"],
                    file_type="csv",
                    artifact_main_directory="clm_experiment_model_output",
                    artifact_name=(
                        f"clm_experiment_model_output_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"CLM Model Output_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["clm_importance_frame_summary.csv"],
                    file_type="csv",
                    artifact_main_directory="clm_experiment_importance_frame",
                    artifact_name=(
                        f"clm_experiment_importance_frame_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"CLM Importance Frame_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["clm_importance_frame_summary.json"],
                    file_type="any",
                    artifact_main_directory="clm_experiment_importance_frame",
                    artifact_name=(
                        f"clm_experiment_importance_frame_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"CLM Importance Frame_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["clm_model_summary"],
                    file_type="csv",
                    artifact_main_directory="clm_experiment_model_metrics",
                    artifact_name=(
                        f"clm_experiment_model_metrics_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"CLM Model Metrics_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                if settings.STORE_PLOTS:  # Images
                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["clm_AMCE"],
                        file_type="image",
                        artifact_main_directory="clm_experiment_AMCE",
                        artifact_name=f"clm_experiment_AMCE_{expr_conf.wandb_run.name}",
                        wandb_log_name=f"CLM AMCE_{expr_conf.wandb_run.name}",
                        do_wandb_log=True,
                    )

                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["clm_attribute_importance"],
                        file_type="image",
                        artifact_main_directory="clm_experiment_attribute_importance",
                        artifact_name=f"clm_experiment_attribute_importance_{expr_conf.wandb_run.name}",
                        wandb_log_name=(
                            f"CLM Attribute Importance_{expr_conf.wandb_run.name}"
                        ),
                        do_wandb_log=True,
                    )
                logger.info(
                    "CLM analysis completed successfully for experiment:"
                    f" {expr_conf.title}"
                )

            return True, "clm success"
        else:
            logger.error(f"CLM analysis failed for {expr_conf.title}")
            return False, "clm failed"
    except Exception as e:
        capture_exception()
        logger.error(f"Error in CLM analysis: {e}", exc_info=True)
        return False, f"CLM Model Processing Error: {str(e)}"


def run_ols(expr_conf: ExperimentConfig):
    """
    Runs OLS statistical analysis and then logs results to WANDB
    """
    logger.info(f"Running OLS analysis for experiment: {expr_conf.title}")

    try:
        config = RunnerConfig(
            title=expr_conf.title,
            run_name=expr_conf.wandb_run.name,
            run_id=expr_conf.wandb_run.id,
            model="ols",
            output_dir=Path(expr_conf.folder_names["ols_data"]),
            survey_path=Path(expr_conf.file_paths["mappings"]),
            survey_setup_path=Path(expr_conf.file_paths["survey_results"]),
        )

        results = OlsRunner(config).run()
        if results:
            results.log(logger, config.run_id, config.run_name)

            if settings.USE_WANDB_STORAGE:
                expr_conf.wandb_run.wandb_log({
                    "OLS R Squared": results.r2,
                    "OLS # Significant Variables": results.num_sig_vars,
                    "OLS # of variables": results.num_vars,
                    "OLS Ratio of Significant Variables": (
                        results.num_sig_vars / results.num_vars
                    ),
                })

                # Files
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["ols_model_summary"],
                    file_type="csv",
                    artifact_main_directory="ols_experiment_model_metrics",
                    artifact_name=(
                        f"ols_experiment_model_metrics_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"OLS Model Metrics_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["ols_coefficients"],
                    file_type="csv",
                    artifact_main_directory="ols_experiment_model_output",
                    artifact_name=(
                        f"ols_experiment_model_output_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=f"OLS Model Output_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["ols_parameter_summary"],
                    file_type="csv",
                    artifact_main_directory="ols_experiment_parameter_summary",
                    artifact_name=(
                        f"ols_experiment_parameter_summary_{expr_conf.wandb_run.name}"
                    ),
                    wandb_log_name=(
                        f"OLS Model Parameter Summary_{expr_conf.wandb_run.name}"
                    ),
                    do_wandb_log=True,
                )

                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths["ols_diagnostics_table"],
                    file_type="csv",
                    artifact_main_directory="ols_experiment_ols_diagnostics_table",
                    artifact_name=f"ols_experiment_parameter_ols_diagnostics_table_{expr_conf.wandb_run.name}",
                    wandb_log_name=f"Ols Diagnostics Table_{expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                # Images
                if settings.STORE_PLOTS:
                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["ols_AMCE"],
                        file_type="image",
                        artifact_main_directory="ols_experiment_AMCE",
                        artifact_name=f"ols_experiment_AMCE_{expr_conf.wandb_run.name}",
                        wandb_log_name=f"OLS AMCE_{expr_conf.wandb_run.name}",
                        do_wandb_log=True,
                    )

                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["ols_attribute_importance"],
                        file_type="image",
                        artifact_main_directory="ols_experiment_attribute_importance",
                        artifact_name=f"ols_experiment_attribute_importance_{expr_conf.wandb_run.name}",
                        wandb_log_name=(
                            f"OLS Attribute Importance_{expr_conf.wandb_run.name}"
                        ),
                        do_wandb_log=True,
                    )
            logger.info(
                f"OLS analysis completed successfully for experiment: {expr_conf.title}"
            )
            return True, "ols success"
        else:
            logger.error(f"OLS analysis failed for {expr_conf.title}")
            return False, "ols failed"

    except Exception as e:
        logger.error(f"Error in OLS analysis: {e}", exc_info=True)
        capture_exception(e)
        return False, f"OLS Model Processing Error: {str(e)}"


def run_analytics(expr_conf: ExperimentConfig):
    """
    Performs both CLM and OLS analyses
    """
    clm_res = run_clm(expr_conf)
    expr_conf.experiment_section_status.append(clm_res)

    ols_res = run_ols(expr_conf)
    expr_conf.experiment_section_status.append(ols_res)


def execute(expr_conf: ExperimentConfig):
    """
    A controller which:
    - runs surveys,
    - conducts randomness inspection,
    - performs analysis for each  experiment
    based upon experiment settings
    """
    run_name = expr_conf.wandb_run.name

    def conduct_research():
        """Run research in background thread"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                get_research_report(why_prompt=expr_conf.why_prompt)
            )
            with open(
                expr_conf.file_paths["research_report"], "w", encoding="utf-8"
            ) as f:
                json.dump(result, f, ensure_ascii=False)
            expr_conf.research_report = result
        except Exception as e:
            logger.error(f"Error in research: {e}")
            capture_exception(e)
            expr_conf.research_report = None
        finally:
            loop.close()

    if not expr_conf.is_hb_run and expr_conf.do_research:
        logger.info("Conducting research using GPT Researcher")
        research_thread = threading.Thread(target=conduct_research)
        research_thread.start()
    if expr_conf.experiment_type == "conjoint":
        logger.info(f"Saving initial config for experiment: {expr_conf.why_prompt}")
        initial_config = {
            "experiment_design": {
                "pre_cooked_attributes_and_levels_lookup": (
                    expr_conf.pre_cooked_attributes_and_levels_lookup
                ),
                "why_prompt": expr_conf.why_prompt,
                "country": expr_conf.country,
                "year": expr_conf.year,
                "is_private": expr_conf.is_private,
                "experiment_type": expr_conf.experiment_type,
                "expr_llm_model": expr_conf.expr_llm_model.value,
                "target_population": (
                    expr_conf.target_population.model_dump()
                    if expr_conf.country in USA_NAMES
                    else {}
                ),
            }
        }
    else:
        logger.info(
            "Saving initial config for concept testing:"
            f" {expr_conf.concept_description}"
        )
        initial_config = {
            "experiment_design": {
                "country": expr_conf.country,
                "year": expr_conf.year,
                "is_private": expr_conf.is_private,
                "experiment_type": expr_conf.experiment_type,
                "image_name": expr_conf.image_name,
                "concept_description": expr_conf.concept_description,
                "concept_statements": expr_conf.concept_statements,
                "expr_llm_model": expr_conf.expr_llm_model.value,
                "target_population": (
                    expr_conf.target_population.model_dump()
                    if expr_conf.country in USA_NAMES
                    else {}
                ),
            }
        }

    if expr_conf.population_traits:
        initial_config["experiment_design"][
            "population_traits"
        ] = expr_conf.population_traits.model_dump()

    expr_conf.wandb_run.update_config(initial_config)

    if expr_conf.experiment_type == "conjoint":
        logger.info(f"Executing experiment: {expr_conf.title}")
    else:
        logger.info(f"Executing concept testing: {expr_conf.concept_description}")
    labels = {
        "wandb_run_id": f"{expr_conf.wandb_run.id}",
        "wandb_run_name": f"{expr_conf.wandb_run.name}",
    }

    logger.info("Running Survey", {"labels": labels})
    survey_res = run_survey(expr_conf)
    expr_conf.experiment_section_status.append(survey_res)

    if not survey_res[2] and expr_conf.target_population:
        if "Black" in expr_conf.target_population.get("racial_group"):
            expr_conf.target_population.get("racial_group").append("African American")
            expr_conf.target_population.get("racial_group").remove("Black")

    for tag in expr_conf.wandb_run.tags:
        if "failure" in tag.lower():
            logger.warning(
                f"Experiment {expr_conf.title} marked as Invalid due to failure tag:"
                f" {tag}"
            )
            expr_conf.wandb_run.add_tags(["Invalid"])
            break

    if expr_conf.experiment_type == "conjoint":
        paper_reported_coefficients = (
            expr_conf.file_paths.get("paper_reported_coefficients")
            if expr_conf.is_hb_run
            else ""
        )

        if expr_conf.is_hb_run and expr_conf.hb_folder:
            if survey_res[0]:
                logger.info("Running HB analytics")
                (
                    Join_tables,
                    Spearmen_correlation_AMCE,
                    Spearmen_correlation_Beta,
                    MAPE_AMCE,
                    MAPE_Beta,
                    Coverage_Prob,
                    Compare_Sign,
                    Num_Active_parm,
                    Similarity_matrix,
                ) = Run_Model(
                    experiment_mapping_file=expr_conf.file_paths["mappings"],
                    experiment_survey_file=expr_conf.file_paths["survey_results"],
                    experiment_HB_file=paper_reported_coefficients,
                    Add_Personas=False,
                    MNP_Model=expr_conf.mnp_model,
                )
            else:
                Spearmen_correlation_AMCE = 0
                Spearmen_correlation_Beta = 0
                MAPE_AMCE = 1000
                MAPE_Beta = 1000
                Coverage_Prob = 0
                Compare_Sign = 0
                Num_Active_parm = 0
                Similarity_matrix = {
                    "Frobenius_similarity": 0,
                    "Manhattan_similarity": 0,
                    "Spectral_similarity": 0,
                }
            if expr_conf.mnp_model:
                expr_conf.wandb_run.wandb_log({"Model": "MNP"})
            else:
                expr_conf.wandb_run.wandb_log({"Model": "MNL"})

            expr_conf.wandb_run.wandb_log({
                "Spearman Correlation Feature Importance": round(
                    Spearmen_correlation_AMCE, 2
                )
            })
            expr_conf.wandb_run.wandb_log({
                "Spearman Correlation Beta Values": round(Spearmen_correlation_Beta, 2)
            })
            expr_conf.wandb_run.wandb_log({
                "Standard Deviation Normalized Mean Absolute Error Feature Importance": round(
                    MAPE_AMCE, 2
                )
            })
            expr_conf.wandb_run.wandb_log({
                "Standard Deviation Normalized Mean Absolute Error Beta Values": round(
                    MAPE_Beta, 2
                )
            })
            expr_conf.wandb_run.wandb_log(
                {"Coverage Probability for Betas": round(Coverage_Prob, 2)}
            )
            expr_conf.wandb_run.wandb_log({
                "Percentage of Parameters with same Calculated & Reported Sign": round(
                    Compare_Sign, 2
                )
            })
            expr_conf.wandb_run.wandb_log(
                {"Number of Parameters Estimated": Num_Active_parm}
            )
            expr_conf.wandb_run.wandb_log({"LLM Model": expr_conf.expr_llm_model.value})
            expr_conf.wandb_run.wandb_log({
                "Frobenius_similarity": Similarity_matrix["Frobenius_similarity"],
            })
            expr_conf.wandb_run.wandb_log({
                "Manhattan_similarity": Similarity_matrix["Manhattan_similarity"],
            })
            expr_conf.wandb_run.wandb_log({
                "Spectral_similarity": Similarity_matrix["Spectral_similarity"],
            })
            expr_conf.wandb_run.wandb_log({
                "tasks_per_respondent": expr_conf.tasks_per_respondent,
            })
            expr_conf.wandb_run.wandb_log({
                "sample_size": expr_conf.sample_size,
            })
            expr_conf.wandb_run.wandb_log({
                "total_number_of_tasks": expr_conf.total_number_of_tasks,
            })
            if survey_res[0]:
                amce_table_path = os.path.join(
                    settings.PROJECT_ROOT_DIRECTORY,
                    "app",
                    "logs",
                    "analytics",
                    run_name,
                    f"amce_table_{run_name}.csv",
                )
                os.makedirs(os.path.dirname(amce_table_path), exist_ok=True)
                Join_tables.to_csv(amce_table_path, index=False, encoding="utf-8-sig")
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=amce_table_path,
                    file_type="any",
                    artifact_main_directory="amce_table",
                    artifact_name=f"amce_table-{expr_conf.wandb_run.name}",
                    wandb_log_name=f"AMCE Table {expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                    file_extension=".csv",
                )
            update_wandb_configs(expr_conf)
        else:
            if survey_res[0]:
                logger.info("Preparing to run R analytics")
                mappings_path = expr_conf.file_paths.get("mappings")
                survey_results_path = expr_conf.file_paths.get("survey_results")
                calculations_script_path = os.path.join(
                    settings.PROJECT_ROOT_DIRECTORY,
                    "R",
                    "Calculations.R",
                )
                calculations_output_path = os.path.join(
                    settings.PROJECT_ROOT_DIRECTORY,
                    "app",
                    "logs",
                    "analytics",
                    run_name,
                    f"calculations_{run_name}.json",
                )
                amce_path = os.path.join(
                    settings.PROJECT_ROOT_DIRECTORY,
                    "app",
                    "logs",
                    "analytics",
                    run_name,
                    f"amce_{run_name}.json",
                )
                os.makedirs(os.path.dirname(calculations_output_path), exist_ok=True)
                os.makedirs(os.path.dirname(amce_path), exist_ok=True)
                message = run_r_analytics(
                    expr_conf.wandb_run,
                    mappings_path,
                    survey_results_path,
                    paper_reported_coefficients,
                    calculations_script_path,
                    calculations_output_path,
                    amce_path,
                    expr_conf.is_hb_run,
                )
                if message == "failed":
                    logger.critical(f"Error in R analytics: {message}")
                else:
                    logger.success(
                        f"Experiment execution completed for: {expr_conf.title}"
                    )

                with open(amce_path, "r") as file:
                    logger.info("Reading AMCE results from R analytics output")
                    amce = json.load(file)
                    r_squared = amce.get("AMCE_Results", {}).get("R_Squared")[0]
                    expr_conf.r_squared = int(round(r_squared * 100, 0))
                    expr_conf.confidence_level = get_confidence_level(float(r_squared))

                    if expr_conf.is_hb_run:
                        spearman_correlation = amce.get("AMCE_Results", {}).get(
                            "R_Spearman"
                        )[0]
                        expr_conf.spearman_correlation = round(spearman_correlation, 4)

                        coverage_probability = amce.get("AMCE_Results", {}).get(
                            "Coverage_Probability"
                        )[0]
                        expr_conf.coverage_probability = round(coverage_probability, 4)

                        expr_conf.wandb_run.wandb_log(
                            {"Spearman Correlation": spearman_correlation}
                        )
                        expr_conf.wandb_run.wandb_log(
                            {"Coverage Probability": coverage_probability}
                        )

                expr_conf.wandb_run.wandb_log({"R Squared": r_squared})

                with open(amce_path, "r") as f:
                    amce_data = json.load(f)
                amce_data = amce_data["AMCE_Results"]["AMCE_Results"]
                df = pd.DataFrame(amce_data)
                amce_table_path = os.path.join(
                    settings.PROJECT_ROOT_DIRECTORY,
                    "app",
                    "logs",
                    "analytics",
                    run_name,
                    f"amce_table_{run_name}.csv",
                )
                df.to_csv(amce_table_path, index=False)
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=amce_table_path,
                    file_type="csv",
                    artifact_main_directory="amce_table",
                    artifact_name=f"amce_table-{expr_conf.wandb_run.name}",
                    wandb_log_name=f"AMCE Table {expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )
                plot_amce(
                    df=df,
                    output_file_path=expr_conf.file_paths.get("amce_graph"),
                    is_hb_run=expr_conf.is_hb_run,
                )
                expr_conf.wandb_run.wandb_log_artifact(
                    file_path=expr_conf.file_paths.get("amce_graph"),
                    file_type="image",
                    artifact_main_directory="amce_graph",
                    artifact_name=f"amce_graph-{expr_conf.wandb_run.name}",
                    wandb_log_name=f"AMCE Graph {expr_conf.wandb_run.name}",
                    do_wandb_log=True,
                )

                if not expr_conf.is_hb_run and expr_conf.do_research:
                    research_thread.join()
                    expr_conf.wandb_run.wandb_log_artifact(
                        file_path=expr_conf.file_paths["research_report"],
                        file_type="any",
                        artifact_main_directory="research_report",
                        artifact_name=(
                            f"experiment_research_report_{expr_conf.wandb_run.name}"
                        ),
                        file_extension=".json",
                    )
                logger.info("Getting experiment trace data from Langsmith")
                ls_trace_manager = LangSmithTraceManager()
                langsmith_total_token_data = ls_trace_manager.extract_run_data(
                    expr_conf.wandb_run.id,
                    is_root=True,
                    run_type="chain",
                    select=[
                        "token_count",
                        "cost_amount",
                    ],
                    error=False,
                )
                expr_conf.token_count = langsmith_total_token_data.get("token_count", 0)
                expr_conf.cost_amount = round(
                    langsmith_total_token_data.get("cost_amount", 0), 2
                )
                expr_conf.langsmith_sharelinks = ls_trace_manager.share_all_runs(
                    expr_conf.wandb_run.id, is_root=True
                )
                expr_conf.wandb_run.wandb_log(
                    {"Total Token": langsmith_total_token_data.get("token_count", 0)}
                )
                expr_conf.wandb_run.wandb_log({
                    "Total Cost": round(
                        langsmith_total_token_data.get("cost_amount", 0), 2
                    )
                })
            else:
                r_squared = 0
                expr_conf.r_squared = 0
                expr_conf.confidence_level = get_confidence_level(float(r_squared))
                expr_conf.token_count = 0
                expr_conf.cost_amount = 0
                expr_conf.langsmith_sharelinks = None
                expr_conf.wandb_run.wandb_log({"Total Token": 0})
                expr_conf.wandb_run.wandb_log({"Total Cost": 0})
            update_wandb_configs(expr_conf)
            if expr_conf.langsmith_sharelinks:
                logger.info("Langsmith sharelinks generated successfully")
                create_langsmith_files_local(expr_conf)
                push_langsmith_files_to_wandb(expr_conf)


def r_statistic(x, y):
    # Supporting function for calculating Spearman correlation (p value)
    return spearmanr(x, y, nan_policy="omit").correlation


def get_similarity_score(
    expr_conf: ExperimentConfig,
    first_run_id: str,
    first_experiment_name: str,
    second_run_id: str,
    second_experiment_name: str,
    model_name: str = "clm",
):
    # NOT USED
    # Get the run model outputs from Wandb configs and run the correlation matrix
    # It assumes that the levels are the same for both runs/experiments

    # 1: Get the model output configs here from Wandb
    #    --coef, varname are the columns names
    # 2: store the coefficients with the level names(varname) in an ordered way
    # Pick the coefficients and feed to spearman
    try:
        w_api = WandbAPI()
        first_run_model_output_path = w_api.get_wandb_experiment_model_output(
            first_run_id,
            settings.EXPERIMENT_DATA_FOLDER_NAME,
            model_name,
        )

        second_run_model_output_path = w_api.get_wandb_experiment_model_output(
            second_run_id,
            settings.EXPERIMENT_DATA_FOLDER_NAME,
            model_name,
        )

        if first_run_model_output_path and second_run_model_output_path:
            with open(first_run_model_output_path, mode="r") as first_expr_file:
                first_model_output = json.load(first_expr_file)
                first_model_df = pd.DataFrame(
                    data=first_model_output["data"],
                    columns=first_model_output["columns"],
                )
                first_model_df.sort_values(by="varname")

            with open(second_run_model_output_path, mode="r") as second_expr_file:
                second_model_output = json.load(second_expr_file)
                second_model_df = pd.DataFrame(
                    data=second_model_output["data"],
                    columns=second_model_output["columns"],
                )
                second_model_df.sort_values(by="varname")

            if not (first_model_df["varname"] == second_model_df["varname"]).all():
                return False, "Varnames/Levels aren't similar for the runs"

            first_models_coefs = first_model_df["coef"].apply(
                lambda x: float(unidecode(x))
            )
            spearman_result = spearmanr(
                first_models_coefs.values,
                second_model_df["coef"].values,
                nan_policy="omit",
                alternative="two-sided",
            )
            res_exact = permutation_test(
                (first_model_df["coef"].values, second_model_df["coef"].values),
                r_statistic,
                permutation_type="pairings",
            )

            delete_local_dir(settings.EXPERIMENT_DATA_FOLDER_NAME, first_run_id)
            delete_local_dir(settings.EXPERIMENT_DATA_FOLDER_NAME, second_run_id)

            return True, (spearman_result.correlation, res_exact.pvalue)

    except Exception as e:
        logger.error(f"Error computing similarity score: {e}", exc_info=True)
        capture_exception(e)
        return False, str(e)


def convert_coefficent_to_float(expr_conf: ExperimentConfig, c: Any) -> float:
    """
    Take input of the coefficent in any format and convert into float
    """
    try:
        if isinstance(c, float):
            return c
        if isinstance(c, int):
            return float(c)
        elif not c.isascii():
            return float(unidecode(c))
        elif isinstance(c, str):
            return float(c)
        else:
            return c
    except Exception as e:
        logger.error(f"Error converting coefficent to float: {e}", exc_info=True)
        capture_exception(e)
        return c


def compute_similarity_score_with_baseline(expr_conf: ExperimentConfig):
    logger.info(
        f"Computing similarity score with baseline for experiment: {expr_conf.title}"
    )

    try:
        model_name = get_value_from_list(
            expr_conf.paper_data,
            "regression_model",
        )

        if model_name == "clm":
            model_path = expr_conf.file_paths["clm_model_coefficients"]
        elif model_name == "ols":
            model_path = expr_conf.file_paths["ols_coefficients"]
        else:
            model_path = expr_conf.file_paths["ols_coefficients"]

        baseline_model_path = WandbAPI().get_wandb_experiment_model_output(
            expr_conf.hb_run_id,
            settings.EXPERIMENT_DATA_FOLDER_NAME,
            model_name=model_name,
        )

        if baseline_model_path and model_path:
            with open(baseline_model_path, mode="r") as first_expr_file:
                first_model_output = json.load(first_expr_file)

                first_model_df = pd.DataFrame(
                    data=first_model_output["data"],
                    columns=first_model_output["columns"],
                )

                first_model_df.sort_values(by="varname").reset_index(
                    drop=True, inplace=True
                )

            second_model_df = pd.read_csv(model_path)
            second_model_df.sort_values(by="varname").reset_index(
                drop=True, inplace=True
            )

            if not (first_model_df["varname"] == second_model_df["varname"]).all():
                return False, "Varnames/Levels aren't similar for the runs"

            # Making sure the format of model coefficents are consisent
            first_models_coefs = first_model_df["coef"].apply(
                lambda c: convert_coefficent_to_float(expr_conf, c)
            )

            second_model_coefs = second_model_df["coef"].apply(
                lambda c: convert_coefficent_to_float(expr_conf, c)
            )

            spearman_result = spearmanr(
                first_models_coefs.values,
                second_model_coefs,
                nan_policy="omit",
                alternative="two-sided",
            )

            res_exact = permutation_test(
                (first_model_df["coef"].values, second_model_df["coef"].values),
                r_statistic,
                permutation_type="pairings",
            )

            delete_local_dir(settings.EXPERIMENT_DATA_FOLDER_NAME, expr_conf.hb_run_id)
            logger.info(
                "Similarity score computed successfully for experiment:"
                f" {expr_conf.title}"
            )

            return True, (spearman_result.correlation, res_exact.pvalue)

    except Exception as e:
        logger.error(f"Error computing similarity score: {e}", exc_info=True)
        capture_exception(e)
        return False, str(e)  # TODO: why not raise instead of returning False?🤔


def get_confidence_level(r_squared: float) -> str:
    if r_squared < 0.4:
        return "Low"
    elif r_squared < 0.65:
        return "Reasonable"
    else:
        return "High"


def create_langsmith_files_local(expr_conf: ExperimentConfig):
    """Create Langsmith files for the experiment."""
    logger.info(f"Writing Langsmith files locally for experiment: {expr_conf.title}")

    if settings.USE_LOCAL_STORAGE:
        try:
            with open(
                expr_conf.file_paths["langsmith_sharelinks"], "w", encoding="utf-8"
            ) as f:
                json.dump(expr_conf.langsmith_sharelinks, f, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving Langsmith files: {e}", exc_info=True)
            capture_exception(e)
        else:
            logger.info("Langsmith files saved successfully")


def push_langsmith_files_to_wandb(expr_conf: ExperimentConfig):
    """Push Langsmith files to WandB."""
    logger.info(f"Pushing Langsmith files to WandB for experiment: {expr_conf.title}")
    if settings.USE_WANDB_STORAGE:
        try:
            expr_conf.wandb_run.wandb_log_artifact(
                file_path=expr_conf.file_paths["langsmith_sharelinks"],
                file_type="any",
                artifact_main_directory="langsmith_sharelinks",
                artifact_name=f"langsmith_sharelinks_{expr_conf.wandb_run.name}",
                file_extension=".json",
            )

            logger.info(
                "Langsmith files successfully pushed to storage for experiment:"
                f" {expr_conf.title}"
            )
        except Exception as e:
            logger.error(f"Error logging artifacts to WandB: {e}", exc_info=True)
            capture_exception(e)


def plot_amce(df: pd.DataFrame, output_file_path: str, is_hb_run: bool):
    df["level_ids"] = df["level_ids"].astype(int)

    df_sorted = df.sort_values(["level_ids"], ascending=False)
    if is_hb_run:
        df_sorted["AMCE"] = df_sorted["Calculated AMCE"].astype(float)
        df_sorted["std_error"] = df_sorted["Calculated std_error"].astype(float)
        df_sorted["significance"] = df_sorted["Calculated significance"]
    else:
        df_sorted["AMCE"] = df_sorted["AMCE"].astype(float)
        df_sorted["std_error"] = df_sorted["std_error"].astype(float)

    df_sorted = df_sorted[df_sorted["attribute_text"] != "Intercept"]
    df_sorted.loc[:, "AMCE_LL"] = df_sorted["AMCE"] - 1.96 * df_sorted["std_error"]
    df_sorted.loc[:, "AMCE_UL"] = df_sorted["AMCE"] + 1.96 * df_sorted["std_error"]

    df_sorted["AMCE_LL"] = np.where(df_sorted["AMCE_LL"] < -1, -1, df_sorted["AMCE_LL"])
    df_sorted["AMCE_UL"] = np.where(df_sorted["AMCE_LL"] > 1, 1, df_sorted["AMCE_UL"])

    df_sorted["level_text"] = df_sorted["level_text"].astype(str)

    y_label = []
    for i, (_, row) in enumerate(df_sorted.iterrows()):
        if row["significance"] != "Base Level":
            y_label.append(f"{row['attribute_text']} : {row['level_text']}")
        else:
            y_label.append(f"{row['attribute_text']} (Base): {row['level_text']}")

    df_sorted["Y-label"] = y_label
    plt.style.context("fivethirtyeight")
    plt.rcParams["ytick.major.pad"] = 0.2

    fig, ax = plt.subplots(figsize=(18, 21))

    for i, (_, row) in enumerate(df_sorted.iterrows()):
        if row["significance"] != "Base Level":
            if row["AMCE"] < 0:
                ax.plot(
                    [row["AMCE"], row["AMCE"]], [i, i], "o-", color="red", markersize=5
                )
                ax.plot(
                    [row["AMCE_LL"], row["AMCE_UL"]],
                    [i, i],
                    "-",
                    color="red",
                    linewidth=1,
                )
            elif row["AMCE"] > 0:
                ax.plot(
                    [row["AMCE"], row["AMCE"]], [i, i], "o-", color="blue", markersize=5
                )
                ax.plot(
                    [row["AMCE_LL"], row["AMCE_UL"]],
                    [i, i],
                    "-",
                    color="blue",
                    linewidth=1,
                )
        else:
            ax.plot(
                [row["AMCE"], row["AMCE"]], [i, i], "o-", color="grey", markersize=5
            )
            ax.plot(
                [row["AMCE_LL"], row["AMCE_UL"]], [i, i], "-", color="grey", linewidth=1
            )

    ax.set_yticks(range(len(df_sorted)))
    wrapped_labels = []
    for label in df_sorted["Y-label"]:
        wrapped = textwrap.fill(
            label, width=80, break_long_words=False, break_on_hyphens=False
        )
        wrapped_labels.append(wrapped)

    ax.set_yticklabels([])
    for i, label in enumerate(wrapped_labels):
        ax.text(
            -1.05,
            i,
            r"{}".format(label),
            ha="right",
            va="center",
            fontsize=12,
            fontweight="bold",
            linespacing=1.5,
        )

    ax.spines["top"].set_visible(False)
    ax.spines["right"].set_visible(False)
    ax.spines["bottom"].set_visible(False)
    ax.spines["left"].set_visible(False)
    ax.set_xlim(-1.0, 1.0)
    ax.set_xticks([-1.0, -0.7, -0.4, 0.0, 0.4, 0.7, 1.0])
    ax.axvline(x=0, color="red", linestyle="--", linewidth=0.8)

    ax.set_title("Importance spectrum", fontsize=16, fontweight="bold")

    ax.set_facecolor("lightgrey")
    ax.grid(color="white")
    plt.tight_layout()
    plt.tick_params(axis="y", which="major", labelsize=18)
    plt.xticks([-1.0, -0.7, -0.4, 0.0, 0.4, 0.7, 1.0], weight="bold")

    plt.tight_layout()
    plt.savefig(output_file_path, bbox_inches="tight", dpi=300)
    plt.close()
