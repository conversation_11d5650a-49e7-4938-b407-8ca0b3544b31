from math import *

import numpy as np
import pandas as pd
from patsy.contrasts import Sum
from scipy.stats import chi2_contingency, qmc

from app.core.orthogonal import orthogonal_arrays


def get_oa(factors, *args, **kwargs):
    oa = orthogonal_arrays.orthogonal_array(factors)
    return oa


def HaltonSequence(n, dim):
    n = int(n) + 10
    dim = int(dim)
    prim = np.array([
        2,
        3,
        5,
        7,
        11,
        13,
        17,
        19,
        23,
        29,
        31,
        37,
        41,
        43,
        47,
        53,
        59,
        61,
        67,
        71,
        73,
        79,
        83,
        89,
        97,
        101,
        103,
        107,
        109,
        113,
        127,
        131,
        137,
        139,
        149,
        151,
        157,
        163,
        167,
        173,
        179,
        181,
        191,
        193,
        197,
        199,
        211,
        223,
        227,
        229,
        233,
        239,
        241,
        251,
        257,
        263,
        269,
        271,
        277,
        281,
        283,
        293,
        307,
        311,
        313,
        317,
        331,
        337,
        347,
        349,
        353,
        359,
        367,
        373,
        379,
        383,
        389,
        397,
        401,
        409,
        419,
        421,
        431,
        433,
        439,
        443,
        449,
        457,
        461,
        463,
        467,
        479,
        487,
        491,
        499,
        503,
        509,
        521,
        523,
        541,
    ])
    prim = prim[:, np.newaxis]
    hs = np.zeros((n, dim))
    for idim in range(dim):
        b = prim[idim, 0]
        hs[:, idim] = halton(n, b)

    return hs[10:n, :]


def halton(n, s):
    k = floor(log(n + 1) / log(s))
    phi = np.zeros((1, 1))
    i = 1
    count = 0
    while i <= k:
        count = count + 1
        x = phi
        j = 1
        while j < s:
            y = phi + (j / s**i)
            x = np.vstack((x, y))
            j += 1

        phi = x
        i += 1

    x = phi
    j = 1
    while (j < s) and (len(x) < (n + 1)):
        y = phi + (j / s**i)
        x = np.vstack((x, y))
        j += 1

    out = x[1 : (n + 1), 0]
    return out


def keep_unique_rows(arr):
    # Convert to a structured array
    dtype = [("", arr.dtype)] * arr.shape[1]
    structured = arr.view(dtype)

    # Get unique rows
    _, idx = np.unique(structured, return_index=True)

    # Sort the index to maintain original order
    idx.sort()

    # Return the unique rows in their original order
    return arr[idx]


def quantile_binning(data, num_bins):
    # Calculate quantiles
    quantiles = np.linspace(0, 100, num_bins + 1)
    bins = np.percentile(data, quantiles)

    # Assign bins
    bin_assignments = np.digitize(data, bins[1:-1])
    bin_assignments = 1 + bin_assignments
    return bin_assignments


def generate_orthogonal_array_Sobol(factors_dict, exponent):
    # Extract the number of factors and their levels
    factor_names = list(factors_dict.keys())
    factor_levels = [len(value) for _, value in factors_dict.items()]
    sampler = qmc.Sobol(d=len(factor_levels), scramble=True)
    designs = sampler.random_base2(m=exponent)
    Num_runs = designs.shape[0]
    df = np.zeros((Num_runs, len(factor_names)))
    for index, value in enumerate(factor_names):
        df[:, index] = quantile_binning(designs[:, index], factor_levels[index])
    Unique_rows = keep_unique_rows(df)
    Unique_rows = Unique_rows.astype(int)
    df = pd.DataFrame(Unique_rows, columns=factor_names)
    return df


def generate_orthogonal_array_Halton(factors_dict, num_runs):
    # Extract the number of factors and their levels
    factor_names = list(factors_dict.keys())
    factor_levels = [len(value) for _, value in factors_dict.items()]
    Num_runs = num_runs

    designs = HaltonSequence(Num_runs, len(factor_levels))
    df = np.zeros((Num_runs, len(factor_names)))
    for index, value in enumerate(factor_names):
        df[:, index] = quantile_binning(designs[:, index], factor_levels[index])
    Unique_rows = keep_unique_rows(df)
    Unique_rows = Unique_rows.astype(int)
    df = pd.DataFrame(Unique_rows, columns=factor_names)
    return df


def blockgen(DES, NBLOCKS, NCS, REPS):
    blocks = np.repeat(np.arange(1, NBLOCKS + 1), NCS / NBLOCKS).copy()
    blocks.shape = (NCS, 1)
    bestcorr = np.inf
    bestblock = blocks.copy()

    for _ in range(0, REPS):
        np.random.shuffle(blocks)
        blockmat = np.repeat(blocks, int(np.max(DES[:, 1]))).copy()
        blockmat.shape = (blockmat.shape[0], 1)
        sumcorr = 0

        for a in range(2, DES.shape[1]):
            d = DES[:, a].copy()
            d.shape = (d.shape[0], 1)
            c = cross(blockmat, d)
            corr = chi2_contingency(c)[1]
            sumcorr = sumcorr + corr

        if sumcorr < bestcorr:
            bestblock = blockmat.copy()
            bestcorr = sumcorr

    bestblock.shape = (bestblock.shape[0], 1)

    return bestblock


def cross(x, y):
    tab = []

    for i in np.unique(x):
        cols = []

        for j in np.unique(y):
            c = np.count_nonzero((x == i) & (y == j))
            cols = cols + [c]

        tab = tab + [cols]

    return np.array(tab)


def get_Ortho_contrast_coding(Num_factors):
    factor_levels = [str(i + 1) for i in range(Num_factors)]
    contrast_matrix = Sum().code_without_intercept(factor_levels)
    intercept_column = np.ones(Num_factors)
    design_matrix_with_intercept = np.column_stack(
        (intercept_column, contrast_matrix.matrix)
    )
    SOCC, _ = np.linalg.qr(design_matrix_with_intercept)
    final_mat = sqrt(Num_factors) * SOCC
    final_mat = np.round(final_mat, 6)
    final_mat = final_mat[:, 1:]
    return final_mat


def D_efficiency(DES):
    for i in range(0, DES.shape[1]):
        curr_ortho_mat = get_Ortho_contrast_coding(int(np.unique(DES[:, i]).shape[0]))

        linew_list = [curr_ortho_mat[idx - 1, :].tolist() for idx in DES[:, i]]
        new_list = np.array(linew_list)
        try:
            temp = new_list.shape[1]
        except:
            new_list = new_list[:, np.newaxis]

        if i == 0:
            X_mat = new_list
        else:
            X_mat = np.hstack((X_mat, new_list))
    try:
        vce = np.linalg.inv(X_mat.T @ X_mat)
        detvce = np.linalg.det(vce)
        dr = detvce ** (1 / vce.shape[0])
        ND = DES.shape[0]
        deff = 100 / (dr * ND)
    except Exception:
        deff = 0
    return deff
