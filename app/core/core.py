import asyncio
import itertools
import os
import re
import sys
import time
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from copy import deepcopy
from functools import partial
from math import *
from typing import Any, Dict, List, TypeVar

import numpy as np
import pandas as pd
from billiard.exceptions import SoftTimeLimitExceeded
from langsmith import Client, trace
from sentry_sdk import capture_exception
from tenacity import retry, stop_after_attempt

import app.api.v2.schemas.latent_variables as lv_models
import app.api.v2.schemas.latent_variables_core as lv_models_core
from app.api.v1.helpers.attributes_levels import get_attributes, get_levels
from app.api.v1.helpers.population import create_population
from app.api.v1.schemas.attributes_levels import (
    AttributesLevelsRequest,
    LeveledAttributeResponse,
)
from app.api.v1.schemas.experiments import (
    DependentVariableResponse,
    ObjectOfStudyResponse,
    OutcomePhraseResponse,
    StudyContextResponse,
    SurveyResponseType,
    TargetBehaviorResponse,
)
from app.api.v1.schemas.traits import BinaryVariableToNaturalLanguageResponse
from app.api.v2.endpoints.attributes_levels import create_attributes_and_levels
from app.api.v2.endpoints.concept_testing import generate_statement_scores
from app.api.v2.endpoints.latent_variables import latent_variables_full_run
from app.api.v2.helpers.core import (
    convert_analytics_traits_data_to_dict,
    get_persona_statement_scores_info,
    get_traits_data_for_analytics,
)
from app.api.v2.schemas.concept_testing import CTStatementScoresRequest
from app.auth.constants import USA_NAMES
from app.core.experiment_config import ExperimentConfig
from app.core.global_config import settings
from app.core.orthogonal import io
from app.core.utils.aws_s3 import download_file_from_s3
from app.core.utils.common import (
    chunk_list,
    convert_image_to_base64,
    delete_local_artifacts,
    get_enum_value,
    get_max_workers,
    is_reference_level,
    summarize_image,
)
from app.core.utils.constants import capitals
from app.core.utils.logging import app_logger
from app.core.utils.models import SurveyChoiceResponse, SurveyResponse, SurveyTask
from app.core.utils.type_enums import LLMModel
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor
from app.llm_prompt.prompt_templates import (
    DETAILED_IMAGE_DESCRIPTION_TEMPLATE,
    IMAGE_BASED_INTRO_TEMPLATE,
    PRICE_QUESTION_TEMPLATE,
)
from app.personas.population_trait_additions import (
    add_traits_from_firebase_uniformally_to_df,
)

logger = app_logger.get_logger(__name__)

# range of tasks that an individual will perform
Num_tasks = [5, 6, 7, 8, 9, 10, 11, 12]

Generator_Configuration = [
    [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    [
        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        [0, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5],
    ],
    [
        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        [0, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5],
        [1, 0, 3, 3, 2, 4, 4, 5, 5, 6, 6, 7, 7, 7],
    ],
    [
        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        [0, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5],
        [1, 0, 3, 3, 2, 4, 4, 5, 5, 6, 6, 7, 7, 7],
        [0, 1, 0, 4, 4, 2, 5, 6, 6, 7, 8, 8, 9, 9],
    ],
    [
        [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        [0, 2, 2, 2, 3, 3, 3, 4, 4, 4, 5, 5, 5, 5],
        [1, 0, 3, 3, 2, 4, 4, 5, 5, 6, 6, 7, 7, 7],
        [0, 1, 0, 4, 4, 2, 5, 6, 6, 7, 8, 8, 9, 9],
        [1, 2, 4, 0, 5, 5, 2, 7, 7, 8, 9, 10, 11, 12],
    ],
]

Generator_Configuration_Attribute_levels = [
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
]
Block_Display_Frequency = 3
# Columns to be extracted for synthetic population Data
Required_Population_Columns = [
    "Person ID",
    "Age",
    "Sex",
    "Education Level",
    "Census Division",
    "Race",
    "Hispanic/Latino Origin",
    "Home Ownership",
    "Number of Vehicles in Household",
    "Living in a Urban Area",
    "Household Annual Income",
    "Number of Female Children in the household",
    "Number of Male Children in the household",
    "Number of Children Aged zero to five years old in the household",
    "Number of Children Aged six to ten years old in the household",
    "Number of Children Aged eleven to fifteen years old in the household",
]

# core traits to query for in postgres
DB_Population_Columns = {
    "age": "Age",
    "number_of_children": "Number of Children",
    "household_with_children": "Household with Children",
    "education_level": "Education Level",
    "household_income": "Household Income",
    "state": "State",
    "gender": "Gender",
    "racial_group": "Racial Group",
}

# mapping postgres columns to column names to consume by LLM
DB_Population_Columns_Extended = {
    "ID": "ID",
    "household_income": "household income",
    "vehicles_in_household": "vehicles in household",
    "census_division": "census division",
    "state": "state",
    "gender": "gender",
    "marital_status": "marital status",
    "family_size": "family size",
    "age": "age",
    "education_level": "education level",
    "hispanic_latino": "hispanic latino",
    "household_wt": "household wt",
    "person_wt": "person wt",
    "veteran_status": "veteran status",
    "migration_status": "migration status",
    "speaks_english": "speaks english",
    "health_insurance_coverage": "health insurance coverage",
    "number_of_children": "number of children",
    "racial_group": "racial group",
}

DB_Flipped_Columns = {
    "Vehicles In Household": "vehicles_in_household",
    "Census Division": "census_division",
    "Marital Status": "marital_status",
    "Family Size": "family_size",
    "Hispanic Latino": "hispanic_latino",
    "Veteran Status": "veteran_status",
    "Migration Status": "migration_status",
    "Speaks English": "speaks_english",
    "Health Insurance Coverage": "health_insurance_coverage",
    "Language Spoken": "language_spoken",
}

non_us_demo_trait_columns = [
    "Gender",
    "Age",
    "Education Level",
    "Race",
    "Hispanic/Latino Origin",
    "Home Ownership",
    "Household Size",
    "Number of Vehicles in Household",
    "Household with Children",
]

# Global variables. Needs to be set from or during API call
Orthogonal_Design = (
    1  # Set 1 to use Orthogonal design. Otherwise, random design is generated
)
Same_Attribute_shuffle_order = (
    True  # Set true to perform shuffling of attributes during task display
)
Population_Definition = (
    False  # Set True only if frontend has population definition module
)
Include_Firebase_Traits_Uniform = True  # Add traits uniformly from firebase
T = TypeVar("T", bound=Any)


Currency_Names = [
    "Aruban Florin",
    "Lek",
    "UAE Dirham",
    "Argentine Peso",
    "Armenian Dram",
    "Australian Dollar",
    "Burundi Franc",
    "Taka",
    "Bahraini Dinar",
    "Bahamian Dollar",
    "Belize Dollar",
    "Bermudian Dollar",
    "Boliviano",
    "Barbados Dollar",
    "Brunei Dollar",
    "Ngultrum",
    "Pula",
    "Canadian Dollar",
    "Swiss Franc",
    "Chilean Peso",
    "Yuan Renminbi",
    "Colombian Peso",
    "Comoro Franc",
    "Cabo Verde Escudo",
    "Costa Rican Colon",
    "Cuban Peso",
    "Cayman Islands Dollar",
    "Czech Koruna",
    "Djibouti Franc",
    "Danish Krone",
    "Dominican Peso",
    "Algerian Dinar",
    "Egyptian Pound",
    "Nakfa",
    "Fiji Dollar",
    "Falkland Islands Pound",
    "Pound Sterling",
    "Gibraltar Pound",
    "Guinea Franc",
    "Dalasi",
    "Quetzal",
    "Guyana Dollar",
    "Hong Kong Dollar",
    "Lempira",
    "Kuna",
    "Gourde",
    "Forint",
    "Rupiah",
    "Indian Rupee",
    "Iranian Rial",
    "Iraqi Dinar",
    "Iceland Krona",
    "New Israeli Sheqel",
    "Jamaican Dollar",
    "Jordanian Dinar",
    "Yen",
    "Tenge",
    "Kenyan Shilling",
    "Som",
    "Riel",
    "Won",
    "Kuwaiti Dinar",
    "Kip",
    "Lebanese Pound",
    "Liberian Dollar",
    "Libyan Dinar",
    "Sri Lanka Rupee",
    "Loti",
    "Pataca",
    "Moroccan Dirham",
    "Moldovan Leu",
    "Rufiyaa",
    "Mexican Peso",
    "Denar",
    "Kyat",
    "Tugrik",
    "Ouguiya",
    "Mauritius Rupee",
    "Malawi Kwacha",
    "Malaysian Ringgit",
    "Namibia Dollar",
    "Naira",
    "Cordoba Oro",
    "Norwegian Krone",
    "Nepalese Rupee",
    "New Zealand Dollar",
    "Rial Omani",
    "Pakistan Rupee",
    "Sol",
    "Philippine Peso",
    "Kina",
    "North Korean Won",
    "Guarani",
    "Qatari Rial",
    "Russian Ruble",
    "Rwanda Franc",
    "Saudi Riyal",
    "Singapore Dollar",
    "Saint Helena Pound",
    "Solomon Islands Dollar",
    "Leone",
    "El Salvador Colon",
    "Somali Shilling",
    "South Sudanese Pound",
    "Dobra",
    "Swedish Krona",
    "Lilangeni",
    "Seychelles Rupee",
    "Syrian Pound",
    "Baht",
    "Pa'anga",
    "Trinidad and Tobago Dollar",
    "Tunisian Dinar",
    "Tanzanian Shilling",
    "Uganda Shilling",
    "Peso Uruguayo",
    "US Dollar",
    "Uzbekistan Sum",
    "Dong",
    "Vatu",
    "Tala",
    "Rand",
]

# Currency abbreviations
Currency_Abbreviations = [
    "AWG",
    "ALL",
    "AED",
    "ARS",
    "AMD",
    "AUD",
    "BIF",
    "BDT",
    "BHD",
    "BSD",
    "BZD",
    "BMD",
    "BOB",
    "BBD",
    "BND",
    "BTN",
    "BWP",
    "CAD",
    "CHF",
    "CLP",
    "CNY",
    "COP",
    "KMF",
    "CVE",
    "CRC",
    "CUP",
    "KYD",
    "CZK",
    "DJF",
    "DKK",
    "DOP",
    "DZD",
    "EGP",
    "ERN",
    "FJD",
    "FKP",
    "GBP",
    "GIP",
    "GNF",
    "GMD",
    "GTQ",
    "GYD",
    "HKD",
    "HNL",
    "HRK",
    "HTG",
    "HUF",
    "IDR",
    "INR",
    "IRR",
    "IQD",
    "ISK",
    "ILS",
    "JMD",
    "JOD",
    "JPY",
    "KZT",
    "KES",
    "KGS",
    "KHR",
    "KRW",
    "KWD",
    "LAK",
    "LBP",
    "LRD",
    "LYD",
    "LKR",
    "LSL",
    "MOP",
    "MAD",
    "MDL",
    "MVR",
    "MXN",
    "MKD",
    "MMK",
    "MNT",
    "MRO",
    "MUR",
    "MWK",
    "MYR",
    "NAD",
    "NGN",
    "NIO",
    "NOK",
    "NPR",
    "NZD",
    "OMR",
    "PKR",
    "PEN",
    "PHP",
    "PGK",
    "KPW",
    "PYG",
    "QAR",
    "RUB",
    "RWF",
    "SAR",
    "SGD",
    "SHP",
    "SBD",
    "SLL",
    "SVC",
    "SOS",
    "SSP",
    "STD",
    "SEK",
    "SZL",
    "SCR",
    "SYP",
    "THB",
    "TOP",
    "TTD",
    "TND",
    "TZS",
    "UGX",
    "UYU",
    "USD",
    "UZS",
    "VND",
    "VUV",
    "WST",
    "ZAR",
]

Currency_Symbols = [
    "\$",
    "£",
    "€",
    "¥",
    "¥",
    "₹",
    "$",
    "Fr",
    "₽",
    "R$",
    "R",
    "kr",
    "kr",
    "kr",
    "₩",
    "₺",
    "﷼",
    "د.إ",
    "RM",
    "Rp",
    "฿",
    "₪",
]
all_units = [
    # Percent
    "%",
    "percent",
    # Length
    "nm",
    "μm",
    "um",
    "angstrom",
    "Å",
    "pc",
    "ly",
    "cm",
    "mm",
    "m",
    "km",
    "in",
    "ft",
    "yd",
    "mile",
    # Area
    "m²",
    "cm²",
    "ft²",
    "sq m",
    "sq ft",
    "acre",
    "acres",
    "hectare",
    "hectares",
    # Volume
    "L",
    "l",
    "ml",
    "m³",
    "cm³",
    "ft³",
    "gal",
    "qt",
    "pint",
    "cup",
    "fl oz",
    # Mass / Weight
    "kg",
    "g",
    "mg",
    "lb",
    "lbs",
    "oz",
    "ton",
    "tonne",
    "mt",
    "ct",
    "slug",
    "stone",
    # Time
    "s",
    "ms",
    "μs",
    "us",
    "ns",
    "sec",
    "second",
    "seconds",
    "min",
    "minute",
    "minutes",
    "h",
    "hr",
    "hour",
    "hours",
    "days",
    "weeks",
    "months",
    "years",
    # Digital Storage
    "MB",
    "GB",
    "TB",
    "PB",
    "EB",
    "ZB",
    "YB",
    "kb",
    "KiB",
    "MiB",
    "GiB",
    "bit",
    "byte",
    # Energy / Power
    "kcal",
    "cal",
    "J",
    "kJ",
    "Wh",
    "kWh",
    "MW",
    "GW",
    "BTU",
    "HP",
    # Temperature
    "K",
    "°C",
    "Celsius",
    "°F",
    "Fahrenheit",
    "R",
    # Frequency
    "Hz",
    "kHz",
    "MHz",
    "GHz",
    "THz",
    "rad/s",
    # Scientific / Concentration
    "mol",
    "mmol",
    "mol/L",
    "ppm",
    "ppb",
    "ppt",
    "mg/L",
    "μg/L",
    # Business / UX Metrics
    "stars",
    "points",
    "ratings",
    "visits",
    "users",
    "sessions",
    "clicks",
    "views",
    "conversions",
    "likes",
    "shares",
    "CTR",
    "ROI",
    "NPS",
    "score",
    "rank",
    # Finance / General Quantities
    "units",
    "items",
    "orders",
    "transactions",
    "revenue",
    "profits",
    "losses",
    # Power
    "W",
    "kW",
    "MW",
    "GW",
    "mW",
    "Wh",
    "kWh",
    "HP",
    "dBm",
    "VA",
    "kVA",
    # Noise / Sound
    "dB",
    "dBA",
    "dBC",
    "SPL",
    "phon",
    "sone",
]

Known_Units = (
    [
        "%",
        "percent",
        "kg",
        "g",
        "mg",
        "lbs",
        "oz",
        "cm",
        "mm",
        "m",
        "km",
        "in",
        "ft",
        "yd",
        "mile",
        "$",
        "€",
        "£",
        "₹",
        "¥",
        "₩",
        "₽",
        "₺",
        "USD",
        "EUR",
        "INR",
        "JPY",
        "KRW",
        "RUB",
        "TRY",
        "EURO",
        "EUROS",
        "sec",
        "second",
        "seconds",
        "min",
        "minute",
        "hour",
        "hours",
        "MB",
        "GB",
        "TB",
        "kb",
        "bit",
        "byte",
        "kcal",
        "cal",
        "J",
        "kJ",
        "°C",
        "Celsius",
        "°F",
        "Fahrenheit",
        "K",
        "Hz",
        "kHz",
        "MHz",
        "GHz",
        "stars",
        "points",
        "visits",
        "users",
        "ratings",
        "days",
        "weeks",
        "months",
        "years",
        "W",
        "tokens",
        "coins",
        "euros",
    ]
    + all_units
    + Currency_Names
    + Currency_Abbreviations
    + Currency_Symbols
)

Known_Units = set(Known_Units)
Known_Units = list(Known_Units)

Start_context_keywords = [
    "range",
    "between",
    "from",
    "within",
    "spanning",
    "covering",
    "ranges from",
    "ranging from",
    "spans",
    "covers",
    "is between",
    "is within",
]
End_context_keywords = ["to", "through", "until", "up to", "and"]

Currency_Names_Regex = "|".join([re.escape(name) + "s?" for name in Currency_Names])
Currency_Abbreviations_Regex = "|".join(
    [re.escape(name) + "s?" for name in Currency_Abbreviations]
)
Currency_Symbols_Regex = "|".join([re.escape(name) + "s?" for name in Currency_Symbols])

all_currency_units = (
    f"{Currency_Symbols_Regex}|{Currency_Abbreviations_Regex}|{Currency_Names_Regex}"
)
Start_context_Regex = "|".join(
    [re.escape(name) + "s?" for name in Start_context_keywords]
)
End_context_Regex = "|".join([re.escape(name) + "s?" for name in End_context_keywords])
unit_pattern_regex = "|".join([re.escape(name) + "s?" for name in all_units])

Range_Phrase_Pattern = re.compile(
    rf"(?:(?:{Start_context_Regex})\s+)?"
    rf"(?P<first_unit_before>{all_currency_units})?"
    rf"(?P<first>{all_currency_units}?\d{1,3}(?:[,]\d{3})*|\d+(?:\.\d+)?)"
    rf"(?P<first_unit_after>("
    rf"{unit_pattern_regex}|"
    rf"{all_currency_units}|[a-zA-Z]{{2,20}}"
    rf"))?"
    rf"\s*(?:-|–|—|{End_context_Regex})\s*"
    rf"(?P<second_unit_before>{all_currency_units})?"
    rf"(?P<second>{all_currency_units}?\d{1,3}(?:[,]\d{3})*|\d+(?:\.\d+)?)"
    rf"(?P<second_unit_after>("
    rf"{unit_pattern_regex}|"
    rf"{all_currency_units}|[a-zA-Z]{{2,20}}"
    rf"))?",
    flags=re.IGNORECASE,
)

Range_Phrase_Pattern_For_Replacement = re.compile(
    rf"(?:(?:{Start_context_Regex})\s+)?"
    rf"(?P<first_unit_before>{all_currency_units})?"
    rf"(?P<first>"
    r"-?(?:\d{1,3}(?:[,_]\d{3})*|\d+)"
    r"(?:\.\d+)?"
    r"(?:[eE][+-]?\d+)?"
    r")"
    rf"(?P<first_unit_after>("
    r"{unit_pattern_regex}|"
    rf"{all_currency_units}|[a-zA-Z]{{2,20}}"
    r"))?"
    rf"\s*(?:-|–|—|{End_context_Regex})\s*"
    rf"(?P<second_unit_before>{all_currency_units})?"
    rf"(?P<second>"
    r"-?(?:\d{1,3}(?:[,_]\d{3})*|\d+)"
    r"(?:\.\d+)?"
    r"(?:[eE][+-]?\d+)?"
    r")"
    rf"(?P<second_unit_after>("
    r"{unit_pattern_regex}|"
    rf"{all_currency_units}|[a-zA-Z]{{2,20}}"
    r"))?",
    flags=re.IGNORECASE,
)


def filter_single_number_strings(strings, Null_level):
    filtered_strings = []
    level_continuous_count = 0
    is_continuous_var = False
    var_type = "int"
    if Null_level:
        strings = strings[1:]

    for s in strings:
        # Modified regex pattern to handle plain numbers, commas, and underscores
        numbers = re.findall(r"-?\d+(?:[,_]*\d+)*(?:\.\d+)?(?:[eE][+-]?\d+)?", s)

        # Remove commas and underscores from the found numbers
        cleaned_numbers = [num for num in numbers]

        # If there's exactly one number, convert it to float and add to the filtered list
        if len(cleaned_numbers) > 0 and len(cleaned_numbers) < 3:
            if len(cleaned_numbers) == 1:
                float_value = [string_to_float(num) for num in cleaned_numbers]
                if float_value is not None:
                    filtered_strings.append(float_value)
                    level_continuous_count += 1
            else:
                match = Range_Phrase_Pattern.search(s)
                if match:
                    # Extract all possible unit slots
                    units = [
                        match.group("first_unit_before"),
                        match.group("first_unit_after"),
                        match.group("second_unit_before"),
                        match.group("second_unit_after"),
                    ]

                    # Normalize and filter
                    normalized_units = [u.lower() for u in units if u]
                    unique_units = set(normalized_units)

                    # Only proceed if all detected units are the same and valid
                    Mismatch = len(unique_units) > 1
                    Unknown_unit = unique_units and not unique_units.issubset(
                        {u.lower() for u in Known_Units}
                    )

                    if not Mismatch and not Unknown_unit:
                        float_value = [string_to_float(num) for num in cleaned_numbers]
                        if None not in float_value:
                            if is_monotonic_increase(float_value):
                                filtered_strings.append(float_value)
                                level_continuous_count += 1

    if level_continuous_count == len(strings):
        is_continuous_var = True
        flatten_list = [i for sublist in filtered_strings for i in sublist]
        if has_float_element(flatten_list):
            var_type = "float"
    return is_continuous_var, filtered_strings, var_type


def string_to_float(s):
    cleaned_number = s.replace(",", "").replace("", "")
    try:
        curr_num = float(cleaned_number)
        if is_semantic_integer(curr_num):
            return int(curr_num)
        else:
            return curr_num
    except ValueError:
        return None


def is_semantic_integer(n):
    return isinstance(n, float) and n.is_integer()


def has_float_element(lst):
    for item in lst:
        if isinstance(item, float) and not item.is_integer():
            return True
    return False


def is_monotonic_decrease(nums):
    return all(nums[i] >= nums[i + 1] for i in range(len(nums) - 1))


def is_monotonic_increase(nums):
    return all(nums[i] <= nums[i + 1] for i in range(len(nums) - 1))


def replace_number_in_sentence(sentence, new_number, num_unique_values):

    # Fallback pattern for single numbers
    single_number_pattern = re.compile(r"-?\d+(?:[,_]*\d+)*(?:\.\d+)?(?:[eE][+-]?\d+)?")

    if num_unique_values == 2:
        match = Range_Phrase_Pattern_For_Replacement.search(sentence)
        if not match:
            return sentence

        # Extract all possible unit slots
        units = [
            match.group("first_unit_before"),
            match.group("first_unit_after"),
            match.group("second_unit_before"),
            match.group("second_unit_after"),
        ]

        # Normalize and filter
        normalized_units = [u.lower() for u in units if u]
        unique_units = set(normalized_units)

        # Only proceed if all detected units are the same and valid
        if len(unique_units) > 1:
            return sentence  # Mismatch
        if unique_units and not unique_units.issubset({u.lower() for u in Known_Units}):
            return sentence  # Unknown unit

        unit_before = (
            match.group("second_unit_before") or match.group("first_unit_before") or ""
        )
        unit_after = (
            match.group("second_unit_after") or match.group("first_unit_after") or ""
        )
        replacement = f"{unit_before}{new_number}{unit_after}"
        if new_number is not None:
            return sentence[: match.start()] + replacement + sentence[match.end() :]
        else:
            return sentence
    else:
        # Fallback: handle single number replacement
        match = single_number_pattern.search(sentence)
        if match and new_number is not None:
            return sentence[: match.start()] + str(new_number) + sentence[match.end() :]

    return sentence


def distribute_expanded_blocks(num_individuals, num_blocks):
    blocks = list(range(1, num_blocks + 1))
    distribution = [[] for _ in range(num_individuals)]

    for i, block in enumerate(blocks):
        distribution[i % num_individuals].append(block)

    return distribution


def convert_list_to_dict(lst):
    demo_dict = {}
    for item in lst:
        key, value = item.split(": ", 1)  # split on first occurrence only
        demo_dict[key] = value

    return demo_dict


def add_new_level(al_lookup):
    # Create a deep copy of the original dictionary
    new_al_lookup = {
        "get_attribute_text": al_lookup["get_attribute_text"].copy(),
        "get_level_ids": {},
        "get_attribute_id": {},
        "get_level_text": {},
    }

    # Step 1: Update get_level_ids by adding a new first level
    last_list_max = 0
    old_new_mapping = {}
    for key in al_lookup["get_level_ids"]:
        original_ids = al_lookup["get_level_ids"][key]
        new_first_id = last_list_max + 1
        new_list = [new_first_id] + [
            new_first_id + i for i in range(1, len(original_ids) + 1)
        ]
        new_al_lookup["get_level_ids"][key] = new_list
        last_list_max = new_list[-1]
        for i in range(1, len(original_ids) + 1):
            old_new_mapping[original_ids[i - 1]] = new_first_id + i

    # Step 2: Update get_attribute_id with new mappings
    for key in new_al_lookup["get_level_ids"]:
        for level_id in new_al_lookup["get_level_ids"][key]:
            new_al_lookup["get_attribute_id"][level_id] = int(key)

    # Step 3: Update get_level_text
    # First, copy existing text with shifted indices
    for key, value in al_lookup["get_level_text"].items():
        new_key = old_new_mapping[key]
        new_al_lookup["get_level_text"][new_key] = value

    # Add "Not available" for new first levels
    for key in new_al_lookup["get_level_ids"]:
        first_id = new_al_lookup["get_level_ids"][key][0]
        new_al_lookup["get_level_text"][first_id] = "Not available"
    new_al_lookup["get_level_text"] = dict(
        sorted(new_al_lookup["get_level_text"].items())
    )
    return new_al_lookup


def Alternative_Shuffle(data_file, Order_list_orig, alternative_shuffle_seed):
    logger.info("Shuffling alternatives")
    Order_list = [i + 1 for i in range(len(Order_list_orig))]
    Order_list = [Order_list for i in range(data_file.shape[0])]
    Shuffled_Order = shuffle_list_of_lists(Order_list, alternative_shuffle_seed)
    Shuffled_Order = np.array(Shuffled_Order)
    Alt_col_names = [f"Alt_{index + 1}_Final" for index in range(len(Order_list_orig))]
    for index, iname in enumerate(Alt_col_names):
        curr_alt_store = []
        for row_num in range(data_file.shape[0]):
            curr_col_name = Order_list_orig[Shuffled_Order[row_num, index] - 1]
            curr_value = data_file.loc[row_num, curr_col_name]
            curr_alt_store.append(curr_value)
        data_file[iname] = curr_alt_store
    return data_file, Shuffled_Order


def Attribute_Shuffle(
    data_file,
    Num_alts,
    Unique_Attributes,
    attribute_name_levels_grouped,
    attribute_type,
    attribute_continuous_value,
    null_level,
    attribute_levels_type_indicator,
    expr_conf: ExperimentConfig,
):
    logger.info("Shuffling attributes")
    # Attribute shuffling
    All_Orignal_levels = [[] for i in range(Num_alts)]
    All_level_text = [[] for i in range(Num_alts)]
    All_shuffled_text = [[] for i in range(Num_alts)]

    Price_Generation_seed = expr_conf.price_generation_seed
    Attribute_shuffle_seed = expr_conf.attribute_shuffle_seed

    if (not expr_conf.is_hb_run) and (not expr_conf.hb_folder):
        for _, row in data_file.iterrows():
            for i in range(1, Num_alts + 1):
                curr_data = row[f"Alt_{i}"]
                levels_text = []
                Orignal_levels = []
                for index_att, att_name in enumerate(Unique_Attributes):
                    key_list = attribute_name_levels_grouped[att_name]
                    key_value = key_list[curr_data[index_att] - 1]
                    if attribute_type[att_name][0] == "Continuous":
                        att_name_new = f"{att_name}"
                        Numeric_values = attribute_continuous_value[att_name]
                        if null_level:
                            if curr_data[index_att] == 1:
                                key_value_new = "Not available"
                                att_level = f"{att_name_new} : {key_value_new}"
                            else:
                                key_value_new, next_seed, num_unique_values = (
                                    Draw_price(
                                        curr_data[index_att],
                                        Numeric_values,
                                        attribute_levels_type_indicator[att_name],
                                        attribute_type[att_name][1],
                                        Price_Generation_seed,
                                    )
                                )
                                Price_Generation_seed = next_seed
                                final_price_text = replace_number_in_sentence(
                                    key_value, key_value_new, num_unique_values
                                )
                                att_level = f"{att_name_new} : {final_price_text}"
                        else:
                            key_value_new, next_seed, num_unique_values = Draw_price(
                                curr_data[index_att],
                                Numeric_values,
                                attribute_levels_type_indicator[att_name],
                                attribute_type[att_name][1],
                                Price_Generation_seed,
                            )
                            Price_Generation_seed = next_seed
                            final_price_text = replace_number_in_sentence(
                                key_value, key_value_new, num_unique_values
                            )
                            att_level = f"{att_name_new} : {final_price_text}"
                        levels_text.append(att_level)
                        Orignal_levels.append(key_value_new)
                    else:
                        att_level = f"{att_name} : {key_value}"
                        levels_text.append(att_level)
                        Orignal_levels.append(curr_data[index_att])

                if i == 1:
                    (
                        shuffled_levels_text,
                        shuffled_levels_int,
                        next_seed,
                    ) = shuffle_array(deepcopy(levels_text), Attribute_shuffle_seed)
                    Attribute_shuffle_seed = next_seed
                else:
                    if Same_Attribute_shuffle_order == True:
                        shuffled_levels_text = [
                            levels_text[j] for j in shuffled_levels_int
                        ]
                    else:
                        shuffled_levels_text, _, next_seed = shuffle_array(
                            deepcopy(levels_text), Attribute_shuffle_seed
                        )
                        Attribute_shuffle_seed = next_seed

                final_level_text, final_shufled_level_text = [], []
                for i1, i2 in zip(levels_text, shuffled_levels_text):
                    i11 = i1.split(":")[1].strip()
                    i22 = i2.split(":")[1].strip()
                    if i11 != "Not available":
                        final_level_text.append(i1)
                    if i22 != "Not available":
                        final_shufled_level_text.append(i2)

                All_level_text[i - 1].append(final_level_text)
                All_shuffled_text[i - 1].append(final_shufled_level_text)
                All_Orignal_levels[i - 1].append(Orignal_levels)
    else:
        for _, row in data_file.iterrows():
            for i in range(1, Num_alts + 1):
                curr_data = row[f"Alt_{i}"]
                levels_text = []
                Orignal_levels = []
                for index_att, att_name in enumerate(Unique_Attributes):
                    key_list = attribute_name_levels_grouped[att_name]
                    key_value = key_list[curr_data[index_att] - 1]
                    att_level = f"{att_name} : {key_value}"
                    levels_text.append(att_level)
                    Orignal_levels.append(curr_data[index_att])
                if i == 1:
                    (
                        shuffled_levels_text,
                        shuffled_levels_int,
                        next_seed,
                    ) = shuffle_array(deepcopy(levels_text), Attribute_shuffle_seed)
                    Attribute_shuffle_seed = next_seed
                else:
                    if Same_Attribute_shuffle_order == True:
                        shuffled_levels_text = [
                            levels_text[j] for j in shuffled_levels_int
                        ]
                    else:
                        shuffled_levels_text, _, next_seed = shuffle_array(
                            deepcopy(levels_text), Attribute_shuffle_seed
                        )
                        Attribute_shuffle_seed = next_seed

                final_level_text, final_shufled_level_text = [], []
                for i1, i2 in zip(levels_text, shuffled_levels_text):
                    i11 = i1.split(":")[1].strip()
                    i22 = i2.split(":")[1].strip()
                    if i11 != "Not available":
                        final_level_text.append(i1)
                    if i22 != "Not available":
                        final_shufled_level_text.append(i2)

                All_level_text[i - 1].append(final_level_text)
                All_shuffled_text[i - 1].append(final_shufled_level_text)
                All_Orignal_levels[i - 1].append(Orignal_levels)

    for i in range(1, Num_alts + 1):
        data_file[f"Alt_{i}"] = All_Orignal_levels[i - 1]
        data_file[f"Alt_{i}F"] = All_level_text[i - 1]
        data_file[f"Alt_{i}F_shuffled"] = All_shuffled_text[i - 1]

    return data_file


def Orthogonal_Design_Matrix(
    expr_conf: ExperimentConfig,
    attribute_number,
    attribute_levels,
    attribute_type,
    attribute_name_levels_grouped,
    Unique_Attributes,
    binary_choice,
    External_Task_Determined,
    New_Req_task,
    Empirical_Sample_Size,
    attribute_levels_type_indicator,
):
    logger.info("Generating orthogonal design matrix")
    Max_Level, Min_Level = 0, 20
    Design_empty = True
    Num_alts = calculate_max_num_options(attribute_name_levels_grouped, binary_choice)
    if External_Task_Determined:
        Design_metrix_size = New_Req_task * Empirical_Sample_Size

    while Design_empty:
        factors = {}
        for key, value in attribute_number.items():
            temp = attribute_levels[key]
            if (
                attribute_type[value][0] == "Continuous"
                and expr_conf.is_hb_run == False
            ):
                if attribute_levels_type_indicator[value] == "Uniform Single":
                    temp = temp[0 : len(temp) - 1]
            Max_Level = max(Max_Level, len(temp))
            Min_Level = min(Min_Level, len(temp))
            if min(temp) == 0:
                temp = [i + 1 for i in temp]
            elif min(temp) > 1:
                temp = [i + 1 - min(temp) for i in temp]
            factors[value] = temp

        New_factors = {}
        New_factors_generators = {}
        Alt_Specific_names = [[] for i in range(Num_alts)]
        for i in range(1, Num_alts + 1):
            for att_name in Unique_Attributes:
                value = factors[att_name]
                key_name = att_name + "_" + str(i)
                if i == 1:
                    New_factors_generators[key_name] = value
                New_factors[key_name] = value
                Alt_Specific_names[i - 1].append(key_name)

        logger.info("Generating all options design based on pre-calculated matrices")
        df = io.get_oa(New_factors)
        if len(df) == 0:
            logger.info("Generating one option design based on pre-calculated matrices")
            df = io.get_oa(New_factors_generators)
            if len(df) == 0:
                attr_values = [
                    len(New_factors_generators[f"{att_name}_1"])
                    for att_name in Unique_Attributes
                ]
                total_combinations = np.prod(attr_values)
                recommended_samples = int(
                    np.sqrt(total_combinations) * np.log(len(attr_values))
                )
                if External_Task_Determined:
                    if Design_metrix_size > recommended_samples:
                        recommended_samples = Design_metrix_size
                sobol_exponent = int(ceil(np.log(recommended_samples) / np.log(2)))
                if expr_conf.use_halton_draws:
                    logger.info("Generating one option design using Halton Draws")
                    df = io.generate_orthogonal_array_Halton(
                        New_factors_generators, recommended_samples
                    )
                else:
                    logger.info(
                        "Generating one option design using Sobol sequence for"
                        f" alternatives: {Num_alts}"
                    )
                    df = io.generate_orthogonal_array_Sobol(
                        New_factors_generators, sobol_exponent
                    )
                    logger.info(
                        "Finished Generating one option design using Sobol sequence"
                        f" for alternatives: {Num_alts}"
                    )
            df = df - 1
            generator_cols = Generator_Configuration[Num_alts - 1 - 1]
            All_names_flat = Alt_Specific_names[0].copy()
            if Num_alts == 2:
                generator_cols = [generator_cols]

            for ialt in range(2, Num_alts + 1):
                All_names_flat.extend(Alt_Specific_names[ialt - 1])
                curr_gen_config = generator_cols[ialt - 2]
                for iatt, att_name in enumerate(Unique_Attributes):
                    Num_levels = int(len(factors[att_name]))
                    key_name = att_name + "_" + str(ialt)
                    first_alt_att_col_value = df[f"{att_name}_1"].values
                    curr_add_gen_index = Generator_Configuration_Attribute_levels.index(
                        Num_levels
                    )
                    curr_add_gen_value = curr_gen_config[curr_add_gen_index]
                    curr_alt_att_col_value = (
                        first_alt_att_col_value + curr_add_gen_value
                    ) % Num_levels
                    df[key_name] = curr_alt_att_col_value

            df = df[All_names_flat]
            df = df + 1
        else:
            All_names_flat = Alt_Specific_names[0].copy()
            for ialt in range(2, Num_alts + 1):
                All_names_flat.extend(Alt_Specific_names[ialt - 1])
        df = df[All_names_flat]
        # DROP OBVIOUS SCENARIOS (make sure that shown attribute levels are maximally separate)
        Keep_row = [True] * df.shape[0]
        for index in range(df.shape[0]):
            curr_values = df.loc[index, :]
            curr_values = curr_values.tolist()
            for profile_1 in range(1, Num_alts):
                profile1_values = curr_values[
                    (profile_1 - 1)
                    * len(Unique_Attributes) : (profile_1)
                    * len(Unique_Attributes)
                ]
                for profile_2 in range(profile_1 + 1, Num_alts + 1):
                    profile2_values = curr_values[
                        (profile_2 - 1)
                        * len(Unique_Attributes) : (profile_2)
                        * len(Unique_Attributes)
                    ]
                    check_diff = [
                        bool(a == b) for a, b in zip(profile1_values, profile2_values)
                    ]
                    if all(check_diff):
                        Keep_row[index] = False
                        break

        df = df[Keep_row]
        if len(df) == 0:
            Num_alts = Num_alts - 1
        else:
            Design_empty = False
            df.reset_index(drop=True, inplace=True)

    if External_Task_Determined:
        if df.shape[0] < Design_metrix_size:
            Design_by_length = df.shape[0] % Empirical_Sample_Size
            if Design_by_length == 0:
                Req_task = int(df.shape[0] / Empirical_Sample_Size)
                sampled_df = deepcopy(df)
            else:
                Req_task = int(round(df.shape[0] / Empirical_Sample_Size))
                Req_sample_df = Empirical_Sample_Size * Req_task
                sampled_df = df.sample(n=int(Req_sample_df), replace=False)
        elif df.shape[0] > Design_metrix_size:
            Req_task = New_Req_task
            sampled_df = df.sample(n=int(Design_metrix_size), replace=False)
        else:
            Req_task = New_Req_task
            sampled_df = deepcopy(df)
    else:
        try:
            if df.shape[0] > recommended_samples:
                df = df.sample(n=int(recommended_samples), replace=False)
        except Exception:
            pass
        if df.shape[0] < min(Num_tasks):
            Req_task = df.shape[0]
            sampled_df = deepcopy(df)
        else:
            Profile_mod_task = [df.shape[0] % i for i in Num_tasks]
            try:
                Req_task = Num_tasks[Profile_mod_task.index(0)]
                sampled_df = deepcopy(df)
            except Exception:
                Req_task = Num_tasks[Profile_mod_task.index(min(Profile_mod_task))]
                np.random.seed(1)
                sampled_df = df.sample(
                    n=int(df.shape[0] - min(Profile_mod_task)), replace=False
                )

    Design_WB = deepcopy(sampled_df)
    Design_WB = Design_WB[All_names_flat]

    Design_WB = Design_WB.values
    Design_WB = np.reshape(Design_WB, (-1, len(Unique_Attributes)))

    D_eff = io.D_efficiency(Design_WB)

    grprow = np.repeat(np.arange(1, sampled_df.shape[0] + 1), Num_alts)
    grprow.shape = (sampled_df.shape[0] * Num_alts, 1)
    altrow = np.tile(np.arange(1, Num_alts + 1), sampled_df.shape[0])
    altrow.shape = (sampled_df.shape[0] * Num_alts, 1)

    bestdes = np.concatenate((grprow, altrow, Design_WB), 1).copy()
    Num_blocks = int(sampled_df.shape[0] / Req_task)
    logger.info("Generating block design")
    start_time = time.time()
    block_num = io.blockgen(
        bestdes,
        Num_blocks,
        sampled_df.shape[0],
        expr_conf.block_optimization_iteration_limit,
    )
    logger.info(
        f"Finished generating block design in {time.time() - start_time} seconds"
    )
    Req_block = [block_num[i, 0] for i in range(0, block_num.shape[0], Num_alts)]

    sampled_df["block"] = Req_block
    sampled_df.sort_values(by="block", inplace=True)
    sampled_df["Task"] = np.tile(np.arange(1, Req_task + 1), Num_blocks)
    sampled_df = sampled_df[All_names_flat + ["block", "Task"]]
    return (
        sampled_df,
        Min_Level,
        Max_Level,
        Req_task,
        Alt_Specific_names,
        D_eff,
        Num_blocks,
        Num_alts,
    )


def getPopulationData(population_traits, limit=None):
    core_traits = {}
    for key, value in population_traits.items():
        if value and len(value) > 0:
            if key in DB_Population_Columns:
                core_traits[key] = value
    logger.info(f"Core traits: {core_traits}")
    converted_traits = {
        "age": core_traits["age"] if "age" in core_traits else None,
        "education_level": (
            core_traits["education_level"] if "education_level" in core_traits else None
        ),
        "gender": core_traits["gender"] if "gender" in core_traits else None,
        "household_income": (
            core_traits["household_income"]
            if "household_income" in core_traits
            else None
        ),
        "number_of_children": (
            core_traits["number_of_children"]
            if "number_of_children" in core_traits
            else None
        ),
        "racial_group": (
            core_traits["racial_group"] if "racial_group" in core_traits else None
        ),
        "state": core_traits["state"] if "state" in core_traits else None,
    }
    return create_population(converted_traits, limit=limit)


def extract_number_from_string(s: str) -> list[int]:
    return [int(num) for num in re.findall(r"\d+", s)]


def Draw_price(
    curr_level, all_levels, level_indicator, level_data_type, generator_seed
):
    if sys.getsizeof(generator_seed) < 50:
        np.random.seed(generator_seed)
    else:
        np.random.set_state(generator_seed)

    num_unique_values = 0
    try:
        if level_indicator == "Uniform Single":
            all_level = [ele[0] for ele in all_levels]
            price_value = np.random.uniform(
                float(all_level[curr_level - 1]), float(all_level[curr_level]), 1
            )[0]
            num_unique_values = 1
        elif level_indicator == "Uniform Double":
            all_level = all_levels[curr_level - 1]
            price_value = np.random.uniform(
                float(all_level[0]), float(all_level[1]), 1
            )[0]
            num_unique_values = 2
        elif level_indicator == "Mixed":
            all_level = all_levels[curr_level - 1]
            if len(all_level) == 1:
                price_value = float(all_level[0])
                num_unique_values = 1
            else:
                price_value = np.random.uniform(
                    float(all_level[0]), float(all_level[1]), 1
                )[0]
                num_unique_values = 2

        price_value = round(price_value, 2)
        if level_data_type == "int":
            price_value = round(price_value, 0)
            price_value = int(price_value)
        generator_seed = np.random.get_state()
    except Exception as e:
        logger.exception(e)
        raise

    return (price_value, generator_seed, num_unique_values)


def shuffle_list_of_lists(input_list, generator_seed):
    # Create a copy of the original list of lists to avoid modifying it directly
    shuffled_list = []

    for inner_list in input_list:
        np_array = np.array(inner_list)
        if sys.getsizeof(generator_seed) < 50:
            np.random.seed(generator_seed)
        else:
            np.random.set_state(generator_seed)
        np.random.shuffle(np_array)
        shuffled_list.append(np_array.tolist())
        generator_seed = np.random.get_state()

    return shuffled_list


def calculate_max_num_options(attributes, binary_choice):
    if not binary_choice:
        num_attributes = len(attributes)
        total_combinations = prod([len(levels) for levels in attributes.values()])

        base_recommended = ceil(log2(total_combinations))

        # Calculate maximum options that would keep total information under 20 units
        # Total information = num_attributes × num_options
        max_options_info_limit = int(round(20 / num_attributes, 0))

        # Ensure we have at least num_attributes + 1 options (minimum requirement)
        min_required = num_attributes + 1

        max_options = min(base_recommended, max_options_info_limit, 6)
        final_options = max(max_options, min_required)

        if final_options * num_attributes > 20:
            final_options = int(round(20 / num_attributes, 0))
        final_options = max(final_options, 2)
    else:
        final_options = 2
    return final_options


def assign_numbers(num_individuals, num_numbers):
    numbers = np.repeat(range(1, num_numbers + 1), num_individuals // num_numbers)

    # If there are any remaining individuals, distribute them randomly among the numbers
    remaining_individuals = num_individuals % num_numbers
    if remaining_individuals > 0:
        numbers = np.concatenate([
            numbers,
            np.random.choice(range(1, num_numbers + 1), remaining_individuals),
        ])

    # Shuffle the list of numbers to assign randomly
    np.random.shuffle(numbers)

    return numbers


def shuffle_array(array, generator_seed):
    orignal_ordering = [i for i in range(len(array))]
    for i in range(len(array) - 1, 0, -1):
        if sys.getsizeof(generator_seed) < 50:
            np.random.seed(generator_seed)
        else:
            np.random.set_state(generator_seed)

        j = np.random.randint(0, i)
        array[i], array[j] = array[j], array[i]
        orignal_ordering[i], orignal_ordering[j] = (
            orignal_ordering[j],
            orignal_ordering[i],
        )

        generator_seed = np.random.get_state()

    return (array, orignal_ordering, generator_seed)


def dataframe_to_list(df, personas_to_remove):
    result = []

    for index, row in df.iterrows():
        row_data = []

        for column in df.columns:
            # Append the column name and corresponding value formatted as "column name: value" to the row_data list
            if column not in personas_to_remove:
                row_data.append(f"{column}: {row[column]}")

        # Append the row_data list to the result list
        result.append(row_data)

    return result


def dataframe_to_dict(df, personas_to_remove):
    result = []

    for index, row in df.iterrows():
        row_data = {}

        for column in df.columns:
            # Append the column name and corresponding value formatted as "column name: value" to the row_data list
            if column not in personas_to_remove:
                row_data[column] = row[column]

        # Append the row_data list to the result list
        result.append(row_data)

    return result


def Selection_filter(Persona_data, Selection_Dict):
    filter = np.zeros((len(Persona_data), len(Selection_Dict) + 1))
    count = -1
    for key, value in Selection_Dict.items():
        count += 1
        if len(value) > 0:
            if key == "Age":
                if len(value) == 2:
                    temp = (Persona_data[key] >= value[0]) & (
                        Persona_data[key] <= value[1]
                    ).astype(int)
                if len(value) == 1:
                    temp = (Persona_data[key] >= value[0]).astype(int)
            elif key == "Household Income":
                if len(value) == 2:
                    temp = (Persona_data[key + " Lower"] >= value[0]) & (
                        Persona_data[key + " Upper"] <= value[1]
                    ).astype(int)
                if len(value) == 1:
                    temp = (Persona_data[key + " Lower"] >= value[0]).astype(int)
            else:
                temp = Persona_data[key].isin(value).astype(int)
            filter[:, count] = temp
        else:
            filter[:, count] = np.ones(len(Persona_data))

    filter[:, len(Selection_Dict)] = np.product(
        filter[:, 0 : len(Selection_Dict)], axis=1
    )

    if np.sum(filter[:, len(Selection_Dict)]) == 0:
        Available_data = pd.DataFrame()
        return Available_data
    else:
        Available_data = deepcopy(Persona_data)
        Available_data = Available_data.loc[filter[:, 3] == 1]
        Available_data.reset_index(drop=True, inplace=True)
        Available_data["ID"] = np.arange(1, Available_data.shape[0] + 1)
        Available_data = Available_data[Required_Population_Columns]
        return Available_data


# Function to kind key of a value in dictionary
def find_key(dictionary, value):
    for key, val in dictionary.items():
        if val == value:
            return key
    return None


def create_attribute_and_level_lookup_tables(
    attributes_and_levels: list[list[str]],
    null_levels: bool,
) -> dict:
    """
    Creates a pseudo-DAG mapping levels to attributes and attributes to levels.
    This is used to map the levels to the choices in the conjoint simulation.

    A convenience function for keeping track of ids of attributes and levels.
    Several lookup tables are precomputed and returned in the output:
                    1) attribute_id to attribute_text
                    2) attribute_id to [level_id]
                    3) level_id to attribute_id
                    4) level_id to level_text

    input shape:
    [
                    ("attribute_text", [level_text, level_text, ...], "attribute_type"),
                    ("attribute_text", [level_text, level_text, ...]),  # attribute_type defaults to "non-monetary"
                    ...
    ]

    output shape:
    {
                    "get_attribute_text" : {
                                    "attribute_id_0" : "first attribute text",
                                    "attribute_id_1" : "second attribute text",
                                    ...
                    },
                    "get_level_ids" {
                                    "attribute_id_0": [level_id_0, level_id_1],
                                    "attribute_id_1": [level_id_2, level_id_3],
                                    ...
                    },
                    "get_attribute_id" : {
                                    "level_id_0" : "attribute_id_0",
                                    "level_id_1" : "attribute_id_0",
                                    ...
                    },
                    "get_level_text" : {
                                    "level_id_0" : "first level text",
                                    "level_id_1" : "second level text",
                                    ...
                    }
    }
    """

    lookup = {
        "get_attribute_text": {},
        "get_level_ids": {},
        "get_attribute_id": {},
        "get_level_text": {},
    }

    level_id_counter = 1
    reference_iterator = 1
    for attribute_id, attribute_data in enumerate(attributes_and_levels):
        # Handle both 2-element and 3-element tuples
        if len(attribute_data) == 3:
            attribute_text, levels, att_type = attribute_data
        elif len(attribute_data) == 2:
            attribute_text, levels = attribute_data
            att_type = "non-monetary"  # Default to non-monetary if not specified
        else:
            pass

        if null_levels:
            if attribute_text != PRICE_QUESTION_TEMPLATE:
                if not is_reference_level(levels[0]):
                    levels.insert(0, f"Reference{reference_iterator}")
                    reference_iterator += 1

        lookup["get_attribute_text"][attribute_id] = attribute_text
        lookup["get_level_ids"][attribute_id] = [
            i + level_id_counter for i in range(len(levels))
        ]

        for i, level_text in enumerate(levels):
            lookup["get_attribute_id"][i + level_id_counter] = attribute_id
            lookup["get_level_text"][i + level_id_counter] = level_text

        level_id_counter += len(levels)
    return lookup


def prepare_survey_task(task: SurveyTask) -> dict:
    return {
        "demographics_section": task.persona_traits,
        "guidance_section": task.guidance_section,
        "country": task.country,
        "year": task.year,
        "study_subject": task.study_subject,
        "options": task.options,
        "why_prompt": task.why_prompt,
        "target_behavior": task.target_behavior,
        "optimization": task.optimization_context,
        "context": task.context,
        "dependent_variable": task.dependent_variable,
        "optimizing": task.optimization_context[:-1] + "ing",
        "none_choice_instruction": task.none_choice_instruction,
        "wording_for_study_context": task.wording_for_study_context,
        "response_format": task.response_format,
        "probabilistic_response_instructions": task.probabilistic_response_instructions,
        "survey_time_proportion": task.survey_time_proportion,
    }


def execute_survey_task(
    task: dict,
    llm_model: LLMModel,
    response_type: SurveyResponseType,
    run_id: str,
    target_behavior_context: bool,
    ls_client: Client,
    num_alts: int,
    llm_temperature: float,
) -> list:
    logger.info("Executing survey task")

    pe = LCELPromptExecutor(llm_model=llm_model)
    pe.llm_settings["temperature"] = llm_temperature
    pb = PromptBuilder()
    if target_behavior_context:
        survey_prompt = (
            pb.create_survey_prompt_with_target_behavior_with_decision_strategy()
        )
        trace_name = "Survey with Target Behavior and Decision Strategy"
    prompt_with_args = survey_prompt.format_prompt(
        **{**task, "options": pb.format_options(task["options"])}
    )

    with trace(
        client=ls_client,
        name=trace_name,
        project_name=run_id,
        run_type="llm",
        tags=["survey", trace_name],
        metadata={
            "llm_model": llm_model.value,
            "response_type": response_type.value,
            "task": task,
        },
    ) as run:
        try:
            result = pe.execute(
                prompt=prompt_with_args,
                args=None,
                output_object=SurveyChoiceResponse,
                description="Getting survey responses..",
            )
            option = [{
                "selected_option": result.get("selected_option"),
                "decision_strategy": result.get("decision_strategy"),
                "llm_choice": result.get("llm_choice"),
                "reason": result.get("reason"),
            }]
        except Exception:
            option = [{
                "selected_option": (
                    "error" if response_type == SurveyResponseType.DISCRETE else {}
                ),
                "decision_strategy": "Not Available",
                "llm_choice": "Not Available",
                "reason": "Not Available",
            }]
        option = validate_survey_response(choice=option[0], num_alts=num_alts)

        run.add_metadata({"result": option})
        run.end()

    logger.success(f"Survey executed successfully. \nResults: {option}")
    return option


def execute_survey_batch(
    batch: list[dict],
    llm_model: LLMModel,
    response_type: SurveyResponseType,
    run_id: str,
    target_behavior_context: bool,
    ls_client: Client,
    extract_decision_strategy: bool,
    num_alts: int,
    llm_temperature: float,
) -> list[list]:
    logger.info(f"Executing survey batch of size: {len(batch)}")

    pe = LCELPromptExecutor(llm_model=llm_model)
    pe.llm_settings["temperature"] = llm_temperature
    pb = PromptBuilder()
    survey_successful = True

    if target_behavior_context:
        survey_prompt = (
            pb.create_survey_prompt_with_target_behavior_with_decision_strategy()
        )
        trace_name = "Survey with Target Behavior and Decision Strategy"
    prompts_with_args = [
        survey_prompt.format_prompt(
            **{**task, "options": pb.format_options(task["options"])}
        )
        for task in batch
    ]

    with trace(
        client=ls_client,
        name=trace_name,
        project_name=run_id,
        run_type="llm",
        tags=["survey", response_type.value],
        metadata={
            "llm_model": llm_model.value,
            "response_type": response_type.value,
            "batch_size": len(batch),
            "extract_decision_strategy": extract_decision_strategy,
        },
    ) as run:
        try:
            results = pe.batch_execute(
                prompts=prompts_with_args,
                args=None,
                output_object=SurveyChoiceResponse,
                description="Batch-DPR",
            )
            options = [
                {
                    "selected_option": result.get("selected_option"),
                    "decision_strategy": result.get("decision_strategy"),
                    "llm_choice": result.get("llm_choice"),
                    "reason": result.get("reason"),
                }
                for result in results
            ]
        except Exception:
            survey_successful = False
            options = [
                {
                    "selected_option": (
                        "error" if response_type == SurveyResponseType.DISCRETE else {}
                    ),
                    "decision_strategy": "Not Available",
                    "llm_choice": "error",
                    "reason": "",
                }
                for i in range(len(batch))
            ]
        options = [
            validate_survey_response(choice=option, num_alts=num_alts)
            for option in options
        ]

        run.add_metadata({"results": options})
        run.end()
    if survey_successful:
        logger.success(f"Survey batch executed successfully.")
    else:
        logger.error(f"Survey batch execute failed.")
    return options


def validate_survey_response(choice: dict, num_alts: int) -> list:
    logger.info("Validating survey response")

    chosen_option = choice.get("selected_option", "")
    if isinstance(chosen_option, str):
        chosen_option = extract_number_from_string(chosen_option)
        if len(chosen_option) == 1:
            chosen_option = chosen_option[0]
        else:
            chosen_option = 0
        decision_strategy = choice.get("decision_strategy", "error")
        reason = choice.get("reason", "")
        return [chosen_option, decision_strategy, "", reason]
    if isinstance(chosen_option, dict):
        if len(chosen_option) == 0:
            return [0] * num_alts + [0] + ["error"] + ["error"]
        try:
            for alt in range(num_alts):
                if f"Option {alt + 1}" not in chosen_option.keys():
                    chosen_option[f"Option {alt + 1}"] = 0
            sorted_choice = dict(
                sorted(
                    chosen_option.items(),
                    key=lambda x: int(re.search(r"\d+", x[0]).group()),
                )
            )
            prob = list(sorted_choice.values())
            decision_strategy = choice.get("decision_strategy", "error")
            chosen_option = choice.get("llm_choice", "error")
            chosen_option = extract_number_from_string(chosen_option)
            if len(chosen_option) == 1:
                chosen_option = chosen_option[0]
            else:
                chosen_option = 0
            reason = choice.get("reason", "")
            return prob + [chosen_option] + [decision_strategy] + [reason]
        except Exception:
            return [0] * num_alts + [0] + ["error"] + ["error"]


def get_discrete_choice_survey_responses(
    survey_tasks: list[SurveyTask], expr_conf: ExperimentConfig, num_alts: int
):
    logger.info("Starting discrete choice survey responses generation")
    batch_size = expr_conf.batch_size
    prepared_tasks = [prepare_survey_task(task) for task in survey_tasks]
    llm_model = expr_conf.expr_llm_model
    number_of_workers = get_max_workers()
    execute_batch = partial(
        execute_survey_batch,
        llm_model=llm_model,
        response_type=expr_conf.response_type,
        run_id=expr_conf.wandb_run.id,
        ls_client=expr_conf.wandb_run.ls_client,
        target_behavior_context=expr_conf.target_behavior_context,
        extract_decision_strategy=expr_conf.extract_decision_strategy,
        num_alts=num_alts,
        llm_temperature=expr_conf.llm_temperature,
    )

    if expr_conf.use_threading:
        if expr_conf.use_batching:
            logger.info(f"Using threading with batching. Batch size: {batch_size}")

            batches = [
                prepared_tasks[i : i + batch_size]
                for i in range(0, len(prepared_tasks), batch_size)
            ]
            if expr_conf.use_multiprocessing:
                with ProcessPoolExecutor(max_workers=number_of_workers) as executor:
                    results = list(executor.map(execute_batch, batches))
            else:
                with ThreadPoolExecutor(max_workers=number_of_workers) as executor:
                    results = list(executor.map(execute_batch, batches))
        else:
            logger.info("Using threading without batching")
            if expr_conf.use_multiprocessing:
                with ProcessPoolExecutor(max_workers=number_of_workers) as executor:
                    results = list(executor.map(execute_batch, prepared_tasks))
            else:
                with ThreadPoolExecutor(max_workers=number_of_workers) as executor:
                    results = list(executor.map(execute_batch, prepared_tasks))
        results = list(itertools.chain.from_iterable(results))

    else:  # Sequential
        logger.info(
            f"Using sequential execution with batching. Batch size: {batch_size}"
        )

        if expr_conf.use_batching:
            batches = [
                prepared_tasks[i : i + batch_size]
                for i in range(0, len(prepared_tasks), batch_size)
            ]
            results = [
                item
                for batch in batches
                for item in execute_survey_batch(
                    batch=batch,
                    llm_model=llm_model,
                    response_type=expr_conf.response_type,
                    run_id=expr_conf.wandb_run.id,
                    ls_client=expr_conf.wandb_run.ls_client,
                    target_behavior_context=expr_conf.target_behavior_context,
                    extract_decision_strategy=expr_conf.extract_decision_strategy,
                    num_alts=num_alts,
                    llm_temperature=expr_conf.llm_temperature,
                )
            ]
        else:
            logger.info("Using sequential execution without batching")

            results = [
                execute_survey_task(
                    task=task,
                    llm_model=llm_model,
                    response_type=expr_conf.response_type,
                    run_id=expr_conf.wandb_run.id,
                    ls_client=expr_conf.wandb_run.ls_client,
                    target_behavior_context=expr_conf.target_behavior_context,
                    num_alts=num_alts,
                    llm_temperature=expr_conf.llm_temperature,
                )
                for task in prepared_tasks
            ]
    survey_responses = [
        SurveyResponse(
            selected_option=(
                [result[0]]
                if expr_conf.response_type == SurveyResponseType.DISCRETE
                else result[0:-3]
            ),
            task_number=survey_tasks[index].task_number,
            persona_id=survey_tasks[index].persona_id,
            decision_strategy=(
                result[1]
                if expr_conf.response_type == SurveyResponseType.DISCRETE
                else result[-2]
            ),
            llm_chosen=(
                result[-3]
                if expr_conf.response_type == SurveyResponseType.PROBABILISTIC
                else 0
            ),
            reason=result[-1],
        )
        for index, result in enumerate(results)
    ]
    logger.info("Survey responses generation completed.")
    return survey_responses


def get_decision_time_text(ts: list):
    return f"""
DECISION TIMING & PROCESSING
To ensure a realistic decision-making process, follow these cognitive steps based on Hick's Law:
1. Scan all options briefly (Maximum of {ts[0]} sec)
2. Eliminate clearly unviable options based on key criteria (Maximum of {ts[1]} sec)
3. Compare top contenders by weighing trade-offs (Maximum of {ts[2]} sec)
4. Finalize decision and justify your heuristic (Maximum of {ts[3]} sec)

You must internally simulate a response time between {round(ts[4], 2)} and {round(ts[5], 2)} seconds before selecting your option.
This range reflects natural variation in decision-making time.
If choices are difficult to compare, take additional steps to clarify trade-offs before responding.
"""


def get_guidance_section_instruction_discrete():
    return """RESPONSE GUIDANCE
Before selecting an option, explain your reasoning step by step as if thinking aloud.
1. Start by considering how your demographic traits and lifestyle influence your priorities for the decision.
2. Evaluate the options in the survey question based on these priorities.
3. Justify if you eliminate any options and reasons behind preference for other options.
4. Clearly state what decision rule heuristic you used ("C" (compensatory), "NC" (non-compensatory) or "H" (hybrid)).
Keep the explanation detailed enough to describe the decision process, but brief enough to avoid unnecessary elaboration—aim for a natural length that feels complete without being overly verbose.
The explanation must include a discussion on relevant demographic features which impact the final decision.
"""


def get_guidance_section_instruction_probabilistic():
    return """RESPONSE GUIDANCE
Before assigning preference probability to an option, explain your reasoning step by step as if thinking aloud.

1. Start by considering how your demographic traits and lifestyle influence your priorities for the decision.
2. Evaluate the options in the survey question based on these priorities.
3. Justify if you eliminate any options and reasons behind preference for other options.
4. Clearly state what decision rule heuristic you used ("C" (compensatory), "NC" (non-compensatory) or "H" (hybrid)).
5. It's not necessary that you choose the option with the highest probability. You may choose any option as long as it is justified based on your heuristic, interactions of demographic features, and the context of the task.
Keep the explanation detailed enough to describe the decision process, but brief enough to avoid unnecessary elaboration—aim for a natural length that feels complete without being overly verbose.
The explanation must include a discussion on relevant demographic features which impact the final decision.
"""


def get_random_index(probabilities, seed):
    if sys.getsizeof(seed) < 50:
        np.random.seed(seed)
    else:
        np.random.set_state(seed)

    rand_value = np.random.random((1, 1))
    state = np.random.get_state()

    cumulative_sum = list(itertools.accumulate(probabilities))
    for index, value in enumerate(cumulative_sum):
        if rand_value <= value:
            return index + 1, state

    chosen_option = np.random.randint(1, len(probabilities) + 1)
    return chosen_option, state


def create_dce_table_fast(
    expr_conf: ExperimentConfig,
    Master_File_df: pd.DataFrame,
    survey_results: list[SurveyResponse],
    Number_of_Alts: int,
    Attribute_col_names: list[str],
    Shuffled_array: np.array,
    Prob_to_Discrete_Seed: int,
):
    task_answers = []
    parse_error_count = 0
    all_answers = []
    all_decision_strategies = []
    final_alt_num = Number_of_Alts

    if expr_conf.add_neither_option:
        Order_list_orig = [i for i in range(1, Number_of_Alts + 2)]
        final_alt_num += 1
    else:
        Order_list_orig = [i for i in range(1, Number_of_Alts + 1)]

    Actual_Alt_Data = Shuffled_array
    state_seed = Prob_to_Discrete_Seed

    if expr_conf.response_type == SurveyResponseType.PROBABILISTIC:
        for index, task in enumerate(survey_results):
            task_answer = []
            task_answer.append(task.persona_id)
            task_answer.append(task.task_number)
            if np.sum(task.selected_option) == 0:
                parse_error_count += 1
                for i in range(final_alt_num):
                    task_answer.append(0)
                curr_answer_letter = "error"
                task_answer.append(curr_answer_letter)
                task_answer.append(curr_answer_letter)
                task_answer.append(curr_answer_letter)
                task_answers.append(task_answer)
            else:
                curr_alt_order = Actual_Alt_Data[index, :].tolist()
                all_probs = task.selected_option
                if len(all_probs) == len(curr_alt_order):
                    Alt_Prob_combined = np.hstack([
                        np.array(curr_alt_order)[:, np.newaxis],
                        np.array(all_probs)[:, np.newaxis],
                    ])
                    Alt_Prob_combined_sorted = Alt_Prob_combined[
                        Alt_Prob_combined[:, 0].argsort()
                    ]
                    curr_prob = []
                    for ele in Alt_Prob_combined_sorted[:, 1]:
                        task_answer.append(ele)
                        curr_prob.append(ele)
                    curr_chosen_option, seed_next = get_random_index(
                        curr_prob, state_seed
                    )
                    state_seed = seed_next
                    curr_answer_letter = chr(64 + curr_chosen_option)
                    current_llm_answer = task.llm_chosen
                    if current_llm_answer == 0:
                        current_llm_answer_letter = "error"
                    else:
                        curr_alt_order = Actual_Alt_Data[index, :].tolist()
                        curr_answer = task.llm_chosen - 1
                        curr_chosen_option = curr_alt_order[curr_answer]
                        current_llm_answer_letter = chr(64 + curr_chosen_option)
                    task_answer.append(current_llm_answer_letter)
                    task_answer.append(curr_answer_letter)
                    task_answer.append(task.reason)
                    all_answers.append(current_llm_answer_letter)
                    task_answers.append(task_answer)
                else:
                    parse_error_count += 1
                    for i in range(final_alt_num):
                        task_answer.append(0)
                    curr_answer_letter = "error"
                    task_answer.append(curr_answer_letter)
                    task_answer.append(curr_answer_letter)
                    task_answer.append(curr_answer_letter)
                    task_answers.append(task_answer)
            if expr_conf.extract_decision_strategy:
                all_decision_strategies.append(task.decision_strategy)
    elif expr_conf.response_type == SurveyResponseType.DISCRETE:
        for index, task in enumerate(survey_results):
            task_answer = []
            task_answer.append(task.persona_id)
            task_answer.append(task.task_number)
            if task.selected_option[0] == 0:
                parse_error_count += 1
                curr_answer_letter = "error"
            else:
                curr_alt_order = Actual_Alt_Data[index, :].tolist()
                curr_answer = task.selected_option[0] - 1
                curr_chosen_option = curr_alt_order[curr_answer]
                curr_answer_letter = chr(64 + curr_chosen_option)
            task_answer.append(curr_answer_letter)
            task_answer.append(task.reason)
            all_answers.append(curr_answer_letter)
            task_answers.append(task_answer)
            if expr_conf.extract_decision_strategy:
                all_decision_strategies.append(task.decision_strategy)

    if expr_conf.response_type == SurveyResponseType.PROBABILISTIC:
        Task_Response_df = pd.DataFrame(
            data=task_answers,
            columns=["ID", "Task"]
            + [f"Alt_{index}" for index in range(1, final_alt_num + 1)]
            + ["chosen_choice_letter", "derived_choice_letter", "llm_reasoning"],
        )
        if expr_conf.extract_decision_strategy:
            Task_Response_df["decision_strategy"] = all_decision_strategies

        Task_Response_df = pd.melt(
            Task_Response_df,
            id_vars=[
                "ID",
                "Task",
                "chosen_choice_letter",
                "decision_strategy",
                "derived_choice_letter",
                "llm_reasoning",
            ],
            value_vars=[f"Alt_{index}" for index in range(1, final_alt_num + 1)],
            var_name="Alts",
            value_name="Probability",
        )
        Task_Response_df["Alts"] = Task_Response_df["Alts"].apply(
            lambda x: x.split("_")[1]
        )
        Task_Response_df["Alts"] = Task_Response_df["Alts"].astype(int)
        Task_Response_df.sort_values(by=["ID", "Task", "Alts"], inplace=True)
    elif expr_conf.response_type == SurveyResponseType.DISCRETE:
        Task_Response_df = pd.DataFrame(
            data=task_answers,
            columns=["ID", "Task", "chosen_choice_letter", "llm_reasoning"],
        )
        if expr_conf.extract_decision_strategy:
            Task_Response_df["decision_strategy"] = all_decision_strategies
        Task_Response_df = Task_Response_df.loc[
            Task_Response_df.index.repeat(len(Order_list_orig))
        ].reset_index(drop=True)
        Task_Response_df["Alts"] = Order_list_orig * int(
            len(Task_Response_df) // len(Order_list_orig)
        )

    Master_File_df = Master_File_df[
        ["ID", "Persona", "Persona_NL", "block", "Task"]
        + [f"Alt_{i}" for i in range(1, len(Order_list_orig) + 1)]
    ]
    Master_File_df = pd.melt(
        Master_File_df,
        id_vars=["ID", "Persona", "Persona_NL", "block", "Task"],
        value_vars=[f"Alt_{i}" for i in range(1, len(Order_list_orig) + 1)],
        var_name="Alts",
        value_name="Alt_configuration",
    )
    Master_File_df["Alts"] = Master_File_df["Alts"].apply(lambda x: x.split("_")[1])
    Master_File_df["Alts"] = Master_File_df["Alts"].astype(int)
    Master_File_df.sort_values(by=["ID", "Task", "Alts"], inplace=True)
    Task_Response_Attribute_df = pd.merge(
        Master_File_df, Task_Response_df, how="left", on=["ID", "Task", "Alts"]
    )
    Task_Response_Attribute_df.reset_index(drop=True, inplace=True)
    Task_Response_Attribute_df.sort_values(by=["ID", "Task", "Alts"], inplace=True)
    alt_configs = Task_Response_Attribute_df["Alt_configuration"].apply(pd.Series)
    Task_Response_Attribute_df[Attribute_col_names] = alt_configs
    Task_Response_Attribute_df.drop("Alt_configuration", axis=1, inplace=True)
    Task_Response_Attribute_df.reset_index(drop=True, inplace=True)
    Task_Response_Attribute_df = Task_Response_Attribute_df[
        ["ID", "Persona", "Persona_NL", "Task", "Alts", "chosen_choice_letter"]
        + (
            ["Probability"]
            if expr_conf.response_type == SurveyResponseType.PROBABILISTIC
            else []
        )
        + (
            ["derived_choice_letter"]
            if expr_conf.response_type == SurveyResponseType.PROBABILISTIC
            else []
        )
        + (["decision_strategy"] if expr_conf.extract_decision_strategy else [])
        + ["llm_reasoning"]
        + Attribute_col_names
    ]

    Task_Response_Attribute_df = Task_Response_Attribute_df[
        Task_Response_Attribute_df["chosen_choice_letter"] != "error"
    ]
    if len(Task_Response_Attribute_df) > 0:
        Task_Response_Attribute_df.reset_index(drop=True, inplace=True)

    return Task_Response_Attribute_df, parse_error_count, all_answers


# The Idea here is to look at the traits that we can add based on the ones we have through one LLM call with batches, then we will remove the ones that we could predict from traits_dict and the ones that are remaining are the ones we add uniformly below
###
def determine_predictable_traits(all_traits, new_traits, llm_model):
    """
    Determine which new traits can be predicted from existing traits

    Args:
        all_traits: List of all existing traits in the dataset
        new_traits: List of new traits we want to predict
        llm_model: The LLM model to use

    Returns:
        List of predictable traits
    """
    # Call LLM to determine predictable traits
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.traits_predictable()
    result = pe.execute(
        prompt=prompt,
        args={"known_traits": all_traits, "new_traits": new_traits},
        output_object=dict,  # Expecting a dictionary output
        description="Determining predictable traits",
    )

    # Filter to only traits that can be predicted
    predictable_traits = [trait for trait, can_predict in result.items() if can_predict]
    print(f"Predictable traits: {predictable_traits}")

    return predictable_traits


def predict_trait_values_batch(
    respondents_batch: List[Dict],
    trait_to_predict: str,
    possible_values: List[str],
    llm_model,
):
    """
    Predict values for a specific trait for a batch of respondents

    Args:
        respondents_batch: List of respondent dictionaries
        trait_to_predict: The trait we want to predict
        possible_values: Possible values for this trait
        llm_model: The LLM model to use

    Returns:
        List of predicted values for each respondent
    """
    # Your consistent prompt template
    #     prompt_template = f"""Given a person with the following traits, predict their most likely value for '{trait_to_predict}'.
    # Choose from these possible values: {', '.join(possible_values)}

    # Traits:
    # {{traits}}

    # Return your answer as a JSON object in this format:
    # {{
    #     "predicted_value": "your prediction here"
    # }}
    # Where "your prediction here" is one of: {', '.join(possible_values)}
    # """

    # prompts = []
    # for respondent in respondents_batch:
    #     # Format traits for this respondent
    #     traits_text = "\n".join([f"{trait}: {value}" for trait, value in respondent.items() if trait != 'ID'])
    #     prompts.append(prompt_template.format(traits=traits_text))
    pb = PromptBuilder()
    survey_prompt = pb.predict_traits()
    prompts = [
        survey_prompt.format_prompt(
            traits=respondent,
            trait_to_predict=trait_to_predict,
            possible_values=possible_values,
        )
        for respondent in respondents_batch
    ]
    # Call LLM in batch
    try:
        pe = LCELPromptExecutor(llm_model=llm_model)
        results = pe.batch_execute(
            prompts=prompts,
            args=None,
            output_object=dict,  # Changed from str to dict
            description=f"Batch predicting {trait_to_predict}",
        )
        # Extract the predicted values from the JSON responses
        extracted_values = []
        for result in results:
            if isinstance(result, dict) and "predicted_value" in result:
                extracted_values.append(result["predicted_value"])
            else:
                print(f"Unexpected result format: {result}")
                extracted_values.append("error")
        return extracted_values
    except Exception as e:
        print(f"Error in batch processing: {e}")
        return ["error"] * len(respondents_batch)


def list_chunker(lst, batch_size):
    """Split a list into chunks of specified size"""
    for i in range(0, len(lst), batch_size):
        yield lst[i : i + batch_size]


def predict_new_traits_workflow(
    df: pd.DataFrame,
    new_traits_with_values: Dict[str, List[str]],
    llm_model,
    batch_size: int = 20,
    max_workers: int = 5,
):
    """
    Complete workflow to predict new traits for a dataframe

    Args:
        df: DataFrame with respondent data
        new_traits_with_values: Dictionary mapping trait names to their possible values
        llm_model: The LLM model to use
        batch_size: Number of respondents in each batch
        max_workers: Number of parallel threads

    Returns:
        DataFrame with new columns added for predictable traits
    """
    # Get existing trait names (columns)
    existing_traits = df.columns.tolist()

    # Convert DataFrame rows to dictionaries for processing
    respondents = df.to_dict("records")

    # Process each predictable trait
    for trait in new_traits_with_values:
        possible_values = new_traits_with_values[trait]
        print(f"Processing trait: {trait} with possible values: {possible_values}")

        # Split respondents into batches
        batches = list(list_chunker(respondents, batch_size))
        print(
            f"Processing {len(df)} respondents in {len(batches)} batches"
            f" with {max_workers} workers"
        )

        # Create partial function with fixed parameters
        batch_processor = partial(
            predict_trait_values_batch,
            trait_to_predict=trait,
            possible_values=possible_values,
            llm_model=llm_model,
        )

        # Process batches in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            batch_results = list(executor.map(batch_processor, batches))

        # Flatten results
        all_results = list(itertools.chain.from_iterable(batch_results))

        # Add results as a new column
        df[trait] = all_results

    return df


def append_persona_statement_scores_info(
    cur_persona_info, persona_statement_scores_info
):
    """Append persona statement scores info to current persona info."""

    assert isinstance(cur_persona_info, list)
    return cur_persona_info + persona_statement_scores_info


def dce_experiment(expr_conf: ExperimentConfig):
    logger.info(f"Starting DCE experiment for: {expr_conf.why_prompt}")
    logger.info(
        f"Configuration: country={expr_conf.country},"
        f" attribute_count={expr_conf.attribute_count},"
        f" level_count={expr_conf.level_count}"
    )

    logger.info("STEP 1: Initializing attributes and levels")

    if expr_conf.expr_llm_model in [
        LLMModel.HAIKU,
        # LLMModel.GCP_HAIKU,
    ]:
        expr_conf.use_threading = False
        expr_conf.use_batching = False
    if expr_conf.pre_cooked_attributes_and_levels_lookup:
        al_lookup_original = create_attribute_and_level_lookup_tables(
            attributes_and_levels=expr_conf.pre_cooked_attributes_and_levels_lookup,
            null_levels=False,
        )
        logger.info(
            "Using pre-cooked attributes and levels with"
            f" {len(expr_conf.pre_cooked_attributes_and_levels_lookup)} attributes"
        )
        pb = PromptBuilder()
        pe = LCELPromptExecutor(llm_model=LLMModel.AZURE_GPTO3_MINI)
        args = {"al_lookup": expr_conf.pre_cooked_attributes_and_levels_lookup}
        response = pe.execute(
            prompt=pb.standarize_numerical_attr(),
            args=args,
            output_object=LeveledAttributeResponse,
            description="Standardizing numerical attributes..",
        )
        precooked_attrs_new = []
        for attr_info in expr_conf.pre_cooked_attributes_and_levels_lookup:
            att_name = attr_info[0]
            att_levels = attr_info[1]
            att_type = attr_info[2]

            for att_info_new in response.get("attributes_levels"):
                att_name_new = att_info_new["attribute"]
                att_levels_new = att_info_new["levels"]

                if att_name == att_name_new and len(att_levels) == len(att_levels_new):
                    precooked_attrs_new.append([att_name_new, att_levels_new, att_type])
                    break
        if len(precooked_attrs_new) == len(
            expr_conf.pre_cooked_attributes_and_levels_lookup
        ):
            expr_conf.pre_cooked_attributes_and_levels_lookup = precooked_attrs_new

        al_lookup = create_attribute_and_level_lookup_tables(
            attributes_and_levels=expr_conf.pre_cooked_attributes_and_levels_lookup,
            null_levels=False,
        )
    elif expr_conf.use_api:
        (
            al_lookup,
            brand_attribute_combinations,
        ) = create_attributes_levels_lookup_table_from_api(
            prompt=expr_conf.why_prompt,
            country=expr_conf.country,
            year=int(expr_conf.year),
            attribute_count=expr_conf.attribute_count,
            level_count=expr_conf.level_count,
            max_length=expr_conf.max_length,
            levels_llm_model=expr_conf.expr_llm_model,
            null_levels=expr_conf.null_levels,
        )
        al_lookup_original = al_lookup
        pre_cooked_attrs = []
        for k, v in al_lookup["get_level_ids"].items():
            att_name = al_lookup["get_attribute_text"][k]
            att_levels = [al_lookup["get_level_text"][level_id] for level_id in v]
            pre_cooked_attrs.append([att_name, att_levels])
            expr_conf.pre_cooked_attributes_and_levels_lookup = pre_cooked_attrs

        expr_conf.realworld_products = brand_attribute_combinations
        logger.info("Attributes & Levels generated from API")
    else:
        # TODO: consider removing this block since it is not used
        al_lookup = create_attributes_levels_lookup(expr_conf)
        al_lookup_original = al_lookup
        pre_cooked_attrs = []
        for k, v in al_lookup["get_level_ids"].items():
            att_name = al_lookup["get_attribute_text"][k]
            att_levels = [al_lookup["get_level_text"][level_id] for level_id in v]
            pre_cooked_attrs.append([att_name, att_levels])
            expr_conf.pre_cooked_attributes_and_levels_lookup = pre_cooked_attrs
        logger.info("Attributes & Levels generated locally")

    Monetary_attributes = []
    if not expr_conf.is_hb_run:
        for attribute in expr_conf.pre_cooked_attributes_and_levels_lookup:
            # Check if attribute has a third element and if it's "monetary"
            if len(attribute) >= 3 and attribute[2] == "monetary":
                Monetary_attributes.append(attribute[0])

    Null_level = expr_conf.null_levels if not expr_conf.is_hb_run else False
    if not expr_conf.is_hb_run:
        expr_conf.add_neither_option = True
    if Null_level:
        al_lookup = add_new_level(al_lookup)
        al_lookup_original = add_new_level(al_lookup_original)

    if expr_conf.is_hb_run:
        if expr_conf.target_population:
            expr_conf.country = USA_NAMES[0]

    if expr_conf.country not in USA_NAMES:
        expr_conf.state = [get_country_capital(country=expr_conf.country)]
    else:
        if expr_conf.target_population.state:
            expr_conf.state = [expr_conf.target_population.state.value]
        else:
            expr_conf.state = []

    External_Persona = False
    if len(expr_conf.external_personas) > 0:
        External_Persona = True

        if expr_conf.is_hb_run:
            External_Persona = False

    if not External_Persona:
        logger.info("STEP 2: Loading and preparing population data")
        if expr_conf.country in USA_NAMES:
            if not expr_conf.target_population and expr_conf.is_hb_run:
                expr_conf.target_population.age = [20, 50]
                expr_conf.target_population.household_income = [50000, 250000]
                expr_conf.target_population.education_level = None
                expr_conf.target_population.state = None
                expr_conf.target_population.gender = None
                expr_conf.target_population.number_of_children = None
                expr_conf.target_population.racial_group = None

            logger.info("Loading population data for USA")

            target_population = expr_conf.target_population.model_dump()
            expr_conf.target_population = {
                k: (
                    [get_enum_value(v1) for v1 in v]
                    if isinstance(v, list)
                    else get_enum_value(v)
                )
                for k, v in target_population.items()
            }
            if "African American" in expr_conf.target_population.get("racial_group"):
                expr_conf.target_population.get("racial_group").append("Black")
                expr_conf.target_population.get("racial_group").remove(
                    "African American"
                )
            try:
                Matched_Population = getPopulationData(expr_conf.target_population)
                Matched_Population = Matched_Population.drop_duplicates(
                    subset=["age", "gender", "household_income"]
                )
            except Exception as e:
                logger.error(f"Error in getPopulationData: {str(e)}", exc_info=True)
                capture_exception()
                raise
            Matched_Population = Matched_Population[
                DB_Population_Columns_Extended.keys()
            ]
            try:
                Matched_Population["racial_group"] = Matched_Population[
                    "racial_group"
                ].replace("Black", "African American")
            except Exception:
                pass
            try:
                Matched_Population["racial_group"] = Matched_Population[
                    "racial_group"
                ].replace("White", "Caucasian")
            except Exception:
                pass

            logger.info(
                "Population data loaded from database. Total records"
                f" {len(Matched_Population)}"
            )
            db_columns = ["family_size", "vehicles_in_household"]
            try:
                Matched_Population.loc[
                    Matched_Population[db_columns[0]] > 4, db_columns[0]
                ] = "More than 4 people"
                Matched_Population.loc[
                    Matched_Population[db_columns[1]] > 4, db_columns[1]
                ] = "More than 4 vehicles"
                Matched_Population[db_columns] = Matched_Population[db_columns].astype(
                    str
                )
            except SoftTimeLimitExceeded:
                capture_exception()
                raise SoftTimeLimitExceeded
            except Exception:
                pass
        else:
            logger.info("Loading population data for non-US countries")
            current_dir = os.path.dirname(os.path.abspath(__file__))
            Population_file = os.path.join(current_dir, "Filtered_Persona_Data.csv")
            Population_data = pd.read_csv(Population_file)
            expr_conf.target_population = {}

            if Population_Definition is True:
                Target_Population = expr_conf.population_traits.model_dump()
                Mapping_Target_code = {
                    "age": "Age",
                    "education": "Education Level",
                    "gender": "Sex",
                    "income": "Household Income",
                }

                Selection_Dictonary = {}
                for k, v in Target_Population.items():
                    new_key = Mapping_Target_code[k]
                    if k in ["education", "gender"]:
                        new_val = []
                        if len(v) > 0:
                            new_val = [i.value for i in v]
                    else:
                        new_val = v
                    Selection_Dictonary[new_key] = new_val

                # Filter the population data based on the selection criteria
                Matched_Population = Selection_filter(
                    Population_data, Selection_Dictonary
                )
                if len(Matched_Population) == 0:
                    logger.info(
                        "No matching population found. Performing random sampling from"
                        " existing population"
                    )
                    Population_data["ID"] = np.arange(1, Population_data.shape[0] + 1)
                    Matched_Population = Population_data[Required_Population_Columns]
            else:
                Matched_Population = Population_data[non_us_demo_trait_columns]

        logger.info("Population data prepared successfully")

        if Include_Firebase_Traits_Uniform:
            traits_dict = expr_conf.population_traits.model_dump()
            if expr_conf.country in USA_NAMES:
                db_flipped_columns_keys = list(DB_Flipped_Columns.keys())
                to_remove = []
                for key, item in traits_dict.items():
                    if key not in db_flipped_columns_keys:
                        try:
                            Matched_Population.drop(
                                columns=DB_Flipped_Columns[key], axis=1, inplace=True
                            )
                        except Exception:
                            pass
                    else:
                        to_remove.append(key)
                for key in to_remove:
                    traits_dict.pop(key)
            else:
                to_remove = []
                for key, item in traits_dict.items():
                    if key not in non_us_demo_trait_columns:
                        try:
                            Matched_Population.drop(columns=key, axis=1, inplace=True)
                        except Exception:
                            pass
                    else:
                        to_remove.append(key)
                for key in to_remove:
                    traits_dict.pop(key)

            if expr_conf.predict_unknown_traits:
                # Get the predictable traits first
                predictable_traits = determine_predictable_traits(
                    all_traits=Matched_Population.columns.tolist(),
                    new_traits=list(traits_dict.keys()),
                    llm_model=LLMModel.AZURE_GPT4,
                )

                # Process the predictable traits with LLM
                if predictable_traits:
                    # Run the prediction workflow only on predictable traits
                    predictable_traits_dict = {
                        k: traits_dict[k]
                        for k in predictable_traits
                        if k in traits_dict
                    }

                    Matched_Population = predict_new_traits_workflow(
                        df=Matched_Population,
                        new_traits_with_values=predictable_traits_dict,
                        llm_model=LLMModel.GCP_SONNET,
                        batch_size=40,
                        max_workers=8,
                    )

                    # Remove the predictable traits from traits_dict
                    for trait in predictable_traits:
                        if trait in traits_dict:
                            traits_dict.pop(trait)

            # Now traits_dict only contains traits that couldn't be predicted
            # and we can proceed with adding them uniformly
            if len(traits_dict) > 0:
                (
                    Matched_Population,
                    new_traits_columns,
                ) = add_traits_from_firebase_uniformally_to_df(
                    Matched_Population, traits_dict
                )
    else:
        logger.info("Loading user provided persona")
        Matched_Population = expr_conf.external_personas

    attribute_number = al_lookup["get_attribute_text"]
    attribute_levels = al_lookup["get_level_ids"]
    attribute_levels_description = al_lookup["get_level_text"]

    # Mapping string names to integer levels
    attribute_levels_grouped = {}
    Unique_Attributes = []
    for key, value in attribute_number.items():
        Unique_Attributes.append(value)
        value = attribute_levels[key]
        try:
            temp = [attribute_levels_description[i] for i in value]
        except Exception:
            temp = [attribute_levels_description[str(i)] for i in value]
        attribute_levels_grouped[int(key)] = temp

    logger.info("Mapping attribute names and their levels")
    attribute_name_levels_grouped = {}
    Max_Level_Attribute = 0
    for key, att_name in enumerate(Unique_Attributes):
        att_name_key = find_key(attribute_number, att_name)
        value = attribute_levels[att_name_key]
        try:
            temp = [attribute_levels_description[i] for i in value]
        except Exception:
            temp = [attribute_levels_description[str(i)] for i in value]
        Max_Level_Attribute = max(Max_Level_Attribute, len(temp))
        attribute_name_levels_grouped[att_name] = temp

    # ------------------------------------------------------------------------
    #                          Creating Survey Intro Prompt
    # ------------------------------------------------------------------------
    logger.info("STEP 3: Generating target behavior and study context")

    pe = LCELPromptExecutor(llm_model=expr_conf.expr_llm_model)
    pb = PromptBuilder()
    target_behavior_prompt = pb.get_target_behavior_prompt()
    logger.info("Generating target behavior...")
    try:
        target_behavior = pe.execute(
            prompt=target_behavior_prompt,
            args={"why_prompt": expr_conf.why_prompt},
            output_object=TargetBehaviorResponse,
            description="Generating target behavior...",
        )
    except Exception:
        logger.info("Failed to generate target behavior. exiting..")
        all_personas_dce = pd.DataFrame()
        survey_summary = {}
        mappings = {
            "experimentor why question type": expr_conf.prompt_type,
            "experimentor why question prompt": expr_conf.why_prompt,
            "attributes_and_levels_lookup": al_lookup_original,
        }
        return mappings, all_personas_dce, survey_summary, [], pd.DataFrame(), False
    if target_behavior:
        logger.info(f"Target behavior: {target_behavior}")
        logger.info("Generating study context...")
        study_context_prompt = pb.get_study_context_prompt()
        study_context = pe.execute(
            prompt=study_context_prompt,
            args={
                "target_behavior": target_behavior.get("target_behavior"),
                "attributes": Unique_Attributes,
            },
            output_object=StudyContextResponse,
            description="Generating study context...",
        )
        if study_context:
            logger.info(f"Study context: {study_context.get('study_context')}")
            expr_conf.target_behavior_context = True
            expr_conf.target_behavior = target_behavior.get("target_behavior")
            expr_conf.optimization_context = target_behavior.get("optimization")
            expr_conf.context = study_context.get("context")
            study_subject = ""

    attribute_type = {}
    attribute_type_only = {}
    attribute_continuous_value = {}
    attribute_levels_new = {}
    attribute_levels_type_indicator = {}
    start_count = 0

    logger.info("STEP 4: Processing attribute types and levels")

    if (not expr_conf.is_hb_run) and (not expr_conf.hb_folder):
        for key, value in attribute_number.items():
            att_levels = attribute_name_levels_grouped[value]
            is_continuous_var, levels_continuous, attribute_data_type = (
                filter_single_number_strings(att_levels, Null_level)
            )
            if is_continuous_var:
                attribute_type[value] = ["Continuous", attribute_data_type]
                attribute_type_only[value] = "Continuous"
                size_of_levels = [len(ele) for ele in levels_continuous]
                unique_size_of_levels = list(set(size_of_levels))
                if len(unique_size_of_levels) == 1:
                    if unique_size_of_levels[0] == 1:
                        levels_values = [float(ele[0]) for ele in levels_continuous]
                        levels_continuous = levels_values
                        levels_continuous.sort()
                        if levels_continuous[0] == 0:
                            levels_continuous.insert(0, 0)
                        levels_continuous = [[ele] for ele in levels_continuous]
                        attribute_levels_type_indicator[value] = "Uniform Single"
                    elif unique_size_of_levels[0] == 2:
                        attribute_levels_type_indicator[value] = "Uniform Double"
                else:
                    # if len(levels_continuous[0]) == 1:
                    #     if levels_continuous[0][0] == 0:
                    #         levels_continuous[0].append(0)
                    attribute_levels_type_indicator[value] = "Mixed"
                if Null_level:
                    levels_continuous.insert(0, ["Not available"])
                attribute_continuous_value[value] = levels_continuous
                attribute_levels_new[key] = [
                    ic + 1 + start_count for ic in range(len(levels_continuous))
                ]
                start_count += len(levels_continuous)
            else:
                attribute_type[value] = ["Categorical", "Not required"]
                attribute_type_only[value] = "Categorical"
                attribute_levels_type_indicator[value] = "Mixed"
                attribute_continuous_value[value] = att_levels
                attribute_levels_new[key] = [
                    ic + 1 + start_count for ic in range(len(att_levels))
                ]
                start_count += len(att_levels)
    else:
        for key, value in attribute_number.items():
            att_levels = attribute_name_levels_grouped[value]
            attribute_type[value] = ["Categorical", "Not required"]
            attribute_type_only[value] = "Categorical"
            attribute_continuous_value[value] = att_levels
            attribute_levels_new[key] = [
                ic + 1 + start_count for ic in range(len(att_levels))
            ]
            start_count += len(att_levels)

    if len(Matched_Population) != 0:
        Max_Task = Num_tasks[-1]
        Max_Num_Alts = calculate_max_num_options(
            attribute_name_levels_grouped, expr_conf.binary_choice
        )
        Thoretical_Sample_Size = int(
            (500 * Max_Level_Attribute) / (Max_Task * Max_Num_Alts)
        )
        Empirical_Sample_Size = len(Matched_Population)

        External_Task_Determined = False
        New_Req_task = 0
        if Empirical_Sample_Size < Thoretical_Sample_Size:
            New_Req_task = ceil(
                (Thoretical_Sample_Size * Max_Task) / Empirical_Sample_Size
            )
            New_Req_task = int(New_Req_task)
            External_Task_Determined = True

        logger.info("STEP 5: Creating experimental design matrix")

        (
            sampled_df,
            Min_Level,
            Max_Level,
            Req_task,
            Alt_Specific_names,
            D_eff,
            Num_blocks,
            Num_alts,
        ) = Orthogonal_Design_Matrix(
            expr_conf,
            attribute_number,
            attribute_levels_new,
            attribute_type,
            attribute_name_levels_grouped,
            Unique_Attributes,
            expr_conf.binary_choice,
            External_Task_Determined,
            New_Req_task,
            Empirical_Sample_Size,
            attribute_levels_type_indicator,
        )
        """
        Design_matrix = deepcopy(sampled_df)
        Design_matrix = Design_matrix[
            list(itertools.chain.from_iterable(Alt_Specific_names))
        ]
        Design_matrix = Design_matrix.values
        Design_matrix = np.reshape(
            Design_matrix, ((sampled_df.shape[0]) * Num_alts, len(Unique_Attributes))
        )
        Design_matrix = pd.DataFrame(Design_matrix, columns=Unique_Attributes)

        curr_offset = 0
        for att_num, att_name in enumerate(Unique_Attributes[1:]):
            curr_offset += len(
                attribute_name_levels_grouped[Unique_Attributes[att_num]]
            )
            curr_att_type = attribute_type[att_name][0]
            if curr_att_type != "Continuous":
                temp = Design_matrix[att_name].values
                nonzero_rows = temp != 0
                temp[nonzero_rows] += curr_offset
                Design_matrix[att_name] = temp
        expr_conf.design_matrix = Design_matrix
        """

        logger.info(f"Tasks per respondent: {Req_task}")
        expr_conf.tasks_per_respondent = Req_task

        Current_Persona_Length = len(Matched_Population)
        if not expr_conf.is_hb_run:
            Min_Individual_Req = int((500 * Max_Level) / (Req_task * Num_alts))
        else:
            Min_Individual_Req = min(500, Current_Persona_Length)

        Min_Individual_Req = min(Current_Persona_Length, Min_Individual_Req)
        # Min_Individual_Req = 20
        logger.info(f"Sample size: {Min_Individual_Req}")

        Design_df = deepcopy(sampled_df)
        Design_df.reset_index(drop=True, inplace=True)
        Design_df = Design_df[["block", "Task"]]
        for i in range(1, Num_alts + 1):
            curr_data = sampled_df[Alt_Specific_names[i - 1]].values
            Design_df[f"Alt_{i}"] = curr_data.tolist()

        expr_conf.sample_size = Min_Individual_Req
        if Min_Individual_Req < Current_Persona_Length:
            expr_conf.population_seed += int(np.random.randint(0, 1000000, 1)[0])
            np.random.seed(expr_conf.population_seed)
            Matched_Population = Matched_Population.sample(
                n=Min_Individual_Req, replace=False
            )
            Matched_Population.reset_index(drop=True, inplace=True)

        if not External_Persona:
            try:
                Matched_Population.drop("ID", axis=1, inplace=True)
            except:
                pass
            Matched_Population["ID"] = np.arange(1, Matched_Population.shape[0] + 1)

            np.random.seed(0)
            random_block = assign_numbers(Min_Individual_Req, Num_blocks)
        else:
            random_block_list = distribute_expanded_blocks(
                Min_Individual_Req, Num_blocks
            )
            random_block = [item for sublist in random_block_list for item in sublist]

        unique, counts = np.unique(random_block, return_counts=True)
        Proportion = np.round((counts / Min_Individual_Req) * 100, 0)
        value_counts = np.hstack(
            (unique.reshape(-1, 1), counts.reshape(-1, 1), Proportion.reshape(-1, 1))
        )
        df_value_counts = pd.DataFrame(value_counts)
        df_value_counts.columns = ["Block Number", "Frequency", "Proportion (%)"]

        if not External_Persona:
            if expr_conf.country in USA_NAMES:
                if not expr_conf.target_population.get("state"):
                    Matched_Population["State of residence"] = Matched_Population[
                        "state"
                    ]
                else:
                    Matched_Population["State of residence"] = expr_conf.state[0]
                try:
                    Matched_Population.drop("state", axis=1, inplace=True)
                except:
                    pass
                unique_states = (
                    Matched_Population["State of residence"].unique().tolist()
                )
            else:
                Matched_Population["State of residence"] = expr_conf.state[0]
                try:
                    Matched_Population.drop("state", axis=1, inplace=True)
                except:
                    pass
                unique_states = (
                    Matched_Population["State of residence"].unique().tolist()
                )

            if len(expr_conf.state) == 0:
                expr_conf.state = unique_states
            Matched_Population["Country of residence"] = expr_conf.country
            Matched_Population["Current year"] = expr_conf.year

            personas_to_remove = [
                "ID",
                "Person ID",
                "Household ID",
                "household wt",
                "person wt",
                "household_wt",
                "person_wt",
            ]

        logger.info("STEP 6: Preparing personas and demographics")
        guidance_section = (
            get_guidance_section_instruction_discrete()
            if expr_conf.response_type == SurveyResponseType.DISCRETE
            else get_guidance_section_instruction_probabilistic()
        )
        if not External_Persona:
            Persona_in_list_format = dataframe_to_list(
                Matched_Population, personas_to_remove
            )
            Persona_in_dict_format = dataframe_to_dict(
                Matched_Population, personas_to_remove
            )
            Unique_persona_columns = list(Matched_Population.columns)

            for persona in personas_to_remove:
                try:
                    Unique_persona_columns.remove(persona)
                except Exception:
                    pass

            matched_population_ids = Matched_Population["ID"].values.tolist()
            if not expr_conf.latent_variables:
                demographics_chunk = list(chunk_list(Persona_in_dict_format, 10))
                demographics_prompts = [[d for d in l] for l in demographics_chunk]
                demographics_batch = partial(
                    execute_demographics_batch,
                    llm_model=expr_conf.expr_llm_model,
                    why_prompt=expr_conf.why_prompt,
                    attributes=Unique_Attributes,
                )
                with ThreadPoolExecutor(max_workers=5) as executor:
                    demographics_responses = list(
                        executor.map(demographics_batch, demographics_prompts)
                    )
                demographics_results = list(
                    itertools.chain.from_iterable(demographics_responses)
                )
                Persona_NL = []

                for id, index in zip(
                    matched_population_ids, range(len(matched_population_ids))
                ):
                    if demographics_results[index] != "error":
                        row_data = demographics_results[index]
                    else:
                        current_persona = Persona_in_list_format[index]
                        row_data = " \n".join(current_persona)
                    Persona_NL.append(row_data)
            else:
                # empty initialization
                Persona_NL = ["" for _ in range(len(matched_population_ids))]
        else:
            Persona_NL = []
            Persona_in_dict_format = []
            Persona_in_list_format = []
            curr_id = 0
            for index, curr_persona in enumerate(expr_conf.external_personas):
                row_data = {}
                row_data_list = []
                curr_id += 1
                for key, value in curr_persona.items():
                    if key not in ["isDescriptionBased", "id"]:
                        if len(value) > 0:
                            row_data[key] = value
                            row_data_list.append(f"{key}: {value}")
                Persona_in_dict_format.append(row_data)
                Persona_in_list_format.append(row_data_list)
                curr_id_num_blocks = len(random_block_list[index])
                for _ in range(curr_id_num_blocks):  # initialization
                    Persona_NL.append({
                        "demographics_section": "",
                        "id": curr_id,
                    })

            if not expr_conf.latent_variables:
                demographics_chunk = list(chunk_list(Persona_in_dict_format, 10))
                demographics_prompts = [[d for d in l] for l in demographics_chunk]
                demographics_batch = partial(
                    execute_demographics_batch,
                    llm_model=expr_conf.expr_llm_model,
                    why_prompt=expr_conf.why_prompt,
                    attributes=Unique_Attributes,
                )
                with ThreadPoolExecutor(max_workers=5) as executor:
                    demographics_responses = list(
                        executor.map(demographics_batch, demographics_prompts)
                    )
                demographics_results = list(
                    itertools.chain.from_iterable(demographics_responses)
                )

                for index in range(len(demographics_results)):
                    if demographics_results[index] != "error":
                        Persona_NL[index]["demographics_section"] = (
                            demographics_results[index]
                        )
                    else:
                        current_persona = Persona_in_list_format[index]
                        Persona_NL[index]["demographics_section"] = " \n".join(
                            current_persona
                        )
            else:
                for index in range(len(Persona_in_list_format)):
                    Persona_NL[index]["demographics_section"] = ""

        logger.info("STEP 7: Creating survey tasks and alternatives")

        Master_Task_file = pd.DataFrame()
        if not External_Persona:
            Master_Task_file["ID"] = Matched_Population["ID"].values
            Master_Task_file["Persona"] = Persona_in_list_format
            Master_Task_file["Persona_NL"] = Persona_NL
            Master_Task_file["block"] = random_block
        else:
            curr_expaned_id = [
                [index + 1] * len(ele) for index, ele in enumerate(random_block_list)
            ]
            curr_id = [item for sublist in curr_expaned_id for item in sublist]
            persona_list_in_format_expanded = []
            persona_nl_expanded = []
            for item in curr_id:
                persona_list_in_format_expanded.append(
                    Persona_in_list_format[int(item) - 1]
                )
                persona_nl_expanded.append(
                    Persona_NL[int(item) - 1]["demographics_section"]
                )
            Master_Task_file["ID"] = curr_id
            Master_Task_file["Persona"] = persona_list_in_format_expanded
            Master_Task_file["Persona_NL"] = persona_nl_expanded
            Master_Task_file["block"] = random_block

        ##################
        # Latent variables

        analytics_traits_data: lv_models_core.TraitsDataForAnalytics | None = None
        if expr_conf.latent_variables:
            df_copy = Master_Task_file.copy(deep=True)

            # Drop duplicates based on 'ID' column
            df_unique_personas = df_copy.drop_duplicates(subset=["ID"], keep="first")
            # row["Persona"] is a list of strings of the form 'key: value'

            # Create personas for latent variables
            # demographics are converted to dictionaries
            personas: list[lv_models.Persona] = []
            for _, row in df_unique_personas[["ID", "Persona"]].iterrows():
                # e.g.: ["key1: value1", "key2: value2"]
                demographics_str_list: list[str] = row["Persona"]
                # convert ["key1: value1", "key2: value2"] into a dictionary
                demographics_dict: dict[str, str] = {}
                for str_ in demographics_str_list:
                    key, value = str_.split(":", 1)  # Split at the first ':'
                    demographics_dict[key.strip()] = value.strip()  # Strip whitespaces

                personas.append(
                    lv_models_core.Persona(id=row["ID"], demographics=demographics_dict)
                )

            full_run_request = lv_models.FullRunRequest(
                why_prompt=expr_conf.why_prompt,
                personas=personas,
                batch_size=20,
                llm_model=expr_conf.expr_llm_model,
            )
            lv_experiment_data: lv_models.FullRunResponse = asyncio.run(
                latent_variables_full_run(full_run_request)
            )

            analytics_traits_data = get_traits_data_for_analytics(
                lv_experiment_data.traits_data
            )

            persona_NL_format_str = lv_models.NLSummaryLLMOutput.get_format_str()

            # persona_wtd = persona_with_traits_data
            for persona_wtd in lv_experiment_data.personas_with_traits_data:
                persona_id = persona_wtd.id
                NL_summary = persona_wtd.NL_summary
                if NL_summary is None:
                    continue

                args = {
                    "demographics": NL_summary.demographics,
                    "personality_traits": NL_summary.personality_traits,
                    "pain_points": NL_summary.pain_points,
                    "behaviors": NL_summary.behaviors,
                    "goals": NL_summary.goals,
                    "motivations": NL_summary.motivations,
                    "trigger_points": NL_summary.trigger_points,
                }
                new_PERSONA_NL = persona_NL_format_str.format(**args)

                Master_Task_file.loc[
                    Master_Task_file["ID"] == persona_id, "Persona_NL"
                ] = new_PERSONA_NL

                persona_statement_scores_info = get_persona_statement_scores_info(
                    persona_wtd.persona_traits_data, analytics_traits_data
                )

                append_partial = partial(
                    append_persona_statement_scores_info,
                    persona_statement_scores_info=persona_statement_scores_info,
                )

                ID_mask = Master_Task_file["ID"] == persona_id

                Master_Task_file.loc[ID_mask, "Persona"] = Master_Task_file.loc[
                    ID_mask, "Persona"
                ].apply(append_partial)

        ##################

        Master_Task_file = pd.merge(Master_Task_file, Design_df, how="left", on="block")

        Master_Task_file = Master_Task_file[
            ["ID", "Persona", "Persona_NL", "block", "Task"]
            + [f"Alt_{i}" for i in range(1, Num_alts + 1)]
        ]
        Master_Task_file["ID"] = Master_Task_file["ID"].astype(int)
        Master_Task_file["Task"] = Master_Task_file["Task"].astype(int)
        Master_Task_file["block"] = Master_Task_file["block"].astype(int)

        Master_Task_file.sort_values(
            by=["ID", "Task"], ascending=[True, True], inplace=True
        )
        Master_Task_file.reset_index(drop=True, inplace=True)

        Master_Task_file = Attribute_Shuffle(
            Master_Task_file,
            Num_alts,
            Unique_Attributes,
            attribute_name_levels_grouped,
            attribute_type,
            attribute_continuous_value,
            Null_level,
            attribute_levels_type_indicator,
            expr_conf,
        )
        logger.info(f"Total number of LLM calls: {Master_Task_file.shape[0]}")
        if expr_conf.add_neither_option:
            Order_list_orig = [f"Alt_{i}F_shuffled" for i in range(1, Num_alts + 1)] + [
                "None Option"
            ]
            Master_Task_file["None Option"] = [
                ["None"] for ele in range(Master_Task_file.shape[0])
            ]
            Master_Task_file[f"Alt_{Num_alts + 1}"] = [
                [0] * len(Unique_Attributes) for ele in range(Master_Task_file.shape[0])
            ]
        else:
            Order_list_orig = [f"Alt_{i}F_shuffled" for i in range(1, Num_alts + 1)]

        Master_Task_file, Shuffled_Order = Alternative_Shuffle(
            Master_Task_file, Order_list_orig, expr_conf.alternative_shuffle_seed
        )
        alt_map = {
            2: "two",
            3: "three",
            4: "four",
            5: "five",
            6: "six",
            7: "seven",
            8: "eight",
            9: "nine",
            10: "ten",
        }
        if expr_conf.add_neither_option:
            final_num_alts = Num_alts + 1
            if expr_conf.response_type == SurveyResponseType.DISCRETE:
                expr_conf.none_choice_instruction = (
                    "- If the scenarios described using factors are not relevant to"
                    " you, then choose the option with no features (None)"
                )
            elif expr_conf.response_type == SurveyResponseType.PROBABILISTIC:
                expr_conf.none_choice_instruction = (
                    "- If the scenarios described using factors are not relevant to"
                    " you, then accordingly assign high probability to the option with"
                    " no features (None)"
                )
            dv_with_opt_out_prompt = pb.dependent_variable_prompt_with_opt_out()
            try:
                dv_response = pe.execute(
                    prompt=dv_with_opt_out_prompt,
                    args={"why_prompt": expr_conf.respondent_dependent_variable},
                    output_object=DependentVariableResponse,
                    description="Generating dependent variable with opt out...",
                )
                expr_conf.respondent_dependent_variable = dv_response.get(
                    "dependent_variable"
                )
            except Exception:
                pass

        else:
            final_num_alts = Num_alts
            expr_conf.none_choice_instruction = ""

        expr_conf.respondent_dependent_variable = (
            expr_conf.respondent_dependent_variable.replace(
                "two",
                (
                    alt_map[final_num_alts - 1]
                    if expr_conf.add_neither_option
                    else alt_map[final_num_alts]
                ),
            )
        )

        if expr_conf.response_type == SurveyResponseType.DISCRETE:
            wording_for_study_context = (
                "Choose the scenario where the presented conditions would be most"
                " conducive to "
            )
            response_format = """{{"selected_option": "Option 2", "decision_strategy": "C", "llm_choice": None, "reason": "I started by scanning all options for price and brand recognition. Option 1 (None) was ruled out since I do need a vehicle. I eliminated Option 2 and Option 5 quickly due to high prices that are far beyond what I can afford. The Ford Mustang Mach-E (Option 3) didn't have a price, making it hard to evaluate. Among the remaining, Option 4 is the most affordable, which is essential given my moderate income and the rising cost of living in New York. Though it lacks brand info, the price point makes it the most realistic choice for maximizing adoption. I used a hybrid strategy, weighing both affordability and availability of clear information."}}"""
            probabilistic_response_instructions = ""
        elif expr_conf.response_type == SurveyResponseType.PROBABILISTIC:
            wording_for_study_context = (
                "Tell us the probability of choosing scenarios where the presented"
                " conditions would be most conducive to "
            )
            response_format = """{{"selected_option": {"Option 1": 0.70, "Option 2": 0.30},"decision_strategy": "C", "llm_choice": "Option 2", "reason": "I started by scanning all options for price and brand recognition. Option 1 (None) was ruled out since I do need a vehicle. I eliminated Option 2 and Option 5 quickly due to high prices that are far beyond what I can afford. The Ford Mustang Mach-E (Option 3) didn't have a price, making it hard to evaluate. Among the remaining, Option 4 is the most affordable, which is essential given my moderate income and the rising cost of living in New York. Though it lacks brand info, the price point makes it the most realistic choice for maximizing adoption. I used a hybrid strategy, weighing both affordability and availability of clear information.""}}"""
            probabilistic_response_instructions = """3. Provide preference probabilities as decimal values between 0 and 1\n4. Ensure probabilities sum to exactly 1.0\n5. Provide the probability scores based on the individual's demographic characteristics. 
The score should reflect their general behavior and preferences related to the trait, as inferred from these demographic factors. Avoid clustering around the middle value (e.g., 0.5). Use the full range of the scale (0-1) to capture the diversity of responses.\n6.Provide the preference probability for each of the option even when the probabilities are 0. The length of selected_option dict MUST always be equal to number of options shown in questionnaire."""
        logger.info("STEP 8: Executing survey tasks")
        if expr_conf.add_decision_time:
            t1 = 3 + 0.5 * log2(Num_alts) + 0.5 * len(Unique_Attributes)
            t2 = 3 + 1 * log2(Num_alts) + 1.5 * len(Unique_Attributes)
            np.random.seed(expr_conf.decision_time_seed)
            T = np.random.uniform(t1, t2, size=len(Master_Task_file))
            weights = np.zeros((len(Master_Task_file), 4))
            weights[:, 0] = np.random.uniform(0.15, 0.20, size=len(Master_Task_file))
            weights[:, 1] = np.random.uniform(0.20, 0.25, size=len(Master_Task_file))
            weights[:, 2] = np.random.uniform(0.35, 0.45, size=len(Master_Task_file))
            weights[:, 3] = np.random.uniform(0.15, 0.25, size=len(Master_Task_file))
            total = np.sum(weights, axis=1)
            d = np.kron(total[:, np.newaxis], np.ones((1, 4)))
            weights = weights / d
            T = np.kron(T[:, np.newaxis], np.ones((1, 4)))
            tw = np.round(T * weights, 2)

        tasks_start_time = time.time()
        survey_tasks = [  # TODO: create a function and apply .map
            SurveyTask(
                task_number=row["Task"],
                persona_traits=row["Persona_NL"],
                persona_id=row["ID"],
                options=extract_options(row),
                prompt_context=expr_conf.survey_prompt,
                year=expr_conf.year,
                country=expr_conf.country,
                study_subject=study_subject,
                why_prompt=expr_conf.why_prompt,
                target_behavior=expr_conf.target_behavior,
                optimization_context=expr_conf.optimization_context,
                context=expr_conf.context,
                dependent_variable=expr_conf.respondent_dependent_variable,
                none_choice_instruction=expr_conf.none_choice_instruction,
                wording_for_study_context=wording_for_study_context,
                response_format=response_format,
                probabilistic_response_instructions=probabilistic_response_instructions,
                survey_time_proportion=(
                    get_decision_time_text(tw[index, :].tolist() + [t1, t2])
                    if expr_conf.add_decision_time
                    else ""
                ),
                guidance_section=guidance_section,
            )
            for index, row in Master_Task_file.iterrows()
        ]
        logger.info(f"Number of survey tasks: {len(survey_tasks)}")

        survey_results = get_discrete_choice_survey_responses(
            survey_tasks=survey_tasks, expr_conf=expr_conf, num_alts=final_num_alts
        )
        tasks_end_time = time.time()
        average_seconds_per_task = (tasks_end_time - tasks_start_time) / len(
            survey_results
        )
        logger.info(f"Average seconds per task: {average_seconds_per_task:.2f}")

        logger.info("STEP 9: Processing survey results")

        # Master_Task_file.drop(columns=["Persona_NL"], inplace=True)
        Prob_to_Discrete_Seed = expr_conf.prob_to_discrete_seed
        dce_table, parse_error_count, all_answers = create_dce_table_fast(
            expr_conf,
            Master_Task_file,
            survey_results,
            Num_alts,
            Unique_Attributes,
            Shuffled_Order,
            Prob_to_Discrete_Seed,
        )
        logger.info(f"Parse error count: {parse_error_count}")
        if len(dce_table) > 0:
            curr_offset = 0
            for att_num, att_name in enumerate(Unique_Attributes[1:]):
                curr_offset += len(
                    attribute_name_levels_grouped[Unique_Attributes[att_num]]
                )
                if expr_conf.is_hb_run == True:
                    temp = dce_table[att_name].values
                    nonzero_rows = temp != 0
                    temp[nonzero_rows] += curr_offset
                    dce_table[att_name] = temp
                else:
                    curr_att_type = attribute_type[att_name][0]
                    if curr_att_type != "Continuous":
                        temp = dce_table[att_name].values
                        nonzero_rows = temp != 0
                        temp[nonzero_rows] += curr_offset
                        dce_table[att_name] = temp
            if len(dce_table) > 0:
                survey_results_file = os.path.join(
                    expr_conf.folder_names.get("survey_results_data"),
                    "survey_results.csv",
                )
                dce_table.to_csv(survey_results_file)
                logger.info(f"Survey results saved to: {survey_results_file}")

            logger.info("STEP 10: Generating final mappings and summary")

            if expr_conf.generate_mappings:
                dce_table_with_labels = deepcopy(dce_table)
                big_list = []
                for i in range(dce_table_with_labels.shape[0]):
                    a = dce_table_with_labels.loc[i, "Persona"]
                    id = dce_table_with_labels.loc[i, "ID"]
                    task = dce_table_with_labels.loc[i, "Task"]
                    b = convert_list_to_dict(a)
                    b["ID"] = id
                    b["Task"] = task
                    big_list.append(b)

                expanded_df = pd.DataFrame(big_list)
                expanded_df.sort_values(by=["ID", "Task"], inplace=True)
                expanded_df.drop(["ID", "Task"], axis=1, inplace=True)
                result_df = pd.concat(
                    [dce_table_with_labels.drop(columns=["Persona"]), expanded_df],
                    axis=1,
                )
                result_df["chosen_choice_letter"] = [
                    ord(x) - 64 for x in result_df["chosen_choice_letter"]
                ]
                result_df["Chosen"] = (
                    result_df["chosen_choice_letter"] == result_df["Alts"]
                ).astype(int)
                result_df.drop(columns=["chosen_choice_letter"], inplace=True)
                for att_num, att_name in enumerate(Unique_Attributes):
                    curr_att_type = attribute_type[att_name][0]
                    if curr_att_type != "Continuous":
                        result_df[att_name] = result_df[att_name].map(
                            al_lookup["get_level_text"]
                        )

                result_df.rename(columns={"Alts": "Option"}, inplace=True)
                result_df = result_df[
                    ["ID", "Task", "Option", "Chosen"]
                    + Unique_Attributes
                    + expanded_df.columns.tolist()
                ]
            else:
                result_df = dce_table
        else:
            result_df = pd.DataFrame()
        survey_summary = {
            "block_distribution": df_value_counts.to_dict(orient="records"),
            "configuration": {
                "attribute_count": len(Unique_Attributes),
                "minimum_levels": Min_Level,
                "maximum_levels": Max_Level,
                "number_of_alternatives": f"{Num_alts} + None",
            },
            "design_summary": {
                "design_type": "orthogonal" if Orthogonal_Design else "Random",
                "number_of_tasks": Req_task,
                "d_efficiency": round(D_eff, 2),
                "required_sample_size": Min_Individual_Req,
                "available_sample_size": Current_Persona_Length,
                "total_openai_calls": Min_Individual_Req * Req_task,
            },
        }
        # TODO: might be better to use a pydantic class to keep things clean and robust
        mappings = {
            "experimentor why question type": expr_conf.prompt_type,
            "experimentor why question prompt": expr_conf.why_prompt,
            "attributes_and_levels_lookup": al_lookup_original,
            "realworld_products": expr_conf.realworld_products,
            "attribute_type": attribute_type_only,
            "monetary_attributes": (
                Monetary_attributes if not expr_conf.is_hb_run else []
            ),
            "parse_error_count": parse_error_count,
            "external_persona_provided": External_Persona,
            "respondent_dependent_variable": expr_conf.respondent_dependent_variable,
            "average_seconds_per_task": average_seconds_per_task,
            "is_hb_run": expr_conf.is_hb_run,
            "null_levels_included": expr_conf.null_levels,
            "opt_out_included": expr_conf.add_neither_option,
            "latent_variables": convert_analytics_traits_data_to_dict(
                analytics_traits_data
            ),
        }
        logger.info(f"DCE experiment completed successfully for {expr_conf.why_prompt}")
        return (
            mappings,
            dce_table,
            survey_summary,
            all_answers,
            result_df,
            External_Persona,
        )

    else:
        logger.info("No matching population found, returning empty results")
        all_personas_dce = pd.DataFrame()
        survey_summary = {}
        mappings = {
            "experimentor why question type": expr_conf.prompt_type,
            "experimentor why question prompt": expr_conf.why_prompt,
            "attributes_and_levels_lookup": al_lookup_original,
        }
        return mappings, all_personas_dce, survey_summary, [], pd.DataFrame(), False


def create_attributes_levels_lookup(expr_conf: ExperimentConfig) -> dict:
    logger.info("Generating attributes and levels lookup table")
    if expr_conf.pre_cooked_attributes_and_levels_lookup:
        return create_attribute_and_level_lookup_tables(
            attributes_and_levels=expr_conf.pre_cooked_attributes_and_levels_lookup,
            null_levels=False,
        )

    attributes = get_attributes(
        why_prompt=expr_conf.why_prompt,
        country=expr_conf.country,
        attribute_count=expr_conf.attribute_count,
        llm_model=expr_conf.levels_llm_model,
        include_price_brand=expr_conf.include_price_brand,
    )
    attrs_levels = [
        [
            attr,
            get_levels(
                why_prompt=expr_conf.why_prompt,
                country=expr_conf.country,
                attribute=attr,
                level_count=expr_conf.level_count,
                llm_model=expr_conf.levels_llm_model,
            ),
        ]
        for attr in attributes
    ]

    return create_attribute_and_level_lookup_tables(
        attributes_and_levels=attrs_levels, null_levels=False
    )


def create_attributes_levels_lookup_table_from_api(
    prompt: str,
    country: str,
    year: int,
    attribute_count: int,
    level_count: int,
    null_levels: bool,
    levels_llm_model: LLMModel | None = LLMModel.GCP_GEMINIFLASH,
    max_length: int = 40,
):
    attributes_levels_request = AttributesLevelsRequest(
        why_prompt=prompt,
        country=country,
        year=year,
        attribute_count=attribute_count,
        level_count=level_count,
        llm_model=levels_llm_model,
        max_length=max_length,
    )
    attributes_levels_response = asyncio.run(
        create_attributes_and_levels(request=attributes_levels_request)
    )
    attributes_levels = attributes_levels_response.get("attributes_levels")
    brand_attribute_combinations = attributes_levels_response.get(
        "brand_attribute_combinations"
    )

    attrs_and_levels = [
        [attr_levels.attribute, attr_levels.levels] for attr_levels in attributes_levels
    ]
    return (
        create_attribute_and_level_lookup_tables(
            attributes_and_levels=attrs_and_levels, null_levels=False
        ),
        brand_attribute_combinations,
    )


@retry(stop=stop_after_attempt(3))
def get_study_subject(why_prompt: str, llm_model: LLMModel, run_id: str | None) -> str:
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    object_of_study_prompt = pb.object_of_study_prompt()
    outcome_phrase_prompt = pb.outcome_phrase_prompt()
    try:
        with trace(
            name="Object of Study Generation",
            project_name=run_id,
            run_type="llm",
            metadata={
                "ls_model_name": llm_model.value,
                "prompt_type": "object_of_study",
            },
        ) as run:
            object_of_study = pe.execute(
                prompt=object_of_study_prompt,
                args={"why_prompt": why_prompt},
                output_object=ObjectOfStudyResponse,
                description="Generating object of study",
            )
            run.add_metadata({"result": object_of_study.get("object_of_study")})
            run.end()
    except Exception as e:
        logger.error(f"Error in object of study generation: {str(e)}", exc_info=True)
        raise e

    try:
        with trace(
            name="Outcome Phrase Generation",
            project_name=run_id,
            run_type="llm",
            metadata={"llm_model": llm_model.value, "prompt_type": "outcome_phrase"},
        ) as run:
            outcome_phrase_response = pe.execute(
                prompt=outcome_phrase_prompt,
                args={
                    "why_prompt": why_prompt,
                    "outcome_phrase": object_of_study.get("object_of_study"),
                },
                output_object=OutcomePhraseResponse,
                description="Generating outcome phrase...",
            )
            run.add_metadata({"result": outcome_phrase_response.get("outcome_phrase")})
            run.end()

    except Exception as e:
        logger.error(f"Error in outcome phrase generation: {str(e)}", exc_info=True)
        return None

    return outcome_phrase_response.get("outcome_phrase")


def extract_options(row: pd.Series) -> dict[str, list[str]]:
    options = {}
    count = 0
    for i in range(1, 10):  # Assuming a maximum of 6 alternatives
        col_name = f"Alt_{i}_Final"
        if col_name in row.index and isinstance(row[col_name], list) and row[col_name]:
            count += 1
            options[f"Option {count}"] = row[col_name]
    return options


def get_country_capital(country: str) -> str:
    return capitals.get(country, "Capital not found")


def execute_demographics_batch(
    demographics_prompts: list[dict],
    llm_model: LLMModel,
    why_prompt: str,
    attributes: list[str],
):
    pb = PromptBuilder()
    pe = LCELPromptExecutor(llm_model=llm_model)
    prompt = pb.dict_to_natural_language_prompt()
    prompts_with_args = [
        prompt.format_prompt(
            demographic_data=demographic_prompt,
            why_prompt=why_prompt,
            attributes=attributes,
        )
        for demographic_prompt in demographics_prompts
    ]
    try:
        results = pe.batch_execute(
            prompts=prompts_with_args,
            args=None,
            output_object=BinaryVariableToNaturalLanguageResponse,
            description="Batch-Converting demographics to text",
        )
        return [result.get("persona", "error") for result in results]
    except:
        return ["error"] * len(demographics_prompts)


def execute_demographics_batch_statement(
    demographics_prompts: list[dict],
    image_description: str,
):
    pb = PromptBuilder()
    pe = LCELPromptExecutor()
    prompt = pb.dict_to_natural_language_prompt_image()
    prompts_with_args = [
        prompt.format_prompt(
            demographic_data=demographic_prompt,
            image_description=image_description,
        )
        for demographic_prompt in demographics_prompts
    ]
    try:
        results = pe.batch_execute(
            prompts=prompts_with_args,
            args=None,
            output_object=BinaryVariableToNaturalLanguageResponse,
            description="Batch-Converting demographics to text",
        )
        return [result.get("persona", "error") for result in results]
    except:
        return ["error"] * len(demographics_prompts)


def Selection_filter_Non_US(Persona_data, Selection_Dict):
    filter = np.zeros((len(Persona_data), len(Selection_Dict) + 1))
    count = -1
    for key, value in Selection_Dict.items():
        count += 1
        if len(value) > 0:
            if key == "Age":
                if len(value) == 2:
                    temp = (Persona_data[key] >= value[0]) & (
                        Persona_data[key] <= value[1]
                    ).astype(int)
                if len(value) == 1:
                    temp = (Persona_data[key] >= value[0]).astype(int)
            else:
                temp = Persona_data[key].isin(value).astype(int)
            filter[:, count] = temp
        else:
            filter[:, count] = np.ones(len(Persona_data))

    filter[:, len(Selection_Dict)] = np.product(
        filter[:, 0 : len(Selection_Dict)], axis=1
    )

    if np.sum(filter[:, len(Selection_Dict)]) == 0:
        Available_data = pd.DataFrame()
        return Available_data
    else:
        Available_data = deepcopy(Persona_data)
        Available_data = Available_data.loc[filter[:, len(Selection_Dict)] == 1]
        Available_data.reset_index(drop=True, inplace=True)
        Available_data["ID"] = np.arange(1, Available_data.shape[0] + 1)
        Available_data = Available_data[Required_Population_Columns]
        return Available_data


def Run_Statement(expr_conf: ExperimentConfig):
    """"""
    try:
        if expr_conf.image_name:
            images_folder = os.path.join(
                settings.PROJECT_ROOT_DIRECTORY,
                "app",
                "logs",
                "images",
            )
            if not os.path.exists(images_folder):
                os.makedirs(images_folder, exist_ok=True)
            local_image_path = os.path.join(
                images_folder,
                expr_conf.image_name,
            )
            download_file_from_s3(
                bucket_name=settings.AWS_S3_BUCKET_NAME,
                object_name=expr_conf.image_name,
                local_file_path=local_image_path,
            )
            base64_image = convert_image_to_base64(local_image_path)
            delete_local_artifacts(local_image_path)
            image_details = summarize_image(
                img_base64=base64_image, prompt=DETAILED_IMAGE_DESCRIPTION_TEMPLATE
            )
            if not expr_conf.concept_description:
                expr_conf.concept_description = summarize_image(
                    img_base64=base64_image, prompt=IMAGE_BASED_INTRO_TEMPLATE
                )
        else:
            image_details = ""
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        image_details = ""

    if expr_conf.country not in USA_NAMES:
        expr_conf.state = [get_country_capital(country=expr_conf.country)]
    else:
        if expr_conf.target_population.state:
            expr_conf.state = [expr_conf.target_population.state.value]
        else:
            expr_conf.state = []

    External_Persona = False
    if len(expr_conf.external_personas) > 0:
        External_Persona = True

        if expr_conf.is_hb_run:
            External_Persona = False

    if not External_Persona:
        logger.info("STEP 2: Loading and preparing population data")
        if expr_conf.country in USA_NAMES:
            logger.info("Loading population data for USA")

            target_population = expr_conf.target_population.model_dump()
            expr_conf.target_population = {
                k: (
                    [get_enum_value(v1) for v1 in v]
                    if isinstance(v, list)
                    else get_enum_value(v)
                )
                for k, v in target_population.items()
            }
            if "African American" in expr_conf.target_population.get("racial_group"):
                expr_conf.target_population.get("racial_group").append("Black")
                expr_conf.target_population.get("racial_group").remove(
                    "African American"
                )
            try:
                Matched_Population = getPopulationData(expr_conf.target_population)
                Matched_Population = Matched_Population.drop_duplicates(
                    subset=["age", "gender", "household_income"]
                )
            except Exception as e:
                logger.error(f"Error in getPopulationData: {str(e)}", exc_info=True)
                capture_exception()
                raise
            Matched_Population = Matched_Population[
                DB_Population_Columns_Extended.keys()
            ]
            try:
                Matched_Population["racial_group"] = Matched_Population[
                    "racial_group"
                ].replace("Black", "African American")
            except Exception:
                pass
            try:
                Matched_Population["racial_group"] = Matched_Population[
                    "racial_group"
                ].replace("White", "Caucasian")
            except Exception:
                pass

            logger.info(
                "Population data loaded from database. Total records"
                f" {len(Matched_Population)}"
            )
            db_columns = ["family_size", "vehicles_in_household"]
            try:
                Matched_Population.loc[
                    Matched_Population[db_columns[0]] > 4, db_columns[0]
                ] = "More than 4 people"
                Matched_Population.loc[
                    Matched_Population[db_columns[1]] > 4, db_columns[1]
                ] = "More than 4 vehicles"
                Matched_Population[db_columns] = Matched_Population[db_columns].astype(
                    str
                )
            except SoftTimeLimitExceeded:
                capture_exception()
                raise SoftTimeLimitExceeded
            except Exception:
                pass
        else:
            logger.info("Loading population data for non-US countries")
            current_dir = os.path.dirname(os.path.abspath(__file__))
            Population_file = os.path.join(current_dir, "Filtered_Persona_Data.csv")
            Population_data = pd.read_csv(Population_file)
            expr_conf.target_population = {}

            if Population_Definition is True:
                Target_Population = expr_conf.population_traits.model_dump()
                Mapping_Target_code = {
                    "age": "Age",
                    "education": "Education Level",
                    "gender": "Sex",
                    "income": "Household Income",
                }

                Selection_Dictonary = {}
                for k, v in Target_Population.items():
                    new_key = Mapping_Target_code[k]
                    if k in ["education", "gender"]:
                        new_val = []
                        if len(v) > 0:
                            new_val = [i.value for i in v]
                    else:
                        new_val = v
                    Selection_Dictonary[new_key] = new_val

                Matched_Population = Selection_filter(
                    Population_data, Selection_Dictonary
                )
                if len(Matched_Population) == 0:
                    logger.info(
                        "No matching population found. Performing random sampling from"
                        " existing population"
                    )
                    Population_data["ID"] = np.arange(1, Population_data.shape[0] + 1)
                    Matched_Population = Population_data[Required_Population_Columns]
            else:
                Matched_Population = Population_data[non_us_demo_trait_columns]

        logger.info("Population data prepared successfully")
        if Include_Firebase_Traits_Uniform:
            traits_dict = expr_conf.population_traits.model_dump()
            if expr_conf.country in USA_NAMES:
                db_flipped_columns_keys = list(DB_Flipped_Columns.keys())
                to_remove = []
                for key, item in traits_dict.items():
                    if key not in db_flipped_columns_keys:
                        try:
                            Matched_Population.drop(
                                columns=DB_Flipped_Columns[key], axis=1, inplace=True
                            )
                        except Exception:
                            pass
                    else:
                        to_remove.append(key)
                for key in to_remove:
                    traits_dict.pop(key)
            else:
                to_remove = []
                for key, item in traits_dict.items():
                    if key not in non_us_demo_trait_columns:
                        try:
                            Matched_Population.drop(columns=key, axis=1, inplace=True)
                        except Exception:
                            pass
                    else:
                        to_remove.append(key)
                for key in to_remove:
                    traits_dict.pop(key)
            print("Len of traits_dict", len(traits_dict))
            if len(traits_dict) > 0:
                print("Matched_Population", Matched_Population)
                print("traits_dict", traits_dict)
                (
                    Matched_Population,
                    new_traits_columns,
                ) = add_traits_from_firebase_uniformally_to_df(
                    Matched_Population, traits_dict
                )
    else:
        logger.info("Loading user provided persona")
        Matched_Population = expr_conf.external_personas

    if not External_Persona:
        if expr_conf.country in USA_NAMES:
            if not expr_conf.target_population.get("state"):
                Matched_Population["State of residence"] = Matched_Population["state"]
            else:
                Matched_Population["State of residence"] = expr_conf.state[0]
            try:
                Matched_Population.drop("state", axis=1, inplace=True)
            except:
                pass
            unique_states = Matched_Population["State of residence"].unique().tolist()
        else:
            Matched_Population["State of residence"] = expr_conf.state[0]
            try:
                Matched_Population.drop("state", axis=1, inplace=True)
            except:
                pass
            unique_states = Matched_Population["State of residence"].unique().tolist()

        if len(expr_conf.state) == 0:
            expr_conf.state = unique_states
        Matched_Population["Country of residence"] = expr_conf.country
        Matched_Population["Current year"] = expr_conf.year

        personas_to_remove = [
            "ID",
            "Person ID",
            "Household ID",
            "household wt",
            "person wt",
            "household_wt",
            "person_wt",
        ]
        Persona_in_list_format = dataframe_to_list(
            Matched_Population, personas_to_remove
        )
        Persona_in_dict_format = dataframe_to_dict(
            Matched_Population, personas_to_remove
        )
        Unique_persona_columns = list(Matched_Population.columns)

        for persona in personas_to_remove:
            try:
                Unique_persona_columns.remove(persona)
            except Exception:
                pass

        matched_population_ids = Matched_Population["ID"].values.tolist()
        demographics_chunk = list(chunk_list(Persona_in_dict_format, 10))
        demographics_prompts = [[d for d in l] for l in demographics_chunk]
        demographics_batch = partial(
            execute_demographics_batch_statement,
            image_description=image_details,
        )
        with ThreadPoolExecutor(max_workers=5) as executor:
            demographics_responses = list(
                executor.map(demographics_batch, demographics_prompts)
            )
        demographics_results = list(
            itertools.chain.from_iterable(demographics_responses)
        )
        Persona_NL = []

        for id, index in zip(
            matched_population_ids, range(len(matched_population_ids))
        ):
            if demographics_results[index] != "error":
                row_data = demographics_results[index]
            else:
                current_persona = Persona_in_list_format[index]
                row_data = " \n".join(current_persona)
            Persona_NL.append(row_data)
    else:
        Persona_in_dict_format = []
        Persona_in_list_format = []
        curr_id = 0
        for index, curr_persona in enumerate(expr_conf.external_personas):
            row_data = {}
            row_data_list = []
            curr_id += 1
            for key, value in curr_persona.items():
                if key not in ["isDescriptionBased", "id"]:
                    if len(value) > 0:
                        row_data[key] = value
                        row_data_list.append(f"{key}: {value}")
                Persona_in_dict_format.append(row_data)
                Persona_in_list_format.append(row_data_list)

        demographics_chunk = list(chunk_list(Persona_in_dict_format, 10))
        demographics_prompts = [[d for d in l] for l in demographics_chunk]
        demographics_batch = partial(
            execute_demographics_batch_statement,
            image_description=image_details,
        )
        with ThreadPoolExecutor(max_workers=5) as executor:
            demographics_responses = list(
                executor.map(demographics_batch, demographics_prompts)
            )
        demographics_results = list(
            itertools.chain.from_iterable(demographics_responses)
        )
        Persona_NL = []

        for index in range(len(expr_conf.external_personas)):
            if demographics_results[index] != "error":
                row_data = demographics_results[index]
            else:
                current_persona = Persona_in_list_format[index]
                row_data = " \n".join(current_persona)
            Persona_NL.append(row_data)

    logger.info("Converted Traits into a Natural Language successfully")

    if not expr_conf.external_personas:
        Matched_Population["Persona_NL"] = Persona_NL
    else:
        Matched_Population = pd.DataFrame()
        curr_id = [i + 1 for i in range(len(expr_conf.external_personas))]
        Matched_Population["ID"] = curr_id
        Matched_Population["Persona"] = Persona_in_list_format
        Matched_Population["Persona_NL"] = Persona_NL

    statements_with_scales = []
    Individual_statement = []
    statements_with_scales_dict = {}

    for index, row in enumerate(expr_conf.concept_statements):
        curr_statement = row.get("statement")
        curr_scale = row.get("labels")
        Scale_mapped: dict[int, str] = {i + 1: ele for i, ele in enumerate(curr_scale)}
        statements_with_scales_dict[curr_statement] = Scale_mapped
        statements_with_scales.append(
            lv_models_core.StatementWithLikertScale(
                statement=curr_statement, scale=Scale_mapped
            )
        )
        Individual_statement.append(curr_statement)

    logger.info("Statement list created successfully")

    df_unique_personas = Matched_Population.drop_duplicates(subset=["ID"], keep="first")
    personas = [
        lv_models_core.Persona_NL(id=row["ID"], demographics=row["Persona_NL"])
        for _, row in df_unique_personas[["ID", "Persona_NL"]].iterrows()
    ]
    statement_scores_request = CTStatementScoresRequest(
        personas=personas,
        description_prompt=expr_conf.concept_description,
        image=image_details,
        statements_with_scales=statements_with_scales,
        batch_size=10,
        llm_model=expr_conf.expr_llm_model,
    )

    response = generate_statement_scores(
        req=statement_scores_request, scoring_type=expr_conf.scoring_type
    )

    records = []
    All_responses = response.personas_with_statement_scores
    for persona in All_responses:
        pid = persona.id
        all_statements = persona.statements_with_scores
        for stmt in all_statements:
            statement_text = stmt.statement
            scale = stmt.scale.root
            scores = stmt.score.root
            for i, score in enumerate(scores, start=1):
                records.append({
                    "ID": pid,
                    "Statement": statement_text,
                    "Scale Value": i,
                    "Scale Label": scale[i],
                    "Score": score,
                })

    Score_ID_df = pd.DataFrame(records)

    survey_results = pd.merge(Score_ID_df, Matched_Population, how="left", on=["ID"])
    mappings = {
        "concept_description": expr_conf.concept_description,
        "image_description": image_details,
        "image_name": expr_conf.image_name,
        "concept_statements": expr_conf.concept_statements,
    }
    output = {"survey_results": []}
    for curr_statement, curr_scale in statements_with_scales_dict.items():
        statement_group = survey_results[survey_results["Statement"] == curr_statement]
        num_scale = len(curr_scale)
        responses = {}
        for scale_value in range(1, num_scale + 1):
            scale_label = curr_scale[scale_value]
            label_score = statement_group[
                statement_group["Scale Value"] == scale_value
            ]["Score"].mean()
            responses[scale_label] = int(round(label_score * 100, 0))

        # Calculate net sentiment: (Agree + Strongly Agree) - (Strongly Disagree + Disagree)
        pos = responses.get(curr_scale[4], 0) + responses.get(curr_scale[5], 0)
        neg = responses.get(curr_scale[1], 0) + responses.get(curr_scale[2], 0)
        net_sentiment = int(round((pos - neg), 0))
        score = 0

        for scale_value in range(1, num_scale + 1):
            score += responses[curr_scale[scale_value]] * scale_value

        score = round(score / 100, 2)

        output["survey_results"].append({
            "statement": curr_statement,
            "responses": responses,
            "net_sentiment": net_sentiment,
            "avg_score": score,
        })
    return mappings, survey_results, output, External_Persona
