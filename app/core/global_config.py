import os
from pathlib import Path

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()


class Settings(BaseSettings):
    TITLE: str = "SuperEgo"
    VERSION: str = "0.0.1"
    DESCRIPTION: str = (
        "💕SuperEgo API powers Consulting and Product teams to design and run"
        " cost-effective, ethical, causal experiments. 🚀 Generative AI for"
        " Experimental Design. 👨‍🔬⚗️"
    )

    TAGS_METADATA: list[dict] = [
        {
            "name": "v1.experiments",
            "description": (
                "🧪👨‍🔬⚗️Run a novel experiment using attributes, levels, traits,"
                " dependent variables, preambles, and personas and place results in a"
                " run."
            ),
            "externalDocs": {
                "description": "Description of the Experiment Request Body fields",
                "url": (
                    "https://app.gitbook.com/o/jadOkacqKTkHvAmKDCti/s/RPAuLvXI0lCcTz39j3BR"
                    "/repositories/backend/how-to-run-an-experiment/experiment-request-body-fields"
                ),
            },
        },
        {
            "name": "v1.attributes_levels",
            "description": """📙 Generate product features (attributes) and their variations (levels)""",
        },
        {
            "name": "v1.traits",
            "description": (
                "**Why**. 🔬Causal factors that influence a person's decision."
                " Output:Return a list of persona traits that are available to"
                " subconscious.ai users."
            ),
        },
        {
            "name": "v1.personas",
            "description": (
                "**Who**. 👪Who is the respondent? A respondent is made up of traits."
                " Input: levels_per_trait - the number of levels of persona traits we"
                " would like to return per trait. trait_keys: a comma separated list of"
                " traits from the /traits endpoint. Output: Return a list of persona"
                " traits that are available to subconscious.ai users."
            ),
        },
        {
            "name": "v1.runs",
            "description": """📈Contains the experimental design + Experiment Results from WANDB.""",
        },
        {
            "name": "v1.human-baselines",
            "description": """👥 Human baselines endpoints contains human baseline data and AMCEs""",
        },
        {
            "name": "v1.copilot",
            "description": (
                """🤖 AI-assisted experiment design and causal assumption validation"""
            ),
        },
        {
            "name": "v1.payments",
            "description": """💳 Manage Stripe payment processing operations""",
        },
        {
            "name": "v1.tasks",
            "description": """📝 Monitor and manage Celery task statuses""",
        },
        {
            "name": "v1.market-simulator",
            "description": """🏪 Simulate market conditions and consumer behaviors""",
        },
        {
            "name": "v1.notifications",
            "description": (
                """🔄 Experiment state management and real-time event handling"""
            ),
        },
        {
            "name": "v1.populations",
            "description": """👪 Find respondents with the required core traits""",
        },
        {
            "name": "main",
            "description": "🛠️ Core system endpoints for health checks and diagnostics",
        },
        # V2 Endpoints
        {
            "name": "v2.copilot",
            "description": (
                """🤖 AI-assisted experiment design and causal assumption validation"""
            ),
        },
        {
            "name": "v2.attributes_levels",
            "description": """📙 Generate product features (attributes) and their variations (levels)""",
        },
        # V3 Endpoints
        {
            "name": "v3.chat",
            "description": (
                "💬 Interact with experiment insights using a conversational chat"
                " interface."
            ),
        },
        {
            "name": "v3.runs",
            "description": (
                " ⚙️ Retrieve details and artifacts for specific experiment runs."
            ),
        },
        {
            "name": "v3.artifacts",
            "description": " 📦 Access raw artifact files from experiment runs.",
        },
        {
            "name": "v3.processing",
            "description": (
                " ⚙️ Extract structured data (AMCE, Mindset) from experiment artifacts."
            ),
        },
        {
            "name": "v3.generation",
            "description": (
                " 💡 Generate insights (summaries, sentences) from experiment data"
                " using LLMs."
            ),
        },
        {
            "name": "v3.reports",
            "description": (
                " 📊 Generate consolidated reports (PDF/Text) with experiment insights."
            ),
        },
    ]

    TOS: str = "https://docs.subconscious.ai/support/terms-of-use"

    CONTACT: dict = {
        "name": "Avi Yashchin",
        "url": "https://www.linkedin.com/in/aviyashchin/",
        "email": "<EMAIL>",
    }

    LICENSE: dict = {
        "name": "MIT",
        "url": "https://github.com/Subconscious-ai/PoC/blob/main/LICENSE",
    }

    WANDB_ENTITY: str = os.getenv("WANDB_ENTITY")
    WANDB_PROJECT: str = os.getenv("WANDB_PROJECT")
    WANDB_REPLICATION_PROJECT: str = os.getenv("WANDB_REPLICATION_PROJECT")
    HUMAN_BASELINE_PROJECT: str = os.getenv("HUMAN_BASELINE_PROJECT")
    GITHUB_PAT: str = os.getenv("GITHUB_PAT")
    GITHUB_OWNER: str = os.getenv("GITHUB_OWNER")
    GITHUB_HB_REPO: str = os.getenv("GITHUB_HB_REPO")
    TRANSCRIPTIONS_PATH: str = "transcriptions/transcriptions_consolidated"
    WEBHOOK_URL: str = (
        f"{os.getenv('NEXT_PUBLIC_BACKEND_ENDPOINT')}/api/v1/notifications/webhook"
    )
    PROJECT_ROOT_DIRECTORY: Path = Path(os.path.abspath(__file__)).parent.parent.parent
    TEST_RESOURCES_DIRECTORY: Path = PROJECT_ROOT_DIRECTORY / Path("tests/endpoints")
    VALIDATION_ARTIFACTS_DIRECTORY: Path = PROJECT_ROOT_DIRECTORY / Path(
        "/validation/artifacts"
    )

    EXPERIMENT_DATA_FOLDER_NAME: Path = PROJECT_ROOT_DIRECTORY / Path(
        "app/logs/experiments"
    )

    USE_LOCAL_STORAGE: bool = True
    STORE_PLOTS: bool = True

    USE_RUN_ANALYTICS: bool = True

    USE_WANDB_STORAGE: bool = True

    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "dev")
    DEBUG: str = os.getenv("DEBUG", "False")
    SUPABASE_URL: str = os.getenv("SUPABASE_URL")
    SUPABASE_SERVICE_KEY: str = os.getenv("SUPABASE_SERVICE_KEY")
    AWS_REGION: str = os.getenv("AWS_REGION")
    AWS_S3_ACCESS_KEY_ID: str = os.getenv("AWS_S3_ACCESS_KEY_ID")
    AWS_S3_SECRET_ACCESS_KEY: str = os.getenv("AWS_S3_SECRET_ACCESS_KEY")
    AWS_S3_BUCKET_NAME: str = os.getenv("AWS_S3_BUCKET_NAME")


settings = Settings()
