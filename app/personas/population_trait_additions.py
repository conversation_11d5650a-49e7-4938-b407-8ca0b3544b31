import random

from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)

# Approach: for each trait randomly assign levels to personas
# NOTE: not all combos of uniform traits will be present guarenteed, but individually all will be present
#       this is due to the amount of respondents that would be required
#        - 5 traits with 5 levels = 5^5 = 3125 respondents for every combo to be covered
#        - becomes especially impossible with smaller buckets of personas


# searches through all firebase traits for trait type (religion, age, etc.)
def search_traits_for_type(traits: list, trait_type, levels_specified):

    levels = []
    for trait_level in traits:
        # check for correct trait type, check if level correct/included OR default to all levels
        if trait_level.type == trait_type and (
            len(levels_specified) == 0 or trait_level.id in levels_specified
        ):
            levels.append(trait_level)
    return levels


# returns a dict where key : str trait type, value : List[TraitLevel]
def get_trait_type_to_levels_mapping(traits_all: list, traits_included: list):
    mapping = {}
    for trait_type in traits_included.keys():
        levels_specified = traits_included[trait_type]
        mapping[trait_type] = search_traits_for_type(
            traits_all, trait_type, levels_specified
        )
    return mapping


# adds trait type column to df, row values are uniformally distributed among population
def levels_uniformly_for_population(
    Population_rows, trait_levels_list: list, trait_type: str
):
    # currently add long description as value so trait in prompt is "<trait_type>: <long description>"
    # example: religion: I am atheist

    column_values = []
    num_trait_levels = len(trait_levels_list)

    if num_trait_levels < 1:
        logger.info(f"No levels found for firebase trait {trait_type}")
        return []

    random.shuffle(trait_levels_list)

    for i in range(len(Population_rows)):
        idx = i % num_trait_levels
        column_values.append(trait_levels_list[idx])

    random.shuffle(column_values)
    return column_values


def add_traits_from_firebase_uniformally_to_df(Population_rows, traits_and_levels):
    Population_data = Population_rows.copy()
    traits_name = []

    for trait_type, levels in traits_and_levels.items():
        to_add = levels_uniformly_for_population(Population_data, levels, trait_type)
        if len(to_add) == len(Population_data):
            logger.info(f"Adding value for {trait_type}")
            traits_name.append(trait_type)
            Population_data.loc[:, trait_type] = to_add

    return Population_data, traits_name
