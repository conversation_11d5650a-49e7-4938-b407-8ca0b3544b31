import itertools
from collections import defaultdict

import uuid6

from app.api.v1.helpers.personas import Persona, TraitLevel
from app.core.utils.databases import get_firebase_collection


def create_personas_from_firebase(
    num_of_personas: int, trait_keys: list, levels_per_trait: int
) -> list[Persona]:
    """
    Create a list of Persona instances from the traits in the Firebase database.

    Args:
        num_of_personas (int): The number of personas to create.
        trait_keys (list): A list of trait keys to filter the traits from the database.
        levels_per_trait (int): The maximum number of trait levels to consider per trait type.

    Returns:
        list[Persona]: A list of generated Persona instances.
    """
    # Get all traits from the database and filter them by the keys in trait_keys
    coll = get_firebase_collection("traits")
    chosen_traits = defaultdict(list)
    for item in coll.stream():
        t = TraitLevel(**item.to_dict())
        if t.type in trait_keys and len(chosen_traits[t.type]) < levels_per_trait:
            chosen_traits[t.type].append(t)
    # A cartesian product generator of traits
    traits_product = itertools.product(
        *chosen_traits.values()
    )  # A cartesian product generator of traits
    # Create Persona instances from the traits product and limit the list to num_of_personas
    return [
        Persona(id=uuid6.uuid7(), traits=list(ts))
        for ts in itertools.islice(traits_product, num_of_personas)
    ]


if __name__ == "__main__":
    traits_to_select = ["race ethnicity", "income", "age", "profession", "sex"]
    # Generate 10 personas using the selected traits
    personas = create_personas_from_firebase(10, traits_to_select, 2)

    # Print the generated personas
    for p in personas:
        print(p)
