"""
Objective : Create a function that creates a string representing a persona which has many traits for use in gpt3 synthetic population for use in experimental execution.


1) Pull all personas from full USA population database from firestore "Full_US_synthetic_population"
2) Create a lookup structure that maps ids to traits
3) Logic that creates new the personas
4)

"""

import itertools
import random

# import firebase_admin
# from firebase_admin import credentials
# from firebase_admin import firestore
from app.personas.local_populations import populations

# cred = credentials.Certificate('why-earth-firebase-adminsdk-sg8za-17ff00de9f.json')
# app = firebase_admin.initialize_app(cred)
# db = firestore.client()


# Create a function which takes in the number of personas and a list of traits to be included in the persona, and reutrns a list of strings which represent each persona
def create_personas_from_firebase(
    num_of_personas: int = 2 * 1250 // 20,
    trait_keys: list = ["age", "educational_attainment", "family_income", "enneagram"],
):
    # doc_ref = db.collection(u'experiments').document(str(datetime.now()).replace(" ", "_")+"-"+expr_title) # type: ignore
    # doc_ref.set(d)

    documents_object_list = list(
        db.collection("Full_US_synthetic_population_sentences").list_documents()
    )
    random.shuffle(documents_object_list)

    documents_object_list_sampled = documents_object_list[:num_of_personas]
    # Loop through all documents and print out a subset of three persona traits
    persona_strings = []
    for ob in documents_object_list_sampled:
        ob = ob.get().to_dict()
        # from ob.items only select those that exist in trait_keys
        ob = {k: v for k, v in ob.items() if k in trait_keys}

        persona_string = " ".join((str(v) for k, v in ob.items()))
        persona_strings.append(persona_string)

    # trait_keys = list(documents_object_list[0].get().to_dict())

    # keylist=list(documents_list[0].to_dict())
    # print(keylist)
    return tuple(persona_strings)


def get_string_definition(combination: tuple, personas: dict):
    try:
        string_defination = ""
        for incre, v in enumerate(personas.values()):
            string_defination += f"{v[combination[incre]][1]}. "

        return string_defination.strip()
    except Exception as e:
        return f"Error: {str(e)}"


def create_personas_from_local(
    num_of_personas: int = 20, trait_keys: list = ["age", "race", "income", "gender"]
):
    # Create persona dict with relevant traits using local populations dict
    personas = {}
    for trait in trait_keys:
        personas[trait] = populations[trait]

    # create all combinations
    # all_combinations = list(itertools.product(*personas.values()))
    all_combinations = list(itertools.product(*personas.values())).copy()
    all_combinations = [list(elem) for elem in all_combinations]

    # create a list of all combinations using the get_string_definition function
    all_combinations_string = [
        get_string_definition(combo, personas) for combo in all_combinations
    ]

    # print([x for x in all_combinations])
    return list(all_combinations), personas, all_combinations_string
