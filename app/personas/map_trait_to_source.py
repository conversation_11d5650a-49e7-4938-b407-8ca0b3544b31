CSV_DATA_SOURCE = "csv"


trait_to_source = [
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "age",
        "datasource_type": "Age",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "sex",
        "datasource_type": "Sex",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "educational attainment",
        "datasource_type": "Education Level",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "*",
        "datasource_type": "Census Division",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "race ethnicity",
        "datasource_type": "Race",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "race ethnicity",
        "datasource_type": "Hispanic/Latino Origin",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "home ownership",
        "datasource_type": "Home Ownership",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "*",
        "datasource_type": "Number of Vehicles in Household",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "community type",
        "datasource_type": "Living in a Urban Area",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "income",
        "datasource_type": "Household Annual Income",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "presence of children",
        "datasource_type": "Number of Female Children in the household",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "presence of children",
        "datasource_type": "Number of Male Children in the household",
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "presence of children",
        "datasource_type": (
            "Number of Children Aged zero to five years old in the household"
        ),
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "presence of children",
        "datasource_type": (
            "Number of Children Aged six to ten years old in the household"
        ),
    },
    {
        "data_source": CSV_DATA_SOURCE,
        "trait_type": "presence of children",
        "datasource_type": (
            "Number of Children Aged eleven to fifteen years old in the household"
        ),
    },
]
