populations = {
    "age": {
        # "a0": ['Age-5', ' My age is less than 5'],
        # "a1": ['Age-12', ' My age is between 5 and 12'],
        "a2": ["Age-18", " My age is between 12 and 18"],
        # "a3": ['Age-25', ' My age is between 18 and 25'],
        # "a4": ['Age-35', ' My age is between 25 and 35'],
        "a5": ["Age-45", " My age is between 35 and 45"],
        # "a6": ['Age-55', ' My age is between 45 and 55'],
        # "a7": ['Age-65', ' My age is between 55 and 65'],
        "a8": ["Age-80", " My age is between 65 and 80"],
        # "a9": ['Age-80+', ' My age is over 80']
    },
    "race": {
        "r10": ["Race Ethnicity-White", "I am White"],
        "r11": ["Race Ethnicity-Black", "I am Black"],
        # "r12": ['Race Ethnicity-Alaskan', 'I am Native American or Native Alaskan'],
        "r13": ["Race Ethnicity-Asian", "I am Asian"],
        # "r14": ['Race Ethnicity-Islander', 'I am Native Hawaiian or Pacific Islander'],
        # "r15": ['Race Ethnicity-Mixed-Race','I am not White, Black, Asian, Native American, Native Alaskan, Native Hawaiian, Pacific Islander, nor Mixed-Race'],
        # "r16": ['Race Ethnicity-plural-race', 'I am more than one race'],
    },
    "income": {
        # "i17": ['Income-under15k','My annual family income is not determined because I live in a group home and am under 15'],
        "i18": ["Income-20K", "My annual family income is less than 20K dollars"],
        # "i19": ['Income-40K', 'My annual family income is between 20K and 40K dollars'],
        # "i20": ['Income-60K', 'My annual family income is between 40K and 60K dollars'],
        # "i21": ['Income-100K', 'My annual family income is between 60K and 100K dollars'],
        "i22": [
            "Income-150K",
            "My annual family income is between 100K and 150K dollars",
        ],
        # "i23": ['Income-250K', 'My annual family income is between 150K and 250K dollars'],
        "i24": [
            "Income-500K",
            "My annual family income is between 250K and 500K dollars",
        ],
        # "i25": ['Income-500K', 'My annual family income is more than 500K dollars']
    },
    "gender": {
        "g26": ["Gender-Female", "I identify as a Female"],
        "g27": ["Gender-Male", "I identify as a Male"],
    },
    "occupation": {
        "o28": ["Occupation-Marketing", "I work in Marketing"],
        "o29": ["Occupation-Design", "I work in Product Design"],
        "o30": ["Occupation-Banking", "I work as an Investment Banker"],
        "o31": ["Occupation-Product", "I work as a Product Executive"],
    },
    "education": {
        "e32": [
            "High-School",
            "My highest educational attainment was a high school diploma",
        ],
        "e33": ["Some-College", "My highest educational attainment was some college"],
        "e34": [
            "Associate-Degree",
            "My highest educational attainment was an associate degree",
        ],
        "e35": [
            "Bachelor-degree",
            "My highest educational attainment was a bachelor's degree",
        ],
        "e36": [
            "Masters-degree",
            "My highest educational attainment was a master's degree",
        ],
        "e37": [
            "Graduate-degree",
            "My highest educational attainment was a PhD or equivalent",
        ],
    },
    "2016_vote": {
        "v38": ["Trump", "I voted for Trump in the 2016 presidential election"],
        "v39": ["Clinton", "I voted for Clinton in the 2016 presidential election"],
        "v40": [
            "Johnson",
            "I voted for a third-party candidate in the 2016 presidential election",
        ],
        "v41": ["No-vote", "I did not vote in the 2016 presidential election"],
    },
    "brexit_vote": {
        "b42": ["Brexit-for", "I voted in favor of Brexit"],
        "b43": ["Brexit-against", "I voted against Brexit"],
        "b44": ["Brexit-no-vote", "I did not vote in the Brexit election"],
    },
    "political_ideology": {
        "p45": ["Left", "My political views skew left"],
        "p46": ["Center", "My political views are moderate"],
        "p47": ["Right", "My political views skew right"],
    },
    "ethnocentrism": {
        "e48": [
            "Ethnocentrism-High",
            "I prefer people of a similar race and culture to me",
        ],
        "e49": [
            "Ethnocentrism-Low",
            "I am open to people of all races, ethnicities, and cultures",
        ],
    },
    "work_sector": {
        "w50": ["Public-Sector", "I am a public sector employee"],
        "w51": ["Private-Sector", "I am a private sector employee"],
        "w52": [
            "Public-Student",
            "I am a student preparing for a job in the public sector",
        ],
    },
    "health": {
        "h53": ["Health-Good", "I am in good health"],
        "h54": ["Health-Chronic", "I have a chronic health condition"],
        "h55": ["Health-Severe", "I have a life-threatening health condition"],
    },
    "vaccination": {
        "v56": ["Vaccine-Never", "I have never been vaccinated against COVID-19"],
        "v57": [
            "Vaccine-Base",
            "I received the COVID vaccination but have never received a booster shot",
        ],
        "v58": [
            "Vaccine-Booster",
            "I have received the COVID vaccination as well as one booster shot",
        ],
        "v59": [
            "Vaccine-Multiple_Boosters",
            (
                "I have received the COVID vaccination as well as two or more booster"
                " shots"
            ),
        ],
    },
    "have_children": {
        "c60": ["Children-None", "I do not have children"],
        "c61": [
            "Children-Young_Girl",
            "I have at least one daughter under the age of 3",
        ],
        "c62": ["Children-Young_Boy", "I have at least one son under the age of 3"],
        "c63": ["Children-Both_Young", "I have a daughter and son under the age of 3"],
    },
    "nonprofit_affiliation": {
        "na64": ["Nonprofit-Volunteer", "I volunteer for a non-profit organization"],
        "na65": [
            "Nonprofit-Admin",
            "I work an administrative role for a non-profit organization",
        ],
        "na66": [
            "Nonprofit-Exec",
            "I work an executive role for a non-profit organization",
        ],
    },
    "religion": {
        "r67": ["Religion-Christian", "I am Christian"],
        "r68": ["Religion-Jewish", "I am Jewish"],
        "r69": ["Religion-Muslim", "I am Muslim"],
        "r70": ["Religion-Atheist", "I am an Atheist"],
    },
    "marital_status": {
        "ms71": ["Married-Never", "I have never been married"],
        "ms72": ["Married-Divorced", "I was previously married and am now divorced"],
        "ms73": ["Married-Widowed", "I was previously married and am now widowed"],
        "ms74": ["Married-Current", "I am currently married"],
    },
    "investor_experience": {
        "ie75": ["Investor-Never", "I have never invested in shares"],
        "ie76": [
            "Investor-Sometimes",
            "I follow the stock market but do not invest much",
        ],
        "ie77": [
            "Investor-Frequent",
            "I currently have substantial investments in shares",
        ],
    },
    "physical_activity": {
        "pa78": ["Physical-Sedentary", "I live a sedentary lifestyle"],
        "pa79": [
            "Physical-Active_Job",
            "I work a physically-demanding job but do not exercise much otherwise",
        ],
        "pa80": ["Physical-Exercise", "I work a sitting job, but exercise frequently"],
        "pa81": [
            "Physical-Job_and_Exercise",
            "I work a physically-demanding job and also exercise frequently",
        ],
    },
    "employer": {
        "e82": ["Employer-Corporate", "I am employed by a large corporation"],
        "e83": ["Employer-Small", "I am employed by a small business"],
        "e84": ["Employer-NGO", "I am employed by an NGO"],
        "e85": ["Employer-Government", "I am employed by the government"],
        "e86": ["Employer-University", "I am employed by a university"],
        "e87": ["Employer-Self", "I am self-employed"],
        "e88": ["Employer-None", "I am not employed"],
    },
    "country_of_residency": {
        "cr89": ["Country-US", "I live in the United States"],
        "cr90": ["Country-Gaza", "I live in Gaza"],
        "cr91": ["Country-Israel", "I live in Israel"],
        "cr92": ["Country-Qatar", "I live in Qatar"],
        "cr93": ["Country-China", "I live in China"],
        "cr94": ["Country-Denmark", "I live in Denmark"],
        "cr95": ["Country-Ethiopia", "I live in Ethiopia"],
    },
    "privacy_affinity": {
        "cr96": ["Privacy-None", "My work does not relate to privacy issues"],
        "cr97": ["Privacy-Lawyer", "I am a lawyer who works on privacy issues"],
        "cr98": ["Privacy-Advocate", "I am a member of a privacy advocacy group"],
    },
    "sexuality": {
        "s99": ["Sexuality-Straight", "I am heterosexual"],
        "s100": ["Sexuality-Bi", "I am bisexual"],
        "s101": ["Sexuality-Gay", "I am homosexual"],
    },
    "urban-rural": {
        "ur102": ["Urban_Rural-Big_City", "I live in a big city"],
        "ur103": ["Urban_Rural-Small_City", "I live in a small city"],
        "ur104": ["Urban_Rural-Village", "I live in a village"],
        "ur105": ["Urban_Rural-Rural", "I live in the countryside"],
    },
}
