#!/usr/bin/env python

from dataclasses import asdict, dataclass
from enum import Enum
from typing import Optional

from app.core.utils.databases import get_firebase_collection


class MeasurementType(Enum):
    NOMINAL = "nominal"
    ORDINAL = "ordinal"
    INTERVAL = "interval"
    RATIO = "ratio"


@dataclass
class Trait:
    set_type: Optional[str]  # Big Five, 8 Values
    type: str  # religion
    # Not an actual enum type because of Firestore serialization issues.
    measurement_type: str  # enum: nominal, ordinal, interval, ratio
    ordinal_rank: Optional[int]
    short_description: str  # Satanist
    long_description: str  # My religion is Satanism


personas_new = {
    1: (
        "2016_vote-Trump",
        "I voted for <PERSON> in the 2016 presidential election",
        "nominal",
        None,
    ),
    2: (
        "2016_vote-Clinton",
        "I voted for <PERSON> in the 2016 presidential election",
        "nominal",
        None,
    ),
    3: (
        "2016_vote-Johnson",
        "I voted for a third-party candidate in the 2016 presidential election",
        "nominal",
        None,
    ),
    4: (
        "2016_vote-No_vote",
        "I did not vote in the 2016 presidential election",
        "nominal",
        None,
    ),
    5: ("brexit_vote-Brexit_for", "I voted in favor of <PERSON><PERSON><PERSON><PERSON>", "nominal", None),
    6: ("brexit_vote-Brexit_against", "I voted against Brexit", "nominal", None),
    7: (
        "brexit_vote-Brexit_no_vote",
        "I did not vote in the Brexit election",
        "nominal",
        None,
    ),
    8: (
        "political_ideology-Left",
        (
            "Politically, I am affiliated with the left, or liberal, values. I rate"
            " personal issues strongly and economic issues weakly."
        ),
        "nominal",
        None,
    ),
    9: (
        "political_ideology-Center",
        (
            "Politically, I am affiliated with the centrist, or moderate, values. I"
            " rate Personal issues and economic issues equally."
        ),
        "nominal",
        None,
    ),
    10: (
        "political_ideology-Right",
        (
            "Politically, I am affiliated with the right, or conservative, values. I"
            " rate personal issues weakly and economic issues strongly."
        ),
        "nominal",
        None,
    ),
    11: (
        "ethnocentrism-High",
        "I prefer people of a similar race and culture to me",
        "nominal",
        None,
    ),
    12: (
        "ethnocentrism-Low",
        "I am open to people of all races, ethnicities, and cultures",
        "nominal",
        None,
    ),
    13: ("work_sector-Public_Sector", "I am a public sector employee", "nominal", None),
    14: (
        "work_sector-Private_Sector",
        "I am a private sector employee",
        "nominal",
        None,
    ),
    15: (
        "work_sector-Public_Student",
        "I am a student preparing for a job in the public sector",
        "nominal",
        None,
    ),
}

for _, v in personas_new.items():
    t, tv = v[0].split("-")
    set_type = "None"
    if t.startswith("8 Values"):
        set_type = '"8 Values"'
    elif t.startswith("Big Five"):
        set_type = '"Big Five"'

    ordinal_rank = "None"
    if v[2] and v[2].strip() == "ordinal":
        ordinal_rank = str(v[3])

    print(
        f"""Trait(set_type={set_type}, type="{t.strip().lower()}", measurement_type="{v[2].strip()}", ordinal_rank={ordinal_rank}, short_description="{tv.strip()}", long_description="{v[1].strip()}"),"""
    )


traits_new = [
    Trait(
        set_type=None,
        type="2016_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Trump",
        long_description="I voted for Trump in the 2016 presidential election",
    ),
    Trait(
        set_type=None,
        type="2016_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Clinton",
        long_description="I voted for Clinton in the 2016 presidential election",
    ),
    Trait(
        set_type=None,
        type="2016_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Johnson",
        long_description=(
            "I voted for a third-party candidate in the 2016 presidential election"
        ),
    ),
    Trait(
        set_type=None,
        type="2016_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="No_vote",
        long_description="I did not vote in the 2016 presidential election",
    ),
    Trait(
        set_type=None,
        type="brexit_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Brexit_for",
        long_description="I voted in favor of Brexit",
    ),
    Trait(
        set_type=None,
        type="brexit_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Brexit_against",
        long_description="I voted against Brexit",
    ),
    Trait(
        set_type=None,
        type="brexit_vote",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Brexit_no_vote",
        long_description="I did not vote in the Brexit election",
    ),
    Trait(
        set_type=None,
        type="political_ideology",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Left",
        long_description=(
            "Politically, I am affiliated with the left, or liberal, values. I rate"
            " personal issues strongly and economic issues weakly."
        ),
    ),
    Trait(
        set_type=None,
        type="political_ideology",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Center",
        long_description=(
            "Politically, I am affiliated with the centrist, or moderate, values. I"
            " rate Personal issues and economic issues equally."
        ),
    ),
    Trait(
        set_type=None,
        type="political_ideology",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Right",
        long_description=(
            "Politically, I am affiliated with the right, or conservative, values. I"
            " rate personal issues weakly and economic issues strongly."
        ),
    ),
    Trait(
        set_type=None,
        type="ethnocentrism",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="High",
        long_description="I prefer people of a similar race and culture to me",
    ),
    Trait(
        set_type=None,
        type="ethnocentrism",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Low",
        long_description="I am open to people of all races, ethnicities, and cultures",
    ),
    Trait(
        set_type=None,
        type="work_sector",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Public_Sector",
        long_description="I am a public sector employee",
    ),
    Trait(
        set_type=None,
        type="work_sector",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Private_Sector",
        long_description="I am a private sector employee",
    ),
    Trait(
        set_type=None,
        type="work_sector",
        measurement_type="nominal",
        ordinal_rank=None,
        short_description="Public_Student",
        long_description="I am a student preparing for a job in the public sector",
    ),
]

coll = get_firebase_collection("traits")

for trait in traits_new:
    td = asdict(trait)
    coll.add(asdict(trait))
