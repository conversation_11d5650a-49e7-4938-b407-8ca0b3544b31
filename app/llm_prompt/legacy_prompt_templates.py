import inspect

# Version 0.1
attributes_template_v1 = inspect.cleandoc("""
        {{idea}}. In dashed bullet points, {{attribute_count}} unique considerations {action}, without explanation, are:
        -""")

# Version 0.1
levels_price_template_v1 = inspect.cleandoc("""
                           I would like to understand '{idea}?'. List {num_levels} personal and unique answers to 
                           '{attribute}?'. Do not repeat phrases.
                           The desired format for each answer is a vignette under 160 characters, separated with dashed 
                           bullet points.
                           -""")

# Version 0.1
levels_template_v1 = inspect.cleandoc("""
                {idea}.
                One consideration is {attribute}.
                In dashed bullet points, {num_levels} unique examples of {attribute}, without explanation, are:
                -""")
