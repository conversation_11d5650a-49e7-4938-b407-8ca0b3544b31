import asyncio
import os
from contextlib import asynccontextmanager
from functools import cached_property
from typing import Type, Union

import backoff
from langchain.prompts import PromptTemplate
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_cohere import Chat<PERSON><PERSON><PERSON>
from langchain_community.chat_models import ChatPerplexity
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
from langchain_core.runnables import RunnableWithFallbacks
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_google_vertexai import VertexAI
from langchain_google_vertexai.model_garden import ChatAnthropicVertex
from langchain_openai import AzureChatOpenAI, ChatOpenAI, OpenAI
from openai import APIConnectionError, APIError, APITimeoutError, RateLimitError
from pydantic import BaseModel
from sentry_sdk import capture_exception
from yaspin import yaspin

from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel
from app.exceptions import CountedRateLimitError
from app.llm_prompt.llm_settings import get_model_settings

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.getenv(
    "SUBCONSCIOUSAI_DEV_GCP_SERVICE_ACCOUNT_KEY_VERTEX"
)
logger = app_logger.get_logger(__name__)


class APIKeyPool:
    """
    A class to manage a pool of API keys and rotate through them in a round-robin fashion.

    This class helps in distributing the load across multiple API keys, which can help in
    avoiding rate limits and ensuring efficient use of available keys.
    """

    def __init__(self, keys: list[str]):
        """
        Initialize the APIKeyPool with a list of API keys.

        Args:
            keys (List[str]): A list of API key strings.
        """
        self.keys = keys
        self.current_index = 0

    def get_next_key(self) -> str:
        """
        Get the next API key in the rotation.

        This method implements a round-robin selection of API keys.

        Returns:
            str: The next API key to be used.
        """
        key = self.keys[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.keys)
        return key


pplxty_api_key_pool = APIKeyPool([
    os.getenv("PERPLEXITY_API_KEY"),
    os.getenv("PERPLEXITY_API_KEY2"),
    # Add more API keys as needed
])


class LCELPromptExecutor(BaseModel):
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH
    llm_settings: dict = None
    max_tokens: int | None = None

    def __init__(self, temperature: float = None, **data):
        super().__init__(**data)
        self.llm_settings = self.get_model_settings(self.llm_model)

        if temperature is not None:
            self.llm_settings["temperature"] = temperature
        if self.max_tokens:
            self.llm_settings["max_tokens"] = self.max_tokens

    @staticmethod
    def get_model_settings(llm_model: LLMModel) -> dict:
        settings = get_model_settings(llm_model).get_parameters()
        return settings

    @cached_property
    def model(self) -> RunnableWithFallbacks:
        params = self.llm_settings
        main_model: BaseChatModel
        backup_model: BaseChatModel
        backup_model2: BaseChatModel

        gemini_settings = get_model_settings(LLMModel.GCP_GEMINIFLASH)
        gcp_sonnet_settings = get_model_settings(LLMModel.GCP_SONNET)
        openai_settings = get_model_settings(LLMModel.GPT4)

        # Azure Models
        if self.llm_model == LLMModel.AZURE_GPTO3_MINI:
            main_model = AzureChatOpenAI(**params)
            backup_params = params.copy()
            backup_params["model_name"] = "gpt-4o-mini"
            backup_params["model"] = "gpt-4o-mini"
            backup_model = AzureChatOpenAI(**backup_params)
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        elif self.llm_model == LLMModel.AZURE_GPT4O_MINI:
            main_model = AzureChatOpenAI(**params)
            backup_params = params.copy()
            backup_params["model_name"] = "o3-mini"
            backup_params["model"] = "o3-mini"
            backup_model = AzureChatOpenAI(**backup_params)
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        elif self.llm_model == LLMModel.AZURE_GPT4:
            main_model = AzureChatOpenAI(**params)
            backup_params = params.copy()
            backup_params["model_name"] = "o3-mini"
            backup_params["model"] = "o3-mini"
            backup_model = AzureChatOpenAI(**backup_params)
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        # GCP Models
        elif self.llm_model == LLMModel.GCP_GEMINI:
            main_model = VertexAI(**params)
            backup_model = ChatOpenAI(**openai_settings.get_parameters())
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        elif self.llm_model == LLMModel.GCP_GEMINIFLASH:
            main_model = VertexAI(**params)
            backup_model = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
            backup_model2 = ChatOpenAI(**openai_settings.get_parameters())
        elif self.llm_model == LLMModel.GCP_SONNET:
            main_model = ChatAnthropicVertex(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatOpenAI(**openai_settings.get_parameters())
        elif self.llm_model in [
            LLMModel.GCP_HAIKU,
            # LLMModel.GCP_OPUS,
        ]:
            main_model = ChatAnthropicVertex(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatOpenAI(**openai_settings.get_parameters())
        # OpenAI Models
        elif self.llm_model in [
            LLMModel.GPT3,
            LLMModel.GPT4,
            LLMModel.GPTo1,
            LLMModel.GPTo3,
        ]:
            main_model = ChatOpenAI(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        elif self.llm_model == LLMModel.GPT3_INSTRUCT:
            main_model = OpenAI(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        # Cohere Models
        elif self.llm_model == LLMModel.COHERE:
            main_model = ChatCohere(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        # Anthropic Models
        elif self.llm_model in [
            LLMModel.OPUS,
            LLMModel.SONNET,
            LLMModel.HAIKU,
        ]:
            main_model = ChatAnthropic(**params)
            backup_model = VertexAI(**gemini_settings.get_parameters())
            backup_model2 = ChatOpenAI(**openai_settings.get_parameters())
        # Google Models
        elif self.llm_model == LLMModel.GEMINI:
            main_model = ChatGoogleGenerativeAI(**params)
            backup_model = ChatOpenAI(**openai_settings.get_parameters())
            backup_model2 = ChatAnthropicVertex(**gcp_sonnet_settings.get_parameters())
        else:
            raise ValueError(f"Unsupported model type: {self.llm_model}")

        return main_model.with_fallbacks([backup_model, backup_model2])

    @staticmethod
    def get_parser(output_object: Type[BaseModel] | None):
        parser = (
            JsonOutputParser(pydantic_object=output_object)
            if output_object
            else StrOutputParser()
        )
        return parser

    @cached_property
    def model_identifiers(self) -> tuple[str, str, str]:
        main_model = self.model.runnable
        fallback_model1 = self.model.fallbacks[0]
        fallback_model2 = self.model.fallbacks[1]

        model_identifier = (
            getattr(main_model, "model_name", None)
            or getattr(main_model, "model", None)
            or str(self.llm_model)
        )
        fallback_identifier = (
            getattr(fallback_model1, "model_name", None)
            or getattr(fallback_model1, "model", None)
            or "Fallback"
        )
        fallback_identifier2 = (
            getattr(fallback_model2, "model_name", None)
            or getattr(fallback_model2, "model", None)
            or "Fallback2"
        )
        return model_identifier, fallback_identifier, fallback_identifier2

    @backoff.on_exception(
        backoff.expo,
        exception=(RateLimitError, APIError, APIConnectionError, APITimeoutError),
        max_tries=5,
        on_backoff=lambda details: logger.warning(
            f"LCELPromptExecutor.execute: Retry attempt {details['tries']} after"
            f" {details['wait']} seconds due to {details['exception']}"
        ),
    )
    def execute(
        self,
        prompt: Union[PromptTemplate, list[PromptTemplate]],
        args: dict,
        output_object: Type[BaseModel] | None,
        description: str | None = "Executing prompt",
    ):
        try:
            parser = self.get_parser(output_object)
            model_identifier, fallback_model, fallback_model2 = self.model_identifiers

            chain = prompt | self.model | parser if args else self.model | parser
            with yaspin(
                text=(
                    f"{description}. base"
                    f" model:{model_identifier},fm1:{fallback_model},fm2:{fallback_model2}"
                ),
                timer=True,
            ) as sp:

                result = chain.invoke(args if args else prompt)

                sp.ok()
                logger.success(f"LCELPromptExecutor.execute: {result}")
            return result
        except RateLimitError as err:
            capture_exception()
            logger.error(f"Rate limit error: {err}")
            raise CountedRateLimitError()
        except Exception as e:
            capture_exception()
            logger.error(f"Exception in LCELPromptExecutor.execute: {e}")
            raise

    @backoff.on_exception(
        backoff.expo,
        exception=(RateLimitError, APIError, APIConnectionError, APITimeoutError),
        max_tries=5,
        on_backoff=lambda details: logger.warning(
            f"LCELPromptExecutor.batch_execute: Retry attempt {details['tries']} after"
            f" {details['wait']} seconds due to {details['exception']}"
        ),
    )
    def batch_execute(
        self,
        prompts: list[PromptTemplate],
        args: dict,
        output_object: Type[BaseModel] | None,
        description: str | None = "Executing batch prompts",
    ) -> dict:
        try:
            parser = self.get_parser(output_object)
            model_identifier, fallback_model, fallback_model2 = self.model_identifiers

            chain = prompts | self.model | parser if args else self.model | parser
            with yaspin(
                text=(
                    f"{description}.base"
                    f" model:{model_identifier},fm1:{fallback_model},fm2:{fallback_model2}"
                ),
                timer=True,
            ) as sp:
                results = chain.batch(args if args else prompts)

                sp.ok()
                logger.success(f"LCELPromptExecutor.batch_execute: {results}")
            return results
        except RateLimitError as err:
            capture_exception()
            logger.error(f"Rate limit error: {err}")
            raise CountedRateLimitError()
        except Exception as e:
            capture_exception()
            logger.error(f"Exception in LCELPromptExecutor.batch_execute: {e}")
            raise


class PerplexityPromptExecutor(BaseModel):
    """
    A class to execute prompts using the Perplexity API.

    This executor handles the creation of chat schemas, parsing of responses,
    and execution of prompts while managing API keys through the APIKeyPool.
    """

    llm_model: str = LLMModel.PPLXTY_CHAT_MODEL
    temperature: float = 0.0
    api_key: str = os.getenv("PERPLEXITY_API_KEY")

    @staticmethod
    def get_parser(output_object: Type[BaseModel] | None):
        """
        Get the appropriate parser based on the output object type.

        Args:
            output_object (Type[BaseModel] | None): The expected output object type.

        Returns:
            Union[JsonOutputParser, StrOutputParser]: The selected parser.
        """
        parser = (
            JsonOutputParser(pydantic_object=output_object)
            if output_object
            else StrOutputParser()
        )
        return parser

    @backoff.on_exception(
        backoff.expo,
        exception=(RateLimitError, APIError, APIConnectionError, APITimeoutError),
        max_tries=5,
        on_backoff=lambda details: logger.warning(
            f"PerplexityPromptExecutor.execute: Retry attempt {details['tries']} after"
            f" {details['wait']} seconds due to {details['exception']}"
        ),
    )
    async def execute(
        self,
        prompt: PromptTemplate,
        args: dict,
        output_object: Type[BaseModel] | None,
        description: str | None = "Executing prompt",
    ) -> dict:
        """
        Execute a prompt using the Perplexity API.

        This method sets up the chat model, executes the prompt, and handles any errors that occur.

        Args:
            prompt (PromptTemplate): The prompt template to be executed.
            args (dict): Arguments to be passed to the prompt.
            output_object (Type[BaseModel] | None): The expected output object type.
            description (str | None): A description of the execution for logging purposes.

        Returns:
            dict: The response from the API.

        Raises:
            CountedRateLimitError: If a rate limit error occurs.
            Exception: For any other errors during execution.
        """
        try:
            chat_model = ChatPerplexity(
                model=self.llm_model,
                api_key=self.api_key,
                temperature=self.temperature,
            )

            parser = self.get_parser(output_object)

            chain = prompt | chat_model | parser

            key_name = f"KEY {pplxty_api_key_pool.keys.index(self.api_key) + 1}"

            with yaspin(
                text=(
                    f"{description} using model from Perplexity"
                    f" `{self.llm_model.value}` used API Key: {key_name}"
                ),
                timer=True,
            ) as sp:
                response = await asyncio.to_thread(
                    chain.invoke,
                    args,
                    config={
                        "metadata": {
                            "ls_provider": "perplexity",
                            "ls_model_name": self.llm_model,
                        }
                    },
                )
                sp.ok()
                logger.success(f"PerplexityPromptExecutor.execute: {response}")

            return response
        except RateLimitError as err:
            capture_exception()
            logger.error(f"Perplexity Rate limit error: {err}")
            raise CountedRateLimitError()
        except Exception as e:
            capture_exception()
            logger.error(f"Error in PerplexityPromptExecutor.execute: {str(e)}")
            raise

    class Config:
        protected_namespaces = ("settings_",)
        arbitrary_types_allowed = True


@asynccontextmanager
async def get_perplexity_executor():
    """
    Asynchronous context manager to get a PerplexityPromptExecutor with a fresh API key.

    This context manager ensures that each execution uses the next API key in the rotation,
    promoting balanced usage of all available API keys.

    Yields:
        PerplexityPromptExecutor: An executor instance with a newly assigned API key.
    """
    # api_key = pplxty_api_key_pool.get_next_key()
    executor = PerplexityPromptExecutor(api_key=os.getenv("PERPLEXITY_API_KEY"))
    try:
        yield executor
    finally:
        pass  # No need to update usage count as we're using round-robin
