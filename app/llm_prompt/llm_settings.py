import json
import os
from pathlib import Path
from typing import Optional

from google.oauth2 import service_account
from pydantic_settings import BaseSettings

from app.core.global_config import settings
from app.core.utils.type_enums import LLMModel


class BaseLLMSettings(BaseSettings):
    """Common settings for all LLMs"""

    model_name: Optional[str] = None
    model: Optional[str] = None
    temperature: float = 0.0
    api_key: Optional[str] = None
    top_p: Optional[float] = None
    max_tokens: int = 4096
    max_retries: Optional[int] = None
    model_kwargs: Optional[dict] = None
    timeout: Optional[int] = None

    def get_parameters(self) -> dict:
        """Returns a dictionary containing the parameters necessary to define a new LLM instance."""
        params = {
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }
        if self.api_key:
            params["api_key"] = self.api_key
        if self.model_name:
            params["model_name"] = self.model_name
        if self.model:
            params["model"] = self.model
        if self.top_p:
            params["top_p"] = self.top_p
        if self.max_retries:
            params["max_retries"] = self.max_retries
        if self.timeout:
            params["timeout"] = self.timeout
        if self.model_kwargs:
            params["model_kwargs"] = self.model_kwargs
        return params

    class Config:
        protected_namespaces = ("settings_",)


class GPT3_INSTRUCTSettings(BaseLLMSettings):
    model: str = "gpt-3.5-turbo-instruct"
    api_key: str = os.getenv("OPENAI_API_KEY")
    top_p: float = 1.0
    max_retries: int = 3
    timeout: int = 10

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["api_key"] = self.api_key
        params["model_name"] = self.model_name
        params["top_p"] = self.top_p
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        return params


class GPT3Settings(BaseLLMSettings):
    model: str = "gpt-3.5-turbo-0125"
    api_key: str = os.getenv("OPENAI_API_KEY")
    max_retries: int = 3
    timeout: int = 10
    top_p: float = 1.0

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["api_key"] = self.api_key
        params["model_name"] = self.model_name
        params["top_p"] = self.top_p
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        return params


class GPT4Settings(BaseLLMSettings):
    model: str = "gpt-4o"
    api_key: str = os.getenv("OPENAI_API_KEY")
    max_retries: int = 3
    timeout: int = 30
    top_p: float = 1.0

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["api_key"] = self.api_key
        params["model_name"] = self.model_name
        params["top_p"] = self.top_p
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        return params


class GPTo3Settings(BaseLLMSettings):
    model: str = "o3-mini-2025-01-31"
    api_key: str = os.getenv("OPENAI_API_KEY")
    max_retries: int = 3
    timeout: int = 60
    top_p: float = 1.0

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        # params["api_key"] = self.api_key
        # params["model_name"] = self.model_name
        params.pop("top_p")
        # params["max_retries"] = self.max_retries
        # params["timeout"] = self.timeout
        params.pop("temperature", None)
        params.pop("max_tokens")
        return params


class GPTo1Settings(BaseLLMSettings):
    model: str = "o1-mini-2024-09-12"
    api_key: str = os.getenv("OPENAI_API_KEY")
    max_retries: int = 3
    timeout: int = 60
    top_p: float = 1.0

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        # params["api_key"] = self.api_key
        # params["model_name"] = self.model_name
        params.pop("top_p")
        # params["max_retries"] = self.max_retries
        # params["timeout"] = self.timeout
        params.pop("temperature", None)
        params.pop("max_tokens")
        return params


class CohereSettings(BaseLLMSettings):
    model: str = "command-r-plus-08-2024"
    api_key: str = os.getenv("COHERE_API_KEY")
    max_retries: int = 3
    timeout: int = 10

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["api_key"] = self.api_key
        params["model"] = self.model
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        return params


class OpusSettings(BaseLLMSettings):
    model_name: str = "claude-3-opus-********"
    top_p: float = 1.0
    api_key: str = os.getenv("ANTHROPIC_PROD")
    max_retries: int = 3
    timeout: int = 20

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["model_name"] = self.model_name
        params["api_key"] = self.api_key
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        params["top_p"] = self.top_p
        return params


class SonnetSettings(BaseLLMSettings):
    model_name: str = "claude-3-sonnet-********"
    top_p: float = 1.0
    api_key: str = os.getenv("ANTHROPIC_PROD")
    max_retries: int = 3
    timeout: int = 20

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["model_name"] = self.model_name
        params["api_key"] = self.api_key
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        params["top_p"] = self.top_p
        return params


class HaikuSettings(BaseLLMSettings):
    model_name: str = "claude-3-haiku-20240307"
    top_p: float = 1.0
    api_key: str = os.getenv("ANTHROPIC_PROD")
    max_retries: int = 3
    timeout: int = 20

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["model_name"] = self.model_name
        params["api_key"] = self.api_key
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        params["top_p"] = self.top_p
        return params


class AzureOpenAI4oSettings(BaseLLMSettings):
    model_name: str = "gpt-4ov2"
    api_version: str = "2024-12-01-preview"
    api_key: str = os.getenv("AZURE_OPENAI_API_KEY1")
    azure_endpoint: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    timeout: int = 60
    max_retries: int = 3

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "azure_endpoint": self.azure_endpoint,
            "api_version": self.api_version,
            "api_key": self.api_key,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
        })
        return params


class AzureOpenAI35Settings(BaseLLMSettings):
    model_name: str = "gpt_35"
    api_version: str = "2024-12-01-preview"
    api_key: str = os.getenv("AZURE_OPENAI_API_KEY1")
    azure_endpoint: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    timeout: int = 60
    max_retries: int = 3

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "azure_endpoint": self.azure_endpoint,
            "api_version": self.api_version,
            "api_key": self.api_key,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
        })
        return params


class AzureOpenAI4oMiniSettings(BaseLLMSettings):
    model_name: str = "gpt-4o-mini"
    api_version: str = "2024-12-01-preview"
    api_key: str = os.getenv("AZURE_OPENAI_API_KEY1")
    azure_endpoint: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    timeout: int = 60
    max_retries: int = 3

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "azure_endpoint": self.azure_endpoint,
            "api_version": self.api_version,
            "api_key": self.api_key,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
        })
        return params


class AzureOpenAIo3MiniSettings(BaseLLMSettings):
    model: str = "o3-mini"
    model_name: str = "o3-mini"
    api_version: str = "2024-12-01-preview"
    api_key: str = os.getenv("AZURE_OPENAI_API_KEY1")
    azure_endpoint: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    timeout: int = 60
    max_retries: int = 3

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.pop("max_tokens")
        params.pop("temperature")
        params.update({
            "azure_endpoint": self.azure_endpoint,
            "api_version": self.api_version,
            "api_key": self.api_key,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
        })
        return params


class AzureOpenAICompletionsSettings(BaseLLMSettings):
    """Settings for Azure OpenAI Completions API (non-chat models)"""

    model_name: str = "gpt-4o-mini"  # The model to use
    deployment_id: str = "gpt-4o-mini"  # The deployment ID in Azure
    api_version: str = (
        "2024-12-01-preview"  # Using preview version that supports logprobs
    )
    api_key: str = os.getenv("AZURE_OPENAI_API_KEY1")
    azure_endpoint: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    timeout: int = 60
    max_retries: int = 3
    logprobs: int = 5  # Number of top logprobs to return
    chat_completions: bool = True  # Use chat completions API instead of completions API

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "azure_endpoint": self.azure_endpoint,
            "deployment_id": self.deployment_id,
            "api_version": self.api_version,
            "api_key": self.api_key,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "logprobs": self.logprobs,
            "chat_completions": self.chat_completions,
        })
        return params


class GoogleGenAISettings(BaseLLMSettings):
    model: str = "gemini-1.5-pro"
    google_api_key: str = os.getenv("GEMINI_API_KEY")
    max_retries: int = 3
    timeout: int = 30

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["google_api_key"] = self.google_api_key
        params["max_retries"] = self.max_retries
        params["timeout"] = self.timeout
        return params


class BaseVertexAISettings(BaseLLMSettings):
    """Base settings for Vertex AI models"""

    credentials_path: Optional[str] = None

    def _setup_credentials(self) -> str:
        """Create temporary credentials file and return its path"""
        credentials_json = os.getenv(
            "SUBCONSCIOUSAI_DEV_GCP_SERVICE_ACCOUNT_KEY_VERTEX"
        )
        if not credentials_json:
            raise ValueError("GCP service account credentials not found in environment")

        try:
            credentials = json.loads(credentials_json)
            temp_dir = Path(settings.EXPERIMENT_DATA_FOLDER_NAME) / "tmp"
            creds_file = temp_dir / "gcp_credentials.json"

            # Create the directory if it doesn't exist
            temp_dir.mkdir(parents=True, exist_ok=True)

            with open(creds_file, "w") as f:
                json.dump(credentials, f)

            return str(creds_file)

        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format in GCP service account credentials")

    @property
    def project_id(self) -> str:
        """Extract project_id from service account credentials JSON"""
        credentials_json = os.getenv(
            "SUBCONSCIOUSAI_DEV_GCP_SERVICE_ACCOUNT_KEY_VERTEX"
        )
        if not credentials_json:
            raise ValueError("GCP service account credentials not found in environment")

        try:
            credentials = json.loads(credentials_json)
            return credentials.get("project_id")
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format in GCP service account credentials")
        except KeyError:
            raise ValueError("project_id not found in GCP service account credentials")

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params["project"] = self.project_id

        credentials_path = self._setup_credentials()
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = credentials_path

        return params

    def __del__(self):
        """Cleanup temporary credentials file"""
        if self.credentials_path and os.path.exists(self.credentials_path):
            try:
                os.remove(self.credentials_path)
            except Exception:
                pass


class VertexAIGeminiSettings(BaseVertexAISettings):
    """Settings for Gemini models on Vertex AI"""

    model_name: str = "gemini-1.5-flash-002"
    location: str = "us-central1"

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "model_name": self.model_name,
            "location": self.location,
        })
        return params


class VertexAIGemini2FlashSettings(BaseVertexAISettings):
    """Settings for Gemini models on Vertex AI"""

    model_name: str = "gemini-2.0-flash-001"
    location: str = "us-central1"

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "model_name": self.model_name,
            "location": self.location,
        })
        return params


class VertexAIClaudeSettings(BaseVertexAISettings):
    """Base settings for Claude models on Vertex AI"""

    location: str = "us-east5"

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "model_name": self.model_name,
            "location": self.location,
        })
        return params


class VertexAIHaikuSettings(VertexAIClaudeSettings):
    model_name: str = "claude-3-haiku@20240307"


class VertexAIOpusSettings(VertexAIClaudeSettings):
    model_name: str = "claude-3-opus@********"


class VertexAISonnetSettings(VertexAIClaudeSettings):
    model_name: str = "claude-3-5-sonnet@********"


class VertexAILlamaSettings(BaseVertexAISettings):
    location: str = "us-central1"
    model_name: str = "meta/llama3-405b-instruct-maas"

    def get_parameters(self) -> dict:
        params = super().get_parameters()

        credentials_json = os.getenv(
            "SUBCONSCIOUSAI_DEV_GCP_SERVICE_ACCOUNT_KEY_VERTEX"
        )
        if not credentials_json:
            raise ValueError("GCP service account credentials not found in environment")

        try:
            credentials_info = json.loads(credentials_json)
            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=["https://www.googleapis.com/auth/cloud-platform"],
            )
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format in GCP service account credentials")
        except Exception as e:
            raise ValueError(f"Error creating credentials: {str(e)}")

        params.update({
            "location": self.location,
            "model_name": self.model_name,
            "credentials": credentials,
        })
        return params


def get_model_settings(llm_model: LLMModel):
    """
    Returns an instance of the settings class based on the provided model type.
    """

    # Azure Models
    if llm_model == LLMModel.AZURE_GPT4:
        return AzureOpenAI4oSettings()
    if llm_model == LLMModel.AZURE_GPT35:
        return AzureOpenAI35Settings()
    if llm_model == LLMModel.AZURE_GPT4O_MINI:
        return AzureOpenAI4oMiniSettings()
    if llm_model == LLMModel.AZURE_GPTO3_MINI:
        return AzureOpenAIo3MiniSettings()
    if llm_model == LLMModel.AZURE_COMPLETIONS:
        return AzureOpenAICompletionsSettings()
    # Cohere Models
    elif llm_model == LLMModel.COHERE:
        return CohereSettings()
    # GCP Models
    elif llm_model == LLMModel.GCP_GEMINI:
        return VertexAIGeminiSettings()
    elif llm_model == LLMModel.GCP_GEMINIFLASH:
        return VertexAIGemini2FlashSettings()
    elif llm_model == LLMModel.GCP_HAIKU:
        return VertexAIHaikuSettings()
    # elif llm_model == LLMModel.GCP_OPUS:
    #     return VertexAIOpusSettings()
    elif llm_model == LLMModel.GCP_SONNET:
        return VertexAISonnetSettings()
    # elif llm_model == LLMModel.GCP_LLAMA:
    #     return VertexAILlamaSettings()

    # OpenAI Models
    elif llm_model == LLMModel.GPT3_INSTRUCT:
        return GPT3_INSTRUCTSettings()
    elif llm_model == LLMModel.GPT3:
        return GPT3Settings()
    elif llm_model == LLMModel.GPT4:
        return GPT4Settings()
    elif llm_model == LLMModel.GPTo1:
        return GPTo1Settings()
    elif llm_model == LLMModel.GPTo3:
        return GPTo3Settings()

    # Anthropic Models
    elif llm_model == LLMModel.OPUS:
        return OpusSettings()
    elif llm_model == LLMModel.SONNET:
        return SonnetSettings()
    elif llm_model == LLMModel.HAIKU:
        return HaikuSettings()

    # Google Models
    elif llm_model == LLMModel.GEMINI:
        return GoogleGenAISettings()
    else:
        raise ValueError("Unsupported model type")
