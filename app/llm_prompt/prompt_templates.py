import inspect

# NOTE: Awkward newline breaks with multiline quotes are intentional and necessary to receive
# properly-formatted response

# Version 0.2
ATTRIBUTES_TEMPLATE = inspect.cleandoc(
    "Prompt: '{{why_prompt}}?'List {{attribute_count}} personal and unique questions"
    " exploring"
    " features of this Prompt. Suggest non-repetitive questions, whilst refraining from"
    " asking any questions related to price or brand. Instead of asking for price and"
    " brand, ask descriptive questions about attributes and features that influence"
    " decision-making about the Prompt. The question should not include examples. The"
    " desired format"
    """ for each answer is a vignette under {{max_length}} characters, separated with dashed bullet points.
    -"""
)

LLM_ATTRIBUTES_TEMPLATE = inspect.cleandoc("""
    I am trying to understand {prompt_type}.
    {why_prompt} In dashed bullet points, {attribute_count} unique attributes to consider when valuing this {prompt_type} are:
    The attributes and its levels (units) should correspond to the the country: {country}.
    Example: If the country is USA, price levels should be in $ or USD, Range should be in miles.
    {format_instructions}""")

LLM_LEVELS_TEMPLATE = inspect.cleandoc("""
    I am trying to understand {why_prompt}.
    These are the factors relevant to the current context: {existing_attributes}.
    We also think that the following attribute can also be relevant to the current context: {attribute}.
    Considering the list of existing attributes, we need to add {level_count} unique levels to the attribute {attribute}.
    If the label of the attribute: {attribute}, does not match the context, please provide a new attribute label.
    Ensure that the new label is independent/not correlated with the existing attributes.
    Generate {level_count} unique levels of {attribute} for the country {country}.
    For example, if the attribute is "Battery Range", and country is "United States" the levels could be
    "Range of 150 miles per charge", - Reason: Here the country is United States so the units are miles
    "Range of 250 miles per charge",
    "Range of 350 miles per charge"...

    Important Requirements:
    1. The attributes and its levels (units) should correspond to the the {country}
    2. The response must always be in English regardless of country.
    Example1: If the country is USA, price levels should be in $ or USD, Range should be in miles.
    Example2: If the country is India, price levels should be in INR, Range should be in km.
    {format_instructions}""")

DEPENDENT_VARIABLE_TEMPLATE = inspect.cleandoc(
    "Given a Prompt from a Research Investigator, please generate a Preference"
    " Instruction that represents how to make a decision between three options, which"
    " gives maximal insight into the Research Investigator's Prompt. Prompt: I would"
    " like to understand European Carbon Tax policy in the early 2000's. Preference"
    " Instruction: Based on the descriptions of the multinational carbon tax policies,"
    " select which option you prefer. Prompt: I would like to understand Italian"
    " citizenship decisions in the 1970's. Preference Instruction: Please read the"
    " descriptions of the individuals applying for British citizenship carefully. Then,"
    " please indicate which of the two individuals you would rather grant citizenship"
    " to. Prompt: I would like to understand vaccine distribution policy in Canada."
    " Preference Instruction: Please read the descriptions and select which of the"
    " individuals should have priority and receive the vaccine immediately. Prompt: I"
    " would like to understand early 19th century USA Immigration Policy. Preference"
    " Instruction: Please indicate which of the two immigrants you would personally"
    " prefer to see admitted to the United States. Prompt: I would like to understand"
    " factors influencing public office candidate choices in Germany pre-world war 2."
    " Preference Instruction: Please read the descriptions of the two candidates"
    " carefully. Then, please indicate which of the two candidates you would prefer to"
    " hire for public service. Prompt: I would like to understand attitudes towards"
    " people with previous ISIS involvement in Iraq. Preference Instruction: Please"
    " indicate which of the two individuals is more deserving of punishment. Prompt: I"
    " am trying to understand why people choose certain dentists. Preference"
    " Instruction: Please evaluate each dentist. Then, select which dentist you would"
    " prefer to visit. Prompt: I am trying to understand people's magazine preferences."
    " Preference Instruction: You will be presented with detailed proposals for new"
    " magazines. Please read each proposal carefully. Then, select which of the two"
    " magazines you would prefer to buy. Prompt: I am trying to understand public"
    " health insurance policies. Preference Instruction: You will be presented with"
    " policy plans to redesign the American healthcare system and the way it is funded."
    " Please read each policy plan descriptions carefully. Then, select which of the"
    " two policy plans you would prefer. Prompt: I am trying to understand why people"
    " invest in energy companies. Preference Instruction: Please read the description"
    " of both energy companies. Then, please select the company whose stock you are"
    " more likely to invest in. Prompt: I am trying to understand why people attend"
    " certain conferences. Preference Instruction: Please read the descriptions of both"
    " conferences carefully. Then, please indicate which of the two conferences you"
    " would prefer to attend. Prompt: I am trying to understand why people purchase"
    " food enhancers. Preference Instruction:Please read the descriptions of both"
    " nanofoods carefully. Then, please select which of the two nanofoods you would"
    " prefer to purchase. Prompt: I am trying to understand movie preferences."
    " Preference Instruction: Please read the descriptions of both movies. Then, please"
    " select which of the two movies you would prefer to watch. Prompt: I am trying to"
    " understand people's perceptions on bullying. Preference Instruction: Please read"
    " both situations carefully. Then, please select which of the two examples was a"
    " more severe example of the child being bullied. Prompt:I am trying to understand"
    " why people attend certain wine-tasting courses.Preference Instruction:Please read"
    " each programme description carefully. Then, please select which of the two"
    " programs you would prefer to attend. Prompt: I am trying to understand why people"
    " buy weight loss products. Preference Instruction:Please read the two treatment"
    " descriptions carefully. Then, please indicate which of the two treatment options"
    " you would be more likely to use. Prompt: I am trying to understand smartphone"
    " purchasing decisions. Preference Instruction:Please read the descriptions of the"
    " two smartphones described. Then, please select which smartphone you would prefer"
    " to buy. Prompt: I am trying to understand match decisions on dating apps."
    " Preference Instruction: Please read the profiles of both people carefully. Then,"
    " please select which of the two individuals you would prefer to date. Prompt: I am"
    " trying to understand why people choose certain master's programmes"
    " abroad.Preference Instruction: Please read the descriptions of both master's"
    " degree programmes. Then, select which of the two programmes you would prefer to"
    " attend. Prompt: I am trying to understand bottled water buying decisions."
    " Preference Instruction: Please read the descriptions of the two types of bottled"
    " water available. Then, please select which of the two bottled waters you would"
    " prefer to purchase. Prompt: I am trying to understand why people believe viral"
    " posts on Facebook. Preference Instruction: Please read the two scenarios that"
    " have been published on Facebook. Then, please select which of the two messages is"
    " more believable. Prompt: I am trying to understand why people choose to send"
    " their kids to certain daycares. Preference Instruction:Please read the"
    " descriptions of both daycare facilities. Then, please select which of the two"
    " facilities you would prefer to send your child to. Prompt: I am trying to design"
    " a new electric car for the American Markets. Preference Instruction:Please read"
    " the Product descriptions carefully. Then, please indicate which of the two"
    " electric cars you would like to purchase. Prompt: Why do people choose to have"
    " kids? Preference Instruction: Please read the family descriptions carefully."
    " Then, please indicate which of the two families you would chose to have kids"
    " with. Prompt: How can we consume food most ethically? Preference Instruction:"
    " Please read the diet descriptions carefully. Then, please indicate which of the"
    " two diets you feel is the most ethical. Prompt: How do people measure success?"
    " Preference Instruction: Please read the life descriptions carefully. Then, please"
    " indicate which of the two life outcomes you would consider more successful."
    " Prompt: {why_prompt} Preference Instruction: "
)

DEPENDENT_VARIABLE_TEMPLATE_DISCRETE = inspect.cleandoc(
    "Given a Prompt from a Research Investigator, please generate a Preference"
    " Instruction that represents how to make a decision between options. The"
    " instruction should: 1) Use first-person perspective (what would YOU"
    " choose/prefer/experience) 2) Clearly indicate direction of impact (positive or"
    " negative) without assuming specific factors 3) Give maximal insight into the"
    " Research Investigator's Prompt Prompt: I would like to understand European Carbon"
    " Tax policy in the early 2000's. Preference Instruction: Based on the descriptions"
    " of the multinational carbon tax policies, select which option would have the most"
    " positive impact on you personally. Prompt: I would like to understand Italian"
    " citizenship decisions in the 1970's. Preference Instruction: Please read the"
    " descriptions of the individuals applying for Italian citizenship carefully. Then,"
    " please indicate which of the two individuals you would grant citizenship to."
    " Prompt: I would like to understand vaccine distribution policy in Canada."
    " Preference Instruction: Please read the descriptions and select which of the"
    " individuals you would give priority for receiving the vaccine immediately."
    " Prompt: I would like to understand factors influencing public office candidate"
    " choices in Germany pre-world war 2. Preference Instruction: Please read the"
    " descriptions of the two candidates carefully. Then, please indicate which of the"
    " two candidates you would vote for. Prompt: I am trying to understand why people"
    " choose certain dentists. Preference Instruction: Please evaluate each dentist."
    " Then, select which dentist you would visit. Prompt: I am trying to understand"
    " smartphone purchasing decisions. Preference Instruction: Please read the"
    " descriptions of the two smartphones described. Then, select which smartphone you"
    " would buy. Prompt: I am trying to understand social media platform features"
    " influence on mental health. Preference Instruction: Please read the descriptions"
    " of each social media platform carefully. Then, please indicate which of the"
    " platforms would have the most positive impact on your mental health. Prompt: I am"
    " trying to understand bottled water buying decisions. Preference Instruction:"
    " Please read the descriptions of the two types of bottled water available. Then,"
    " please select which of the two bottled waters you would purchase. Prompt:"
    " {why_prompt} Preference Instruction: "
)

DEPENDENT_VARIABLE_TEMPLATE_PROBABILITSTIC = inspect.cleandoc("""
    Given a Prompt from a Research Investigator, please generate a Preference
    Instruction that represents how to make a decision between options. The
    instruction should: 1) Use first-person perspective (what would YOU
    choose/prefer/experience) 2) Clearly indicate direction of impact (positive or
    negative) without assuming specific factors 3) Give maximal insight into the
    Research Investigator's Prompt 

	Prompt: I would like to understand European Carbon Tax policy in the early 2000's. 
	Preference Instruction: Based on the descriptions of the multinational carbon tax policies, tell us the probability which suggest your preference for the options. Also, provide your preferred option.
    
	Prompt: I would like to understand Italian citizenship decisions in the 1970's. 
	Preference Instruction: Please read the descriptions of the individuals applying for Italian citizenship carefully. Then,
    please indicate your preference probability for the two individuals you would grant citizenship to. Also, provide your preferred option.

    Prompt: I would like to understand vaccine distribution policy in Canada.
    Preference Instruction: Please read the descriptions and provide your preference probability for the individuals to give priority for receiving the vaccine immediately. Also, provide your preferred option.
     
	Prompt: I would like to understand factors influencing public office candidate choices in Germany pre-world war 2. 
	Preference Instruction: Please read the descriptions of the two candidates carefully. Then, please indicate your preference probability of voting for two candidates. Also, provide your preferred option.

	Prompt: I am trying to understand why people choose certain dentists.
	Preference Instruction: Please evaluate each dentist. Then, provide your preference probability of visiting each dentist. Also, provide your preferred option.

	Prompt: I am trying to understand smartphone purchasing decisions.
	Preference Instruction: Please read the descriptions of the two smartphones described. Then, provide your purchase probability for each of the smartphones. Also, provide your preferred option.

	Prompt: I am trying to understand social media platform features influence on mental health.
	Preference Instruction: Please read the descriptions of each social media platform carefully. Then, please indicate your probability for each platform to have the most positive impact on your mental health. Also, provide your preferred option.

	Prompt: I am trying to understand bottled water buying decisions.
	Preference Instruction: Please read the descriptions of the two types of bottled water available. Then, please provide your purchase probability for two bottled waters. Also, provide your preferred option.

	Prompt: {why_prompt} Preference Instruction: 
""")

DEPENDENT_VARIABLE_TEMPLATE_WITH_OPT_OUT = inspect.cleandoc("""
    Given a Prompt from a Research Investigator, please generate a Preference
    Instruction that represents how to make a decision between options including opt-out. The
    instruction should: 1) Use first-person perspective (what would YOU
    choose/prefer/experience) 2) Clearly indicate direction of impact (positive or
    negative) without assuming specific factors 3) Give maximal insight into the
    Research Investigator's Prompt 4) The instruction should clearly state that the decision should include opt-out.

	Prompt: Based on the descriptions of the multinational carbon tax policies, tell us the probability which suggest your preference for the options. Also, provide your preferred option.
	Preference Instruction: Based on the descriptions of the multinational carbon tax policies, tell us the probability which suggest your preference for the options including opt-out (Don't like these carbon tax policies). Also, provide your preferred option.
    
	Prompt: Please read the descriptions of the individuals applying for Italian citizenship carefully. Then, please indicate your preference probability for the two individuals you would grant citizenship to. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions of the individuals applying for Italian citizenship carefully. Then, please indicate your preference probability for granting citizenship to two individuals including opt-out (Not granting citizenship to these individuals). Also, provide your preferred option.

    Prompt: Please read the descriptions and provide your preference probability for the individuals to give priority for receiving the vaccine immediately. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions and provide your preference probability for the individuals to give priority for receiving the vaccine immediately including opt-out (No priority for these individuals). Also, provide your preferred option.
     
	Prompt: Please read the descriptions of the two candidates carefully. Then, please indicate your preference probability of voting for two candidates. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions of the two candidates carefully. Then, please indicate your preference probability of voting for two candidates including opt-out (Not voting for these candidates). Also, provide your preferred option.

	Prompt: Please evaluate each dentist. Then, provide your preference probability of visiting each dentist. Also, provide your preferred option.
	Preference Instruction: Please evaluate each dentist. Then, provide your preference probability of visiting each dentist including opt-out (No visit to these dentist). Also, provide your preferred option.

	Prompt: Please read the descriptions of the two smartphones described. Then, provide your purchase probability for each of the smartphones. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions of the two smartphones described. Then, provide your purchase probability for each of the smartphones including opt-out (No purchase of these smartphone). Also, provide your preferred option.

	Prompt: Please read the descriptions of each social media platform carefully. Then, please indicate your probability for each platform to have the most positive impact on your mental health. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions of each social media platform carefully. Then, please indicate your probability for each platform to have the most positive impact on your mental health including opt-out (No social media use). Also, provide your preferred option.

	Prompt: Please read the descriptions of the two types of bottled water available. Then, please provide your purchase probability for two bottled waters. Also, provide your preferred option.
	Preference Instruction: Please read the descriptions of the two types of bottled water available. Then, please provide your purchase probability for two bottled waters including opt-out (No purchase of these bottled waters). Also, provide your preferred option.
	 
	Prompt: Based on the descriptions of the multinational carbon tax policies, select which option would have the most positive impact on you personally. 
    Preference Instruction: Based on the descriptions of the multinational carbon tax policies, select which option would have the most positive impact on you personally including opt-out (No impact on you personally). 
    
    Prompt: Please read the  descriptions of the individuals applying for Italian citizenship carefully. Then, please indicate which of the two individuals you would grant citizenship to. 
    Preference Instruction: descriptions of the individuals applying for Italian citizenship carefully. Then, please indicate which of the two individuals you would grant citizenship to including opt-out (Not granting citizenship to these individuals). 
	
    Prompt: Please read the descriptions and select which of the individuals you would give priority for receiving the vaccine immediately. 
    Preference Instruction: Please read the descriptions and select which of the individuals you would give priority for receiving the vaccine immediately including opt-out (No priority for these individuals). 
    
    Prompt: Please read the descriptions of the two candidates carefully. Then, please indicate which of the two candidates you would vote for. 
    Preference Instruction: Please read the descriptions of the two candidates carefully. Then, please indicate which of the two candidates you would vote for including opt-out (Not voting for these candidates). 
    
    Prompt: Please evaluate each dentist. Then, select which dentist you would visit. 
    Preference Instruction: Please evaluate each dentist. Then, select which dentist you would visit including opt-out (No visit to these dentist). 
    
    Prompt: Please read the descriptions of the two smartphones described. Then, select which smartphone you would buy. 
    Preference Instruction: Please read the descriptions of the two smartphones described. Then, select which smartphone you would buy including opt-out (No purchase of these smartphone). 
    
    Prompt: Please read the descriptions of each social media platform carefully. Then, please indicate which of the platforms would have the most positive impact on your mental health. 
    Preference Instruction: Please read the descriptions of each social media platform carefully. Then, please indicate which of the platforms would have the most positive impact on your mental health including opt-out (No social media use). 
    
    Prompt: Please read the descriptions of the two types of bottled water available. Then, please select which of the two bottled waters you would purchase. 
    Preference Instruction: Please read the descriptions of the two types of bottled water available. Then, please select which of the two bottled waters you would purchase including opt-out (No purchase of these bottled waters). 

	Prompt: {why_prompt} Preference Instruction:
	
	{format_instructions}
""")

EXPERIMENT_TEMPLATE = inspect.cleandoc("""
        We are running a survey about: \"{{why_prompt}}\".
        Please consider the following two scenarios carefully and make a selection.
        {{persona_description}}
        These are the {prompt_type}:
        {{choices}}
        {decision_prompt}:""")

PROMPT = (
    "Respondent Instructions: Imagine you are a human with the following "
    "characteristics - "
    "{persona} "
    "Imagine you live in {where_preamble} in {when_preamble}."
    " \nInstructions: Respond to a questionnaire about "
    "{why_prompt}"
    " \nConsider Options A, B and C in the Questionnaire below. "
    "{respondent_dependent_variable}. "
    "Please respond with a single letter only: A, B or C. "
    "\n\nQuestionnaire:\n{scenarios_text}\nResponse: I choose option"
)

PROMPT_PREAMBLE = (
    "Respondent Characteristics: Imagine you are a human with the following"
    " characteristics - {persona} {respondent_instructions_preamble}\nInstructions:"
    " Respond to a {prompt_type} Question about"
    " {why_prompt}\nConsider Options A, B and C in the"
    " {prompt_type} Question below. {respondent_dependent_variable} Please "
    " respond with a single letter only: A, B or C."
)

SCENARIOS_PROMPT = (
    "Option A: {scenario_a_text}.\nOption B: {scenario_b_text}.\nOption C: Neither of"
    " these."
)

ATTRIBUTE_LEVELS_TEMPLATE = inspect.cleandoc(
    "I am creating an experiment to understand the factors influencing human"
    ' decision-making. This is the behavior I want to understand: "{why_prompt}?"'
    " Attributes: List {attribute_count} distinct, pointed reasons which could"
    " influence behavior related to the prompt. Refrain from choosing qualities related"
    " to brand. If price is an applicable attribute, please include it. Levels: For"
    " each attribute, list {level_count} precise, mutually-exclusive, nominal examples"
    " (levels) of each attribute. Make sure every level is unique across all"
    " attributes. If the attribute is price, please list ten equally-spaced levels. The"
    " desired format for each level is a complete descriptive sentence of {max_length}"
    " characters or less which refers to the attribute language. Output the attributes"
    ' and levels in the following JSON  format: {{"prompt": <question>, "attributes":'
    ' [{{"attribute": <attribute text here>, "levels": [<level text>, <level'
    ' text>...]}}, ...]}} Example: {{"prompt": "Why do people choose to go to certain'
    ' dentists?","attributes": [{{"attribute": "Reputation", "levels": ["The dentist is'
    ' well-known and highly-rated online", "The dentist was recommended to you by a'
    ' friend", "The dentist has a few poor ratings online"]}},{{"attribute": "Price",'
    ' "levels": ["The dentist costs $30 per visit", "The dentist costs $60 per visit",'
    ' "The dentist costs $90 per visit", "The dentist costs $120 per visit", "The'
    ' dentist costs $150 per visit", "The dentist costs $180 per visit", "The dentist'
    ' costs $210 per visit", "The dentist costs $240 per visit","The dentist costs $270'
    ' per visit", "The dentist costs $300 per visit"]}}]}}'
)
ATTRIBUTE_LEVELS_TEMPLATE_V2 = inspect.cleandoc(
    "I am creating an experiment to understand the factors influencing human"
    ' decision-making. This is the behavior I want to understand: "{why_prompt}?"'
    " Attributes: List {attribute_count} distinct, pointed reasons which could"
    " influence behavior related to the prompt in the country {country}. Refrain from"
    " choosing qualities related to brand. If price is an applicable attribute, please"
    " include it. Levels: For each attribute, list {level_count} precise, MUTUALLY"
    " EXCLUSIVE, QUANTIFIABLE nominal examples (levels) of each attribute. Make sure"
    " every level is unique across all attributes. When specifying quantities, mention"
    " the relevant units in parentheses after the number. The attributes and its levels"
    " (units) should correspond to the the {country}Example 1: If the country is USA,"
    " price levels should be in $ or USD, Range should be in miles.If the attribute is"
    " price, please list five equally-spaced levels. For quantitative attributes,"
    " don't repeat unit of measurement in the attribute levels. for example, for"
    " attribute 'Range', write 'The car has a range of 200 miles' instead of 'The"
    " car has a range of 200 miles (miles)'. The desired format for each level is a"
    " complete descriptive sentence of {max_length} characters or less which refers to"
    " the attribute language. Output the attributes and levels in the following JSON "
    ' format: {{"prompt": <question>, "attributes": [{{"attribute": <attribute text'
    ' here>, "levels": [<level text>, <level text>...]}}, ...]}} Example: {{"prompt":'
    ' "Why do people choose to go to certain dentists?","country": "United'
    ' States","attributes": [{{"attribute":"Reputation", "levels": ["The dentist is'
    ' well-known and highly-rated online (rated over 4.5 stars out of 5)", "The dentist'
    ' was recommended to you by a close friend (they would attend your wedding)", "The'
    " dentist has a few poor ratings online (fewer than 3 poor"
    ' ratings)"]}},{{"attribute": "Price", "levels": ["The dentist costs $30 per'
    ' visit", "The dentist costs $60 per visit", "The dentist costs $90 per visit",'
    ' "The dentist costs $120 per visit", "The dentist costs $150 per visit", "The'
    ' dentist costs $180 per visit", "The dentist costs $210 per visit", "The dentist'
    ' costs $240 per visit","The dentist costs $270 per visit", "The dentist costs $300'
    ' per visit"]}}]}}'
)
ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE = inspect.cleandoc("""
    First analyze the provided context for completeness by checking:
    1. Presence of specific parameters (e.g., origin-destination for travel, product specifications for goods)
    2. Geographic scope (specific location vs general market)
    3. Temporal boundaries (specific timeframe vs general period)
    4. Target audience specification

    Then, for each feature:
    * If the context is complete (all specific parameters present): Use quantitative levels with precise ranges
    * If the context is partial or generic: Use qualitative levels that capture relative differences
    * If mixing both types: Justify why certain features can be quantitative while others must remain qualitative
    For quantitative features, ensure:
    •	All levels are consistent in format (either all single values or all ranges).
    •	If ranges are used, their widths are narrow enough to avoid ambiguity or difficulty in interpreting values.
    •	Ranges should reflect realistic granularity based on the feature's context.
    Generate relevant features along with their levels (maximum of {level_count} levels per feature and a maximum of {attribute_count} features) for the given context: {why_prompt}.
    The features will be used to design a stated preference survey. Hence, ensure features are maximally independent and does not have confounding or mediation.
    Ensure that the levels of the features are reflective of the country {country} and year {year}.

    Important
    1. Do not make assumptions about the type of context
    2. Allows flexibility in level definition (qual/quant) based on provided information
    3. Enforces consistency within similar feature types
    4. If the context does not specify explicit details such as time, place, or product, ensure all features and levels are globally applicable, prioritizing relative (qualitative) interpretations unless universally applicable quantitative benchmarks exist.
    5. If the context specifies explicit details such as time, place, or product, prioritize local applicability by using specific (quantitative) benchmarks wherever possible to enhance relevance.
    6. Do not include justifications or explanations in the response.
    7. For each feature, ensure the number of levels are always equal to {level_count} levels per feature.
    8. Whenever applicable, include the measurement of unit in the feature name.
    9. Write the feature level text as descriptive as possible but do not exceed 40 characters limit.
{format_instructions}
""")

ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE_WITH_SCORE = inspect.cleandoc(
    """You are a market research data scientist whose goal is to generate relevant orthogonal feature for a behavioural statement.
    First, perform a systematic context analysis by checking and explicitly documenting:

    1. Required Parameters Check:
       * List all domain-specific required parameters
       * Mark each as [Present/Absent] in the given context
       * Create a completeness score (% of required parameters present)

    2. Scope Definition:
       * Geographic: [Specific Location/Regional/Global]
       * Temporal: [Specific Period/General]
       * Target Audience: [Specific Segment/General Public]
       * Product/Service: [Specific/Category/Generic]

    3. Context Classification:
       * Complete Context: All required parameters present
       * Partial Context: Some parameters present
       * Generic Context: No specific parameters

    # Feature Count Validation
    1. Initial Feature Generation:
       * MUST generate exactly {attribute_count} features
       * If fewer features generated:
         - Generate additional features by:
           * Subdividing existing features into more specific aspects
           * Considering complementary market dimensions
           * Adding regional/cultural variants if applicable
       * If more features generated:
         - Remove excess features based on:
           * Lowest context relevance score
           * Highest correlation with other features
           * Document removal justification

    2. Feature Set Completeness Check:
       * Verify total feature count equals {attribute_count}
       * Each feature must be unique and measurable
       * Document feature selection rationale

    # Feature Generation Rules
    Based on the context classification:

    1. Complete Context (Score = 100%):
       * Generate quantitative features with specific ranges
       * Ranges must include:
         - Minimum value (documented source)
         - Maximum value (documented source)
         - Step size (justified by domain practice)

    2. Partial Context (Score > 50%):
       * Generate mixed features:
         - Quantitative: Only for parameters explicitly present
         - Qualitative: For parameters with missing specifics
       * Each quantitative feature must link to present parameters

    3. Generic Context (Score ≤ 50%):
       * Generate only qualitative features
       * Use relative scales (e.g., Low/Medium/High)
       * Avoid any numeric values

    # Feature Independence Validation
    For each feature pair (Fi, Fj):
    1. Check direct correlation potential
    2. Check mediation pathways
    3. If correlation > threshold:
       * Remove dependent feature
       * Document removal reason

    # Level Generation Rules and Constraints
    For each feature type:

    1. Quantitative Levels:
       * Required parameters: [list specific to domain]
       * Validation checks:
         - All parameters present
         - Range validity
         - Step size consistency
       * MUST generate exactly {level_count} levels:
         - If range permits fewer levels:
           * Decrease step size to allow {level_count} divisions
         - If range too broad:
           * Adjust step size to create exactly {level_count} levels

    2. Qualitative Levels:
       * Default for incomplete context
       * Ordinal scale preference
       * Clear distinction between levels
       * MUST generate a minimum of 2 and a maximum of {level_count} levels

    # Level Description Rules
    For each level description:
    1. Structure Requirements:
       * Format: [Key Feature] with [Benefit/Specification]
       * Maximum length: 40 characters
       * Must include quantifiable elements where possible

    2. Content Guidelines:
       * Primary Feature: Main distinguishing characteristic
       * Supporting Element: Must include at least one of:
         - Quantifiable metric (years, %, ratings)
         - Specific technology/feature
         - Clear benefit statement
         - Service/support detail

    3. Progression Rules:
       * Each level must show clear advancement from previous
       * Use industry-standard terminology
       * Include market-relevant specifications
       * Maintain consistent description pattern across levels

    4. Clarity Requirements:
       * Avoid technical jargon
       * Use market-standard terms
       * Include measurable differentiators
       * Reference local market standards when applicable

    # Level Count Validation
    For each feature F:
    1. Count generated levels N
    2. If N < {level_count}:
       * For quantitative features:
         - Subdivide existing ranges while maintaining min/max bounds
         - Ensure step size remains consistent
    3. If N > {level_count}:
       * Remove excess levels furthest from median/center
       * Document removal justification

    # Level Description Validation
    For each level description:
    1. Check completeness:
       * Has primary feature
       * Has supporting element
       * Includes quantifiable metric where possible

    2. Verify differentiation:
       * Clear progression from previous level
       * Distinct value proposition
       * Measurable difference where applicable

    3. Validate clarity:
       * Understandable by target audience
       * Industry-standard terminology
       * Market-relevant metrics

    # Final Output Validation
    1. Feature Count Check:
       * Verify exactly {attribute_count} features generated
       * No duplicate features or levels allowed

    2. Level Description Check:
       * All descriptions within 40 character limit
       * Clear progression between levels
       * Measurable differentiators present
       * Market-relevant terminology used

    <example>
        Input:  "attribute_count": 2,
              "country": "Italy",
              "level_count": 5,
              "why_prompt": "What key attributes do you want your new brand to embody?",
              "year": 2025

        Before (Simple Output):
        'attributes_levels': [
            ('attribute': 'Brand Personality',
             'level_type': 'qualitative',
             'levels': ('Traditional & Conservative',
                        'Balanced & Moderate',
                        'Contemporary & Progressive',
                        'Innovative & Bold',
                        'Revolutionary & Disruptive')),
            ('attribute': 'Cultural Resonance',
             'level_type': 'qualitative',
             'levels': ('Locally Rooted',
                        'Regional Appeal',
                        'National Recognition',
                        'European Integration',
                        'Global Mindset'))]

        After (Enhanced Descriptive Output):
        'attributes_levels': [
            ('attribute': 'Brand Personality',
             'level_type': 'qualitative',
             'levels': ('Classic Design with Local Crafting',
                        'Modern Style with Regional Touch',
                        'Premium Build with Euro Standards',
                        'Innovation with Global Technology',
                        'Future-Ready with Green Design')),
            ('attribute': 'Cultural Resonance',
             'level_type': 'qualitative',
             'levels': ('Local Focus with City Network',
                        'Regional Focus with 5-State Reach',
                        'National Brand with 90% Coverage',
                        'EU Certified with Multi-Region',
                        'Global Standard with Local Adapt'))]
    </example>

    Important Requirements:
    1. Whenever applicable, include the measurement unit in the feature name
    2. Write the feature level description as descriptive as possible keeping in mind that the intended audience is non-technical individuals but do not exceed 40 characters limit
    3. Do not include any additional text or justification
    4. Ensure that unit of measurement is consistent across all levels of the same feature. e.g. if the unit of measurement for one level is "miles" or "$ per month", then all levels of that feature should have "miles" or "$ per month" as the unit of measurement.
    5. Ensure consistency in the description of levels. 
    if one level contains numerical value, then all levels should contain numerical values. 
    e.g. if one level is "Costs $5 per month", then avoid using level description as "free of cost" and instead use "Costs $0 per month".
    6. If a level contains a numerical value, and a qualitative description of the numerical value, then ensure that the qualitative description is consistent across all levels.
    Avoid levels like:"levels": ["1-2 (Below Average)","3-4 (Average)","5-6 (Above Average)","7-8 (Excellent)","9-10 ("]. 
    Instead, use levels like:"levels": ["1-2 (Below Average)","3-4 (Average)","5-6 (Above Average)","7-8 (Excellent)","9-10 (Outstanding)"]. 
    
    Variables:
    {level_count}: Exact number of levels per feature
    {attribute_count}: Exact number of features
    {why_prompt}: Context question
    {country}: Geographic scope
    {year}: Temporal scope

    {format_instructions}"""
)

ENHANCE_ATTRIBUTE_LEVELS_TEMPLATE = inspect.cleandoc("""
We have a set of attributes/features which are being used to design a
stated preference survey. Please rephrase the attributes and levels to
improve the readability considering a non-technical survey respondents.

Inputs:
The statement: {why_prompt}
The existing attributes and their levels: {existing_attributes_levels}

**Before proceeding with enhancing the attributes and levels, please perform the following steps to handle potential duplicate attributes:**

1. **Standardize Attribute Names**: For each attribute, rephrase its name to a clear and standardized form that accurately reflects its meaning. For example, 'battery Range per charge' could be standardized to 'Driving Range on Full Charge', and 'RANGE' could also be standardized to 'Driving Range on Full Charge'.

2. **Merge Duplicate Attributes**: If multiple attributes have the same standardized name, merge them into a single attribute. For the merged attribute, combine the levels from all the duplicate attributes, removing any duplicate or overlapping levels. Ensure the resulting levels are mutually exclusive and collectively exhaustive, covering the full range of values from the original sets.

3. **Enhance Readability**: After merging duplicates, proceed to enhance the readability of the remaining attributes and their levels, following the important rules provided below.

**IMPORTANT RULES:**
1. Exactly return the same number of attributes and levels as the existing attributes and levels after merging duplicates. Do not ignore any existing attributes or levels unless they are duplicates!
2. Do not generate new attributes and levels. Only enhance the existing attributes and levels.
3. Do not alter the numbers/quantities or any numerical values along with the unit of measurement and context conveyed through the original attributes and their levels unless there is a duplication of attributes or levels. For example: attribute duplication: mileage, range, distance covered in one full tank or charge. Levels duplication: "250+miles", "more than 250 miles". Remove duplicate attributes completely and same for levels.
4. IF YOU USE ABBREVIATIONS IN ANY WAY MAKE SURE TO DEFINE THE FULL PHRASE (e.g., instead of saying LLM say LLM (Large Language Model) to make sure people know what is meant).
5. Ensure consistency in the description of levels. 
   If one level contains a numerical value, then all levels should contain numerical values. e.g., if one level is "Costs $5 per month", then avoid using level description as "free of cost" and instead use "Costs $0 per month". 
   When using ranges, make sure to always define an upper and lower limit for the values, this means levels like "Under 20 minutes" are not good and should instead be "[lower_bound - 20 minutes]" where lower_bound is something you will have to estimate. 
   If there's an upper limit, make sure not to write something like "$4,001+ (High Incentive)" but instead "[4,001 - Upper_bound]" where Upper_bound is something you must estimate.
6. If a level contains a numerical value and a qualitative description of the numerical value, then ensure that the qualitative description is consistent across all levels. 
   Avoid levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 ("]. 
   Instead, use levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 (Outstanding)"].
7. When using ranges for numerical levels, make sure that there is a space between the dashes and the numbers, so [4-5] should be [4 - 5].
8. ALWAYS QUANTIFY NUMERICAL VALUES IN SET RANGES, SO FOR EXAMPLE NEVER SAY "NUMERICAL VALUE"+ OR UNDER/OVER "NUMERICAL VALUE". If you have no reference point for what's below or above, make up a reasonable assumption for the lower or upper bound.
{format_instructions}""")

GPT4_TEST_TEMPLATE = inspect.cleandoc(
    "I am trying to generate Attributes and Levels in a discrete-choice"
    " experiment to understand decision-making for a given Research"
    " Objective. "
    # To include when we include Respondents/Trait Data
    # 'I am trying to generate Attributes and Levels in a discrete-choice experiment to '
    # 'understand decision-making for a given Research Objective and Respondents. '
    "The experiment will contain: A Research Objective, Attributes and"
    " Levels."
    # To include when we include Respondents/Trait Data
    # 'The experiment will contain: A Research Objective, Respondents, Attributes and Levels. \n'
    "A Research Objective is the object of choice, for which preferences"
    " will be quantified."
    "Attributes characterize the object of interest and Levels are the"
    " individual features to be tested, among which the survey will elicit"
    " trade-offs. "
    "Attributes may include such features as effectiveness, safety, or mode"
    " of administration of a treatment, or anything else that helps"
    " understand the cause of human decision making. Levels describe the"
    " possible values, outcomes, interventions, or technologies associated"
    " with each attribute. For example, a service attribute could include"
    " levels of service quality or waiting time. "
    # Research Objective + Attribute + Level Experiment Design
    "Research Objective: To understand the factors that influence the"
    ' decision-making process related to the Research Object: "{why_prompt}?"'
    # To include when we include Respondents/Trait Data
    # 'Respondents: The traits of people whose preferences are being asked include: {{Traits}} \n'
    "Attributes: List {attribute_count} descriptive and unique attributes"
    " that characterize the Research Objective."
    "Suggest non-repetitive attributes, whilst refraining from choosing"
    " qualities related to brand. Choose attributes and levels that"
    " influence decision-making about the Research Objective. If price is"
    " an applicable attribute, please include it as one of the attributes."
    " The desired format for each answer is a vignette under {max_length}"
    " characters. \n"
    "Levels: For each attribute, list {level_count} unique examples of each"
    " attribute. "
    "Make sure every level is unique across all attributes. If the"
    " attribute is price, please list ten, equally-spaced levels. "
    # Experiment Format
    "The desired format is a complete descriptive sentence for each level"
    " which "
    "contains the attribute language. Output the attribute questions and"
    " levels in the following JSON "
    "format:"
    '{{"prompt": <question>, "attributes": [{{"attribute": <attribute text'
    " here>, "
    '"levels": [<level text>, <level text>...]}}, ...]}} '
    "Example:"
    '{{"prompt": "Why do people choose to go to certain'
    ' dentists?","attributes": '
    '[{{"attribute": "Reputation", "levels": ["The dentist is well-known'
    ' and highly-rated online", '
    '"The dentist was recommended to you by a friend", "The dentist has a'
    ' few poor ratings online"]}},'
    '{{"attribute": "Price", "levels": ["The dentist costs $40 per visit",'
    ' "The dentist costs $80 per visit", '
    '"The dentist costs $120 per visit", "The dentist costs $160 per'
    ' visit", '
    '"The dentist costs $200 per visit"]]}}'
)

# Version 0.2
LEVELS_PRICE_TEMPLATE = inspect.cleandoc(
    "{why_prompt}. One consideration is {attribute}. In dashed bullet points under"
    " {max_length} characters for each example, {level_count} unique examples of"
    """ {attribute}, without explanation, are:
    -"""
)

# Version 0.2
LEVELS_TEMPLATE = inspect.cleandoc(
    "I would like to understand '{why_prompt}'. List {level_count} personal and unique"
    " answers"
    " to '{attribute}?'. Do not"
    " repeat phrases. The desired format for each answer is a succinct vignette under"
    " {max_length} characters which"
    """ contains no bullets or line breaks. Each vignette should be separated with new lines and dashed bullet points.
    -"""
)

PRICE_QUESTION_TEMPLATE = ["Price", "Cost"]

CAUSALITY_TEMPLATE = inspect.cleandoc("""
**Role**: You are an **interdisciplinary stated preference methodologist** with expertise in designing conjoint analysis studies across diverse domains, including but not limited to: **economics, healthcare, transportation, consumer behavior, technology adoption, environmental policy, energy systems, public health, urban planning, education, insurance, finance, psychology, and patient preference research**.

You adapt your language, structure, and conceptual framing to reflect the conventions and expectations of the relevant domain — whether scientific precision in healthcare, behavioral framing in psychology, cost-benefit trade-offs in economics, or service design parameters in transportation and infrastructure.

Your job is to determine if a problem statement can be answered using a stated preference survey approach. Follow the instructions below:

## Step 0: Statement Rephrasing (if needed)
If the original problem statement is ambiguous, vague, or poorly structured, rephrase it into a complete, clear sentence that preserves its intent. This rephrased statement will be used for Steps 1 through 3. Do not simplify the meaning or remove important contextual information.

## Step 1: Statement Completion Check
- If it is a complete English sentence, proceed to Feature Generation.
- If incomplete, attempt to extract the object of study:
  - If object of study can be identified, proceed to Step 2.
  - If not, respond with: `{{"is_causal": false, "is_harmful": true, "suggestions": []}}`
- If input contains illegal or unethical content, respond with:
  `{{"is_causal": false, "is_harmful": true, "suggestions": []}}`
- If input is vague or one-word (e.g., "Car"), respond with:
  `{{"is_causal": false, "is_harmful": true, "suggestions": []}}`

## Step 2: Feature Generation Assessment

### Pre-Suggestion Check: Length and Intent Matching
Before generating suggestions:
- Estimate the **word count** and **intent** (e.g., product choice, treatment selection, service adoption) of the rephrased input.
- Ensure each suggestion matches or exceeds the **original word count**.
- Retain or enhance the original **intent, structure, and richness**.
- If the original includes multiple value propositions, brand names, or qualifiers, reflect all of them in each suggestion.
- Match **stylistic tone and terminology** appropriate to the inferred domain (e.g., clinical precision in healthcare, feature-driven framing in consumer goods).

### Suggestion Generation Logic
- Reformulate the statement to identify external, manipulable attributes that generate measurable features and form valid choice sets (e.g., product design, campaign strategy, messaging approach).
- If real-world features are unavailable, construct plausible **perceptual, psychological, or communication-based trade-offs**.
- **Mandatory Brand and Feature Inclusion**: If specific brand names or claims are included (e.g., "Daylee," "24g protein," "digestive health"), ensure all suggestions include them.
- **Expansion for Short Inputs**: Expand vague or brief statements into realistic choice contexts with consumer behavior signals, brand examples, or feature trade-offs.
- **Dynamic Context Addition**: Infer the statement's domain (e.g., travel, electronics, infrastructure) and add a specific, realistic context to each suggestion:
       - For travel: Add a plausible origin-destination (e.g., "New York to Tokyo" for long-haul flights).
       - For products: Specify a product instance (e.g., "Samsung Galaxy phone" for smartphones). Use multiple well-known brands if plausible.
       - For services/infrastructure: Define a scenario (e.g., "urban rail project in Chicago").
       - If domain is unclear, use a generic but realistic example tied to the object of study. 
- **Action Verb Inclusion**: Each suggestion must include a relevant **action verb** that reflects the behavioral or decision stage being investigated. Common verbs include *awareness, consideration, preference, choice, purchase, usage, switching, loyalty, retention*. Select a verb appropriate to the inferred domain using the following guide:
        - **Consumer Goods** → purchase, preference, consideration, loyalty
        - **Healthcare** → treatment choice, adoption, side effect consideration
        - **Transportation** → mode choice, route selection, travel decision
        - **Technology & Subscriptions** → usage, adoption, subscription, retention
        - **Public Policy & Governance** → support, compliance, adoption, policy preference
        - **Education** → enrollment, learning engagement, program selection
        Use the action verb to make the intent of each suggestion more specific and measurable.
- **Domain-Aware Framing**: Use conventions appropriate to each field:
  - **Healthcare** → treatment outcomes, patient burden, side effects
  - **Economics** → trade-offs, utility, incentives
  - **Consumer Goods** → benefit-led, claims, context of use
  - **Transportation** → route attributes, travel context
  - **Public Policy** → compliance, support, societal impact
- **Message Framing Domains**: For brand trust or perception problems, generate messaging-based trade-offs such as:
  - Voice (corporate vs. personal), Transparency (honesty vs. pivoting), Framing (inclusive vs. assertive), etc.
- Ensure each suggestion is:
  - **At least as long** as the original statement (word count or character-based comparison)
  - Equally or more **descriptive and feature-rich**
  - Matched in **tone, structure, and intent type**
  - Written in the **style of the inferred domain**

## Step 3: Stated Preference Potential
- Can features (real or perceptual) be combined into realistic choice alternatives?
- Are responses quantifiable?
- Can trade-offs be reasonably inferred and measured?

## Step 4: Final Determination
- If all Step 3 conditions are met: `"is_causal": true`
  - Return 3 related **questions** that explore measurable trade-offs.
  - Set `"is_harmful": false`
- If Step 3 conditions fail: `"is_causal": false`
  - Return 3 **reformulated suggestions** suitable for stated preference analysis.
  - Set `"is_harmful": false`

## Rules
- Avoid "How" questions unless the domain requires them.
- Match or exceed the original in **richness, specificity, and domain tone**.
- Never oversimplify multi-line or value-dense prompts.
- Use domain-specific language and assumptions.
- Suggestions must not appear shorter or shallower than the original.
- Ensure **minimum 50+ words** or 3–4 lines for multi-idea inputs.
- Even for short prompts, **expand meaningfully** into a measurable choice context.

Variable:
Problem statement: {why_prompt}
{format_instructions}
""")


SINGLE_STATEMENT_CAUSALITY_TEMPLATE = inspect.cleandoc(
    """**Role**: You are an **interdisciplinary stated preference methodologist** with expertise in designing conjoint analysis studies across diverse domains, including but not limited to: **economics, healthcare, transportation, consumer behavior, technology adoption, environmental policy, energy systems, public health, urban planning, education, insurance, finance, psychology, and patient preference research**.
You adapt your language, structure, and conceptual framing to reflect the conventions and expectations of the relevant domain — whether scientific precision in healthcare, behavioral framing in psychology, cost-benefit trade-offs in economics, or service design parameters in transportation and infrastructure.
Your job is to determine if a problem statement can be answered using a stated preference survey approach.
Follow the instructions below:
## Step 1: Feature Generation Assessment
     - Reformulate the statement to identify external, manipulable attributes that generate measurable features and form valid choice sets (e.g., product purchase options).
     - Ensure suggestions preserve or exceed the original statement's detail and intent.
     - **Mandatory Brand and Feature Inclusion**: If the original statement mentions specific brand names, product names, or feature claims (e.g., "Daylee," "24g protein," "digestive health"), all suggestions must explicitly include them to ensure contextual continuity and real-world relevance.
     - **Length and Richness Matching**: Suggestions must match or exceed the original statement's length and descriptive richness. This ensures the user perceives them as equally informative and not oversimplified. Include detailed qualifiers (e.g., ingredient benefits, usage contexts, product formats, and brand claims) to reflect the original complexity.
     - **Expansion for Short Inputs**: If the input is short or vague but implies a consumer decision problem (e.g., "What drives purchase of energy drinks?"), expand the suggestion using plausible product context, consumer behavior cues, and real-world category examples to build a comprehensive choice scenario.
     - **Incorporating History for Depth and Clarity**: If the input is vague, ambiguous, or oversimplified, revisit earlier versions in the **History** to recover more detailed product context, implied decision features, or prior domain framing. Leverage those to enhance current suggestions without contradicting the latest version.
	 - **Dynamic Context Addition**: Infer the statement's domain (e.g., travel, electronics, infrastructure) and add a specific, realistic context to each suggestion:
       - For travel: Add a plausible origin-destination (e.g., "New York to Tokyo" for long-haul flights).
       - For products: Specify a product instance (e.g., "Samsung Galaxy phone" for smartphones). Use multiple well-known brands if plausible.
       - For services/infrastructure: Define a scenario (e.g., "urban rail project in Chicago").
       - If domain is unclear, use a generic but realistic example tied to the object of study.
	 - **Domain-Aware Framing**: Infer the appropriate domain based on the topic (e.g., "patient treatment trade-offs" → healthcare; "route choices" → transportation), and frame each suggestion using terminology, assumptions, and framing conventions relevant to that field:
	   - For **healthcare**: Use clinical language, reference treatment outcomes, patient burden, or side effects.
	   - For **economics**: Emphasize trade-offs, incentives, opportunity costs, and utility.
	   - For **transportation**: Reference origin-destination pairs, service attributes (e.g., travel time, cost, reliability).
	   - For **psychology/behavioral sciences**: Emphasize motivations, attitudes, cognitive framing, or behavioral trade-offs.
	   - For **consumer goods**: Use benefit- and feature-led framing with reference to purchase context and brand claims.
	   - For **public policy or infrastructure**: Reference societal impacts, funding mechanisms, and usage scenarios.

## Step 2: Stated Preference Potential
Then assess if these features can be used to construct a stated preference survey:

    - Can features be combined into realistic choice alternatives?
    - Are trade-offs between features meaningful?
    - Can responses be quantified?

Examples
    Input: "What is the meaning of life?"
    Output Template: is_causal: False, suggestion: "When evaluating your life's purpose, what combination of factors would you find most fulfilling?"

    Input: "Which transportation mode do you prefer?"
    Output Template: is_causal: False, suggestion: "What public transportation features influence commuter mode choice?"

    Input: "What factors affects the choice of purchasing a car?"
    Output Template: is_causal: True, suggestion: ""

    Input: "How does human activity impact global warming?"
    Output Template: is_causal: False, suggestion: "What carbon pricing mechanisms would influence industrial emission behavior?"

Implementation Steps

1. Evaluate if the statement directly enables feature identification
2. For is_causal: False, provide exactly 1 feature-gathering reformulation.
3. For is_causal: True, provide an empty string for suggestion.
4. Assess if identified features can create meaningful choice sets

IMPORTANT:
1. Only respond in JSON format without any further explanation, reasoning or extra details.
2. Usually, the problem statements which starts with "How" are not suitable for analysis using a stated preference survey approach. So, avoid such statements in the suggestion.
3. If a statement is not suitable for analysis using stated preference survey approach, then it cannot be part of the suggestion.
4. The suggestion must pass Step 1: Feature Generation Assessment and Step 2: Stated Preference Potential
5. Avoid suggestion that involve personal practices or choices that an individual can change themselves
6. Focus on external factors that can be manipulated and combined into meaningful choice sets
7. Do not copy suggestion from the examples provided.
8. Avoid "How" questions unless they are essential to preserve the brand or product context from the original statement.
  - Suggestion must not appear overly brief compared to the input statement, and should be stylistically aligned with the inferred domain, maintaining realism, terminology, and framing appropriate to that context (e.g., technical precision in healthcare vs. behavioral tone in consumer choices).
    If the input is longer than 3 lines or contains multiple value propositions, ensure suggestion is similarly descriptive (minimum 3–4 lines or 50+ words).
  - Even if the input statement is brief, suggestion may be longer if needed to express a complete and nuanced trade-off scenario suitable for conjoint analysis. Ensure that suggestion is detailed enough to reflect measurable product alternatives, clarify the consumer decision context, and preserve implied intent or unstated assumptions (e.g., price sensitivity, convenience, or functional needs).

Variable:
Problem statement: {why_prompt}
{format_instructions}"""
)

ORTHOGONAL_ATTRIBUTE_LEVELS_TEMPLATE = inspect.cleandoc("""
    I am creating an experiment to understand the factors influencing human decision-making.
    This is the prompt I want to understand: "{why_prompt}?"
    Given the following existing attributes and levels: {existing_attributes_levels}, Create new, orthogonal attributes and levels as follows:
    Attributes: List {new_attribute_count} new, orthogonal, pointed reasons which would influence behavior related to the prompt in the country {country}.
    Refrain from choosing qualities related to brand.
    Levels: For each attribute, list {level_count} precise, orthogonal, mutually-exclusive examples (levels) of each attribute.
    Make sure every level must be unique across all attributes and don't include the existing attributes and levels in the new attributes and levels.
    The attributes and its levels should correspond to the the {country} e.g. for USA, price levels should be in $ or USD, for UK, price levels should be in £ or GBP.
    {format_instructions}""")

OBJECT_OF_STUDY_TEMPLATE = inspect.cleandoc("""
    Given the sentence below, extract the object of study such that the outcome_phrase is gramatically correct and readable.
    For example, in the sentence "I would like to design a new electric car for the American markets", the object of study is "design of electric car for the American markets".
    and the outcome_phrase is "We are trying to understand the factors which affect the choice of a new electric car for the American markets".
    Another example is, in the sentence " I would like to understand why would people buy a tiny solar powered guitar amplifier that you can put in your pocket?",
    the object of study is "a tiny solar powered guitar amplifier that you can put in your pocket".
    and the outcome_phrase is "We are trying to understand the factors which affect the choice of a tiny solar powered guitar amplifier that you can put in your pocket".
    Ensure that the outcome_phrase is grammatically correct and humanly readable.
    sentence: {why_prompt}
    
    IMPORTANT RULES:
    - Only respond in JSON format without any further explanation, reasoning or extra details.
    {format_instructions}""")

OUTCOME_PHRASE_TEMPLATE = inspect.cleandoc("""
    Rewrite the given sentence such that it is concise and grammatically correct.
    Write a sentence using {why_prompt} and {outcome_phrase} such that the tone of the sentence is neutral.
    For example, given the why_prompt: I would like to design a new electric car for the American markets, and the object_of_study: design of electric car for the American markets,
    the resulting sentence would be "We are trying to understand the factors which affects the choice of a new electric car for the American markets".
    Another example is, given the why_prompt: I would like to understand why would people buy a tiny solar powered guitar amplifier that you can put in your pocket?,
    and the object_of_study: a tiny solar powered guitar amplifier that you can put in your pocket,
    the resulting sentence would be "We are trying to understand the factors which affects the choice of a tiny solar powered guitar amplifier that you can put in your pocket".
    sentence: why_prompt: {why_prompt}, object_of_study: {outcome_phrase}
    
    IMPORTANT RULES:
    - Only respond in JSON format without any further explanation, reasoning or extra details.
    {format_instructions}
    """)

SURVEY_PROMPT_TEMPLATE = inspect.cleandoc("""
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are an agent with the following personas: {persona_traits}.
    Currently living in {state}, {country}, Time of Survey: year: {year}.

    Instructions:
    {study_subject}.
    In this context, we have designed a discrete choice survey question where you are presented with three options/alternatives including None of these.
    The options/alternatives are defined as a function of attribute-level combinations.
    Below we provide the list of attributes/features and the possible values (attribute level) it may take.
    {attributes_levels}.

    Consider the options in the Questionnaire below. Please read the specifications carefully.
    Then, select the most preferred option considering that the current year is {year} and your place of residence is {country}.

    Questionnaire:
    {options}
    {none_option}: None of these

    {format_instructions}
    """)

SURVEY_PROMPT_TEMPLATE_DISCRETE_WITHOUT_STUDY_SUBJECT = inspect.cleandoc("""
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are a human with the following traits: {persona_traits}.
    Currently living in {state}, {country}.

    Instructions:
    We are trying to understand the following concept: {why_prompt}
    In this context, we have designed a discrete choice survey.

    Consider the options in the Questionnaire below.
    Then, select the most preferred option considering that the current year is {year} and your place of residence is {country}.

    Questionnaire:
    {dependent_variable}
    {options}

    IMPORTANT INSTRUCTIONS:
    Please adhere to the instructions provided below. Do not deviate.
    1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
    2. Example response format: {{"selected_option": "Option 2"}}

    {format_instructions}
    """)

SURVEY_PROMPT_TEMPLATE_DISCRETE_WITHOUT_STUDY_SUBJECT_WITH_DECISION_STRATEGY = inspect.cleandoc(
    """
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are a human with the following traits: {persona_traits}.
    Currently living in {state}, {country}.

    Instructions:
    We are trying to understand the following concept: {why_prompt}
    In this context, we have designed a discrete choice survey.

    Consider the options in the Questionnaire below.
    Then, select the most preferred option considering that the current year is {year} and your place of residence is {country}.

    You must also identify which type of decision rule heuristic you used to arrive at that preferred option. This can be either "C" (compensatory), "NC" (non-compensatory) or "H" (hybrid).

    Questionnaire:
    {dependent_variable}
    {options}

    IMPORTANT INSTRUCTIONS:
    Please adhere to the instructions provided below. Do not deviate.
    1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
    2. Example response format: {{"selected_option": "Option 2", "decision_strategy": "C"}}

    {format_instructions}
    """
)

SURVEY_PROMPT_TEMPLATE_DISCRETE = inspect.cleandoc("""
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are a human with the following traits: {persona_traits}.
    Currently living in {state}, {country}.

    Instructions:
    {study_subject}.
    In this context, we have designed a discrete choice survey question.

    Consider the options in the Questionnaire below.
    Then, select the most preferred option considering that the current year is {year} and your place of residence is {country}.


    Questionnaire:
    {options}

    IMPORTANT INSTRUCTIONS:
    Please adhere to the instructions provided below. Do not deviate.
    1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
    2. Example response format: {{"selected_option": "Option 2"}}

    {format_instructions}
    """)

SURVEY_PROMPT_TEMPLATE_DISCRETE_WITH_DECISION_STRATEGY = inspect.cleandoc("""
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are a human with the following traits: {persona_traits}.
    Currently living in {state}, {country}.

    Instructions:
    {study_subject}.
    In this context, we have designed a discrete choice survey question.

    Consider the options in the Questionnaire below.
    Then, select the most preferred option considering that the current year is {year} and your place of residence is {country}.

    You must also identify which type of decision rule heuristic you used to arrive at that preferred option. This can be either "C" (compensatory), "NC" (non-compensatory) or "H" (hybrid).

    Questionnaire:
    {options}

    IMPORTANT INSTRUCTIONS:
    Please adhere to the instructions provided below. Do not deviate.
    1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
    2. Example response format: {{"selected_option": "Option 2", "decision_strategy": "C"}}

    {format_instructions}
    """)

SURVEY_PROMPT_TEMPLATE_PROBABILISTIC = inspect.cleandoc("""
    Respondent Instructions:
    You are answering questions as if you are a human. Do not break character.
    You are a human with the following traits: {persona_traits}.
    Currently living in {state}, {country}.

    Instructions:
    {study_subject}.
    In this context, we have designed a discrete choice survey question.

    Consider the options in the Questionnaire below.
    Please tell us your probability of selecting each option considering that the current year is {year} and your place of residence is {country}.


    Questionnaire:
    {options}

    IMPORTANT INSTRUCTIONS:
    1. Respond ONLY with the JSON object shown in the example - no explanations or additional text
    2. Provide probabilities as decimal values between 0 and 1
    3. Ensure probabilities sum to exactly 1.0
    4. Example response format: {{"selected_option": {{"Option 1": 0.70, "Option 2": 0.30}}}}
    5. DO NOT include any other text, explanations, or reasoning
    6. The response must be exactly in this format to be processed

    {format_instructions}
    """)
PRODUCT_ATTRIBUTES_WITHBRAND_TEMPLATE = inspect.cleandoc("""

You are a market research data scientist whose goal is to generate relevant orthogonal features for a given real-life product of a brand and its corresponding price levels, explicitly provided in the input as `{brands}`.

Your task is to systematically analyze the provided product and price information to generate attributes. Follow the steps below:

### Systematic Context Analysis
1. **Required Parameters Check:**
   * List all domain-specific required parameters based on `{brands}`.
   * Mark each parameter as [Present/Absent] in the given context.
   * Create a completeness score (Percent of required parameters present).

2. **Scope Definition:**
   * Geographic: [Specific Location/Regional/Global]
   * Temporal: [Specific Period/General]
   * Target Audience: [Specific Segment/General Public]
   * Product/Service: [Specific/Category/Generic]

3. **Context Classification:**
   * Complete Context: All required parameters present.
   * Partial Context: Some parameters present.
   * Generic Context: No specific parameters provided.

### Feature Count Validation
1. **Initial Feature Generation:**
   * Use the provided `{brands}` details to generate **exactly `{attribute_count}` features** that you would find for a product with the specific brands and price levels
   * If fewer features are generated:
     - Create additional features by:
       * Subdividing existing features into more specific aspects of `{brands}`.
       * Considering complementary market dimensions related to `{brands}`.
       * Adding regional or cultural variants, if applicable.
   * If more features are generated:
     - Remove excess features based on:
       * Lowest context relevance score (specifically for `{brands}`).
       * Highest correlation with other features.
       * Document removal justification.

2. **Feature Set Completeness Check:**
   * Ensure the total feature count equals `{attribute_count}`.
   * Each feature must be unique, measurable, and relevant to the provided `{brands}` information.
   * Document the rationale for selecting each feature.

### Feature Generation Rules
Based on the context classification and the given `{brands}`:

1. **Complete Context (Score = 100%):**
   * Generate quantitative features specific to the product attributes and price levels in `{brands}`.
   * Ranges for quantitative features must include:
     - Minimum value (documented source).
     - Maximum value (documented source).
     - Step size (justified by domain practice).

2. **Partial Context (Score > 50%):**
   * Generate mixed features:
     - Quantitative: For parameters explicitly available in `{brands}`.
     - Qualitative: For parameters with missing specifics in `{brands}`.
   * Link each quantitative feature to present parameters.

3. **Generic Context (Score ≤ 50%):**
   * Generate only qualitative features.
   * Use relative scales (e.g., Low/Medium/High).
   * Avoid numeric values.

### Feature Independence Validation
For each feature pair (Fi, Fj):
1. Check direct correlation potential.
2. Check mediation pathways.
3. If correlation > threshold:
   * Remove the dependent feature.
   * Document the reason for removal.

### Final Output Validation
1. **Feature Count Check:**
   * Verify the total feature count equals `{attribute_count}`.
   * Ensure no duplicate features or levels.
   * Never include "Price" or "Brand" as attributes or any sort of variants relating to these two

#### Input Examples

1. **Input:**
   - why_prompt: "Why are you considering purchasing an electric car?"
   - country: "USA"
   - brands: [{{"Model": "Tesla Model 3", "Price": "$35,000"}}, {{"Model": "Chevy Bolt", "Price": "$26,500"}}]
   - attribute_count: 8

   **Response:**
   {{
       "attributes": [
           "Model", "Range (miles)", "Charging time", "Battery capacity (kWh)", "Acceleration (0-60 mph)", "Top speed", "Federal tax incentives", "State-specific incentives"
       ]
   }}
Input:

why_prompt: "Why are you considering purchasing a toothbrush?"
country: "USA"
brands: [{{"Product": "Oral-B Pro 1000", "Price": "$49.99"}}, {{"Product": "Philips Sonicare 4100", "Price": "$39.99"}}]
attribute_count: 6
Response:
{{
    "attributes": [
        "Bristle softness", "Head size", "Handle design", "Manual or electric", "ADA Seal of Acceptance", "Bristle shape"
    ]
}}
Important Requirements:
 - Use the product and price information from {brands} as the primary context for feature generation.
 - Ensure each generated feature is relevant to the attributes of the product and price levels provided.
 - Do not include "Price" or "Brand" as an attribute.
 - Ensure that the generated features are aimed towards non-technical individuals and are concise and descriptive. Avoid
 using technical jargon or complex terms.
    Variables:
    {attribute_count}: Exact number of features
    {why_prompt}: Context question
    {country}: Geographic scope
    {brands}: Products of a brand and it's corresponding price levels


    {format_instructions}""")
PRODUCT_ATTRIBUTES_TEMPLATE = inspect.cleandoc("""

    You are a market research data scientist whose goal is to generate relevant orthogonal feature for a behavioural statement.
    First, perform a systematic context analysis by checking and explicitly documenting:

    1. Required Parameters Check:
       * List all domain-specific required parameters
       * Mark each as [Present/Absent] in the given context
       * Create a completeness score (% of required parameters present)

    2. Scope Definition:
       * Geographic: [Specific Location/Regional/Global]
       * Temporal: [Specific Period/General]
       * Target Audience: [Specific Segment/General Public]
       * Product/Service: [Specific/Category/Generic]

    3. Context Classification:
       * Complete Context: All required parameters present
       * Partial Context: Some parameters present
       * Generic Context: No specific parameters

    # Feature Count Validation
    1. Initial Feature Generation:
       * MUST generate exactly {attribute_count} features
       * If fewer features generated:
         - Generate additional features by:
           * Subdividing existing features into more specific aspects
           * Considering complementary market dimensions
           * Adding regional/cultural variants if applicable
       * If more features generated:
         - Remove excess features based on:
           * Lowest context relevance score
           * Highest correlation with other features
           * Document removal justification

    2. Feature Set Completeness Check:
       * Verify total feature count equals {attribute_count}
       * Each feature must be unique and measurable
       * Document feature selection rationale

    # Feature Generation Rules
    Based on the context classification:

    1. Complete Context (Score = 100%):
       * Generate quantitative features with specific ranges
       * Ranges must include:
         - Minimum value (documented source)
         - Maximum value (documented source)
         - Step size (justified by domain practice)

    2. Partial Context (Score > 50%):
       * Generate mixed features:
         - Quantitative: Only for parameters explicitly present
         - Qualitative: For parameters with missing specifics
       * Each quantitative feature must link to present parameters

    3. Generic Context (Score ≤ 50%):
       * Generate only qualitative features
       * Use relative scales (e.g., Low/Medium/High)
       * Avoid any numeric values

    # Feature Independence Validation
    For each feature pair (Fi, Fj):
    1. Check direct correlation potential
    2. Check mediation pathways
    3. If correlation > threshold:
       * Remove dependent feature
       * Document removal reason


    # Final Output Validation
    1. Feature Count Check:
       * Verify exactly {attribute_count} features generated
       * No duplicate features or levels allowed
       * Never include as an feature "Price" or "Brand"
       * Never include a feature that is akin to Price in any way.
    Example 1:
    why_prompt: "Why are you considering purchasing an electric car?"
    country: "USA"
    attribute_count: 8
    Response should be as follows:
        {{
        "attributes":
            [
            "Model", "Range (miles)", "Charging time", "Battery capacity (kWh)", "Acceleration (0-60 mph)", "Top speed", "Federal tax incentives", "State-specific incentives"
            ]
        }}

    Example 2:
    Input:
        why_prompt: "Why are you considering purchasing a toothbrush?"
        country: "USA"
        attribute_count: 6
    Response:
        {{
        "attributes": [
                    "Bristle softness", "Head size", "Handle design", "Manual or electric", "ADA Seal of Acceptance", "Bristle shape"
                    ]
        }}

    Example 3:
    Input:
        why_prompt: "What are key factor when selecting an Internet Service Provider?"
        country: "United States of America"
        attribute_count: 8
    Response:
        {{
            "attributes":
                [
                    "Availability", "Connection type", "Download speed", "Upload speed" "Data caps", "Reliability", "Customer service quality", "Contract terms"
                ]
        }}


    Important Requirements:
    1. Do not include any additional text or justification
    2. Never include "Price" or "Brand" as an attribute

    Variables:
    {attribute_count}: Exact number of features
    {why_prompt}: Context question
    {country}: Geographic scope


    {format_instructions}""")

PRODUCT_ATTRIBUTE_RANKING_TEMPLATE = inspect.cleandoc("""
    Given a list of attributes related to a specific entity or product in the why prompt, prioritize the attributes based on their importance or relevance to the why prompt.
    Rank the top {attribute_count} attributes from most important to least important, ensuring that each attribute is distinct, relevant to the why prompt, and as orthogonal (mutually exclusive or independent) as possible.

    Here's the input:
        why_prompt: {why_prompt}
        attributes: {attributes}
        attribute_count: {attribute_count}

    Important:
    1. Give the response in JSON format, as shown in the examples
    2. Only include the top {attribute_count} attributes in the response.
    3. DO NOT MODIFY ANY PART OF THE ATTRIBUTE NAMES OR THE OUTPUT FORMAT. ANY CHANGE, EVEN A SINGLE CHARACTER (including spaces, underscores, or special characters), WILL BREAK THE SYSTEM.
    4. Ensure the output omits any disclaimers, notes, or explanatory statements, and that attribute names and values are properly formatted without unnecessary escape characters, underscores, or special characters.
    {format_instructions}
    """)

PRODUCT_BRAND_TEMPLATE_SPECIFIC = inspect.cleandoc("""
    Evaluate the given why_prompt: {why_prompt} to identify the specific brand mentioned and provide a detailed list of real-world products from ONLY that brand corresponding to the attribute. Classify the products into levels based on quantitative measures, get only products whose price corresponds to "Mid-Range" products.

Instructions:
    - First, identify the specific brand mentioned in the why_prompt
    - Generate products ONLY from the identified brand
    - Define categories relative to the product or industry. For example:
      -- Mid-Range: Represents mid-tier products from this brand
    - Do not use any commas while mentioning value of the units. Instead of "27,000", use "27000"
    - Use the format [min, max] to show quantitative ranges specific to the product
    - Focus on {country} markets, using appropriate currency or measurement units (e.g., USD for the USA, INR for India)
    - Mention the unit scale if applicable (e.g., Million or Lakh)

Units and Context:
    Provide detailed and context-specific units for price or measurement scales. For example:
    Smartphones: Use "USD per device" instead of just "USD"
    Laptops: Use "USD per laptop" instead of just "USD"
    Specify the unit and context clearly for each product category to ensure clarity and relevance
    If products have specific capacity or features, include them in the unit description (e.g., "USD per iPad (256GB)")

General Principles:
    - Units must reflect common market practices in the {country} context
    - If a product has measurable specifications (e.g., storage, screen size), always link the price to those specifications
    - Each numerical value should explicitly describe what it measures in a practical context
    - Ensure all products listed are currently or recently available from the specified brand

Example:
Input:
why_prompt: "What features of Apple products make you want to buy them?"
attribute: "price"
country: "USA"

Response:
{{
    "units": "USD per device",
    "levels": {{
        "Mid-Range": {{
            "iPhone 13": [599, 799],
            "iPhone 14": [699, 899],
            "iPad Air": [599, 899],
            "iPad mini": [499, 799],
            "Apple Watch SE": [249, 299],
            "AirPods Pro": [249, 249],
            "MacBook Air M1": [999, 1249],
            "iMac 24-inch": [1299, 1499]
        }}
    }}
}}

Important:
    1. ONLY include products from the brand mentioned in the why_prompt
    2. Classify products into exactly one levels:"Mid-Range"
    3. Provide only the attribute name, units, and levels with products and their quantitative ranges in JSON format
    4. Avoid additional details, schema descriptions, or internal data structures
    5. Ensure valid formatting with correctly placed double quotes, commas, and braces
    6. Do not include any additional fields or explanations in the output
     7. **ALWAYS return at least 5 products; however, aim to include 8 to 9 products when available.**
     8. ONLY CONSIDER PRODUCTS RELATED TO THE WHY_PROMPT

Input:
why_prompt: {why_prompt}
attribute: {attribute}
country: {country}

Response:
""")

PRODUCT_BRAND_TEMPLATE_GENERAL = inspect.cleandoc("""
    Evaluate the given why_prompt: {why_prompt} and attribute: {attribute} to provide a detailed list of real-world BRANDS (not specific products) corresponding to the product category implied in the why_prompt. Classify the brands into levels based on their market positioning and typical price ranges, specifically we want only brands in the "Mid-Range"

Instructions:
    - Generate ONLY brand names, not specific products
    - Define categories relative to the brand's market positioning. Use only products with Mid-Range price.
      -- Mid-Range: Represents mainstream brands with moderate pricing
    - Do not use any commas while mentioning value of the units. Instead of "27,000", use "27000"
    - Use the format [min, max] to show typical price ranges for the brand's products
    - Focus on {country} markets, using appropriate currency or measurement units
    - Mention the unit scale if applicable (e.g., Million or Lakh)

Units and Context:
    Provide detailed and context-specific units that represent typical products from each brand. For example:
    Coffee brands: Use "USD per pound" instead of just "USD"
    Car brands: Use "USD per vehicle" instead of just "USD"
    Clothing brands: Use "USD per garment" for typical items
    Always specify the unit and context clearly for the product category

General Principles:
    - Units must reflect common market practices in the {country} context
    - Price ranges should represent the typical range of products offered by each brand
    - Each numerical value should represent the typical price range for standard products from that brand
    - Focus on well-known, established brands available in the specified country
Units must reflect common market practices in the {country} context.
    If a product has a measurable capacity or quantity (e.g., weight, volume, or count), always link the price to that measurement.
    Ensure that descriptions are clear, relevant, and practical for the specific product category, focusing on commonly understood metrics for the target market.
    Always avoid vague terms like "per unit" unless there is no other meaningful way to describe the product's quantity or packaging.
    Each numerical value should explicitly describe what it measures in a practical context, such as price per unit, distance per trip, or cost per duration. For example, replace '1000 USD for a flight' with '1000 USD for a business-class round trip from Los Angeles to Tokyo.' Every number should answer 'what is this describing?' to avoid ambiguity. Ambiguous figures without descriptive context should never be included in the response.
Example:
Input:
why_prompt: "What factors influence your coffee purchasing decisions?"
attribute: "price"
country: "USA"
Response:
{{
    "units": "USD per pound",
    "levels": {{
         "Mid-Range": {{
            "Starbucks": [10, 15],
            "Peet's Coffee": [12, 17],
            "Dunkin'": [9, 14],
            "Caribou Coffee": [11, 16],
            "Seattle's Best": [10, 14],
            "Lavazza": [13, 18],
            "illy": [14, 19],
            "Gevalia": [11, 15]
        }}
    }}
}}

Important:
    1. Only include BRAND names, not specific products
    2. Classify brands into exactly one levels: "Mid-Range"
    3. Provide only the attribute name, units, and levels with brands and their typical price ranges in JSON format
    4. Avoid additional details, schema descriptions, or internal data structures
    5. Ensure valid formatting with correctly placed double quotes, commas, and braces
    6. Do not include any additional fields or explanations in the output
    7. ONLY CONSIDER BRANDS RELATING TO THE WHY_PROMPT
Input:
why_prompt: {why_prompt}
attribute: {attribute}
country: {country}

Your respond should be formatted according to this:
{format_instructions}
""")
PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_PRODUCTS = inspect.cleandoc("""

  Given a why_prompt and a list of exactly 5 brand names, generate 1 real-world, mid-range product for each brand.
    
Instructions:
    - Only consider the 5 brands provided in the input list `brands`. Ignore any other brands mentioned in the `why_prompt`.
    - Generate 1 real product per brand, totaling exactly 5 products.
     - Each product must:
        • Be currently available on the market
        • Be representative of the brand
        • Belong to a **mid-range price** category for its type
        • Be semantically relevant to the `why_prompt` (e.g., if the prompt is about performance, pick performance-relevant products)

 - Format prices as integer values in USD without commas (e.g., use 1199 not 1,199).
    - Use specific product names (e.g., "Dell XPS 13" not just "Dell Laptop").
    - Ensure the output is valid JSON:
        • All strings must be enclosed in double quotes
        • No trailing commas
        • No extra fields or explanations outside the JSON

Examples:


Example 1:
Input:
    why_prompt: "Compare the gaming performance of devices from Asus, and HP."
    brands: ["Asus", "MSI", "Acer", "Lenovo", "HP"]
    attribute: "price"
    country: "USA"

Response:
{{
    "units": "USD per device",
    "levels": {{
        "Mid-Range": {{
            "Asus TUF Gaming F15": [899, 1099],
            "MSI GF63 Thin": [749, 949],
            "Acer Nitro 5": [799, 999],
            "Lenovo Legion 5": [899, 1099],
            "HP Victus 15": [749, 949]
        }}
    }}
}}

Example 2:
Input:
    why_prompt: "How do wearables from Garmin, Fitbit, and Amazfit differ in health tracking features?"
    brands: ["Garmin", "Fitbit", "Amazfit", "Huawei", "Withings"]
    attribute: "price"
    country: "USA"

Response:
{{
    "units": "USD per device",
    "levels": {{
        "Mid-Range": {{
            "Garmin Venu Sq 2": [249, 299],
            "Fitbit Versa 4": [199, 229],
            "Amazfit GTR 4": [199, 229],
            "Huawei Watch Fit 2": [129, 179],
            "Withings ScanWatch Light": [249, 299]
        }}
    }}
}}

Example 4 (5+ Brands):
Input:
    why_prompt: "Compare products from Apple, Google, Samsung, and Microsoft"
    brands: ["Apple", "Google", "Samsung", "Microsoft", "Dell"]
    attribute: "price"
    country: "USA"
Response:
{{
    "units": "USD per device",
    "levels": {{
         "Mid-Range": {{
            "Apple iPhone 13": [599, 799],
            "Google Pixel 7": [599, 699],
            "Samsung Galaxy S23": [799, 999],
            "Microsoft Surface Laptop": [899, 1099],
            "Dell XPS 13": [899, 1199]
        }}
    }}
}}

Important:
    1. **THERE NEEDS TO ALWAYS BE FIVE PRODUCTS RETURNED IN THE OUTPUT!!!**
    2. Only include real, currently available products.
    3. Classify products into exactly one level: "Mid-Range".
    4. Include specific product names (e.g., "iPhone 13" rather than just "iPhone").
    5.For large number do not use commas to seperate hundreths places so instaed of 43,000 it must be 43000 other the json format is going to be invalid
    6. Ensure valid JSON formatting with correctly placed double quotes, commas, and braces.
    7. Do not include any additional fields or explanations in the output.

Input:
    why_prompt: {why_prompt}
    brands: {brands}
    attribute: {attribute}
    country: {country}

Response:
""")


PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_BRANDS = inspect.cleandoc("""
    First identify all brands mentioned in the why_prompt: {why_prompt}, then provide a list of exactly 5 real-world BRANDS (not specific products) that must include the identified brands plus additional relevant competitors in the same product category. Classify these brands based on their market positioning and typical price ranges, specifically we want only brands in the "Mid-Range".

Instructions:
    - First, extract all brand names mentioned in the why_prompt
    - Include these extracted brands in your response
    - Add additional relevant competitor brands from the same industry to reach exactly 5 total brands
    - Generate ONLY brand names, not specific products
    - Define categories relative to the brand's market positioning. Use only products with Mid-Range price
      -- Mid-Range: Represents mainstream brands with moderate pricing
    - Do not use any commas while mentioning value of the units. Instead of "27,000", use "27000"
    - Use the format [min, max] to show typical price ranges for the brand's products
    - Focus on {country} markets, using appropriate currency or measurement units
    - Mention the unit scale if applicable (e.g., Million or Lakh)

Units and Context:
    Provide detailed and context-specific units that represent typical products from each brand. For example:
    Tech brands: Use "USD per device" instead of just "USD"
    Car brands: Use "USD per vehicle" instead of just "USD"
    Clothing brands: Use "USD per garment" for typical items
    Always specify the unit and context clearly for the product category

General Principles:
    - Units must reflect common market practices in the {country} context
    - Price ranges should represent the typical range of products offered by each brand
    - Each numerical value should represent the typical price range for standard products from that brand
    - Focus on well-known, established brands available in the specified country
    - MUST include all brands mentioned in the why_prompt
    - Complete the list to exactly 5 brands with relevant competitors

Example:
Input:
why_prompt: "What features of Apple and Google products affect consumer decisions?"
attribute: "price"
country: "USA"

Response:
{{
    "units": "USD per device",
    "levels": {{
         "Mid-Range": {{
            "Apple": [599, 1499],
            "Google": [499, 1299],
            "Samsung": [499, 1399],
            "Microsoft": [599, 1299],
            "Dell": [499, 1199]
        }}
    }}
}}

Important:
    1. Only include BRAND names, not specific products
    2. MUST include all brands mentioned in the why_prompt
    3. Complete the list to exactly 5 total brands
    4. Classify brands into exactly one level: "Mid-Range"
    5. Provide only the attribute name, units, and levels with brands and their typical price ranges in JSON format
    6. Avoid additional details, schema descriptions, or internal data structures
    7. Ensure valid formatting with correctly placed double quotes, commas, and braces
    8. Do not include any additional fields or explanations in the output


    MAKE SURE TO GENERATE RELEVANT BRANDS AND DO N OT HALLUCINATE BRANDS THAT ARE NOT RELATED TO THE WHY PROMPT.
Input:
why_prompt: {why_prompt}
attribute: {attribute}
country: {country}

Response:
""")

PRODUCT_ATTRIBUTE_LEVELS_TEMPLATE = inspect.cleandoc("""
    Generate attribute levels for {attribute} in {country} market for {brand_or_product}.
    
    Input:
    why_prompt: {why_prompt}
    attribute: {attribute}
    country: {country}
    price range: {min_price} {units} to {max_price} {units}
    product/brand: {brand_or_product}
    
    # Research Requirement
    CRITICAL: Research REAL market data for {brand_or_product} in {country}. All levels must be VERIFIABLE, existing options.
    
    # Level Generation Rules and Constraints
    For each feature type:
    
    1. Quantitative Levels:
       * Required parameters: Range boundaries, units, step size
       * Validation checks:
         - All parameters present
         - Range validity (within product offerings)
         - Step size consistency
       * Generate 5-7 levels:
         - Format: ALWAYS use "X-Y [units]" for ranges (e.g., "100-200 km")
         - Ensure NO OVERLAP between ranges
         - If range permits fewer levels:
           * Decrease step size to allow more divisions
         - If range too broad:
           * Adjust step size to create appropriate number of levels
    
    2. Qualitative Levels:
       * Use real options offered by {brand_or_product}
       * Ordinal scale preference with clear progression
       * Clear distinction between levels
       * Generate 5-7 distinct levels
    
    # Level Description Rules
    For each level description:
    1. Structure Requirements:
       * Format: [Key Feature] with [Benefit/Specification]
       * Maximum length: 40 characters
       * Must include quantifiable elements where possible
    
    2. Content Guidelines:
       * Primary Feature: Main distinguishing characteristic
       * Supporting Element: Must include at least one of:
         - Quantifiable metric (years, %, ratings)
         - Specific technology/feature
         - Clear benefit statement
         - Service/support detail
    
    3. Progression Rules:
       * Each level must show clear advancement from previous
       * Use industry-standard terminology
       * Include market-relevant specifications
       * Maintain consistent description pattern across levels
    
    4. Clarity Requirements:
       * Avoid technical jargon
       * Use market-standard terms
       * Include measurable differentiators
       * Reference local market standards when applicable
    
    # Level Count Validation
    For each feature F:
    1. Count generated levels N
    2. If N < 5:
       * For quantitative features:
         - Subdivide existing ranges while maintaining min/max bounds
         - Ensure step size remains consistent
    3. If N > 7:
       * Remove excess levels furthest from median/center
    
    # Level Description Validation
    For each level description:
    1. Check completeness:
       * Has primary feature
       * Has supporting element
       * Includes quantifiable metric where possible
    
    2. Verify differentiation:
       * Clear progression from previous level
       * Distinct value proposition
       * Measurable difference where applicable
    
    3. Validate clarity:
       * Understandable by target audience
       * Industry-standard terminology
       * Market-relevant metrics
    
    
    Examples:
    1. Federal Tax Incentives (USA, Tesla, Electric Vehicles):
       GOOD: {{"levels": ["0-1,500 USD", "1,501-3,000 USD", "3,001-4,500 USD", "4,501-6,000 USD", "6,001-7,500 USD"]}}
       BAD: {{"levels": ["0 USD", "3,750 USD", "7,500 USD", "Up to 7,500 USD"]}}
    
    2. Battery Range (Europe, BMW i4, Electric Vehicles):
       GOOD: {{"levels": ["250-300 km", "301-350 km", "351-400 km", "401-450 km", "451-500 km"]}}
       BAD: {{"levels": ["350 km", "Up to 400 km", "300+ km", "400-500 km range"]}}
    
    3. Display Types (USA, Dell, Laptops):
       GOOD: {{"levels": ["HD TN panel", "FHD IPS panel", "QHD IPS panel", "4K IPS panel", "4K OLED panel"]}}
       BAD: {{"levels": ["HD display", "Good display", "High-quality screen", "Premium display"]}}
    
        **IMPORTANT*** MAKE SURE THESE SPECIFICATIONS ARE MET:
        1) DO NOT INCLUDE THE LEVEL NUMBER YOU PLACE IT AT ONLY THE LEVEL (E.g DO NOT say "35-60 min (Level 2, 240V)" but instead "35-60 min 240V". Another bad example is: "3-5 hours (Level 2)" which should be replaced with "3-5 hours".
        "10-12 hours (Level 1, 120V)")
        2) DO NOT INCLUDE ANY MENTION TO THE SPECIFIC BRAND OR PRODUCT
        3) IF YOU USE ABBREVIATIONS IN ANY WAY MAKE SURE TO DEFINE THE FULL PHRASE (e.g instead of saying LLM say LLM (Large Language Model) to make sure people know what is meant)
        4) Ensure consistency in the description of levels. 
           If one level contains numerical value, then all levels should contain numerical values. e.g. if one level is "Costs $5 per month", then avoid using level description as "free of cost" and instead use "Costs $0 per month". When using ranges make sure to always define an upper and lower limit for the values, this means levels like "Under 20 minutes" is not good and should instead be "[lower_bound - 20 minutes]" where lower_bound is something you will have to estimate. If there's an upper limit make sure not to write something like "$4,001+ (High Incentive)" but instead "[4,001 - Upper_bound]" where Upper_bound is something you must estimate  
        5) If a level contains a numerical value, and a qualitative description of the numerical value, then ensure that the qualitative description is consistent across all levels.
           Avoid levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 ("]. 
           Instead, use levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 (Outstanding)"].
        6) When using ranges for numerical levels make sure that there is a space between the dashes and the numbers so [4-5] should be [4 - 5]
        7) ALWAYS QUANTIFY NUMERICAL VALUES IN SET RANGES, SO FOR EXAMPLE NEVER SAY "NUMERICAL VALUE"+ OR UNDER/OVER "NUMERICAL VALUE". If you have no reference point for what's below or above make up a reasonable assumption for the lower or upper bound.


    Generate response for the input above:
    {format_instructions}
    """)
PRODUCT_ATTRIBUTE_LEVELS_COMPLETE_TEMPLATE = inspect.cleandoc("""
    Generate a REAL PRODUCT from {brand_or_product} in {country} within the price range {min_price} {units} to {max_price} {units}, and specify ONE level for each attribute.
    
    Input:
    why_prompt: {why_prompt}
    attributes: {attributes}
    country: {country}
    price range: {min_price} {units} to {max_price} {units}
    product/brand: {brand_or_product}
    
    # Research Requirement
    CRITICAL: 
    1. Research and identify a REAL, SPECIFIC PRODUCT from {brand_or_product} available in {country} that falls within the price range of {min_price}-{max_price} {units}.
    2. For each attribute in {attributes}, specify exactly ONE level that accurately describes the identified product.
    3. The PRICE RANGE is the PRIMARY CONSTRAINT - the product MUST fit within this range.
    4. PUT ONLY LEVELS FOR ATTRIBUTES MENTIONED. DO NOT INCLUDE ADDITIONAL ATTRIBUTES ESPECIALLY BRAND AND PRICE!
    
    # Level Generation Rules
    For each attribute:
    
    1. Quantitative Attributes:
       * Provide a specific, precise value or narrow range that describes the real product
       * Use actual specifications from the product's documentation
       * Include appropriate units of measurement
       * Format: "X-Y [units]" for ranges or "X [units]" for specific values
    
    2. Qualitative Attributes:
       * Provide the specific configuration, option, or feature present in the real product
       * Use market-standard terminology
       * Be precise and specific to the actual product
    
    # Missing Data Handling - CRITICALLY IMPORTANT
    If exact specifications for an attribute cannot be found:
    1. NEVER leave any attribute blank or return null values
    2. Use informed extrapolation based on:
       * Similar products from the same brand in the price range
       * Industry standards for products in this category and price point
       * Comparable specifications from competitor products
       * Reasonable estimates based on the price point and other known attributes
    3. When extrapolating:
       * Use narrower ranges to indicate uncertainty (e.g., "15 - 17 hours" instead of "10 - 20 hours")
       * Maintain consistency with other known specifications
       * Ensure the extrapolated value is plausible for the price point
       * Use standard industry terminology consistent with the brand's marketing
    4. Indicate confidence in extrapolation by ensuring values are realistic and aligned with market norms
    
    # Level Description Requirements
    For each level description:
    1. Structure:
       * Maximum length: 40 characters
       * Include quantifiable elements where possible
    
    2. Content:
       * Must accurately reflect the actual product's specifications
       * Use industry-standard terminology
       * Include measurable differentiators
       * Reference actual product documentation
    
    3. Accuracy:
       * All levels must be VERIFIABLE against the product's actual specifications
       * Levels must be consistent with each other (describe the same product)
       * All attributes must correspond to the SAME physical product
    
    # Output Validation
    1. Price Validation:
       * Confirm the identified product falls within {min_price}-{max_price} {units}
       * If no product exists in this exact range then just return the range {min_price} - {max_price} {units}. 
        *** It is imperative to always return a price, if you can not find an exact value for the product just default to the range that was passed in. ****
    
    2. Attribute Coverage:
       * Ensure ALL attributes in {attributes} have exactly ONE corresponding level
       * Verify that each level is specific to the identified product
       * EVERY ATTRIBUTE MUST HAVE A VALUE - use informed extrapolation if necessary
    
    3. Product Verification:
       * Confirm the product is currently available in {country}
       * Verify the product is from {brand_or_product}
       * Ensure the product actually exists (not hypothetical)

    **IMPORTANT*** MAKE SURE THESE SPECIFICATIONS ARE MET:
    1) DO NOT INCLUDE THE LEVEL NUMBER YOU PLACE IT AT ONLY THE LEVEL (E.g DO NOT say "35-60 min (Level 2, 240V)" but instead "35-60 min 240V". Another bad example is: "3-5 hours (Level 2)" which should be replaced with "3-5 hours".
    "10-12 hours (Level 1, 120V)")

    3) IF YOU USE ABBREVIATIONS IN ANY WAY MAKE SURE TO DEFINE THE FULL PHRASE (e.g instead of saying LLM say LLM (Large Language Model) to make sure people know what is meant)

    4) Ensure consistency in the description of levels. 
       If one level contains numerical value, then all levels should contain numerical values. e.g. if one level is "Costs $5 per month", then avoid using level description as "free of cost" and instead use "Costs $0 per month". When using ranges make sure to always define an upper and lower limit for the values, this means levels like "Under 20 minutes" is not good and should instead be "[lower_bound - 20 minutes]" where lower_bound is something you will have to estimate. If there's an upper limit make sure not to write something like "$4,001+ (High Incentive)" but instead "[4,001 - Upper_bound]" where Upper_bound is something you must estimate  

    5) If a level contains a numerical value, and a qualitative description of the numerical value, then ensure that the qualitative description is consistent across all levels.
       Avoid levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 ("]. 
       Instead, use levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 (Outstanding)"].

    6) When using ranges for numerical levels make sure that there is a space between the dashes and the numbers so [4-5] should be [4 - 5]

    7) ALWAYS QUANTIFY NUMERICAL VALUES IN SET RANGES, SO FOR EXAMPLE NEVER SAY "NUMERICAL VALUE"+ OR UNDER/OVER "NUMERICAL VALUE". If you have no reference point for what's below or above make up a reasonable assumption for the lower or upper bound.

    # OUTPUT FORMAT - REQUIRED JSON STRUCTURE
    =====================================================
    CRITICAL: You MUST follow this exact JSON format:

    {{
        "attributes": {{
            "Attribute1": "Level1",
            "Attribute2": "Level2",
            ...,
            "AttributeN": "LevelN",
            "Price": "Price value in {units}"
        }},
        "product": "{brand_or_product}"
    }}

    FORMAT VALIDATION CHECKLIST:
    1. The output MUST be valid JSON with all quotes and braces properly closed
    2. "attributes" MUST be a dictionary containing ALL attributes from {attributes} plus "Price"
    3. "product" MUST be the exact value of {brand_or_product} - DO NOT change this value
    4. Price MUST be included within the "attributes" dictionary
    5. DO NOT create a separate "price" key outside of the "attributes" dictionary
    6. The price should be an exact value if possible (not a range) unless no specific product is found
    7. Each attribute MUST have exactly one value - NEVER leave an attribute empty
    8. NEVER include attributes that weren't in the original {attributes} list

    BEFORE SUBMITTING YOUR RESPONSE, CHECK:
    - Is every attribute from {attributes} included exactly once in the "attributes" dictionary?
    - Is "Price" included in the "attributes" dictionary?
    - Is the "product" value exactly equal to {brand_or_product}?
    - Is the JSON format valid with all quotes and braces properly closed?
    - Does every attribute have a non-empty value?
    =====================================================
    
    Examples of correct responses:
    
    Example 1:
    {{
        "attributes": {{
            "Display Type": "OLED HDR",
            "Screen Size": "55 - 65 inches",
            "Resolution": "4K (3840 x 2160)",
            "Smart Features": "Built-in voice assistant",
            "Audio System": "Dolby Atmos 2.1 channel",
            "Price": "4004 $"
        }},
        "product": "Apple"
    }}
    
    Example 2:
    {{
        "attributes": {{
            "Battery Life": "15 - 20 hours",
            "Weight": "1.5 - 1.8 kg",
            "Processor": "Intel Core i7 11th Gen",
            "RAM": "16 GB DDR4",
            "Storage": "512 GB SSD",
            "Price": "3000$"
        }},
        "product": "ASUS"
    }}

    Example 3:
    {{
        "attributes": {{
            "Engine": "2.0L Turbocharged 4-cylinder",
            "Horsepower": "241 - 255 HP",
            "Fuel Economy": "24 - 26 MPG (Miles Per Gallon)",
            "Transmission": "8-speed automatic",
            "Safety Features": "Adaptive cruise control",
            "Interior": "Leather upholstery",
            "Price": "42500 $"
        }},
        "product": "BMW"
    }}
    
    Example 4:
    {{
        "attributes": {{
            "Volume": "750 ml",
            "Formulation": "Bleach-free antibacterial",
            "Surface Types": "Multi-surface compatible",
            "Scent": "Lemon and thyme essential oils",
            "Eco-Friendly": "Biodegradable ingredients",
            "Application Method": "Spray applicator",
            "Price": "8.99 $"
        }},
        "product": "Method"
    }}
    
    ### OUTPUT FORMAT ###:
    RESPOND ONLY WITH VALID JSON. DO NOT INCLUDE ANY TEXT BEFORE OR AFTER THE JSON.
    DO NOT EVER RESPOND WITH ANYTHING BUT THE SPECIFIED JSON FORMAT. KEEP REASONING IMPLICIT.
    IT'S IMPORTANT THAT THE VALUE OF "product" KEY IS EXACTLY THE BRAND THAT IS PASSED INTO THE PROMPT.
    """)
PRODUCT_LEVELS_ALIGNMENT_TEMPLATE = inspect.cleandoc("""
    Transform the attribute levels for a single product to match standardized levels for multiple attributes.
    
    Input:
    product_data: {product_data}
    standardized_attributes: {standardized_attributes}
    
    # Task Description
    You are given:
    1. A SINGLE product with its current attributes and values
    2. A list of standardized attributes, each with its own set of standardized levels
    
    Your task is to:
    1. Keep the exact same structure as the input product_data
    2. For EACH attribute in the standardized_attributes list that exists in the product, replace the current attribute value with the closest matching standardized level
    
    # Processing Steps (FOLLOW PRECISELY)
    
    ## Step 1: Data Preparation
    - Parse the product_data to extract the product name and its attributes
    - Parse the standardized_attributes to create a reference mapping of each attribute name to its valid standardized levels
    - Parse the errors list to identify any previously reported errors for this product
    
    ## Step 2: Error Analysis
    - Before making any new mappings, examine the errors list {errors}
    - If errors list contains any errors for the CURRENT product you're processing:
      * Note exactly which attributes had errors
      * Understand the specific nature of each error (wrong level selection, invalid level format, etc.)
      * Pay special attention to these attributes during mapping
    
    ## Step 3: Attribute Level Matching
    For EACH attribute in the product that is also in standardized_attributes:
    1. Extract the current value for this attribute
    2. Find the EXACT matching standardized level from the standardized_attributes using the rules below
    3. Verify the selected level is EXACTLY one of the predefined levels for that attribute
    4. Replace ONLY this attribute's value with the matched standardized level
    5. Double-check the mapping is correct before proceeding to the next attribute
    
    # Level Matching Rules (Apply These Precisely)
    
    ## 1. Range-to-Range Matching (For numeric ranges with units)
    If current value is in format "X - Y units":
    1. Parse X and Y as numerical values
    2. For each standardized level in format "A - B units":
       * Parse A and B as numerical values
       * Calculate overlap between [X,Y] and [A,B]
       * Calculate center_XY = (X+Y)/2 and center_AB = (A+B)/2
    3. Select the standardized level with:
       * HIGHEST overlap between ranges
       * If multiple have same overlap, choose the one where |center_XY - center_AB| is minimized
    4. The selected level MUST be an EXACT COPY of one of the standardized levels (same format, spacing, units)
    
    ## 2. Single Value Assignment (For single numeric values with units)
    If current value is in format "Z units":
    1. Parse Z as a numerical value
    2. For each standardized level in format "A - B units":
       * Parse A and B as numerical values
    3. SELECT EXACTLY ONE standardized level using these criteria (in order):
       a. The level where A ≤ Z ≤ B
       b. If Z fits in multiple ranges, choose where |Z - (A+B)/2| is minimized
       c. If Z is outside all ranges, choose the level where |Z - A| or |Z - B| is minimized
    4. The selected level MUST be an EXACT COPY of one of the standardized levels
    
    ## 3. Complex Value Extraction (For values with additional specifications)
    If current value has additional specifications (e.g., "10 hours (240V)"):
    1. Extract the core numerical component (e.g., "10 hours")
    2. Apply rule #1 or #2 based on the extracted component
    3. Use ONLY the standardized level format in the output (do not add back the specifications)
    
    ## 4. Text/Categorical Value Matching
    If current value is non-numerical:
    1. Compare the semantic meaning with each standardized level
    2. Select the standardized level with closest semantic equivalence
    3. For exact matches or near synonyms (e.g., "All-wheel drive" and "AWD"), use the exact standardized level text
    4. The selected level MUST be an EXACT COPY of one of the standardized levels
    
    # Critical Validation Steps
    
    ## BEFORE finalizing each attribute mapping:
    1. Check: Is the selected level EXACTLY identical to one of the standardized levels for this attribute?
       * Verify character-by-character including spacing, hyphens, units, and capitalization
    2. Check: Is the selected level the most appropriate match according to the rules above?
    3. Check: If this attribute had previous errors, have they been addressed?
    
    ## AFTER completing all mappings:
    1. Review the entire response to ensure:
       * Every attribute from the original product is present
       * Every mapped attribute now uses an EXACT standardized level
       * The product name and structure match the input
    
    # Error Handling Protocol
    
    ## If {errors} lists "NONE":
    * Proceed normally with mapping based on the rules above
    
    ## If {errors} contains specific error entries:
    * Format of error entries: "ERROR: Product [product_name], Attribute [attribute_name]: [error_description]"
    * For each attribute in the current product that matches an error entry:
      1. Read the error description carefully
      2. Apply special attention to resolving that specific issue
      3. Double-check your mapping for that attribute against the standardized levels
    
    * Common error types and solutions:
      1. "Level not in standardized list": Ensure you select ONLY from the predefined levels
      2. "Incorrect range selected": Review overlap and center distance calculations
      3. "Format mismatch": Ensure exact character-by-character matching with standardized level
    
    * If an error references a product or attribute you are NOT currently processing:
      - Ignore that specific error entry
      - Only address errors relevant to the current product and attributes
    
    # Examples
    
    Input:
    
    Product Data:
    {{"Tesla Model Y": {{"Range (miles)": "330 miles", "Charging time": "10 hours (240V)", "Battery capacity (kWh)": "75 kWh", "Max power (kW)": "220 kW", "Acceleration (0-60 mph)": "4.8 seconds", "Drive type": "All-wheel drive", "Fast charging": "Supercharger compatible"}}}}
    
    Standardized Attributes:
    [
      {{
        "attribute": "Range (miles)",
        "levels": ["200 - 250 miles", "250 - 300 miles", "300 - 350 miles"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Charging time",
        "levels": ["4 - 6 hours", "6 - 8 hours", "8 - 10 hours", "10 - 12 hours"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Battery capacity (kWh)",
        "levels": ["55 - 65 kWh", "65 - 75 kWh", "75 - 85 kWh", "85 - 95 kWh"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Max power (kW)",
        "levels": ["150 - 200 kW", "200 - 250 kW", "250 - 300 kW", "300 - 350 kW"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Acceleration (0-60 mph)",
        "levels": ["3 - 4 seconds", "4 - 5 seconds", "5 - 6 seconds", "6 - 7 seconds", "7 - 8 seconds"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Drive type",
        "levels": ["Front-wheel drive", "Rear-wheel drive", "All-wheel drive"],
        "attribute_type": "categorical"
      }},
      {{
        "attribute": "Fast charging",
        "levels": ["Standard DC fast charging", "High-speed DC fast charging", "Ultra-fast DC charging"],
        "attribute_type": "categorical"
      }}
    ]
    
    Output:
    {{
      "product_data": {{"Tesla Model Y": {{"Range (miles)": "300 - 350 miles", "Charging time": "10 - 12 hours", "Battery capacity (kWh)": "75 - 85 kWh", "Max power (kW)": "200 - 250 kW", "Acceleration (0-60 mph)": "4 - 5 seconds", "Drive type": "All-wheel drive", "Fast charging": "High-speed DC fast charging"}}}}
    }}
    
    # Example 2 (With Error)
    
    Input:
    
    Product Data:
    {{"Honda Prologue": {{"Range (miles)": "283 - 308 miles", "Charging time": "5 - 7 hours (240V charger at 32A)", "Battery capacity (kWh)": "85 kWh"}}}}
    
    Standardized Attributes:
    [
      {{
        "attribute": "Range (miles)",
        "levels": ["250 - 300 miles", "300 - 350 miles", "350 - 400 miles"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Charging time",
        "levels": ["4 - 6 hours", "6 - 8 hours", "8 - 10 hours"],
        "attribute_type": "non-monetary"
      }}
    ]
    
    Errors: "ERROR: Product Honda Prologue, Attribute Range (miles): Selected level '250 - 300 miles' is incorrect. The value '283 - 308 miles' has greater overlap with '300 - 350 miles'."
    
    Correct Output:
    {{
      "product_data": {{"Honda Prologue": {{"Range (miles)": "300 - 350 miles", "Charging time": "6 - 8 hours", "Battery capacity (kWh)": "85 kWh"}}}}
    }}
    
    # Example 3 (Error for a different product)
    
    Input:
    
    Product Data:
    {{"Toyota bZ4X": {{"Range (miles)": "252 miles", "Charging time": "9.5 hours", "Battery capacity (kWh)": "72.8 kWh"}}}}
    
    Standardized Attributes:
    [
      {{
        "attribute": "Range (miles)",
        "levels": ["200 - 250 miles", "250 - 300 miles", "300 - 350 miles"],
        "attribute_type": "non-monetary"
      }},
      {{
        "attribute": "Charging time",
        "levels": ["4 - 6 hours", "6 - 8 hours", "8 - 10 hours", "10 - 12 hours"],
        "attribute_type": "non-monetary"
      }}
    ]
    
    Errors: "ERROR: Product Honda Prologue, Attribute Range (miles): Selected level '250 - 300 miles' is incorrect. The value '283 - 308 miles' has greater overlap with '300 - 350 miles'."
    
    Correct Output:
    {{
      "product_data": {{"Toyota bZ4X": {{"Range (miles)": "250 - 300 miles", "Charging time": "8 - 10 hours", "Battery capacity (kWh)": "72.8 kWh"}}}}
    }}
    
    ### CRITICAL REMINDERS ###
    
    1. ONLY SELECT LEVELS THAT ARE EXACTLY IDENTICAL to one of the standardized levels provided
    2. For ranges, carefully calculate overlap and center distances to find the best match
    3. For attributes not in the standardized list, leave the original value unchanged
    4. CHECK YOUR WORK CAREFULLY - especially for attributes that had previous errors
    5. The output must use THE EXACT SAME STRUCTURE as the input product_data
    
    ### ERRORS TO CHECK ###
    
    Below is a list of previous errors. If any errors relate to the CURRENT product and attributes you are processing, use them to guide your corrections:
    
    {errors}
    
    If it says "NONE", then there are no previous errors. Otherwise, look for errors that match the product name and attributes you are currently processing.
    
    Remember: ONLY address errors that are relevant to your CURRENT product and attributes. Ignore errors about other products or attributes.
    
    Respond with a JSON FORMAT as follows:
    {format_instructions}
    """)

PRODUCT_ATTRIBUTE_LEVEL_SELECTION_TEMPLATE = inspect.cleandoc("""
    Transform the existing attribute levels for {attribute} into optimized levels for a stated preference survey about the causal question: {why_prompt} in the {country} market.
    
    Input:
    why_prompt: {why_prompt}
    attribute: {attribute}
    country: {country}
    existing_levels: {all_levels}
    
    # Transformation Task
    Transform the provided existing levels for {attribute} into EXACTLY {level_count} optimized levels that would be more suitable for a stated preference survey, while preserving the essence of the original data.
    
    # Level Transformation Criteria
    
    1. Maintain Range Integrity:
       * Preserve the overall range of the original levels
       * Keep minimum and maximum boundaries similar to original data
       * Ensure transformed levels cover the same conceptual space
    
    2. Smooth Distribution:
       * Create evenly distributed intervals between levels
       * Eliminate clustering or gaps in the level progression
       * Balance the spacing for better experimental design
    
    3. Standardize Format:
       * For numerical attributes:
         - Use consistent range expressions: "X-Y [units]", make sure numerical values are always put in ranges.
         - Ensure ranges don't overlap
         - Apply uniform increments when appropriate
       * For categorical attributes:
         - Use parallel construction in descriptions
         - Maintain consistent phrasing and terminology
         - Limit level descriptions to maximum 40 characters
    
    4. Enhance Differentiation:
       * Make levels clearly distinguishable from one another
       * Ensure sufficient conceptual distance between adjacent levels
       * Remove ambiguity between similar levels
    
    5. Preserve Market Relevance:
       * Keep levels anchored to actual market offerings
       * Maintain connection to commercial reality
       * Respect important threshold points in the market
    
    # Validation Requirements
    
    1. Count Validation:
       * MUST produce EXACTLY {level_count} levels, matching the input count
    
    2. Range Validation:
       * Verify the transformed levels span approximately the same range as originals
       * Check that minimum and maximum bounds are preserved
    
    3. Format Consistency:
       * Ensure all transformed levels follow the same formatting pattern
       * Verify levels follow the appropriate format for the attribute type
    
    4. Relevance Check:
       * Confirm transformed levels still represent meaningful options in the market
       * Ensure levels are relevant to the causal question being studied
    
    # Transformation Examples
    
    Example 1 - Battery Range (Electric Vehicles):
    Existing Levels: ["105-142 km", "198-253 km", "287-339 km", "402-437 km", "490-545 km"]
    Transformed Levels: {{"levels": ["100-150 km", "200-250 km", "300-350 km", "400-450 km", "500-550 km"]}}

    Example 2 - Coffee Roast Levels:
    Existing Levels: ["Very Light", "Medium Light", "Medium", "Dark-ish", "Extra Dark"]
    Transformed Levels: {{"levels": ["Light", "Medium-Light", "Medium", "Medium-Dark", "Dark"]}}
    


    **IMPORTANT*** MAKE SURE THESE SPECIFICATIONS ARE MET:
        1) DO NOT INCLUDE THE LEVEL NUMBER YOU PLACE IT AT ONLY THE LEVEL (E.g DO NOT say "35-60 min (Level 2, 240V)" but instead "35-60 min 240V". Another bad example is: "3-5 hours (Level 2)" which should be replaced with "3-5 hours".
        "10-12 hours (Level 1, 120V)")
        2) DO NOT INCLUDE ANY MENTION TO THE SPECIFIC BRAND OR PRODUCT
        3) IF YOU USE ABBREVIATIONS IN ANY WAY MAKE SURE TO DEFINE THE FULL PHRASE (e.g instead of saying LLM say LLM (Large Language Model) to make sure people know what is meant)
        4) Ensure consistency in the description of levels. 
           If one level contains numerical value, then all levels should contain numerical values. e.g. if one level is "Costs $5 per month", then avoid using level description as "free of cost" and instead use "Costs $0 per month". When using ranges make sure to always define an upper and lower limit for the values, this means levels like "Under 20 minutes" is not good and should instead be "[lower_bound - 20 minutes]" where lower_bound is something you will have to estimate. If there's an upper limit make sure not to write something like "$4,001+ (High Incentive)" but instead "[4,001 - Upper_bound]" where Upper_bound is something you must estimate  
        5) If a level contains a numerical value, and a qualitative description of the numerical value, then ensure that the qualitative description is consistent across all levels.
           Avoid levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 ("]. 
           Instead, use levels like: "levels": ["1 - 2 (Below Average)","3 - 4 (Average)","5 - 6 (Above Average)","7 - 8 (Excellent)","9 - 10 (Outstanding)"].
        6) When using ranges for numerical levels make sure that there is a space between the dashes and the numbers so [4-5] should be [4 - 5]
        7) ALWAYS QUANTIFY NUMERICAL VALUES IN SET RANGES, SO FOR EXAMPLE NEVER SAY "NUMERICAL VALUE"+ OR UNDER/OVER "NUMERICAL VALUE". If you have no reference point for what's below or above make up a reasonable assumption for the lower or upper bound.

    Respond with a JSON FORMAT as follows:
    {format_instructions}
    """)
QUESTION_GENERATION_TEMPLATE = inspect.cleandoc("""
    Generate two distinct questions to assess the impact of the identified factors based on the user's information, "Why Prompt", and relevant history or constraints. Provide five sample responses for each question that can be used to quantify each factor's influence on the user's decision-making process. Ensure the questions and responses account for the factor type (history or constraints) when formulating them.

    Definitions:
    - History: Factors related to the user's past experiences, established patterns, and previous behaviors. These should reflect actual events or habits that have already occurred, not hypothetical or future scenarios.
    - Constraints: Factors that currently influence or limit the user's decision-making process. These should focus on present circumstances or conditions that affect the user's choices or actions.

    Instructions:
    - Adhere to the response format given in the example

    Example:
    Input:
    Why Prompt: "How do political advertisements or campaigns impact your voting decisions?"
    User Profile Attributes: {{
        "Education levels": ["High School", "Bachelors Degree", "Masters Degree", "Doctor of Philosophy"],
        "Income Levels": [30000, 300000],
        "Age Levels": [18, 70],
        "Genders": ["Male", "Female"]
    }}
    Model: "history"
    Factor: ""Voting Record""

    Response:
    [
    {{
        "question": "How consistently have you voted in past elections?",
        "sample_responses": [
        {{"response": "Every election"}},
        {{"response": "Most elections"}},
        {{"response": "Major elections only"}},
        {{"response": "Once or twice"}},
        {{"response": "Never voted"}}
        ]
    }},
    {{
    "question": "How consistently have you voted for the same political party in past elections?",
    "sample_responses": [
        {{"response": "Always same party"}},
        {{"response": "Mostly same party"}},
        {{"response": "Equal party switching"}},
        {{"response": "Occasional consistency"}},
        {{"response": "Never repeat party"}}
        ]
    }}
    ]

    Input:
        Question: {why_prompt}
        User Profile Attributes: {user_profile_attributes}
        Model: {model}
        Attribute : {dimension}
    Response:

    {format_instructions}
    """)

HISTORY_PROMPT_TEMPLATE = inspect.cleandoc("""
    Analyze the user's information, the provided question and attributes to identify relevant historical factors. Your task is to determine the experiences and conditions that have shaped the user's perspective. Generate history dimensions that cover diverse aspects of the user's past, ensuring each dimension is distinct and non-overlapping in meaning or context.

    Format your response as JSON, using concise one or two-word attributes. Based on the provided information, determine dimensions and their corresponding descriptions for the user's history.

    Critical Note:
    1. The 'history' section must focus exclusively on the user's past experiences and established patterns.
    2. Do not include hypothetical, aspirational, or speculative information. For instance, "Potential future purchases," "Desired features," or "Theoretical considerations" are not to be included as they do not reflect actual past experiences.
    3. Utilize the attributes information provided to gain additional perspective on the question. Avoid generating history dimensions that are already included in the attributes.
    4. "Income Levels" and "Age Levels" should be treated as ranges (e.g., In the Example 1: $80,000-$120,000 for income, 30-35 for age). Genders should be interpreted as distinct categories ("Male," "Female"). Focus exclusively on the user's past experiences for historical factors—do not include speculative or future-based information.

    Example 1:
    Question: "Why are you considering purchasing an electric vehicle?"
    User Profile Attributes: {{
        "Education levels": ["High School Diploma", "Bachelors Degree", "Masters Degree", "PhD Degree"],
        "Income Levels": [80000, 120000],
        "Age Levels": [30, 35],
        "Genders": ["Male", "Female"]
    }}
    Attributes: {{
    "Range",
    "Price",
    "Charging Time",
    "Battery Capacity",
    "Efficiency"
    }}
    Response: {{
    "history": {{
        "Driving Pattern": "The user's typical daily driving distance and habits.",
        "Fuel Costs": "The user's historical spending on fuel for their vehicles.",
        "Maintenance Costs": "The user's past expenses related to vehicle upkeep and repairs.",
        "Vehicle Satisfaction": "The user's level of satisfaction with their previous vehicles.",
        "Usage Pattern": "The user's annual mileage and how they typically use their vehicle.",
        "Driving Experience": "The number of years the user has been driving.",
        "Vehicle-related Activities": "The user's involvement in car-related hobbies or interests.",
        "Vehicle Type": "The types of vehicles the user has owned or primarily used in the past.",
        "Purchase Experience": "The user's history of vehicle purchases, including frequency and types."
        }}
    }}

    Example 2:
    Question: "What factors influence your decision when purchasing shampoo?"
    User Profile Attributes: {{
        "Education levels": ["High School Diploma", "Bachelors Degree"],
        "Income Levels": [20000, 50000],
        "Age Levels": [25, 30],
        "Genders": ["Female"]
    }}
    Attributes: {{
    "Ingredients",
    "Type",
    "Price",
    "Scent",
    "Conditioning"
    }}

    Response: {{
    "history": {{
        "Purchase Channels": "The places or methods through which the user typically buys shampoo (e.g., supermarkets, online, salons).",
        "Price Sensitivity": "The user's historical responsiveness to shampoo prices and willingness to pay.",
        "Shampoo Brands": "The specific shampoo brands the user has used in the past.",
        "Brand Loyalty": "The user's tendency to stick with particular shampoo brands over time.",
        "Purchase Frequency": "How often the user typically buys shampoo."
        }}
    }}

    Example 3:
    Question: "Why do you prefer a specific brand of toothpaste?"
    User Profile Attributes: {{
        "Education levels": ["Primary School", "High School", "Bachelors Degree"],
        "Income Levels": [20000, 50000],
        "Age Levels": [25, 40],
        "Genders": ["Female"]
    }}
    Attributes: {{
    "Dental Benefits",
    "Taste",
    "Ingredients",
    "Price",
    "Packaging"
    }}
    Response: {{
        "history": {{
        "Purchase Location": "The places where the user typically buys toothpaste.",
        "Brand Loyalty": "The user's tendency to consistently choose a particular toothpaste brand over time.",
        "Satisfaction Level": "The user's historical satisfaction with different toothpaste brands.",
        "Purchase Influences": "Factors that have historically influenced the user's toothpaste buying decisions.",
        "Experience with Offers/Discounts": "The user's past interactions with promotional offers or discounts on toothpaste.",
        "Brand Used": "The specific toothpaste brands the user has used in the past."
        }}
    }}

    Example 4:
    Question: "How do advertisements on social media affect your perception of brands?"
    User Profile Attributes: {{
        "Education levels": ["High School", "Bachelors Degree", "Masters Degree"],
        "Income Levels": [80000, 120000],
        "Age Levels": [22, 45],
        "Genders": ["Male", "Female"]
    }}
    Attributes: {{
        "Ad relevance",
        "Ad targeting",
        "Brand Image",
        "Ad frequency",
        "User sentiment"
    }}
    Response: {{
       "history": {{
        "Digital Literacy": "The user's familiarity with navigating digital environments and discerning between different types of online content.",
        "Brand Interactions": "The user's history of interacting with brands on social media.",
        "Online Shopping": "The user's past experiences with online purchases influenced by social media ads.",
        "Ad Exposure": "The frequency and types of advertisements the user encounters on social media.",
        "Consumer Behavior": "The user's purchasing habits and decision-making processes as influenced by digital platforms.",
        "Social Media Usage": "The platforms the user frequently engages with and the extent of their activity."
        }}
    }}

    Example 5:
    Question: "How do political advertisements or campaigns impact your voting decisions?"
    User Profile Attributes: {{
        "Education levels": ["High School", "Bachelors Degree", "Masters Degree", "Doctor of Philosophy"],
        "Income Levels": [30000, 300000],
        "Age Levels": [18, 70],
        "Genders": ["Male", "Female"]
    }}
    "Attributes": {{
    "Campaign Message",
    "Candidate Position",
    "Policy Alignment",
    "Campaign Strategy",
    "Campaign Effectiveness"
    }}
    Response: {{
        "history": {{
        "Media Consumption": "The user's patterns of consuming news, social media, and other forms of media, particularly related to politics.",
        "Party Affiliation": "The political parties the user has historically supported or aligned with.",
        "Civic Education": "The user's educational background in civics, political science, or related subjects, influencing their political views.",
        "Voting Record": "The user's history of voting in elections, including frequency and consistency.",
        "Campaign Exposure": "The user's past exposure to political campaigns, including advertisements and rallies.",
        "Political Engagement": "The user's involvement in political activities, such as campaigning, protests, or donations."
        }}
    }}


    Here's the input:
    Question: {why_prompt}
    User Profile Attributes: {user_profile_attributes}
    Attributes: {attributes}

    Provide your response in JSON format, adhering to the structure in the sample examples. Exclude any schema details, metadata, or validation rules from the output. The response should consist solely of history attributes, focusing on the user's past experiences.
    {format_instructions}

    Response:
    """)

CONSTRAINTS_PROMPT_TEMPLATE = inspect.cleandoc("""
    Analyze the user's information, the given question and attributes to identify relevant constraint factors. Your task is to determine the elements that limit or influence the user's decision-making process. Generate constraint dimensions that cover diverse aspects of the decision-making context, ensuring each dimension is distinct and non-overlapping in meaning or context.

    Format your response as JSON, using concise one or two-word attributes. Based on the provided information, determine subfactors and their corresponding values for constraints.

    Critical Notes:
    1. Utilize the attributes information provided to gain additional perspective on the question. Avoid generating constraints that are already included in the attributes.
    2. Focus on factors related to the decision-making process itself, rather than specific features or user profile information. For instance, if the question involves purchasing an electric vehicle, focus on factors such as budget, infrastructure compatibility, and availability of incentives rather than specific car features like battery capacity, efficiency etc.
    3. Ensure constraints are relevant to the question and user context.
    4. Utilize the attributes information provided to gain additional perspective on the question. Avoid generating history dimensions that are already included in the attributes.

    Example 1:
    Question: "Why are you considering purchasing an electric vehicle?"
    User Profile Attributes: {{
        "Education levels": ["High School Diploma", "Bachelors Degree", "Masters Degree", "PhD Degree"],
        "Income Levels": [80000, 120000],
        "Age Levels": [30, 35],
        "Genders": ["Male", "Female"]
    }}
    Attributes: {{
    "Range",
    "Price",
    "Charging Time",
    "Battery Capacity",
    "Efficiency"
    }}

    Response: {{
        "constraints": {{
            "Resale Value": "The expected future value of the vehicle if the user decides to sell it.",
            "Government Incentives": "Available tax breaks or subsidies for purchasing electric vehicles.",
            "Charging Infrastructure": "The availability and accessibility of charging stations in the user's area.",
            "Recycle Value": "The potential for recycling the vehicle's components at the end of its life.",
            "Budget": "The financial limitations that affect the user's ability to purchase an electric vehicle.",
            "Environmental Impact": "The user's consideration of the vehicle's effect on the environment."
            }}
        }}

    Example 2:
    Question: "What factors influence your decision when purchasing shampoo?"
    User Profile Attributes: {{
        "Education levels": ["High School Diploma", "Bachelors Degree"],
        "Income Levels": [20000, 50000],
        "Age Levels": [25, 30],
        "Genders": ["Female"]
    }}
    attributes: {{
    "Ingredients",
    "Type",
    "Price",
    "Scent",
    "Conditioning"
    }}
    Response: {{
        "constraints": {{
            "Scalp Sensitivity": "Considerations for any skin reactions or allergies.",
            "Brand Loyalty": "User's tendency to stick with familiar or preferred brands.",
            "Budget": "Financial limitations affecting shampoo purchase decisions.",
            "Shopping Convenience": "Ease of purchasing the shampoo in terms of location and time.",
            "Packaging Preferences": "User's preferences for bottle design, size, or eco-friendliness.",
            "Product Availability": "The ease of finding and purchasing the desired shampoo.",
            "Hair Care Considerations": "Specific hair needs that influence shampoo choice."
        }}
    }}

    Example 3:
    Question: "Why do you prefer a specific brand of toothpaste?"
    User Profile Attributes: {{
        "Education levels": ["Primary School", "High School", "Bachelors Degree"],
        "Income Levels": [20000, 50000],
        "Age Levels": [25, 40],
        "Genders": ["Female"]
    }}
    attributes: {{
    "Dental Benefits",
    "Taste",
    "Ingredients",
    "Price",
    "Packaging"
    }}
    Response: {{
        "constraints": {{
            "Taste Preference": "User's liking for certain flavors or taste experiences.",
            "Dentist Recommendation": "Influence of professional advice on toothpaste choice.",
            "Packaging Preference": "User's preferences for tube design, size, or eco-friendliness.",
            "Sensitivity": "Considerations for teeth or gum sensitivity.",
            "Budget": "Financial considerations affecting toothpaste purchase decisions.",
            "Brand Reputation": "The perceived quality and reliability of the toothpaste brand.",
            "Oral health needs": "Specific dental requirements that influence toothpaste choice."
        }}
    }}

    Example 4:
    Question: "How do advertisements on social media affect your perception of brands?"
    User Profile Attributes: {{
        "Education levels": ["High School", "Bachelors Degree", "Masters Degree"],
        "Income Levels": [80000, 120000],
        "Age Levels": [22, 45],
        "Genders": ["Male", "Female"]
    }}
    attributes: {{
        "Ad relevance",
        "Ad targeting",
        "Brand Image",
        "Ad frequency",
        "User sentiment"
    }}
    Response: {{
        "constraints": {{
            "Information Overload": "Excessive ad content leading to difficulty in processing.",
            "Brand Perception": "How social media ads shape overall views of a brand.",
            "Platform Credibility": "Trust in the social media platform hosting the ads.",
            "Ad Fatigue": "Overexposure to ads leading to diminished effectiveness.",
            "Ad Transparency": "Clarity about the nature and source of advertising content.",
            "Advertisement Impact": "The effectiveness of ads in influencing brand perception.",
            "Trust in Ad content": "The level of believability of the information presented in ads."
        }}
    }}

    Example 5:
    Question: "How do political advertisements or campaigns impact your voting decisions?"
    User Profile Attributes: {{
        "Education levels": ["High School", "Bachelors Degree", "Masters Degree", "Doctor of Philosophy"],
        "Income Levels": [30000, 300000],
        "Age Levels": [18, 70],
        "Genders": ["Male", "Female"]
    }}
    "attributes": {{
    "Campaign Message",
    "Candidate Position",
    "Policy Alignment",
    "Campaign Strategy",
    "Campaign Effectiveness"
    }}
    Response: {{
        "constraints": {{
            "Emotional Appeal": "The power of advertisements to evoke feelings that influence voting decisions.",
            "Emotional Appeals": "Impact of emotionally charged campaign messages on decision-making.",
            "Trustworthiness": "Level of confidence in the candidate's integrity and promises.",
            "Time Constraints": "Limited time available for researching candidates and issues.",
            "Media Bias": "Influence of media outlet preferences on political information.",
            "Party Affiliation": "Influence of longstanding political party loyalty.",
            "Credibility": "Perceived truthfulness and reliability of the campaign or advertisement.",
            "Fact-checking Ability": "Capacity to verify claims made in political advertisements.",
            "Information Accessibility": "Ease of obtaining comprehensive information about candidates and policies."
        }}
    }}

    Input:
        Question: {why_prompt}
        User Profile Attributes: {user_profile_attributes}
        Attributes: {attributes}

    Provide your response in JSON format, adhering to the structure in the sample examples. Exclude any schema details, metadata, or validation rules from the output. The response should consist solely of history attributes, focusing on the user's past experiences.
    {format_instructions}
    Response:

    """)

MOTIVATION_TEMPLATE = inspect.cleandoc("""

     OBJECTIVE:
    Generate psychologically valid latent constructs that causally explain user preferences for {why_prompt}, given a set of observed attributes and user demographics.

    INPUT SCHEMA:
    1. Question: {why_prompt}
    2. User Profile Attributes: {user_profile_attributes}
       - age_levels: [min_age, max_age]
       - income_levels: [min_income, max_income]
       - education_levels: [str] where each str represents a different education level present in our population
       - gender_levels: [str] where each str represents a different gender level present in our population
       - race: [str] where each str represents a different race present in our population
    3. Survey Attributes: {attributes}
       - List of observed preference attributes
    4. Country: {country}
       - Cultural context for construct generation
    5. Number of probing questions: {num_questions}
        - This is the number of probing questions you must generate

    MATHEMATICAL FRAMEWORK:
    Let A = {{a₁, a₂, ..., aₙ}} be the set of attributes
    Let P = {{p₁, p₂, p₃}} be the set of psychological constructs where:
    1. ∀pᵢ ∈ P: pᵢ is causally upstream of some subset of A
    2. ∀aⱼ ∈ A: ∃pᵢ ∈ P such that pᵢ → aⱼ (every attribute has at least one parent construct)
    3. ∀pᵢ,pⱼ ∈ P: Correlation(pᵢ,pⱼ) ≈ 0 (orthogonality constraint)

    CONSTRUCT GENERATION PROCEDURE:
    1. Factor Analysis:
       a. Group attributes into clusters based on shared underlying psychological drivers
       b. Identify the common causal psychological construct factor for each cluster
       c. Ensure psychological construct factors are maximally independent
    2. Cultural Calibration:
       a. Adjust construct interpretations based on country-specific cultural norms
       b. Consider cultural dimensions (e.g., individualism-collectivism, power distance)

    3. Demographic Alignment:
       a. Verify psychological constructs are appropriate for the user profile age range
       b. Adjust for income-level considerations
       c. Account for education-level effects
       d. Consider gender-specific psychological patterns if relevant
       e. Account for race-level effects if relevant

    CONSTRUCT VALIDATION CRITERIA:
    1. Causality:
       - Psychological construct must be a clear causal parent of its associated attributes
       - Relationship must be theoretically justifiable

    2. Psychological Validity:
       - Must represent established psychological, social, or personal drivers
       - Should be grounded in behavioral psychology literature

    3. Measurement Properties:
       - Must be quantifiable through behavioral observations
       - Should be stable across time for individual users

    4. Distinctness:
       - No semantic overlap with attributes or other constructs
       - Must capture unique psychological dimension

       BEHAVIORAL PROBING QUESTION FRAMEWORK:
    1. Question Structure:
       - Must target SPECIFIC observable behaviors, not attitudes or preferences
       - Must reference concrete actions that occurred in the past. Try to avoid questions that probe for actions that don't occur very frequently. If our object of study is, by its nature, measuring something that does not occur very frequently then try and base your probing questions on measuring distinct(as in distinct from our current object of study) observed behaviors that occur with the same frequency.
       - If using a temporal component in the probing question make sure that the observed behavior we are trying to account for could be one that occurs in the time-frame given. For example asking "In a typical week how frequently do you consider deal and discounts amongst car providers" is bad since "Considering deals and discounts amongst car providers" only occurs when considering buying a car which is by its nature not a frequently occurence for most.
       - Must be measurable on a 5-point frequency scale
       - Must avoid hypotheticals or future intentions
        - Must avoid direct references to the object of study, additionally must also avoid direct references to the attributes. Instead, use analogous scenarios that involve similar psychological trade-offs and decision patterns. For example, when studying car purchases, ask about other high-stakes durable goods; when studying food choices, ask about other routine consumption decisions.
       - The question tries to capture observed behavior that is made either habitually or unconsciously. Hence, the questions will often relate to observed behavior which involves less or no cost benefit analysis from the individuals perspective.

    2. Behavioral Indicators:
       - Past Actions: Focus on completed behaviors ("How many times in the past month...")
       - Frequency Measures: Use specific time periods ("How often in a typical week...")
       - Choice Patterns: Reference actual decisions made ("When shopping for X, how often...")
       - Resource Allocation: Consider time/money spent ("What percentage of your monthly budget...")

    3. Social Desirability Bias Prevention:
       - Use indirect behavioral measures that correlate with the construct
       - Focus on habitual behaviors that occur without conscious deliberation
       - Ask about specific instances rather than general patterns
       - Include behaviors with actual costs (time, money, effort)
    4. Amount of probing questions and further specifications
        -  Generate {num_questions} probing questions per psychological construct
        - Ensure some probing questions have a negative connotation while the rest are neutral or positive. This helps capture varied responses from the population and further reveals the makeup of their psychological constructs.
    5. Five-point frequency scale framework:
     - For each probing question generate the corresponding five-point frequency scale
     - The frequency scale should reflect the context of the probing question and importantly never repeat the semantic meaning given in the question. For example for the question:"How often do you wear a seatbelt while sitting on the backseat of a car" we could generate "Never, Infrequently, Frequently, Very Frequently, Always". This five point question is valid because we use "Frequent" instead of "Often" which is included in the question and include "Always" and "Never" since people tend to be more polarized to this question.
    - Important note: if a probing question contains the word "Often" never use "Often" as one of the options in the five-point frequency scale for that probing question. Additionally if a probing question contains the word "Frequently" never use "Frequently as one of the options in the five point frequency scale!
    Examples of good probing questions:
       - Psychological construct: "Safety Consciousness" → Question: "How often do you wear a seatbelt while sitting on the backseat of a car"
       - Psychological construct: "Safety Consciousness" → Question: "How safe do you feel while travelling in public transport?"
       - Psychological construct: "Environmental Consciousness" → Question: "How often do you make sure you use the right wastebin when disposing of your trash in a public setting"
       - Psychological construct: "Environmental Consciousness" → Question: "How often do you use a bicycle/walk for trips that require five to ten minutes to get there by car"
       - Psychological construct: "Economic Pragmatism" → Question: "How often do you consider long-term savings when making a significant purchase?"
       - Psychological construct: "Economic Pragmatism" → Question: "How often do you prioritize cost-effectiveness and financial incentives in your purchasing decisions involving day-to-day items?"
        - Psychological construct: "Tech Convenience" → Question: "How often do you use voice commands or smart home devices to control your entertainment systems?"

    Examples of bad probing questions:
       - Psychological construct: "Environmental Consciousness" → Question: "To what extent do you consider the environmental impact of your transportation options?". Reason why this is a bad question: Vague and unclear, difficult to measure using a five point rating scale.
       - Psychological construct: "Environmental Consciousness" → Question: "How important is it for you to reduce your carbon footprint through your transportation choices?". Reason why this is a bad question: Not specific to an observed behavior. Most people will provide positive answers to look good in public.
       - Psychological construct: "Economic Pragmatism" → Question: "How often/frequently do you evaluate the long-term financial benefits of a purchase before making a decision?". Reason why this is a bad question: Not specific to an observed behavior.
       - Psychological construct: "Value-Consciousness" → Question: "In a typical week, how many times do you compare prices or features before subscribing to a new service?". Reason why this is a bad question: Asks for a quantitative measure of an observed behavior in the last two weeks, where this observed behavior might actually only occur rarely
       - Psychological construct: "Tech-Savviness" → Question: "In the past month, how many times have you updated or customized your tech devices to improve user experience?". Reason why this is a bad question: Asks for a quantitative measure of an observed behavior in the last month, where this observed behavior might actually only occur rarely. Instead a better question would be: "How frequently have you updated or customized your tech devices to improve user experience?" removing the temporal compenent entirely
    **Output:**
    Provide:
    - Three psychological constructs that cover the entire range of the attributes and are maximally orthogonal to each other, try to base the psychological construct on the country.


    Example(Note these are just examples they do not necessarily follow the guidelines outlined above and should not be replicated, there should always be {num_questions} probing questions generated):
    Question: "Why are you considering visiting a certain dentist?"
    User Profile Attributes: {{
        "age_levels": [35, 45],
        "income_levels": [100000, 150000],
        "education_levels": ["Masters Degree"],
        "gender_levels": ["Female"]
    }}
    Survey Attributes: [
        "Location Proximity",
        "Reputation and Reviews",
        "Affordability",
        "Insurance Compatibility",
        "Specialization",
        "Availability of Appointments",
        "Clinic Environment",
        "Use of Advanced Technology",
        "Friendliness of Staff"
    ]

    Response: {{
        "motivation": {{
        "Convenience-Seeking": {{
            "How often in the past month have you chosen a closer grocery store over your preferred one to save time?": ["Not at all", "Rarely", "Sometimes", "Frequently", "Very Frequently"],
            "In a typical week, how many times do you order delivery instead of picking up items yourself?": ["Never", "Once or twice", "Occasionally", "Frequently", "Always"],
            "When commuting, how often do you take a shorter but less scenic route to save time?": ["Very Rarely", "Rarely", "Sometimes", "Frequently", "Very frequently"]
        }},
        "Trust in Authority": {{
            "How many times in the past month have you checked online ratings before trying a new restaurant?": ["Never", "Rarely", "Occasionally", "Often", "Very often"],
            "When buying electronics, how often do you read professional review articles before making a decision?": ["Never", "Seldom", "Sometimes", "Frequently", "Always"],
            "How often do you rely on expert recommendations when selecting toothpaste?": ["Never", "Rarely", "Sometimes", "Frequently", "Always"]
        }},
        "Financial Responsibility": {{
            "In the past three months, how often have you compared prices across different stores before purchasing items over $50?": ["Not at all", "Rarely", "Sometimes", "Frequently", "Every time"],
            "How many times in the last month have you used price comparison apps while shopping?": ["Never", "Once", "A few times", "Frequently", "Always"],
            "How frequently do you look for discounts or sales when purchasing groceries?": ["Very Rarely", "Rarely", "Sometimes", "Often", "Very often"]
        }}
    }}

    Provide your response in JSON format, adhering to the structure in the sample examples. Exclude any schema details, metadata, or validation rules from the output. Ensure that each motivation is a valid causal parent node for its attribute.

    Response:
    """)


CHECK_FOR_PRODUCT_TEMPLATE = inspect.cleandoc("""
You are an evaluator for survey questions and product or service statements.
Your task is to analyze the following statement using this two-step process: {why_prompt}
Step 1: Verify real-world feasibility:
Observable/measurable attributes? (yes/no)
    1.1: Must be quantifiable or categorizeable
Specific levels definable? (yes/no)
    2.1: Must have clear, distinct categories or numerical ranges
    2.2: Levels must be mutually exclusive and collectively exhaustive
Data available/collectible? (yes/no)
    3.1: Through surveys, market research, or public sources
    3.2: Within possible cost and time constraints
Products/services exist in market? (yes/no)
    4.1: Must reference a specific product/service that can be defined by concrete attributes (e.g., price, features, specifications)
    4.2: The product category must be currently available to target population.
If all Step 1 answers are "yes", set "product_exists" to true, otherwise set it to false.

Step 2: Only if ALL Step 1 answers are "yes", evaluate on these criteria:
1: Cannot be measured even with identified attributes
2: Major barriers in measuring identified attributes
3: Attributes measurable with complex methods
4: Attributes easily measurable with some effort
5: Attributes routinely measured in existing research

NOTE:
 - Score defines the confidence score of the model in predicting the existence of the product.
 - Score is an integer between 1 and 5, where 1 indicates low confidence and 5 indicates high confidence.

Examples:
1. why_prompt: what factors affect the choice of a GP?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": false,
    "score": 1

2. why_prompt: what factors affect the choice of a long-haul holiday flight
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": false,
    "score": 2

3. why_prompt: what factors affect the choice of a smartphone?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": true,
    "score": 5

4. why_prompt: What species of houseplant is best for a doctor's waiting room?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": false,
    "product_exists": true,
    "score": 3

5. why_prompt: What features of fitness trackers and watches influence consumer purchase decisions?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": true,
    "score": 4

6. why_prompt: what factors affect perception of liberty in democratic societies?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": false,
    "score": 1

7. why_prompt: what factors affect the choice of home security systems?
response:
    "measurable_attributes": true,
    "specific_levels_definable": true,
    "data_available_collectible": true,
    "product_exists": true,
    "score": 4

IMPORTANT:
1. The output response must be in JSON format with the below structure.
2. Avoid providing explanations, reasoning, or additional details in the output response.
{format_instructions}""")

ORDINALITY_CHECK_TEMPLATE = inspect.cleandoc("""
You are an expert in market research.

**Product Idea:** {why_prompt}
**Country:** {country}

We have a set of product attributes and their corresponding levels given in a dictionary format where the attribute is key and the value are the corresponding levels:

**Attributes and Levels:**
{attribute_levels}

For each attribute, please perform the following:

1. **Determine Attribute Type:**
   - Specify whether the attribute is **nominal** or **ordinal**. An attribute may be **ordinal** if there is a ranking amongst what could represent the attribute.
   For example the attribute: "Convenience" is ordinal because a product may be more or less convenient to use. Conversely an attribute is nominal if there is no such ranking, an example is the attribute: "Eye Color", there are so clear rankings to an eye color of "Brown", "Blue", etc...

2. **Ordinal Ranking (if applicable):**
   - If the attribute is **ordinal**, create an ordinal ranking of its levels from lowest to highest in terms of why you have decided the attribute is ordinal in the first place.
   - If the levels contains numeric values (with or without unit of measurement), ensure that the ranking reflects the ascending order based on unit of measurement.
   - Ensure that your reasoning is based on factors such as consumer preferences, market trends, and the context of the product idea and country.

**Critical Notes:**
1. Include **all attributes** in the `attribute_types` section and specify whether each is **nominal** or **ordinal**.
2. For each **ordinal** attribute, include **all levels** in the `ordinal_rankings` section, ranked from lowest to highest.
3. The `...` is not meant to be used literally. Replace it with the remaining attributes or levels as applicable.
4. Ensure the response is comprehensive and includes all attributes and levels provided in the input as directed by the instructions above.

**Output Format:**

{{
    "attribute_types": {{
        "Attribute1": "nominal",
        "Attribute2": "ordinal",
        "Attribute3": "ordinal",
        "Attribute4": "..."
    }},
    "ordinal_rankings": {{
        "Attribute2": ["Level1", "Level2", "Level3"],
        "Attribute3": ["Level1", "Level2", "..."]
    }}
}}
""")

PDF_TO_TRAITS = inspect.cleandoc("""
You are a highly skilled analyst specialized in examining text that describes individuals and identifying structured, data-driven traits. Traits are specific, well-defined characteristics that can be directly inferred from the text. They should be categorized into measurable or identifiable types, and must have a clear, direct connection to the given context and the user's query.

Context Provided:
{context}

User's Question:
{question}

Your Task:
Carefully review the retrieved context and extract only those traits that are genuinely relevant to the user's question. Do not introduce traits that are not strongly supported by the context. For each identified trait, use the following schema:

- long_description: A detailed sentence or paragraph describing the trait. This should be a direct or inferred characteristic of the individual(s), using pronouns like "I" or "My" rather than first names or other identifying information.
- measurement_type: Either "Nominal" (for traits that represent categories without order) or "Ordinal" (for traits with a ranked order or level of measurement). Use "Ordinal" only when a clear order exists.
- ordinal_rank: If measurement_type is "Ordinal," provide the rank or level (e.g., 1 for highest rank). Use null if Nominal.
- set_type: The category or grouping the trait belongs to (e.g., "Education," "Performance," or "Community Type"). If unspecified, use null.
- short_description: A concise, single word or short phrase label for the trait.
- type: A broader category that best fits the trait (e.g., "work_skill," "military_service," "community_type").

Key Instructions:
1. Strict Relevance: Extract only traits that directly and clearly relate to both the given context and the user's specific query. Traits with weak or indirect connections should not be included.
2. Measurable & Identifiable: Include only traits that are objectively measurable, well-defined, and clearly illustrated by the context. If the query is about a specific theme, ensure all traits pertain directly to that theme.
3. Clarity Over Quantity: Avoid traits that are vague, redundant, overly broad, or not clearly supported by the text. If a piece of information does not directly answer the user's question, do not turn it into a trait.
4. No Guessing: If a field (such as set_type or ordinal_rank) is not explicitly stated or cannot be reasonably inferred, use null instead of guessing.
5. Uniqueness: Each trait must be distinct and not overlap unnecessarily with others. Choose the most relevant and informative traits.
6. Completeness: For every included trait, fill out all applicable fields. Use null for fields that do not apply.

Examples:
    Example 1:
        Context: "Find traits relating to work skills."
        Output:
                {{
        "traits": [
            {{
            "long_description": "I consistently apply advanced data analysis techniques to identify patterns and insights.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Skills",
            "short_description": "Data_Analysis",
            "type": "work_skill"
            }},
            {{
            "long_description": "I manage projects effectively, ensuring that tasks are completed on time and within scope.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Skills",
            "short_description": "Project_Management",
            "type": "work_skill"
            }}
        ]
        }}
    Example 2:
        Context: "Find traits relating to leadership qualities."
        Output:
                {{
        "traits": [
            {{
            "long_description": "I inspire team members by setting a clear vision and encouraging collaboration.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Leadership",
            "short_description": "Visionary_Leadership",
            "type": "leadership_quality"
            }},
            {{
            "long_description": "I provide constructive feedback that guides others towards professional growth.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Leadership",
            "short_description": "Constructive_Feedback",
            "type": "leadership_quality"
            }}
        ]
        }}
    Example 3:
        Context: "Find traits relating to sustainability practices."
        Output:
                {{
        "traits": [
            {{
            "long_description": "I actively reduce energy consumption in daily operations by optimizing resource usage.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Sustainability",
            "short_description": "Energy_Conservation",
            "type": "sustainability_practice"
            }},
            {{
            "long_description": "I promote the use of eco-friendly materials and environmentally responsible suppliers.",
            "measurement_type": "Nominal",
            "ordinal_rank": null,
            "set_type": "Sustainability",
            "short_description": "Green_Sourcing",
            "type": "sustainability_practice"
            }}
        ]
        }}

    Respond in JSON format:
    {{
        "traits": [
            {{
                "long_description": "...",
                "measurement_type": "...",
                "ordinal_rank": ...,
                "set_type": ...,
                "short_description": "...",
                "type": "..."
            }},
            ...
        ]
    }}
""")

FETCH_PRODUCTS_PROMPT = inspect.cleandoc("""
### Input Context:
- Product: {product}
- Products so far: {products_sofar}
- why_prompt: {why_prompt}
- country: {country}

Your task is to find a real-world product that most closely resembles the given input product. For clarity the keys of Product are the attributes and each corresponding value are the levels. The product must be relevant to the context described in the why_prompt and available in the specified country. Additionally, the product must:


***MOST IMPORTANT***
YOU MUST ALWAYS RETURN EXACTLY THE SAME ATTRIBUTES(KEYS) PASSED TO YOU BUT THE VALUES MSUT BE THE REAL WORLD VALUES THAT YOU FIND!


1. Be distinct from the products listed in **Products so far** when searching for a new real-world product.
2. Be as competitive as possible with the products in **Products so far** in terms of attributes and levels, while adhering to the constraints outlined below.

### Instructions:
- Find a real-world product that most resembles the input product in the context of the why_prompt and country.
- Use actual brand names and real product details. No invented or hypothetical products are allowed.
- **DO NOT EVER ADD NEW ATTRIBUTES OR ALTER ATTRIBUTE NAMES. USE ONLY THE ATTRIBUTES PASSED TO YOU EXACTLY AS THEY ARE PASSED.**

**Handling Past Products:**
- Ensure that the product you select is **entirely distinct** from the products listed in **Products so far**:
  - The product name must be different.
  - At least one key attribute (e.g., Material, Capacity, Price, or Color) must be distinct from all products in **Products so far**.
- If no competitive product is found that meets these criteria, clearly document this internally (without including it in the output) and select the next best option that maintains distinction.

**Match Attributes and Levels:**
- For each attribute, provide the actual level from the real-world product.
- Ensure that the **format of the level matches the format used in the input product** exactly. For example, if the input specifies `"Price": "500"`, the output must use the same format, such as `"Price": "500"`.
- Do not summarize, extrapolate, or use ranges (e.g., do not return `"Price": "500-1000"` if the input specifies `"Price": "500"`). Use the exact format specified in the input product even if the real-world product level differs.
- Always return **exactly the same attributes (keys)** as provided in the input product. If the input specifies attributes like `"What range and charging time will this car offer": "Up to 500 miles range"`, the output must preserve the same attribute key and provide the most accurate real-world value.

**Cultural and Market Relevance:**
- Ensure the product is contextually relevant, reflecting cultural, market, or regional characteristics tied to the why_prompt and country.

### Examples:

#### Example 1:

**Input:**
{{
    "product": {{
        "attributes": {{
            "Material": "Stainless Steel",
            "Capacity": "500ml",
            "Price": "25",
            "Color": "Blue"
        }}
    }},
    "products_sofar": [
        {{
            "product_name": "Hydro Flask Standard Mouth Bottle",
            "attributes": {{
                "Material": "Stainless Steel",
                "Capacity": "400ml",
                "Price": "35",
                "Color": "Blue"
            }}
        }}
    ],
    "why_prompt": "The user is looking for an eco-friendly water bottle suitable for everyday use.",
    "country": "United States"
}}

**Output:**
{{
    "product": {{
        "product_name": "Klean Kanteen Classic Bottle",
        "attributes": {{
            "Material": "Stainless Steel",
            "Capacity": "500ml",
            "Price": "30",
            "Color": "Green"
        }}
    }}
}}

#### Example 2:

**Input:**
{{
    "product": {{
        "attributes": {{
            "Will this car have advanced safety features?": "Lane keep assist to prevent drifting into other lanes",
            "What range and charging time will this car offer": "Up to 500 miles range, featuring a revolutionary battery system that charges in 45 minutes"
        }}
    }},
    "products_sofar": [
        {{
            "product_name": "Tesla Model S",
            "attributes": {{
                "Will this car have advanced safety features?": "Autopilot with full self-driving capability",
                "What range and charging time will this car offer": "Up to 405 miles range, charging in 30 minutes"
            }}
        }}
    ],
    "why_prompt": "The user wants an electric car with the best safety and range capabilities available.",
    "country": "United States"
}}

**Output:**
{{
    "product": {{
        "product_name": "Lucid Air Grand Touring",
        "attributes": {{
            "Will this car have advanced safety features?": "Driver monitoring system to ensure safe operation",
            "What range and charging time will this car offer": "Up to 516 miles range, charging in 37 minutes"
        }}
    }}
}}

### Additional Notes:
- Always use the exact format of levels specified in the input product for the output, even if the real-world levels differ.
- Avoid ranges, summaries, or adjusted formats in the output. Reflect the real-world level while keeping the input format.
- Ensure that the output is distinct and competitive compared to **Products so far**.
Provide your response in JSON format, adhering to the structure in the sample examples. Exclude any schema details, metadata, or validation rules from the output.
{format_instructions}
Response:

""")
fields = {
    "experiment": {
        "query": (
            "Extract the name of the experiment and the year it was conducted from the"
            " provided text."
        ),
        "prompt": inspect.cleandoc("""


Context Provided:
{context}

User's Question:
{question}

You are an AI assistant tasked with analyzing academic papers and extracting specific information into a structured JSON format. Follow these detailed steps for each field:

### Experiment ID
- Locate the first author's last name and publication year
- Format as "[LastName][Year]" (e.g., "Smith2024")
- Look in: Title page, headers, or first page metadata

Example output:

"transcription":
    {{
    "experiment":"Humburg2013"
}}

 Please provide only your response in JSON format:
    {{
        "transcription":
            {{
                "experiment": "..."
            }}
    }}
DO NOT RETURN ANYTHING BUT THE JSON FORMAT
"""),
    },
    "pre_cooked_attributes_and_levels_lookup": {
        "query": (
            "I would like you to extract all attributes and their corresponding levels"
            " studied in this research. Attributes are the key variables,"
            " characteristics, or factors being analyzed in the study. They represent"
            " the categories or dimensions of interest in the experiment. Levels are"
            " the specific values, options, or categories that each attribute can take."
            " These levels represent the possible variations or states of the attribute"
            " as described in the study."
        ),
        "prompt": inspect.cleandoc("""


Context Provided:
{context}

User's Question:
{question}

You are an AI assistant tasked with analyzing academic papers and extracting specific information into a structured JSON format. Follow these detailed steps for each field:

## Attribute and Level Discovery Process

### Step 1: Explicit Attribute Identification
Primary Search Pattern:
```
Priority 1 - Direct Attribute Statements:
Search for:
- "attributes included"
- "attributes were"
- "attributes are"
- "following attributes"
- "attributes studied"
- "attributes examined"
- "attributes considered"
- "attributes measured"
- "attributes investigated"
- "attributes analyzed"
- "attributes tested"
- "attributes used"

Location Hierarchy:
1. Experimental Design section
2. Methodology section
3. Research Design section
4. Study Design section
```

Example matches:
- "The attributes included in this study were..."
- "We examined the following attributes..."
- "The experiment used six attributes..."

### Step 2: Methodological Description Search
```
Priority 2 - Method Descriptions:
Search for:
- "factors"
- "variables"
- "characteristics"
- "dimensions"
- "features"
- "components"
- "elements"

Combined with:
- "manipulated"
- "varied"
- "studied"
- "analyzed"
- "examined"
- "investigated"
- "considered"

Location:
1. Methods section
2. Experimental procedure
3. Study design
4. Variable operationalization
```

### Step 3: Table and Figure Analysis
```
Priority 3 - Structural Elements:
Search for tables/figures with:
- "Overview of attributes"
- "Attribute descriptions"
- "Variable definitions"
- "Experimental factors"
- "Study variables"
- "Design factors"

Table Headers:
- "Attribute"
- "Factor"
- "Variable"
- "Characteristic"
- "Dimension"
```

### Step 4: Level Identification Process

#### A. Direct Level Statements
```
Search Pattern 1 - Explicit Level Descriptions:
- "[attribute] had [X] levels"
- "levels of [attribute] were"
- "[attribute] varied across [X] levels"
- "[attribute] was set to"
- "[attribute] consisted of"
- "[attribute] included"
- "[attribute] ranged from"

Location Priority:
1. Attribute description paragraphs
2. Experimental design section
3. Variable operationalization
```

#### B. Tabular Level Extraction
```
Search Pattern 2 - Table Structure:
1. Look for tables with:
   - Row/column headers matching identified attributes
   - "Levels" or "Values" in header/subheader
   - Categorized listings under attributes

2. Table Types:
   - Experimental design matrices
   - Factor level tables
   - Variable definition tables
   - Design of experiments tables
```

#### C. Level Context Analysis
```
Search Pattern 3 - Contextual Mentions:
1. For each attribute:
   - Scan surrounding paragraphs
   - Look for enumerations
   - Check for lists or bullet points
   - Identify measurement scales

2. Level Indicators:
   - "ranging from ... to"
   - "categorized as"
   - "classified into"
   - "measured on a scale of"
   - "divided into"
   - "grouped into"
```

### Step 5: Validation and Cross-Reference

#### A. Attribute Validation
```
Verification Process:
1. Compare attributes across:
   - Initial explicit mentions
   - Methodology descriptions
   - Results section
   - Tables and figures
   - Statistical analysis

2. Check for:
   - Consistent naming
   - Complete attribute lists
   - Alternative terminology
   - Grouping structures
```

#### B. Level Validation
```
Verification Steps:
1. For each attribute:
   - Match levels to attribute mentions
   - Verify level completeness
   - Check for level overlap
   - Confirm measurement scales

2. Cross-reference:
   - Design matrices
   - Results tables
   - Analysis sections
   - Appendices
```

Example output:

"transcription":
    {{
     "pre_cooked_attributes_and_levels_lookup": [
    ["Professional expertise",
        ["The candidate has average professional expertise",
         "The candidate has low professional expertise",
         "The candidate has high professional expertise"]
        ],
    ["General academic skills",
        ["The candidate has average general academic skills",
         "The candidate has low general academic skills",
         "The candidate has high general academic skills"]
        ],
    ["Innovative/creative skills",
        ["The candidate has average innovative/creative skills",
         "The candidate has low innovative/creative skills",
         "The candidate has high innovative/creative skills"]
        ],
    ["Strategic/organizational skills",
        ["The candidate has average strategic/organizational skills",
         "The candidate has low strategic/organizational skills",
         "The candidate has high strategic/organizational skills"]
       ],
    ["Interpersonal skills",
       ["The candidate has average interpersonal skills",
        "The candidate has low interpersonal skills",
        "The candidate has high interpersonal skills"]
       ],
    ["Commercial/entrepreneurial skills",
       ["The candidate has average commercial/entrepreneurial skills",
        "The candidate has low commercial/entrepreneurial skills",
        "The candidate has high commercial/entrepreneurial skills"]
       ],
    ["Salary",
       ["The candidate's salary is average",
        "The candidate's salary is 25 percent above average",
        "The candidate's salary is 10 percent above average",
        "The candidate's salary is 10 percent below average",
        "The candidate's salary is 25 percent below average"]
       ]]
}}

 Please provide only your response in JSON format:
    {{
        "transcription":
            {{
                "pre_cooked_attributes_and_levels_lookup": "..."
            }}
    }}
DO NOT RETURN ANYTHING BUT THE JSON FORMAT
"""),
    },
    "population_traits": {
        "query": (
            "Extract concrete traits of the population studied in the paper, such as"
            " Age, Sex, Income, Education Level, and Race and wherever possible include"
            " specific ranges of the population for each trait."
        ),
        "prompt": inspect.cleandoc("""
    Context Provided:
    {context}

    User's Question:
    {question}

    You are an expert at analyzing academic papers and extracting specific population traits into a structured JSON format. Follow these steps meticulously:

    ### Step 1: Identify Relevant Sections
    - Focus on sections like:
      1. "Methodology"
      2. "Study Design"
      3. "Sample Characteristics"
      4. "Participants"
    - Look for mentions of traits in phrases like:
      - "demographic variables"
      - "population traits"
      - "respondents' characteristics"
      - "sample composition"

    ### Step 2: Extract Core Traits and Their Values
    - **Mandatory Core Traits**:
      Extract each trait with its specific values when possible:
      - Age: Capture [min_age, max_age] or specific distribution
      - Sex/Gender: List all categories mentioned
      - Income: Capture [min_income, max_income] or income brackets
      - Education Level: List all educational categories
      - Race/Ethnicity: List all racial/ethnic groups represented
    - If the trait is present in the population studied then return it, if no additional values are found for the trait then indicate "Values not specified" but still include the trait. If the trait is not found in the population do not return it.

    ### Step 3: Include Additional Traits (If Critical)
    - Include traits like Religion, Political Ideology, etc., **only if explicitly emphasized as crucial to the study's focus**.

    ### Step 4: Format and Validate
    - Output the traits as a structured dictionary
    - Example output:
    {{
        "transcription": {{
            "population_traits": {{
                "age": "Values not specified",
                "sex": ["Male", "Female"],
                "education": ["High School", "Bachelor's", "Master's", "Doctorate"],
                "race": ["White", "Black", "Asian", "Hispanic", "Other"]
            }}
        }}
    }}

    ### Notes:
    - Prioritize precision and direct quotation from the paper
    - If a trait is not found to be included in the population in the paper do not return it as one of the population_traits
"""),
    },
    "where_preamble": {
        "query": "Find the location where the study was conducted.",
        "prompt": inspect.cleandoc("""
            Context Provided:
            {context}

            User's Question:
            {question}

            ### Location
            - Extract the location of the study (country or region).
            - Use metadata, methods, or location references.

            Example output:
            {{
                "transcription":
                    {{
                        "where_preamble": "Country/Region"
                    }}
            }}

            DO NOT RETURN ANYTHING BUT THE JSON FORMAT
        """),
    },
    "when_preamble": {
        "query": "Find the year(s) of data collection.",
        "prompt": inspect.cleandoc("""
            Context Provided:
            {context}

            User's Question:
            {question}

            ### Timing
            - Identify the year(s) when data collection occurred.
            - Format as YYYY or YYYY-YYYY.

            Example output:
            {{
                "transcription":
                    {{
                        "when_preamble": "Year"
                    }}
            }}

            DO NOT RETURN ANYTHING BUT THE JSON FORMAT
        """),
    },
    "experimentor_why_question_prompt": {
        "query": (
            "Extract the research objective or primary question that this research"
            " paper is aimed at answering"
        ),
        "prompt": inspect.cleandoc("""
            Context Provided:
            {context}

            User's Question:
            {question}

              ### Research Question Extraction Guidelines
        - Identify the core behavioral mechanism or causal relationship being investigated
        - Phrase as a precise, concise causal question
        - Focus on the fundamental 'why' or 'how' behind the research
        - Emphasize the causal mechanism, not just the topic

        ### Characteristics of an Excellent Causal Question
        - Clearly specifies the causal relationship
        - Uses precise, action-oriented language
        - Highlights the behavioral mechanism
        - Avoids vague or overly broad formulations

        ### Examples of Strong Causal Research Questions
            "experimentor_why_question_prompt": "I would like to investigate the factors influencing the preference for household waste sorting over specialized facilities",
            "experimentor_why_question_prompt": "I would like to understand why most consumers are unwilling to pay a premium for organic eggs compared to eggs with enhanced animal welfare attributes",
            "experimentor_why_question_prompt": "I would like to know: How do Spanish consumers' preferences and willingness to pay for locally grown lamb meat, such as "Ojinegra from Teruel" and "Ternasco,".",
            "experimentor_why_question_prompt": "I would like to investigate: Why do objective signals about the accuracy of a subject's knowledge significantly increase WTP for well-informed individuals but have no such effect on less informed subjects in the context of public goods valuation",

            Example output:
            {{
                "transcription":
                    {{
                        "experimentor_why_question_prompt": ""
                    }}
            }}

            DO NOT RETURN ANYTHING BUT THE JSON FORMAT
        """),
    },
    "respondent_dependent_variable": {
        "query": (
            "Infer the core task instructions for respondents based on the study's"
            " context."
        ),
        "prompt": inspect.cleandoc("""
        Context Provided:
        {context}

        User's Question:
        {question}

        ### Instruction Inference Guidelines
        - Carefully analyze the study's methodology and context
        - Reconstruct the implicit task given to respondents
        - Focus on the core decision-making mechanism
        - Synthesize a precise, concise instruction that captures the essence of the respondent's task

        ### Critical Considerations
        - Look beyond explicit instructions
        - Examine research design, survey structure, and experimental setup
        - Identify the fundamental choice or evaluation process
        - Construct a generalizable task description that mirrors the study's intent

        ### Instruction Characteristics
        - Single sentence
        - Captures the full scope of respondent interaction
        - Reflects the study's core research objective
        - Uses clear, action-oriented language

        ### Inference Process
        1. Identify the alternatives presented
        2. Understand the decision context
        3. Reconstruct the implied task
        4. Formulate a precise instruction

         ### Example of good respondent_dependent_variable:
            "respondent_dependent_variable": "Please read the description of the field experiment. Then, indicate whether you believe that objective signals about a person's knowledge regarding a public good can significantly influence their valuation of it",
            "respondent_dependent_variable": "Please read the descriptions of the two lamb meat options in Spain. Then please select which lamb meat option you would prefer to purchase",
            "respondent_dependent_variable": "Please read the descriptions of the premium eggs. Then, please indicate your preference for which type of eggs you would prefer to buy",
        ### Example Output Format
        {{
            "transcription": {{
                "respondent_dependent_variable": "Please read the description of the following alternatives carefully. Then, please choose your preferred option."
            }}
        }}

        DO NOT RETURN ANYTHING BUT THE JSON FORMAT
    """),
    },
    "paper_data": {
        "query": (
            "Extract bibliographic and research-related details from the study,"
            " including the full citation, first author, regression model, type of"
            " task, experimental design, research domain, and sub-domain. Include all"
            " relevant metadata mentioned in the text or citation sections."
        ),
        "prompt": inspect.cleandoc("""
            Context Provided:
            {context}

            User's Question:
            {question}

            ### Paper Data Extraction
1. **Bibtex Citation**:
   - Extract the full citation of the paper, often found in references or citation metadata.
   - Include all available details: author(s), title, journal, volume, issue, year, DOI, and page numbers.

2. **First Author**:
   - Identify the first author of the paper. This is typically listed at the beginning of the author list.

3. **Regression Model**:
   - Look for the statistical model used in analysis (e.g., OLS, CLM, logistic regression).
   - Search in methodology or analysis sections.

4. **Type of Task**:
   - Determine the type of experimental task conducted (e.g., discrete choice, ranking, conjoint analysis).

5. **Type of Experiment Design**:
   - Identify the design methodology (e.g., fractional factorial, full factorial, randomized block design).

6. **Research Domain**:
   - Specify the primary academic field (e.g., Political Science, Economics, Psychology).

7. **Research Sub-Domain**:
   - Provide a more granular description of the research focus (e.g., Voter Choice, Behavioral Economics, Cognitive Biases).

Example output:

"transcription":
    {{
        "paper_data": [
            ["bibtex_citation", "@article{{title={{...}}, volume={{...}}, DOI={{...}}, number={{...}}, journal={{...}}, publisher={{...}}, author={{...}}, year={{...}}, pages={{...}}}}"],
            ["first_author", "Author, Firstname"],
            ["regression_model", "ols"],
            ["type_of_task", "discrete_choice"],
            ["type_of_experiment_design", "fractional_factorial"],
            ["research_domain", "Political Science"],
            ["research_sub_domain", "Voter Choice"]
        ]
    }}

Please provide only your response in JSON format:
    {{
        "transcription":
            {{
                "paper_data": ...
            }}
    }}
DO NOT RETURN ANYTHING BUT THE JSON FORMAT
"""),
    },
    "null_levels": {
        "query": (
            "Determine if respondents were given a choice to select 'neither' in the"
            " discrete choice experiment."
        ),
        "prompt": inspect.cleandoc("""
Context Provided:
{context}

User's Question:
{question}

### Null Levels
- Identify whether the study includes an explicit 'neither' or 'none' option for respondents in the discrete choice experiment.
- Return true if such an option exists, otherwise return false.

Example output:
{{
    "transcription": {{
        "null_levels": false
    }}
}}

DO NOT RETURN ANYTHING BUT THE JSON FORMAT
"""),
    },
}
CONTAINS_BRAND = inspect.cleandoc("""
You are an expert in decyphering whether a given sentence contains references to real-life brands and determining if price is an appropriate attribute for a conjoint survey analysis.

**Sentence:** {why_prompt}

    Instructions for brand detection:
    - Look through this sentence carefully and check whether it contains a reference to a real life brand or not and if it does count how many real life brands it contains. For example it could have a reference to "Apple", "Samsung", "Nescafe" etc..
    - If you find that there is a reference to a brand, follow these steps to determine if brand is relevant for the study:
      1. Check if the why_prompt focuses specifically on brand choice, brand comparison, or brand preference.
      2. Check if the why_prompt is about a product category where brand is known to be a significant decision factor.
      3. Check if the why_prompt suggests consumers would evaluate multiple brands in their decision-making process.
      4. Check if brand differentiation is central to the research question being asked.
      5. Consider whether including brand would provide meaningful insights given the context of the why_prompt.
    - If at least two of the above conditions are met, return the number of brands found in the why_prompt.
    - If fewer than two conditions are met, or if the why_prompt specifically indicates brand is not relevant to the inquiry, return -1 to indicate brand is not relevant for the study.
    - If there are no brands mentioned in the prompt, return 0.
    
    Instructions for price relevance detection:
    - Determine whether price would be an appropriate attribute to include in a conjoint survey analysis based on the why_prompt.
    - Follow this logical procedure to determine price relevance:
      1. Check if the why_prompt explicitly mentions price, cost, affordability, budget, expense, value for money, or similar price-related terms.
      2. Check if the why_prompt is about purchasing decisions, consumer choices, or market comparisons where price would naturally be a factor.
      3. Check if the why_prompt focuses on product features/attributes that consumers typically evaluate alongside price when making decisions.
      4. Check if the why_prompt is about a product category where price is known to be a significant decision factor (e.g., electronics, cars, subscription services).
      5. Consider whether including price would provide meaningful insights given the context of the why_prompt.
    - If at least two of the above conditions are met, return true for contains_price.
    - If fewer than two conditions are met, or if the why_prompt specifically indicates price is not relevant to the inquiry, return false.

    Examples:
        Input:
            why_prompt: "What are features of Apple and Google products that affect consumers decisions"
        Output:
            {{
                "contains_brand": 2,
                "contains_price": true
            }}
        
        Input:
            why_prompt: "How do consumers choose between budget and premium smartphones?"
        Output:
            {{
                "contains_brand": 0,
                "contains_price": true
            }}
        Input:
            why_prompt: "What factors drive workplace productivity?"
        Output:
            {{
                "contains_brand": -1,
                "contains_price": false
            }}

        **Output Format:**

        {{
            "contains_brand": ..,
            "contains_price": ..
        }}

***IMPORTANT CONSIDERATIONS FOR BRAND DETECTION***
    THIS IS A SMALL POSSIBILITY THAT EVEN IF A WHY_PROMPT CONTAINS BRANDS IT IS NOT RELEVANT THAT WE MUST RECOGNIZE IT. WE ARE USING THIS DETECTION TO DETERMINE WHETHER WE SHOULD INCLUDE BRAND AS AN ATTRIBUTE IN A DISCRETE CHOICE CONJOINT EXPERIMENT.

    FOR EXAMPLE THIS WHY_PROMPT: "What are features of a phone, for example one from Apple, that influences consumer behavior?". ALTHOUGH THIS WHY_PROMPT MENTIONS A BRAND THEY ARE MORE CONCERNED ABOUT THE PHONE IN GENERAL SO IT IS NOT NECESSARY TO FOCUS SOLELY ON PRODUCTS OF APPLE BUT IT MIGHT BE MORE FRUITFUL TO LOOK AT OTHER PHONE BRANDS AS WELL.

    TAKE THIS INTO CONSIDERATION AND IF YOU DEEM IT MIGHT BE MORE FRUITFUL(FOR A WHY_PROMPT WITH ONE BRAND IN IT) TO NOT FOCUS SOLELY ON PRODUCTS OF THAT ONE BRAND BASED ON THE WHY_PROMPT THEN DECIDE TO RETURN 0 FOR contains_brand OTHERWISE CONTINUE AS USUAL.

***IMPORTANT CONSIDERATIONS FOR PRICE DETECTION***
    REMEMBER THAT THE PURPOSE IS TO DETERMINE IF PRICE SHOULD BE INCLUDED AS AN ATTRIBUTE IN A CONJOINT SURVEY ANALYSIS. PRICE IS OFTEN A FUNDAMENTAL ATTRIBUTE IN PRODUCT CHOICE SCENARIOS, BUT MAY NOT BE RELEVANT IN EVERY CONTEXT.
    
    FOR EXAMPLE, IN A WHY_PROMPT LIKE "What aesthetic features make consumers prefer certain watch designs?" PRICE IS NOT DIRECTLY RELEVANT AS THE FOCUS IS SPECIFICALLY ON AESTHETIC ATTRIBUTES.
    
    HOWEVER, IN A WHY_PROMPT LIKE "What factors influence smartphone purchasing decisions?" PRICE WOULD BE HIGHLY RELEVANT EVEN IF NOT EXPLICITLY MENTIONED.

Follow the output format strictly. The contains_brand value should be an integer and the contains_price value should be a boolean (true or false). Importantly always return true or false in lowercase otherwise the JSON response formatting will be messed up!
""")

GENERIC_OR_SPECIFIC_ATTRIBUTE_CHECK = inspect.cleandoc("""
You will help me determine which of the following configuration should be used for a stated preference survey design.
Please pay attention as this is a critical step in the design of survey design.

##Configurations:
	1. Whether to use product-specific attributes whose levels/values are created based on specific real world products (product_specific)
	2. Whether to use attributes whose levels/values are rather generic and reasonably represents real world product features (generic)

## Input variable: 
Statement: {why_prompt}

### Instructions:
Follow these steps precisely and provide detailed explanations for each:
##Step 1: Research Objective Analysis:
	- What is the core objective based on my description?
	- Is the focus on consumer choices or psychological motivations?
	- Am I trying to predict market behavior or understand decision processes?

##Step 2: Target Audience Evaluation
	- How familiar are they likely to be with product attributes?
	- Is their decision-making primarily rational or emotional?
	- Do they need technical knowledge to understand product attributes?

##Step 3: Product Category Assessment
	- Is it primarily utilitarian or hedonic?
	- Are purchase decisions typically based on specifications or feelings?
	- Does this category have clear, measurable attributes?

##Step 4: Competitive Analysis
	- How are similar products positioned?
	- Do competitors emphasize technical specifications or emotional benefits?
	- Are there standardized attributes in this product category?

##Step 5: Measurement Feasibility Check
	- Can the attributes be clearly quantified?
	- Can psychological attributes be reliably measured?
	- Will respondents be able to meaningfully compare these attributes?

##Step 6: Trade-off Analysis
	- Which attribute type would provide more actionable insights?
	- Which approach would yield more reliable responses?
	- Which method better supports the research objectives?

##Step 7: Final Recommendation (Based on all previous analyses)
	- Provide a clear recommendation for attribute type
	- Justify your choice based on the above analyses


## Output Format
   Input: "What factors affect the choice of a Apple products?"
   Output Template: [
    ["attribute_type": "generic"],
    ["reason": "The focus of the question is on undertsnading overall Apple product preferences and not one specific category.
	            Therefore, the attributes must measure overall inclination towards apple product covering entire range."]]

	Input: "What factors affect the choice of a smartphone"
    Output Template: [
	["attribute_type": "product_specific"],
    ["reason": "The focus of the question is on factors affecting choice of a smartphone.
	            Therefore, the attributes must measure specific aspects of the smartphone to derive meaningful results"]]

IMPORTANT RULES:
1. Only respond in JSON format without any additional explanation or details.

{format_instructions}
""")

STATEMENT_MODIFY_TO_SUIT_GENERIC_OR_SPECIFIC_ATTRIBUTE_TEMPLATE = inspect.cleandoc("""
You will help me rephrase the statement such that the attribute_type becomes product_specific.
Below are the steps to be performed to determine attribute_type:

##Definition:
	1. product_specific attributes: attributes whose levels/values are created based on specific real world products (product_specific)
	2. generic attributes: attributes whose levels/values are rather generic and reasonably represents real world product features (generic)
## Rules for rephrasing:
 - Ensure that the rephrased statement preserve or exceed the original statement's detail and intent.
 - The rephrased statement must not appear overly brief compared to the input statement, and should be stylistically aligned with the inferred domain, maintaining realism, terminology, and framing appropriate to that context (e.g., technical precision in healthcare vs. behavioral tone in consumer choices).
 - If the input is longer than 3 lines or contains multiple value propositions, ensure the rephrased statement is similarly descriptive (minimum 3–4 lines or 50+ words).
 - Even if the original statement is brief, the rephrased statement may be longer if needed to express a complete and nuanced trade-off scenario suitable for conjoint analysis. Ensure that the rephrased statement is detailed enough to reflect measurable product alternatives, clarify the consumer decision context, and preserve implied intent or unstated assumptions (e.g., price sensitivity, convenience, or functional needs).

## Input variable: 
Statement: {why_prompt}

### Instructions:
Follow the steps described below to determine attribute type and accordingly rephrase the statement as necessary:
##Step 1: Research Objective Analysis:
	- What is the core objective based on my description?
	- Is the focus on consumer choices or psychological motivations?
	- Am I trying to predict market behavior or understand decision processes?

##Step 2: Target Audience Evaluation
	- How familiar are they likely to be with product attributes?
	- Is their decision-making primarily rational or emotional?
	- Do they need technical knowledge to understand product attributes?

##Step 3: Product Category Assessment
	- Is it primarily utilitarian or hedonic?
	- Are purchase decisions typically based on specifications or feelings?
	- Does this category have clear, measurable attributes?

##Step 4: Competitive Analysis
	- How are similar products positioned?
	- Do competitors emphasize technical specifications or emotional benefits?
	- Are there standardized attributes in this product category?

##Step 5: Measurement Feasibility Check
	- Can the attributes be clearly quantified?
	- Can psychological attributes be reliably measured?
	- Will respondents be able to meaningfully compare these attributes?

##Step 6: Trade-off Analysis
	- Which attribute type would provide more actionable insights?
	- Which approach would yield more reliable responses?
	- Which method better supports the research objectives?

##Step 7: Final Recommendation (Based on all previous analyses)
	- Provide a clear recommendation for attribute type
	- Justify your choice based on the above analyses

##Step 8: If the attribute type is generic (based on the step 7 outcome), then rephrase the sentence and iterate through step 1 to 7 to ensure all checks are correctly verified.
The rephrased statement must preserve or exceed the original meaning and intent while ensuring that the attribute type is product_specific.
If the attribute type is product specific, then return the same statement.

## Examples:
   Input: "What factors affect the choice of a Apple products?"
   Output Template: [
    ["attribute_type": "generic"],
    ["reason": "The focus of the question is on understanding overall Apple product preferences and not one specific category.
	            Therefore, the attributes must measure overall inclination towards apple product covering entire range."
	]
	["modified_statement": "What factors make Apple products appealing compared to its competitors?"]
	]

	Input: "What factors affect the choice of a smartphone"
    Output Template: [
	["attribute_type": "product_specific"],
    ["reason": "The focus of the question is on factors affecting choice of a smartphone.
	            Therefore, the attributes must measure specific aspects of the smartphone to derive meaningful results"],
    ["modified_statement": "What factors affect the choice of a smartphone?"]
	]

IMPORTANT RULES:
1. Only respond in JSON format without any additional explanation or details.

Variable:
Problem statement: {why_prompt}
{format_instructions}
""")

TRAIT_SUGGESTION_PROMPT = inspect.cleandoc("""
You are tasked with generating {number_of_new_traits} new traits, each with {number_of_levels_per_trait} levels, based on the main question/context {why_prompt} and considering the existing traits {selected_traits}. 
Follow the steps below to generate new traits and their levels:
1. **Understand the Context**: Analyze the main question/context {why_prompt} to identify the domain and purpose (e.g., learning preferences, social interactions, decision-making). Consider what aspects are relevant to this context.
2. **Review Existing Traits**: Examine the provided traits {selected_traits} to understand their scope and ensure the new trait is distinct. List the existing traits to confirm no overlap in meaning or intent.
3. **Generate New Traits**: generate new traits which are relevant to the context and do not exist in {selected_traits}.
4. **Ensure Trait Distinctiveness**: Confirm that the newly generated traits are semantically and quantitatively different from any trait in {selected_traits}.
  - Semantic difference: It captures a unique concept (e.g., “Study Environment” vs. “Learning Pace”).
  - Quantitative difference: It measures a different axis (e.g., “Group Size” is not just a subtype of “Study Environment”).
5. **Define Levels for the Trait**: For each of the newly generated traits, generate {number_of_levels_per_trait} levels that:
  - Are relevant to individuals living in {country}.
  - Are **mutually exclusive** and **collectively exhaustive**.
  - Use **clear, consistent terminology** and a **logical order**.
  - Reflect **meaningful distinctions** aligned with the trait and context.
  - Are **descriptive with detailed qualifiers**, yet concise enough to stay within the {max_length} character limit.
6. **Validate Levels**: Ensure that levels meet all criteria and adjust as needed to ensure alignment with the trait and context.
7. **Format Output**: Present the result in the following JSON structure, with no extra text or explanation:

**Requirements**:
- Generate exactly {number_of_levels_per_trait} levels for each trait.
- Ensure the new traits do not exist in {selected_traits}.
- Levels must add meaningful value to the use case in {why_prompt}.
- Keep levels concise yet descriptive, with detailed qualifiers that clarify distinctions and respect the {max_length} character limit.
- Respond only in JSON format as per stated at the end, containing the trait and its levels.

**Important Rules**:
- The generated traits must be semantically and quantitatively distinct from {selected_traits}.
- Do not provide explanations or additional details beyond the JSON output.

{format_instructions}
""")


TRAIT_LEVELS_SUGGESTION_PROMPT = inspect.cleandoc("""
You are tasked with generating {levels_count} unique and relevant levels for a new trait based on the main question/context {why_prompt} and considering the existing traits {existing_traits}.

Follow the steps below to generate a new trait and its levels:
1. **Understand the Context**: Analyze the main question/context {why_prompt} to identify the domain and purpose (e.g., learning preferences, social interactions, decision-making). Consider what aspects are relevant to this context.
2. **Review Existing Traits**: Examine the provided traits {existing_traits} to understand their scope and ensure the new trait is distinct. List the existing traits to confirm no overlap in meaning or intent.
3. **Validate Input Trait**: Carefully evaluate the provided trait '{new_trait}' using the following criteria:
  - **Preserve** the trait as-is if it is **understandable**, **non-technical**, and has **any plausible relevance**—even indirect—to the context {why_prompt}. Err on the side of keeping the input unless it is clearly unusable.
  - Only rephrase the trait if it is:
     - (a) **Clearly irrelevant** to {why_prompt} with no justifiable influence (e.g., “Favorite Color” for electric car purchases), or
     - (b) **Vague, ambiguous, or uninterpretable** in any context (e.g., “Mindset” with no qualifier).
  - **Do not** rephrase the trait simply because another trait might appear more optimal. Your job is to assess whether the given trait is *acceptable*, not to optimize or editorialize it.
  - Example: If the trait is “Political affiliation” and the context is “purchasing an electric car,” **retain it**—it has plausible contextual influence.

4. **Ensure Trait Distinctiveness**: Confirm that the trait (original or rephrased) is semantically and quantitatively different from any trait in {existing_traits}.
  - Semantic difference: It captures a unique concept (e.g., “Study Environment” vs. “Learning Pace”).
  - Quantitative difference: It measures a different axis (e.g., “Group Size” is not just a subtype of “Study Environment”).

5. **Define Levels for the Trait**: For the validated (or rephrased only if required) trait, generate {levels_count} levels that:
  - Are relevant to individuals living in {country}.
  - Are **mutually exclusive** and **collectively exhaustive**.
  - Use **clear, consistent terminology** and a **logical order**.
  - Reflect **meaningful distinctions** aligned with the trait and context.
  - Are **descriptive with detailed qualifiers**, yet concise enough to stay within the {max_length} character limit.

6. **Validate Levels**: Ensure that levels meet all criteria and adjust as needed to ensure alignment with the trait and context.

7. **Format Output**: Present the result in the following JSON structure, with no extra text or explanation:

**Requirements**:
- Generate exactly {levels_count} levels for the trait.
- Ensure the trait (original or rephrased) does not exist in {existing_traits}.
- Levels must add meaningful value to the use case in {why_prompt}.
- Keep levels concise yet descriptive, with detailed qualifiers that clarify distinctions and respect the {max_length} character limit.
- Respond only in JSON format as per stated at the end, containing the trait and its levels.

**Important Rules**:
- The generated trait must be semantically and quantitatively distinct from {existing_traits}.
- Do not provide explanations or additional details beyond the JSON output.

{format_instructions}
""")

TARGET_BEHAVIOUR_PROMPT = inspect.cleandoc("""
Given a research problem statement about a stated preference survey, analyze it to identify:
1. The target behavior being studied
2. Whether the survey aims to identify factors that maximize or minimize this behavior

Important guidelines:
- The target behavior should be a single word or short phrase describing the core behavior
- The optimization direction should be inferred from phrases like:
  * "contribute to X" → maximize X
  * "factors causing X" → maximize X
  * "reduce X" → minimize X
  * "prevent X" → minimize X
- Include clear reasoning for your choices

Problem statement to analyze: {why_prompt}

IMPORTANT RULES:
- Only respond in JSON format without any additional explanation or details.

{format_instructions}
""")

STUDY_CONTEXT_PROMPT = inspect.cleandoc("""
Given a target behavior and its associated attributes for a stated preference survey, generate an appropriate decision context that puts the respondent in a realistic situation where these attributes would influence their behavior.

Target Behavior: {target_behavior}
Attributes being studied: {attributes}

Generate a decision context following these guidelines:
1. The context should be a single sentence starting with "you are" or "you need to"
2. It should be specific enough to make the choice meaningful but generic enough to be widely relatable
3. It should be a situation where all the listed attributes could realistically influence the target behavior
4. The context should not bias towards any particular attribute

IMPORTANT RULES:
- Only respond in JSON format without any additional explanation or details.

{format_instructions}
""")


SURVEY_PROMPT_WITH_TARGET_BEHAVIOR = inspect.cleandoc("""
You are participating in a study examining factors that influence {target_behavior}.
You are to embody and respond as a person with the following characteristics:
DEMOGRAPHIC PROFILE
{demographics_section}

STUDY CONTEXT
Imagine you are in a situation where {context}.
 - Consider the different combinations of factors and select the option that
 would most likely lead to the {optimization} level of {target_behavior}.
 - When evaluating each option, consider how the combination of factors would
 realistically interact to affect {target_behavior}.
 - Choose the scenario where the presented conditions would be most conducive to
 {optimizing} {target_behavior}.
 {none_choice_instruction}

RESPONSE GUIDANCE
When evaluating scenarios, consider these key aspects of your profile:
{guidance_section}

CORE INSTRUCTIONS
{core_instruction}

Questionnaire:
{dependent_variable}
{options}

IMPORTANT INSTRUCTIONS:
Please adhere to the instructions provided below. Do not deviate.
1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
2. Example response format: {{"selected_option": "Option 2"}}

{format_instructions}
""")

SURVEY_PROMPT_WITH_TARGET_BEHAVIOR_WITH_DECISION_STRATEGY = inspect.cleandoc("""
You are participating in a study examining factors that influence {target_behavior}.
You are to embody and respond as a person with the following characteristics:
DEMOGRAPHIC PROFILE
{demographics_section}

STUDY CONTEXT
Imagine you are in a situation where {context}.
 - Consider the different combinations of factors and select the option that
 would most likely lead to the {optimization} level of {target_behavior}.
 - When evaluating each option, consider how the combination of factors would
 realistically interact to affect {target_behavior}.
 - Choose the scenario where the presented conditions would be most conducive to
   {optimizing} {target_behavior}.
 {none_choice_instruction}

RESPONSE GUIDANCE
When evaluating scenarios, consider these key aspects of your profile:
{guidance_section}

CORE INSTRUCTIONS
 - {core_instruction}
 - You must also identify which type of decision rule heuristic you used to arrive at that preferred option. This can be either "C" (compensatory), "NC" (non-compensatory) or "H" (hybrid).

Questionnaire:
{dependent_variable}
{options}

IMPORTANT INSTRUCTIONS:
Please adhere to the instructions provided below. Do not deviate.
1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
2. Example response format: {{"selected_option": "Option 2", "decision_strategy": "C"}}

{format_instructions}
""")

SURVEY_PROMPT_WITH_TARGET_BEHAVIOR_WITH_DECISION_STRATEGY_BOTH = inspect.cleandoc("""
You are tasked with answering a conjoint survey question related to examining factors that influence {target_behavior} as the following individual:
Persona: {demographics_section}
STUDY CONTEXT
Imagine you are in a situation where {context}.
 - Consider the different combinations of factors and select the option that
 would most likely lead to the {optimization} level of {target_behavior}.
 - When evaluating each option, consider how the combination of factors would
 realistically interact to affect {target_behavior}.
 - {wording_for_study_context}{optimizing} {target_behavior}.
 {none_choice_instruction}

Answer the survey question as this person, following these steps:
{survey_time_proportion}
{guidance_section}

Questionnaire:
{dependent_variable}
{options}

IMPORTANT INSTRUCTIONS:
Please adhere to the instructions provided below. Do not deviate.
1. Respond ONLY with the JSON object shown in the example - no explanations, no additional text
2. Example response format: {response_format}
{probabilistic_response_instructions}
    
{format_instructions}
""")

VARIABLE_TO_NATURAL_LANGUAGE_TEMPLATE = inspect.cleandoc("""
For each of the following demographic variables, create natural language
templates for describing a person ('demo') and providing response guidance ('guide').

The 'demo' template should be a phrase that can be part of a larger sentence
describing someone's characteristics.
The 'guide' template should be a consideration point for how this characteristic influences responses.

Use {{}} as the placeholder in both demo and guide templates.

Variables to process:
demographic variables: {demo_variables}

Input Example:
[
['attribute': 'age', 'type': "non-binary"],
['attribute': 'hispanic', 'type': "binary"],
['attribute': 'race', 'type': "non-binary"],
['attribute': 'female children', 'type': "non-binary"],
['attribute': 'male children', 'type': "non-binary"],
['attribute': 'children 0 - 5 years old', 'type': "non-binary"],
['attribute': 'children 6 - 10 years old', 'type': "non-binary"],
['attribute': 'children 11 - 15 years old', 'type': "non-binary"],
]

Output Example for non-binary:
{{
    "age": {{
        "demo": "{{}} years old",
        "guide": "Your life experience and perspective as someone in their {{}}s"
    }},
    "race": {{
        "demo": "{{}} race",
        "guide": "Your unique perspective and experiences as a member of the {{}} racial group"
    }},
    "female children": {{
        "demo": "{{}} female children",
        "guide": "Think about how the presence of {{}} female children in your life might influence your perspective"
    }},
    "male children": {{
        "demo": "{{}} male children",
        "guide": "Think about how the presence of {{}} male children in your life might influence your perspective"
    }},
    "children 0 - 5 years old": {{
        "demo": "{{}} young children (0-5 years)",
        "guide": "Consider the impact of {{}} young children (0-5 years) on your daily routine"
    }},
    "children 6 - 10 years old": {{
        "demo": "{{}} school-age children (6-10 years)",
        "guide": "Consider the impact of {{}} school-age children (6-10 years) on your daily routine"
    }},
    "children 11 - 15 years old": {{
        "demo": "{{}} adolescent children (11-15 years)",
        "guide": "Consider the impact of {{}} adolescent children (11-15 years) on your daily routine"
    }}
}}
Output Example for binary:
{{
    "hispanic": {{
        "demo": "{{}} hispanic",
        "guide": "Your cultural identify as {{}} hispanic"
    }}
}}

Requirements:
Make templates natural-sounding when combined with other characteristics
Ensure 'demo' templates can be joined with commas in a sentence
Ensure 'guide' templates work as individual bullet points
Consider how each characteristic might influence someone's perspective
Make templates flexible enough to handle various possible values
Avoid redundant words when templates are combined
Keep language clear and professional

IMPORTANT INSTRUCTIONS:
1. 'demo' cannot be None.
2. For each of the demographic variables, always provide both 'demo' and 'guide' templates.
3. Do not drop any demographic variables in the output.
4. Only respond in JSON format without any additional explanation or details.
{format_instructions}
""")


# TODO: Tell Subodh which prompts to check.
PERSONA_IN_NATURAL_LANGUAGE_TEMPLATE = inspect.cleandoc("""
You are tasked with creating a persona description based on the following demographic details provided in key-value pairs, tailored to the context of a conjoint survey:  
Demographic Details: {demographic_data}  
Survey Question: {why_prompt}  
Attribute Labels: {attributes}  

Using this information, construct a vivid yet concise persona that embodies the provided demographic traits, imagining their lifestyle, priorities, and 
constraints. Focus exclusively on those demographic details that logically influence their preferences, needs, or 
decision-making related to the survey question and its attributes, automatically disregarding any traits that lack a clear connection to this context. 
Ensure that each relevant trait contributes to a unique perspective—even if some traits are subtle or similar to others. 
Keep the description detailed enough to capture their identity and motivations in this context, but brief enough to avoid unnecessary elaboration—aim for 
a natural length that feels complete without being overly verbose.  

Examples:
Input1:
Demographic Details:{{
    "Age": 35,  
    "Gender": "Male",  
    "Income": "$60000",  
    "Occupation": "Teacher",  
    "Location": "Suburban",  
    "Family Status": "Married with two kids",
}}
Survey Question: What factors affect the choice of a car?
Attribute Labels: ["Cost", "Fuel Economy", "Seating Capacity", "Safety Features", "Luxury Features"]
Output1:
Persona: "I am a 35-year-old male teacher living in the suburbs with my wife and two kids, earning $60,000 a year. 
My focus is on stretching our modest budget to cover family needs, so I prioritize a car with enough seating and solid safety features 
over anything flashy or luxurious."
Input2:
Demographic Details:{{
    "Age": 35,  
    "Gender": "Male",  
    "Income": "$80000",  
    "Occupation": "Teacher",  
    "Location": "Suburban",  
    "Family Status": "Married with two kids",
}}
Survey Question: What factors affect the choice of a car?
Attribute Labels: ["Cost", "Fuel Economy", "Seating Capacity", "Safety Features", "Luxury Features"]
Output2:
Persona: "I am a 35-year-old male teacher in the suburbs with my wife and two kids, pulling in $80,000 annually. 
My higher income gives me some flexibility, so while I still need a car with room for my family and good safety, 
I also consider comfort and a bit of style as nice extras."

IMPORTANT RULES:
1. The persona description must be in the first person.
2. Only respond in JSON format without any additional explanation or details.
{format_instructions}
""")


PERSONA_IN_NATURAL_LANGUAGE_IMAGE_TEMPLATE = inspect.cleandoc("""
You are given demographic details in key-value pairs and a product image (described below).
Your task is to write a concise, vivid persona description that embodies the individual represented by the demographics, focusing on their lifestyle, priorities, and preferences **as they relate to the product shown in the image**.
Do NOT mention product-specific features or attributes. Instead, describe the persona's attitudes, routines, and motivations that would lead to a preference for a product like the one in the image.
Ensure each demographic trait contributes uniquely to their perspective or decision-making. Ignore traits that do not logically influence their preference.
Avoid unnecessary elaboration. Aim for a length that feels complete but not verbose.

**Demographic Details:**
{demographic_data}

**Product Image Description:**
{image_description}

Examples:
Input:
Demographic Details:{{
    "Age": 35,  
    "Gender": "Male",  
    "Income": "$60000",  
    "Occupation": "Teacher",  
    "Location": "Suburban",  
    "Family Status": "Married with two kids",
}}
Product Image Description: "The image features two bottles of a milk-based smoothie called "Milk&." The packaging highlights that the smoothie is high in protein, contains fiber and antioxidants, has no added sugar, and is lactose-free. The bottles are shown in two sizes and display fruit imagery, suggesting flavor options like bananas and strawberries. The product is presented as a convenient, nutritious beverage, likely positioned in the refrigerated section of a grocery store."
Output:
Persona: "A 35-year-old male teacher living in a suburban community with his spouse and two young children, he values both practicality and wellness in his daily life. With a moderate-to-high household income, he is mindful about making choices that benefit his family’s health, energy, and long-term well-being. His job as an educator means busy mornings and active days, so he prefers food and drink options that are convenient, nourishing, and can satisfy diverse tastes within his household.
Being married with children, he seeks products that can easily fit into a family routine—something quick, wholesome, and enjoyable for both adults and kids. Living in the suburbs, he typically shops at larger grocery stores where fresh and healthy selections are accessible. His preferences are shaped by a desire to balance nutrition with ease of use, making him naturally drawn to modern, health-oriented items found in the refrigerated section.
Budget is important, but not at the expense of quality; he is willing to pay a bit more for products that support an active, balanced lifestyle for his family. Overall, he gravitates toward choices that reflect his commitment to health, convenience, and caring for his loved ones."

IMPORTANT RULES:
1. The persona description must be in the first person.
2. Only respond in JSON format without any additional explanation or details.
{format_instructions}
""")

MONETARY_ATTRIBUTES_LEVELS_TEMPLATE = inspect.cleandoc("""
Instructions:
Below is the list of attrclear
ibutes with their levels/values: {existing_attributes_levels}
1. For each attribute, examine all of its values.
2. Check if the values contain recognized currency indicators:
    - Currency symbols: $, €, £, ¥, ₹, ₽, ₩, etc.
    - Currency codes: USD, EUR, GBP, JPY, INR, etc.
    - Common monetary terms: dollars, euros, pounds, etc.
3. Apply these specific rules to determine if an attribute is monetary:
    - If EACH value for an attribute contains at least one recognized currency indicator (symbol or code) anywhere within the value, and all values reference the same indicator
    - If ALL values follow a consistent format typical of monetary values (e.g., numbers with decimal precision typical of currency)
    - If ALL values reasonably represent monetary amounts in a consistent unit
    - IMPORTANT: If ANY values lack an explicit currency indicator, the attribute is NOT monetary
4. Classification criteria:
    - Classify as "MONETARY" only if EVERY value includes a recognized and consistent currency indicator somewhere within the value (a single occurrence in composite formats is acceptable)
    - Classify as "NON-MONETARY" if:
        - ANY values lack an explicit currency indicator
        - ANY values clearly don't represent currency amounts
        - ANY values contain different currencies
    - When in doubt, classify as "NON-MONETARY" - prioritize precision over recall

Examples:
- Attribute: "Cost", levels: ["100 USD", "200 USD", "300 USD"] → "MONETARY"
- Attribute: "Price Range", levels: ["0 - 50 USD", "50 - 100 USD"] → "MONETARY"
- Attribute: "Service Fee", levels: ["10 USD per hour", "20 USD per hour"] → "MONETARY"
- Attribute: "Salary", levels: ["50000", "60000", "70000"] → "NON-MONETARY" (missing currency indicator)
- Attribute: "Mixed", levels: ["100 USD", "200 EUR"] → "NON-MONETARY" (different currencies)
- Attribute: "Price", levels: ["$100", "$200"] → "MONETARY"
- Attribute: "Price", levels: ["100$", "200$"] → "MONETARY"
- Attribute: "Price", levels: ["100 $", "200 $"] → "MONETARY"

Note: Values may include ranges (e.g., "0 - 100 USD") or additional context (e.g., "50 USD per unit"), but are still classified as "MONETARY" if every value explicitly contains the same currency indicator.

IMPORTANT INSTRUCTIONS:
- Only respond in JSON format without any additional explanation or details.

{format_instructions}
""")

CAUSAL_EXPERIMENT_IDEATION_PROMPT = inspect.cleandoc("""
    Your task is to generate 5 new causal experiment ideas - specifically "why" questions to investigate - based on the provided design and results of a prior experiment. Each idea should aim to further investigate or refine the causal relationships uncovered in the prior experiment.
    You will be using a stated preference survey approach to investigate the causal relationships.

    Here are some things to consider when generating new causal experiment ideas:
    Feature Generation Assessment
    First, evaluate if the statement can be reformulated as a feature-generating question:

        - Can it be reworded to identify attributes/factors which are antecedents to the outcome?
        - Will responses generate measurable features?
        - The features should be such that it can be externally changed/manipulated to impact the context statement.
          The features should always be external and not internal. By internal, it means that an individual should not be able
          to change the features by themselves. For example, "What environmental practices would you consider adopting?"
          is incorrect as it refers to personal choices.
        - Can these features be used to create choice sets? The created choice sets should be valid in a sense that it presents options which
          an individual can consider (scenario) or purchase (product). The dependent variable is always framed as which option will you choose or purchase.

    Stated Preference Potential
    Then assess if these features can be used to construct a stated preference survey:

        - Can features be combined into realistic choice alternatives?
        - Are trade-offs between features meaningful?
        - Can responses be quantified?
        
    You will be given the following context for the previous experiment/survey:

    Experiment Design:
    - Why Question: {why_question}
    - Respondent Dependent Variable: {respondent_dependant_variable}
    - Respondent Context: {respondent_context}
    - Population Traits: {population_traits}
    - Attributes and Levels: {attributes_and_levels}

    Experiment Results:
    - {analysis_json_data}

    Generate 5 distinct and well-reasoned causal experiment ideas that could be feasibly executed based on the context provided. Each idea should be returned as a JSON object with the following fields:

    - "why_question": A refined or new causal question to investigate.
    - "rationale": A brief explanation of why this is a valuable follow-up experiment and what insights it seeks to uncover.

    The response should be a JSON array containing exactly 5 of these idea objects.
    
    Here are causal "why" questions you have already generated:
    - {why_question}
    - What factors influence a person's decision to buy a house?
    - What causes individuals to adopt eco-friendly behaviors?
    - What causes a person to want to drive a car?
    - Why do people rent rather than buy?

    This JSON will be automatically parsed, so ensure the format is precise.
""")

CAUSALITY_WITH_RATIONALE_TEMPLATE = inspect.cleandoc(
    """You are a market research data scientist.
  Your job is to determine if a problem statement can be answered using a stated preference survey approach.
  Follow the instructions below:

  ## Step 1: Statement Completion Check
  First, evaluate if the statement is a complete english sentence.
    - If yes, go to Step 2.
    - If No, extract the object of study from the statement. Use the object of study to formulate three statements which can be answered using a stated preference survey approach.
      Evaluate each of three statements for further evaluation by going through Step 2 and Step 3.

  ## Step 2: Feature Generation Assessment
  First, evaluate if the statement can be reformulated as a feature-generating question:

    - Can it be reworded to identify attributes/factors which are antecedents to the outcome?
    - Will responses generate measurable features?
    - The features should be such that it can be externally changed/manipulated to impact the context statement.
      The features should always be external and not internal. By internal, it means that an individual should not be able
      to change the features by themselves. For example, "What environmental practices would you consider adopting?"
      is incorrect as it refers to personal choices.
    - Can these features be used to create choice sets? The created choice sets should be valid in a sense that it presents options which
      an individual can consider (scenario) or purchase (product). The dependent variable is always framed as which option will you choose or purchase.

  ## Step 3: Stated Preference Potential
  Then assess if these features can be used to construct a stated preference survey:

    - Can features be combined into realistic choice alternatives?
    - Are trade-offs between features meaningful?
    - Can responses be quantified?

  Response Format
  Output should follow this structure:
  "is_causal": bool, "rationale": str
  Examples
    Input: "What is the meaning of life?"
    Output Template: is_causal: False, rationale: "The question is too abstract and philosophical to be answered using a stated preference survey approach."

    Input: "Which transportation mode do you prefer?"
    Output Template: is_causal: False, rationale: "The question does not directly enable feature identification and is too broad for a stated preference survey approach."

    Input: "What factors affects the choice of purchasing a car?"
    Output Template: is_causal: True, rationale: ""

    Input: "How does human activity impact global warming?"
    Output Template: is_causal: False, rationale: "The question is too broad and does not focus on specific, measurable features that can be manipulated in a stated preference survey."

    Input: "What factors"
    Output Template: is_causal: False, rationale: "The statement is incomplete and does not contain an object of study."

    Input: "What influences"
    Output Template: is_causal: False, rationale: "The statement is incomplete and does not contain an object of study."

    Input: "Car"
    Output Template: is_causal: False, rationale: "The statement is incomplete and does not contain an object of study."

  Implementation Steps

  1. If the statement involves illegal and harmful activities, respond with is_causal: False and rationale: "The statement involves illegal or harmful activities."
  2. Evaluate if the statement directly enables feature identification
  3. For is_causal: False, provide a rationale explaining why the statement is not suitable for a stated preference survey approach
  4. For is_causal: True, provide an empty string for rationale
  5. Assess if identified features can create meaningful choice sets

  IMPORTANT:
  1. Only respond in JSON format.
  2. If the statement is incomplete or does not contain object of study, then respond with is_causal: False and rationale: "The statement is incomplete and does not contain an object of study."
  3. Usually, the problem statements which starts with "How" are not suitable for analysis using a stated preference survey approach. So, avoid such statements in the suggestions.
  4. If a statement is not suitable for analysis using stated preference survey approach, then it cannot be part of the suggestions.
  5. All the suggestions should pass Step 2: Feature Generation Assessment and Step 3: Stated Preference Potential
  6. Avoid suggestions that involve personal practices or choices that an individual can change themselves
  7. Focus on external factors that can be manipulated and combined into meaningful choice sets
  8. Do not copy suggestions from the examples provided.
  9. Don't be afraid to respond with false to the problem statement unless you are very sure it should be true
  Variable:
  Problem statement: {why_prompt}
  """
)

CAUSAL_EXPERIMENT_IDEATION_REFINEMENT_PROMPT = inspect.cleandoc("""
  Your task is to refine one new causal experiment idea - specifically a "why" question to investigate - based on the provided design and results of a prior experiment. The goal is to further investigate or refine the causal relationships uncovered in the prior experiment.
  You will be using a stated preference survey approach to investigate the causal relationships.

  Here are some things to consider when refining the causal experiment idea:
  Feature Generation Assessment
  First, evaluate if the statement can be reformulated as a feature-generating question:

    - Can it be reworded to identify attributes/factors which are antecedents to the outcome?
    - Will responses generate measurable features?
    - The features should be such that it can be externally changed/manipulated to impact the context statement.
      The features should always be external and not internal. By internal, it means that an individual should not be able
      to change the features by themselves. For example, "What environmental practices would you consider adopting?"
      is incorrect as it refers to personal choices.
    - Can these features be used to create choice sets? The created choice sets should be valid in a sense that it presents options which
      an individual can consider (scenario) or purchase (product). The dependent variable is always framed as which option will you choose or purchase.

  Stated Preference Potential
  Then assess if these features can be used to construct a stated preference survey:

    - Can features be combined into realistic choice alternatives?
    - Are trade-offs between features meaningful?
    - Can responses be quantified?
    
  You will be given the following context for the previous experiment/survey:

  Experiment Design and Results:
  {context}

  Previous Ideas and Feedback:
  {previous_ideas_and_feedback}

  Refine one distinct and well-reasoned causal experiment idea that could be feasibly executed based on the context provided. The idea should be returned as a JSON object with the following fields:

  - "why_question": A refined or new causal question to investigate.
  - "rationale": A brief explanation of why this is a valuable follow-up experiment and what insights it seeks to uncover.

  The response should be a JSON object containing the refined idea.

  This JSON will be automatically parsed, so ensure the format is precise.
""")

REAL_WORLD_CONJOINT_STATEMENTS_PRELIMINARY_TEMPLATE = inspect.cleandoc("""
**Role**: You are an **interdisciplinary stated preference methodologist** with expertise in designing conjoint analysis studies across diverse domains, including but not limited to: **economics, healthcare, transportation, consumer behavior, technology adoption, environmental policy, energy systems, public health, urban planning, education, insurance, finance, psychology, and patient preference research**.

You adapt your language, structure, and conceptual framing to reflect the conventions and expectations of the relevant domain — whether scientific precision in healthcare, behavioral framing in psychology, cost-benefit trade-offs in economics, or service design parameters in transportation and infrastructure.

Your task is to evaluate a problem statement, ensure that it passes the preliminary checks and is ready for the conjoint survey design, and provide feedback if necessary. Follow the process below to analyze the input statement and generate an output in JSON format.
                                                                       
### Statement History Context

You are provided with the full history of statement versions in reverse chronological order under the variable `statement_history`.

Use the **most recent version** (i.e., **input**) as the primary statement for Step 1–3 analysis.

You may refer to earlier versions in the history to:
- Clarify ambiguities or vague references in the latest version.
- Recover useful detail that was dropped in recent simplifications.
- Understand how the user's intent or problem framing has evolved.

The input format is:
"History": ["...","...","..."]
                                                                       
---

**Input**: {why_prompt}  
**History**: {statement_history}
**Task**: Evaluate the statement, determine whether it passes all preliminary checks listed below and provide feedback if necessary.

---

### Aspect Check – Strict Pre-Validation (MANDATORY)
Before proceeding, check for the presence of **all five required contextual elements** using the input and history:

#### REQUIRED:
1. **legal or Ethical Content** 
2. **Object of study** (e.g., product, service, policy, perception, etc.)
3. **Final objective or decision to be supported** (e.g., price sensitivity, messaging, segmentation)
4. **User’s role or designation** in this context (e.g., founder, product manager, analyst, policymaker - this must be an actual recognized designation and not something made up like 'seller' or 'designer'. Bias towards specificity.)
5. **Domain or context** (e.g., industry, sector, or application area)

#### Mandatory Checks:
- If any one of these five elements is missing or unclear, return a **structured error response** and **terminate immediately**. Here, to 'terminate immediately' means that you should not check against any further mandatory checks and return the error response of the first failing check ONLY, i.e only one the first mandatory check failure should be reported at a time.

Here are the mandatory checks and their corresponding error responses:
- If **illegal or unethical content** return:
{{
	"is_causal": false,
	"flag": "Error",
	"suggestions": [{{
		"text": "I'm here to help with safe and respectful conversations. Unfortunately, I can't assist with your current request. If you have a question related to research, product development, or decision-making support, feel free to ask!",
		"attributes": []
	}}]
}}
- If the **Domain or context** is missing return:
{{
  "is_causal": false,
  "flag": "Error",
  "suggestions": [{{
    "text": "Thanks! That gives us a starting point.\n\n"\
        "To tailor suggestions more effectively, could you tell me a bit more about the domain or use case? For example:\n"\
		"- What kind of product, service, or industry are we talking about?\n"\
		"- Who's the target customer or decision-maker?\n"\
		"- Where would this be used — in what market or context?\n"\
		"The more context you can share, the better I can shape the next step.",
    "attributes": []
  }}]
}}

- If the **Final objective or decision to be supported** is missing return:
{{
  "is_causal": false,
  "flag": "Error",
  "suggestions": [{{
    "text": "Thanks, that helps set the scene!\n"\
         "To guide you better, could you tell me the outcome you're aiming for? For example:\n"\
         "- Are you trying to increase your market share?\n"\
         "- Are you evaluating price sensitivity or willingness to pay?\n"\
         "- Is this about prioritizing what matters most to your users?\n"\
         "- Or maybe comparing options for messaging, branding, or design?\n"\
         "Even if you're still exploring, a rough idea of the decision or insight you're after would be super helpful.",
    "attributes": []
  }}]
}}

- If the **User’s role or designation** is missing return:
{{
  "is_causal": false,
  "flag": "Error",
  "suggestions": [{{
    "text": "Thanks — that's helpful context!\n"\
        "Just to tailor things better, could you let me know your role in this project?\n"\
        "For example: Product Manager, Marketing Analyst, UX Researcher, Founder, Consultant...\n"\
        "Knowing your role helps me suggest approaches that fit your goals and responsibilities best.",
    "attributes": []
  }}]
}}

- If the **Object of study** is missing return:
{{
  "is_causal": false,
  "flag": "Error",
  "suggestions": [{{
    "text": "Just to clarify — what is the specific product, service, idea, or policy you're working on?\n This helps me understand your problem better.",
    "attributes": []
  }}]
}}

---
                                                                       
{format_instructions}
""")


REAL_WORLD_CONJOINT_STATEMENTS_TEMPLATE = inspect.cleandoc("""
                                                           
**Role**: You are an **interdisciplinary stated preference methodologist** with expertise in designing conjoint analysis studies across diverse domains, including but not limited to: **economics, healthcare, transportation, consumer behavior, technology adoption, environmental policy, energy systems, public health, urban planning, education, insurance, finance, psychology, and patient preference research**.

You adapt your language, structure, and conceptual framing to reflect the conventions and expectations of the relevant domain — whether scientific precision in healthcare, behavioral framing in psychology, cost-benefit trade-offs in economics, or service design parameters in transportation and infrastructure.

Your task is to evaluate a problem statement and suggest a conjoint survey design with real-world product or service attributes. Follow the process below to analyze the input statement and generate an output in JSON format.
                                                           
### Statement History Context

You are provided with the full history of statement versions in reverse chronological order under the variable `statement_history`.

Use the **most recent version** (i.e., **input**) as the primary statement for Step 1–3 analysis.

You may refer to earlier versions in the history to:
- Clarify ambiguities or vague references in the latest version.
- Recover useful detail that was dropped in recent simplifications.
- Understand how the user's intent or problem framing has evolved.

The input format is:
"History": ["...","...","..."]
---

**Input**: {why_prompt}  
**History**: {statement_history}
**Task**: Evaluate the statement and propose a minimum of {suggestion_count} suggestions which can be answered/modelled using 
a conjoint survey design with attributes and levels that are either:
	- **Observable in the real world**, or
	- **Perception-based/generic trade-offs** (e.g., message framing, tone, endorsement, value signals), if real-world attributes are insufficient.

Use specific context inferred from the statement (e.g., stakeholder role, industry, goals, constraints) to ensure realistic framing.

---

### Step 1:Causality Assessment
- **Objective**: Determine if the statement can be answered using a stated preference survey approach (conjoint analysis).
- **Sub-Steps**:
  1. **Statement Completion Check**:
     - If a complete English sentence, proceed to Feature Generation.
     - If incomplete, extract the object of study and formulate three complete statements, then evaluate each.
  2. **Feature Generation Assessment**:
     - Reformulate the statement to identify external, manipulable attributes that generate measurable features and form valid choice sets (e.g., product design, campaign strategy, messaging approach).
     - If real-world features are unavailable, construct plausible **perceptual, psychological, or communication-based trade-offs**.
   	 - Ensure suggestions preserve or exceed the original statement's detail and intent.
     - **Mandatory Brand and Feature Inclusion**: If the original statement mentions specific brand names, product names, or feature claims (e.g., "Daylee," "24g protein," "digestive health"), all suggestions must explicitly include them to ensure contextual continuity and real-world relevance.
     - **Length and Richness Matching**: Suggestions must match or exceed the original statement's length and descriptive richness. This ensures the user perceives them as equally informative and not oversimplified. Include detailed qualifiers (e.g., ingredient benefits, usage contexts, product formats, and brand claims) to reflect the original complexity.
     - **Expansion for Short Inputs**: If the input is short or vague but implies a consumer decision problem (e.g., "What drives purchase of energy drinks?"), expand the suggestion using plausible product context, consumer behavior cues, and real-world category examples to build a comprehensive choice scenario.
     - **Incorporating Action Verbs**: Each suggestion must clearly reference an appropriate **action verb** that reflects a measurable decision stage or behavioral outcome. These include (but are not limited to): *awareness, consideration, preference, choice, purchase, usage, switching, loyalty, retention*. Choose the verb that best fits the context inferred from the original statement (e.g., *purchase* for consumer products, *preference* for policy evaluations, *usage* for digital services).
	 - **Incorporating History for Depth and Clarity**: If the input is vague, ambiguous, or oversimplified, revisit earlier versions in the **History** to recover more detailed product context, implied decision features, or prior domain framing. Leverage those to enhance current suggestions without contradicting the latest version.
	 - **Dynamic Context Addition**: Infer the statement's domain (e.g., travel, electronics, infrastructure) and add a specific, realistic context to each suggestion:
       - For travel: Add a plausible origin-destination (e.g., "New York to Tokyo" for long-haul flights).
       - For products: Specify a product instance (e.g., "Samsung Galaxy phone" for smartphones). Use multiple well-known brands if plausible.
       - For services/infrastructure: Define a scenario (e.g., "urban rail project in Chicago").
       - If domain is unclear, use a generic but realistic example tied to the object of study.
	 - **Action Verb Inclusion**: Each suggestion must include a relevant **action verb** that reflects the behavioral or decision stage being investigated. Common verbs include *awareness, consideration, preference, choice, purchase, usage, switching, loyalty, retention*. Select a verb appropriate to the inferred domain using the following guide:
		- **Consumer Goods** → purchase, preference, consideration, loyalty
		- **Healthcare** → treatment choice, adoption, side effect consideration
		- **Transportation** → mode choice, route selection, travel decision
		- **Technology & Subscriptions** → usage, adoption, subscription, retention
		- **Public Policy & Governance** → support, compliance, adoption, policy preference
		- **Education** → enrollment, learning engagement, program selection
        Use the action verb to make the intent of each suggestion more specific and measurable.
	 - **Domain-Aware Framing**: Infer the appropriate domain based on the topic (e.g., "patient treatment trade-offs" → healthcare; "route choices" → transportation), and frame each suggestion using terminology, assumptions, and framing conventions relevant to that field:
	   - For **healthcare**: Use clinical language, reference treatment outcomes, patient burden, or side effects.
	   - For **economics**: Emphasize trade-offs, incentives, opportunity costs, and utility.
	   - For **transportation**: Reference origin-destination pairs, service attributes (e.g., travel time, cost, reliability).
	   - For **psychology/behavioral sciences**: Emphasize motivations, attitudes, cognitive framing, or behavioral trade-offs.
	   - For **consumer goods**: Use benefit- and feature-led framing with reference to purchase context and brand claims.
	   - For **public policy or infrastructure**: Reference societal impacts, funding mechanisms, and usage scenarios.
	 - **Message Framing Domains**: For vague or image-related prompts (e.g., "improve brand trust"), generate trade-offs using **communication and trust signals**, such as:
	   - Voice (e.g., corporate vs. personal)
	   - Framing (e.g., inclusive vs. assertive)
	   - Transparency (e.g., admitting mistakes vs. pivoting topics)
	   - Political distancing (e.g., neutral stance vs. cause endorsement)
	   - Stakeholder focus (e.g., investor vs. public vs. employee)
	 - **Richness Matching**: Match or exceed the length and specificity of the input statement. Do not oversimplify.
     - If earlier versions in `statement_history` offer more concrete framing, use that to enhance the current statement.
  	
  3. **Stated Preference Potential**:
     - Can features (real or perceptual) be combined into realistic choice alternatives?
     - Are responses quantifiable?
     - Can trade-offs be reasonably inferred and measured?
	
- `"is_causal": bool` (True if all aspects of **Stated Preference Potential** are true, False otherwise)

- **Rules**:
  - If "is_causal": False, provide exactly {suggestion_count} reformulations; if True, provide {suggestion_count} related questions.
  - Avoid "How" questions unless they are essential to preserve the brand or product context from the original statement.
  - Suggestions must include a context-appropriate **action verb** (e.g., purchase, usage, preference) that aligns with the inferred domain and decision context.
  - Suggestions must not appear overly brief compared to the input statement, and should be stylistically aligned with the inferred domain, maintaining realism, terminology, and framing appropriate to that context (e.g., technical precision in healthcare vs. behavioral tone in consumer choices).
    If the input is longer than 3 lines or contains multiple value propositions, ensure suggestions are similarly descriptive (minimum 3–4 lines or 50+ words).
  - Even if the input statement is brief, suggestions may be longer if needed to express a complete and nuanced trade-off scenario suitable for conjoint analysis. Ensure that suggestions are detailed enough to reflect measurable product alternatives, clarify the consumer decision context, and preserve implied intent or unstated assumptions (e.g., price sensitivity, convenience, or functional needs).
  - If the statement involves illegal/harmful activities or lacks an object of study, return `"is_causal": false, "suggestions": []`.


#### Step 2: Real-World or Perception-Based Feasibility Check
Note: If Step 1 used earlier statements to enrich trade-off contexts, retain those contexts during attribute feasibility checks. 
For each suggestion, check if any supporting feature/brand/domain detail is derived from prior versions in **History**.
- **Objective**: Ensure each suggestion's attributes correspond to either:
   - Real-world products/services with observable data, or
   - Perception-based, attitudinal, or communication strategies that can be realistically tested and evaluated in stated preference experiments.
- **Sub-Steps** (for each suggestion from Step 1):
  1. **Feasibility Criteria**:
     - `"measurable_attributes": bool` (Are the attributes (real or perception-based) quantifiable or categorizable?)
     - `"specific_levels_definable": bool` (Can clear, mutually exclusive levels be defined?)
     - `"data_available_collectible": bool` (Is it possible to collect response data via surveys, experiments, or message testing?)
     - `"products_exists": bool` 
	   - For product-based cases: does the product/service exist?
       - For perception-based cases: is the framing/message strategy, spokesperson type, or brand positioning plausible and commonly used?)
  2. **Scoring** (if all criteria are "yes"):
     - 1: Cannot be measured.
     - 2: Major barriers in measurement.
     - 3: Measurable with complex methods.
     - 4: Easily measurable with effort.
     - 5: Routinely measured in research.
     - Default to 1 if any criterion is "no".
- **Next**: If at least one suggestion has all "yes" answers and a score ≥ 3, proceed to Step 3 with that suggestion; otherwise, stop.

#### Step 3: Attribute Specificity Check
Note: If the highest scoring suggestion includes context sourced from earlier history, consider that in determining whether product-specific or generic/perception-based attributes will be more informative or actionable.
- **Objective**: Determine whether the conjoint survey should use product-specific or generic/perception-based attributes, based on the decision context and feasibility.
- **Sub-Steps**:
  1. **Research Objective Analysis**: Focus on consumer choice, market behavior, or psychological motivations?
  2. **Target Audience Evaluation**: Familiarity with attributes? Rational or emotional decisions?
  3. **Product Category Assessment**: Utilitarian or hedonic? Specification-based decisions?
  4. **Competitive Analysis**: Are real-world competitors differentiated on specs or brand image?
  5. **Measurement Feasibility**: Can the proposed attributes (real or perception-based) be reliably quantified and compared?
  6. **Trade-off Analysis**: Which type of attributes will better expose meaningful differences and yield interpretable insights?
  7. **Recommendation**: Choose either:
	 - "product_specific" – when technical, functional, or tangible product features are central
     - "generic" – when framing, communication, reputation, or emotional trade-offs dominate
  8. **Attributes**: Provide a minimum of {attribute_count} key attributes aligning with the chosen type. 
- **Output**:
  - `"attributes":[str, ...]`
---
{format_instructions}
""")

PERSONA_EXTRACTION_PROMPT = inspect.cleandoc("""
Your task is to extract structured persona data from text extracted from a PDF. The text may describe one or more personas with dynamic attributes (e.g., name, age range, personality traits, behaviors). Your goal is to extract meaningful, relevant, and appropriate fields and map them to the Pydantic `Personas` schema, where each persona is a dictionary with attributes directly as key-value or key-list pairs. Follow this chain-of-thought process to produce accurate and structured output:

1. **Analyze the Text**:
   - Read the full text to identify sections describing personas.
   - Look for cues like a persona name (e.g., "Ella"), attribute labels (e.g., "Age range: 18-25"), or lists (e.g., "Personality traits: analytical, tech-savvy").
   - Note that PDFs may have varied structures, so attributes and their organization may differ.

2. **Identify Personas**:
   - Determine if the text describes one or multiple personas.
   - Represent each persona as a dictionary in the `personas` list.
   - If a persona name is mentioned (e.g., "Ella"), include it with the key "name". If no name is provided, omit the "name" key.

3. **Extract Attributes**:
   - Identify relevant attributes, such as demographic details (e.g., "Age range"), personality traits, pain points, behaviors, or goals.
   - For single-value attributes (e.g., "Age range: 18-25"), store as key-value pairs (e.g., `"age_range": "18-25"`).
   - For list-based attributes (e.g., "Personality traits: analytical, tech-savvy"), store as key-list pairs (e.g., `"personality_traits": ["analytical", "tech-savvy"]`).
   - Normalize keys to lowercase with underscores (e.g., "Age range" becomes "age_range") for consistency.
   - Parse varied formats (e.g., bullet points, comma-separated lists, narrative text) into consistent key-value or key-list structures.
   - Ignore irrelevant text (e.g., promotional phrases like "For answers to this, that, and everything. Yabble it.").

4. **Organize Attributes**:
   - If the text groups attributes into categories (e.g., "Demographic Attributes", "Personality Attributes"), use descriptive keys to reflect this (e.g., "personality_traits", "demographic_age_range").
   - If no categories are specified, use general keys based on attribute type (e.g., "trait", "behavior").
   - Store all attributes directly in the persona dictionary, keeping it flat.

5. **Validate Output**:
   - Ensure each persona dictionary contains meaningful keys and values (e.g., non-empty strings or lists).
   - Map the extracted data to the `Personas` schema, where `personas` is a list of dictionaries.
   - Skip ambiguous or irrelevant attributes to keep the output clean.

6. **Handle Edge Cases**:
   - If no persona data is found, return an empty `Personas` object (`{{"personas": []}}`).
   - If attributes are unstructured or narrative, infer key-value pairs or lists based on context (e.g., split a sentence into traits if it describes behaviors).
   - For multiple personas, ensure each is extracted as a separate dictionary in the `personas` list.
   - If a persona has no attributes, include it as an empty dictionary (`{{}}`) to indicate its presence.

7. **Produce Structured Output**:
   - Return a JSON-compatible dictionary with a single key, `personas`, containing a list of persona dictionaries.
   - Ensure the output is clean and contains only relevant persona data.

**IMPORTANT RULES**:
- Respond ONLY with the JSON object.
- Do not include explanations, additional text, or comments.

**Input Text**:
{text}

**Format Instructions**:
{format_instructions}
""")

VERIFY_BRAND_RELEVANCE_TEMPLATE = inspect.cleandoc("""
Your task is to verify if the provided brands are relevant to the given research context. Analyze the why_prompt and the list of brands to determine if they are appropriate for the stated research objective.

**Input Analysis**:
1. Research Context (why_prompt): {why_prompt}
2. Brands to Verify: {brands}

**Verification Criteria**:
1. **Category Alignment**: Do the brands belong to the product/service category mentioned in the why_prompt?
2. **Market Relevance**: Are these brands commonly associated with the market or consumer segment described?
3. **Contextual Fit**: Do the brands make sense in the context of the research question?
4. **Real-World Presence**: Are these brands that consumers would realistically consider in this context?

**Output Format**:
Return a boolean value (true/false) indicating whether the brands are relevant to the research context.

**Rules**:
- Return true only if ALL brands are relevant to the research context
- Consider both explicit and implicit context from the why_prompt
- Be strict in your evaluation - if any brand seems out of place, return false
- Focus on the practical relevance of the brands to the research question
- Do not include any explanation or additional text in your response

**Example Output**:
true
""")

LIKERT_LABEL_PROMPT_TEMPLATE = inspect.cleandoc("""
You are a survey design expert. Your task is to generate {scale}-point Likert scale labels for a given statement and context.
Additional context information: {image_details}
Follow this reasoning step-by-step:
1. If **additional context** is provided, use it to improve your understanding of the context.
2. **Interpret the statement and context** to determine what is being measured (e.g., agreement, satisfaction, trust, frequency, quality).
3. **Check if a common Likert scale can be used**:
   - Default to a generic {scale}-point scale from "Strongly Disagree" to "Strongly Agree".
   - Only deviate if this generic scale is semantically inappropriate.
4. **If needed, generate a custom Likert scale**:
   - Ensure the scale has a clear negative-to-positive semantic progression.
   - The labels must be meaningful, intuitive, and symmetrical.
5. Labels should correspond to the scale points: {scale_range}.

Now process the following input:

Statement: {statement}

Context: {context}

IMPORTANT RULES:
- Respond ONLY with the JSON object.
- Do not include explanations, additional text, or comments.

{format_instructions}
""")

DETAILED_IMAGE_DESCRIPTION_TEMPLATE = inspect.cleandoc("""
***Role***: You are an assistant tasked with summarizing images.
***Instruction***:
Your task is to:
- Analyze the image and extract all key information, such as the product name, packaging design, visible claims (e.g., 'organic', 'low fat'), and other prominent content details.
- Write a concise, neutral, and engaging summary what the concept is about, including all relevant details from the image.
""")

IMAGE_BASED_INTRO_TEMPLATE = inspect.cleandoc("""
***Role***: You are an assistant tasked with summarizing images.
***Instruction***:
Your task is to:
- Analyze the image and extract key information, such as the product name, packaging design, visible claims (e.g., 'organic', 'low fat'), and other prominent content details.
- Write a concise, neutral, and engaging introductory phrase that summarizes what the concept is about, including relevant details from the image. The phrase should prepare respondents to answer follow-up questions about their perceptions of the product.
- The introduction should set the context, be unbiased, and not imply any positive or negative evaluation.
""")

STANDARDIZE_NUMERICAL_ATTR = inspect.cleandoc("""
1. **Attribute and Level Lookup**: A list of list containing attribute name, levels and attribute type information with the following structure:
    - first element: name of the attribute.
    - second element: a list containing all the levels of attribute.
    - third element: attribute type (monetary or non-monetary)

## CRITICAL STANDARDIZATION REQUIREMENT

***EXTREMELY IMPORTANT: You must identify and standardize ALL instances where a numerical value appears after a rate indicator, regardless of format. Missing even a single case is unacceptable.***

## Instructions for Standardization

For each level text in the input:

1. THOROUGHLY scan for ANY text pattern that represents a rate or proportion between values, including but NOT LIMITED to:
    - "X [value1] per Y [unit]" (standard format)
    - "X [value1] / Y [unit]" (division format)
    - "X [value1]/Y [unit]" (no space division)
    - "X - Z [value1] per Y [unit]" (range format)
    - "X - Z [value1] / Y [unit]" (range with division)
    - "X [value1] for every Y [unit]" (alternative phrasing)
    - "X [value1] to Y [unit]" (ratio format)
    - "X [value1] : Y [unit]" (colon notation)
    - "X [value1] each Y [unit]" (each notation)
    - "up to X [value1] per Y [unit]" (qualified rate)
    - "[value1] of X per Y [unit]" (inverted format)
    - "X [value1] @ Y [unit]" (shorthand notation)
    
    Where:
    - X is any numerical value (may include a range like "X - Z")
    - [value1] is any measurement or descriptor (e.g., USD, miles, hours, grams)
    - Y is a numerical value that MUST be converted (may include commas, decimals, fractions, etc.)
    - [unit] is any unit of measurement (e.g., grams, miles, hours, uses, servings)

2. THOROUGH SCANNING: Examine EVERY level text with extreme scrutiny. Search for ANY indication of a rate relationship where a number appears after words or symbols including but not limited to:
    - "per"
    - "/"
    - "for every"
    - "to" (when used in ratios)
    - "each"
    - ":" (when used in ratios)
    - "@"
    - Any other term or symbol that indicates a rate relationship

3. For EVERY identified pattern, convert the numerical value Y to its English word equivalent:
    - Numbers 1-20: Use their simple word form (e.g., "one", "two", "seventeen")
    - Tens 20-90: Use hyphenated forms when needed (e.g., "twenty-three", "fifty-five")
    - Powers of 10: Use proper words (e.g., "hundred", "thousand", "million")
    - Decimals: Use "point" (e.g., "three point five")
    - Commas: Properly handle large numbers (e.g., "100,000" → "one hundred thousand")
    - Fractions: Convert appropriately (e.g., "1/2" → "one half", "3/4" → "three quarters")

4. Replace the numerical Y with its English word equivalent in the level text, maintaining the EXACT original format otherwise.

## EDGE CASES TO HANDLE (Critically Important)

Pay special attention to these easily missed cases:
- Numbers with commas: "10,000" or "1,234,567"
- Decimal numbers: "3.5" or "0.25"
- Fractions: "1/2" or "3/4"
- Mixed numbers: "2 1/2"
- Ranges where the second part is after the rate indicator: "X [value1] per Y - Z [unit]"
- Numbers with units attached without spaces: "100ml/5kg" → "100ml/five kg"
- Parenthetical rates: "X [value1] (per Y [unit])"
- Multiple rates in one string: "X [value1] per Y [unit] and Z [value2] per W [unit]"
- Percentage expressions: "X% per Y [unit]"
- Scientific notation: "1.5e6 per 1e3 [unit]"
- Ordinal numbers: "every 3rd [unit]" → "every third [unit]"

## COMPREHENSIVE EXAMPLES

Basic Examples:
- "26000 USD per 10 miles" → "26000 USD per ten miles"
- "37150 USD / 12 miles" → "37150 USD / twelve miles"
- "48300 USD/13 miles" → "48300 USD/thirteen miles"

Edge Case Examples:
- "8 - 10 years / 100,000 miles" → "8 - 10 years / one hundred thousand miles"
- "5 gallons per 2.5 hours" → "5 gallons per two point five hours"
- "$15 for every 1/2 pound" → "$15 for every one half pound"
- "Coverage lasts 10 years @ 75,000 miles" → "Coverage lasts 10 years @ seventy-five thousand miles"
- "10ml/1kg" → "10ml/one kg"
- "3 servings (per 2 days)" → "3 servings (per two days)"
- "2 tablets every 4-6 hours" → "2 tablets every four-six hours"
- "Mix at 20:1 ratio" → "Mix at twenty:one ratio"
- "5% interest per 30 days" → "5% interest per thirty days"
- "Efficiency of 80mpg @ 55mph" → "Efficiency of 80mpg @ fifty-five mph"

## Example Input and Output Dictionary

Input:
{{
    [['Brands', ['Nissan Leaf', 'Chevrolet Bolt', 'Hyundai Kona Electric', 'Ford Mustang Mach-E', 'Porsche Taycan'], 'non-monetary'], 
     ['Price', ['26000 USD per 10 miles', '37150 USD / 12 miles', '48300 USD/13 miles', '8 - 10 years / 100,000 miles', 'Warranty: 10yrs@75,000mi'], 'monetary'],
     ['Efficiency', ['30 kWh per 100 miles', '4.5 miles/1 kWh', '3 charges every 7 days', '250 miles:1 charge', '20%/1000 miles'], 'non-monetary']]
}}

Output:
{{
    [['Brands', ['Nissan Leaf', 'Chevrolet Bolt', 'Hyundai Kona Electric', 'Ford Mustang Mach-E', 'Porsche Taycan'], 'non-monetary'], 
     ['Price', ['26000 USD per ten miles', '37150 USD / twelve miles', '48300 USD/thirteen miles', '8 - 10 years / one hundred thousand miles', 'Warranty: 10yrs@seventy-five thousand mi'], 'monetary'],
     ['Efficiency', ['30 kWh per one hundred miles', '4.5 miles/one kWh', '3 charges every seven days', '250 miles:one charge', '20%/one thousand miles'], 'non-monetary']]
}}

***ABSOLUTE CRITICAL REQUIREMENT***
- Don't drop any attribute or its levels. The output must be of the same size as the input.
- You MUST convert EVERY single numerical value after a rate indicator to its English word form. NO EXCEPTIONS.
- The output MUST maintain the IDENTICAL structure and formatting as the input, changing ONLY the numerical values after rate indicators.
- IF THE KEY IS NOT A STRING, DO NOT CHANGE IT TO BE A STRING IN THE AL_LOOKUP OUTPUT.
- Partial implementation is considered a COMPLETE FAILURE. You must catch and convert ALL edge cases.
- Respond only in JSON format without any additional text or explanation.

## Input Variables
Attribute and Level Lookup: {al_lookup}

{format_instructions}
""")


##################
# Latent Variables

LATENT_VARIABLE_GENERATION_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Research Context**: A question or topic area that involves human decision-making, preferences, or behavior. For example:
    - 'What factors affect venture capitalists' decisions to invest in early-stage startups?'

2. **Number of Traits**: How many psychological/behavioral traits or characteristics should be identified (default: 5).

3. **Number of Measurement Statements per Trait**: How many statements should be created to measure each trait (default: 2).

4. **Number of Pain Point Detection Statements per Trait**: How many statements should be created to detect pain points associated with each trait (default: 2).

5. **Number of Labels for the Likert Scale**: How many labels does each likert scale have.


## Output Format

For the given inputs:

1. Identify the specified number of psychological/behavioral traits or characteristics most relevant to the research context.
    - For the research context example given above, here are two examples of traits:
        - Risk Aversion
        - Due Diligence
2. For each trait, provide the specified number of measurement statements and pain point detection statements that could be used on a {scale_labels_count}-point Likert scale. For example, for the trait 'Due Diligence', here is one example of each statement type:
    - trait measurement statement: 'I spend a significant amount of time researching a startup before making an investment decision.'
    - pain point detection statement: 'I often find myself delaying investment decisions due to the need for more information.'
3. Identify potential pain points associated with each trait in the given context.
    - For example, for the trait 'Due Diligence', an example of a pain point is 'Analysis Paralysis'.
4. Provide the specified number of pain point detection statements for each trait.
    - For example, for the trait 'Risk Aversion', here are two examples of pain points:
        - Missed Opportunities
        - Analysis Paralysis
5. Ensure the statements are:
    - Clearly worded and unambiguous.
    - Specific to the context of the research question.
    - Balanced between positive and negative phrasing when appropriate.
    - Written in first-person perspective for self-reporting.
    - Focused on capturing measurable aspects of the trait.


## Instructions for Trait Selection

When selecting traits:
- Consider cognitive, emotional, attitudinal, and behavioral dimensions.
- Include traits that might have both direct and indirect influences on the topic.
- Consider traits that might explain individual differences in the specified context.
- Focus on traits that have established measurement precedents in psychological literature.
- Ensure traits are conceptually distinct from each other.
- Prioritize traits that may reveal barriers, frustrations, or pain points in the context.


## Instructions for Creating Trait Measurement Statements

When creating trait measurement statements:
- Use simple, clear language that respondents can easily understand.
- Avoid double-barreled questions (asking about two things at once).
- Include both general tendency statements and specific behavioral examples.
- Create statements that can differentiate between individuals with different trait levels.
- Ensure statements are contextually relevant to the research question. 
- For each statement, generate a **{scale_labels_count}-point Likert scale** that fits the nature, context and phrasing of the statement.
    - Follow this reasoning step-by-step to create the Likert Scale:
        1. **Interpret the statement and context** to determine what is being measured (e.g., agreement, satisfaction, trust, frequency, quality).
        2. **Check if a common Likert scale can be used**:
            - Default to a generic {scale_labels_count}-point scale from 'Strongly Disagree' to 'Strongly Agree'.
            - Only deviate if this generic scale is semantically inappropriate.
        3. **If needed, generate a custom Likert scale**:
            - Ensure the scale has a clear negative-to-positive semantic progression.
            - The labels must be meaningful, intuitive, and symmetrical.
    - Using a 5-point Likert scale as an example, common variations include:
        - Agreement: (1: 'Strongly Disagree', 2: 'Disagree', 3: 'Neutral', 4: 'Agree', 5: 'Strongly Agree')
        - Frequency: (1: 'Never', 2: 'Rarely', 3: 'Sometimes', 4: 'Often', 5: 'Always')
        - Satisfaction: (1: 'Very Dissatisfied', 2: 'Dissatisfied', 3: 'Neutral', 4: 'Satisfied', 5: 'Very Satisfied')
        - Severity: (1: 'Not Severe', 2: 'Slightly Severe', 3: 'Moderately Severe', 4: 'Very Severe', 5: 'Extremely Severe')
        - Importance: (1: 'Not Important', 2: 'Slightly Important', 3: 'Moderately Important', 4: 'Important', 5: 'Very Important')


## Instructions for Identifying Pain Points

When identifying pain points:
- Consider how each trait might lead to specific frustrations or barriers.
- Identify situations where the trait could create negative experiences.
- Focus on aspects that might prevent adoption, usage, or satisfaction.
- Consider both practical and psychological obstacles.
- Think about how extreme levels (high or low) of each trait might create different pain points.
- Assign each pain point a specific name that succinctly captures its nature.


## Instructions for Creating Pain Point Detection Statements

When creating pain point detection statements:
- Focus specifically on the negative experiences or challenges associated with the trait.
- Make statements that directly measure the frequency or intensity of the pain point.
- Use language that resonates with how people actually experience these challenges.
- Create statements that can be answered on the same 5-point scale as trait measurement statements.
- Ensure statements capture manifestations of the pain point that would be recognizable to the respondent.
- For pain point detection statements, select an appropriate Likert scale that best fits the statement's nature, context and phrasing. (Examples of different Likert scales were already provided above.)


## Input Variables

Research Context: {why_prompt}

Number of Traits: {traits_count}

Number of Measurement Statements per Trait: {measurement_count}

Number of Pain Point Detection Statements per Trait: {pain_point_count}
                                                
Number of Labels for the Likert Scale: {scale_labels_count}


## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")

ADDITIONAL_NOTES_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Research Context**: A context from a question or topic area that involves human decision-making, preferences, or behavior.
    - For example: 'What factors affect venture capitalists' decisions to invest in early-stage startups?'

2. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
        'Gender': Female
        'Income Level': $120,000
        'Location':  'San Francisco, California')

3. **Trait**: A psychological/behavioral trait or characteristic that is relevant for the research context.
    - For the research context example given above, here are two examples of traits:
        - Risk Aversion
        - Due Diligence


## Instructions for Generating Additional Notes

Based on the given trait and the demographic profile, generate insights into how this trait is likely to manifest in this individual. Provide the following two points:
    - **Trait characteristics**: Describe how the trait typically manifests in individuals with this demographic profile within the defined research context.
    - **Behavioral tendencies**: Explain how demographic variables influence the individual's trait-related behaviors within the defined research context.


## Input Variables

Research Context: {why_prompt}

Respondent Profile: {demographics}
                                            
Trait: {trait}


## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")

# TODO: Remove indentation
STATEMENT_PERCENTAGE_SCORES_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

2. **Trait**: A psychological/behavioral trait or characteristic that is relevant for a given research context.
    - For example, for the research context 'What factors affect venture capitalists' decisions to invest in early-stage startups?', here are two examples of traits:
        - Risk Aversion
        - Due Diligence

3. **Trait Measurement Statements With Percentage Scales**: Statements to measure this trait for the respondent, together with identifying indices (int values starting from 0) for each statement and their respective percentage scales.
    - For example, for the trait 'Risk Aversion', here is an example of a trait measurement statement:
        - 'I am comfortable investing in startups with a high potential for failure if the potential return is also high.'
    - Percentage scale: Labels for the endpoints of the percentage scale (0 and 100) for each statement. Some examples of percentage scales are the following:
        - Agreement: ('0 = Strongly Disagree', '100 = Strongly Agree')
        - Frequency: ('0 = Never', '100 = Always')
        - Satisfaction: ('0 = Very Dissatisfied', '100 = Very Satisfied')
        - Severity: ('0 = Not Severe', Severe', '100 = Extremely Severe')
        - Importance: ('0 = Not Important', '100 = Very Important')

4. **Additional Notes**: For a given research context, these notes include:
    - **Trait Characteristics**: Explanation of how the trait typically manifests in individuals with this demographic profile, within the defined research context.
    - **Behavioral Tendencies**: Insights into how demographic variables influence trait-related behaviors in the defined research context.


## Output Format: Instructions for Scoring

Based on the provided demographic profile, the trait, and the additional notes, estimate the percentage score that this individual would give to each trait measurement statement.
    - The scores should be informed by the individual's demographic characteristics and reflect behavioral patterns and preferences typically associated with the relevant traits for that demographic profile.
    - Avoid clustering around the middle value (e.g., 50). Use the full range of the scale (0-100) to capture the diversity of responses. If the individual leans towards disagreement, use a lower value; if they lean towards agreement, use a higher value.


## Input Variables

Respondent Profile: {demographics}

Trait: {trait}

Trait Measurement Statements: {statements_with_scale_labels}

Additional Notes: {additional_notes}


## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")

STATEMENT_LIKERT_SCORES_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

2. **Trait**: A psychological/behavioral trait or characteristic that is relevant for a given research context.
    - For example, for the research context 'What factors affect venture capitalists' decisions to invest in early-stage startups?', here are two examples of traits:
        - Risk Aversion
        - Due Diligence

3. **Trait Measurement Statements With Likert Scales**: Statements to measure this trait for the respondent, together with identifying indices (int values starting from 0) for each statement and their respective likert scales.
    - For example, for the trait 'Risk Aversion', here is an example of a trait measurement statement:
        - 'I am comfortable investing in startups with a high potential for failure if the potential return is also high.'
    - Likert scale: Labels for the Likert scale for each statement. Likert scales typically range from 4-point to 9-point variants. Using a 5-point Likert scale as an example, common variations include:
        - Agreement: ('1 = Strongly Disagree', '2 = Disagree', '3 = Neutral', '4 = Agree', '5 = Strongly Agree')
        - Frequency: ('1 = Never', '2 = Rarely', '3 = Sometimes', '4 = Often', '5 = Always')
        - Satisfaction: ('1 = Very Dissatisfied', '2 = Dissatisfied', '3 = Neutral', '4 = Satisfied', '5 = Very Satisfied')
        - Severity: ('1 = Not Severe', '2 = Slightly Severe', '3 = Moderately Severe', '4 = Very Severe', '5 = Extremely Severe')
        - Importance: ('1 = Not Important', '2 = Slightly Important', '3 = Moderately Important', '4 = Important', '5 = Very Important')

4. **Additional Notes**: For a given research context, these notes include:
    - **Trait Characteristics**: Explanation of how the trait typically manifests in individuals with this demographic profile, within the defined research context.
    - **Behavioral Tendencies**: Insights into how demographic variables influence trait-related behaviors in the defined research context.


## Output Format: Instructions for Scoring

Based on the provided demographic profile, the trait, and the additional notes, choose the score that this individual would give to each trait measurement statement.
    - The scores should be informed by the individual's demographic characteristics and reflect behavioral patterns and preferences typically associated with the relevant traits for that demographic profile.
    - Avoid clustering around the middle value (e.g., 3). Use the full range of the scale (1-5) to capture the diversity of responses. If the individual leans towards disagreement, use a lower value; if they lean towards agreement, use a higher value.


## Input Variables

Respondent Profile: {demographics}

Trait: {trait}

Trait Measurement Statements: {statements_with_scale_labels}

Additional Notes: {additional_notes}


## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")

STATEMENT_SOFT_SCORES_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

2. **Trait**: A psychological/behavioral trait or characteristic that is relevant for a given research context.
    - For example, for the research context 'What factors affect venture capitalists' decisions to invest in early-stage startups?', here are two examples of traits:
        - Risk Aversion
        - Due Diligence

3. **Trait Measurement Statements With Likert Scales**: Statements to measure this trait for the respondent, together with identifying indices (int values starting from 0) for each statement and their respective likert scales.
    - For example, for the trait 'Risk Aversion', here is an example of a trait measurement statement:
        - 'I am comfortable investing in startups with a high potential for failure if the potential return is also high.'
    - Likert scale: Labels for the Likert scale for each statement. Likert scales typically range from 4-point to 9-point variants. Using a 5-point Likert scale as an example, common variations include:
        - Agreement: ('1 = Strongly Disagree', '2 = Disagree', '3 = Neutral', '4 = Agree', '5 = Strongly Agree')
        - Frequency: ('1 = Never', '2 = Rarely', '3 = Sometimes', '4 = Often', '5 = Always')
        - Satisfaction: ('1 = Very Dissatisfied', '2 = Dissatisfied', '3 = Neutral', '4 = Satisfied', '5 = Very Satisfied')
        - Severity: ('1 = Not Severe', '2 = Slightly Severe', '3 = Moderately Severe', '4 = Very Severe', '5 = Extremely Severe')
        - Importance: ('1 = Not Important', '2 = Slightly Important', '3 = Moderately Important', '4 = Important', '5 = Very Important')

4. **Additional Notes**: For a given research context, these notes include:
    - **Trait Characteristics**: Explanation of how the trait typically manifests in individuals with this demographic profile, within the defined research context.
    - **Behavioral Tendencies**: Insights into how demographic variables influence trait-related behaviors in the defined research context.


## Output Format: Instructions for Scoring

For each statement and its associated Likert scale labels, provide a probability distribution over the available labels. Each distribution must satisfy the following:
    - All probability scores should be between 0 and 1.
    - The sum of the scores for each statement must equal 1 (i.e., a valid probability distribution).
    For example, for a five-point Likert scale:
        - Scale: ['Strongly Disagree', 'Disagree', 'Neutral', 'Agree', 'Strongly Agree']
        - Probability distribution: [0.18, 0.63, 0.12, 0.07, 0]
The probability scores should be based on the provided demographic profile, the trait, and the additional notes. More precisely, the scores should be informed by the individual's demographic characteristics and reflect behavioral patterns and preferences typically associated with the relevant traits for that demographic profile.
Avoid assigning high probabilities to middle values (e.g., "Neutral") by default. Instead, use the full range of the scale to reflect more distinct response tendencies. If the individual leans towards disagreement, assign higher probabilities to the lower end of the scale; if they lean towards agreement, assign higher probabilities to the upper end.


## Input Variables

Respondent Profile: {demographics}

Trait: {trait}

Trait Measurement Statements: {statements_with_scale_labels}

Additional Notes: {additional_notes}


## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.
Note that you should have a nested dictionary structure where the keys are, at the top level, the statement indices (int values starting from 0) and in the second level label indices (int values starting from 0).

{format_instructions}
""")

CONCEPT_TESTING_STATEMENT_PERCENTAGE_SCORES_PROMPT = inspect.cleandoc("""
## Objectives
                                                        
In the context of this Study, we would like you to answer/rate few statements,
based on information about the product (image).

-----------------------------------------------------------------------------
## Input Format
                                                        
1. **Study Description**: A description of the product study to conduct.
    - For example: 'A new milk product is being launched in the market and we would like to understand people's perception towards the product.'

2. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

3. **Information about the product**: An image with information about the product to study.
                                                        
4. **Statements With Percentage Scales**: Statements to rate, together with identifying indices (int values starting from 0) for each statement and their respective percentage scales.
    - For example, a statement could be the following:
        'As I deal with the stress of today's new normal, milk does my body good with its vitamins, minerals and protein. Milk helps me wind down after a long day.'
    - Percentage Scale: Labels for the endpoints of the percentage scale (0 and 100) for each statement. Some examples of percentage scales are the following:
        - Agreement: ('100 = Strongly Disagree', '100 = Strongly Agree')
        - Frequency: ('100 = Never', '100 = Always')
        - Satisfaction: ('100 = Very Dissatisfied', '100 = Very Satisfied')
        - Severity: ('100 = Not Severe', Severe', '100 = Extremely Severe')
        - Importance: ('100 = Not Important', '100 = Very Important')

-----------------------------------------------------------------------------
## Instructions for Scoring

Based on the provided demographic profile, the study description, and the information about the product, estimate the percentage score that this individual would give to each statement.
    - The scores should be informed by the individual's demographic characteristics and reflect behavioral patterns and preferences typically associated with the relevant traits for that demographic profile.
    - Avoid clustering around the middle value (e.g., 50). Use the full range of the scale (0-100) to capture the diversity of responses. If the individual leans towards disagreement, use a lower value; if they lean towards agreement, use a higher value.

-----------------------------------------------------------------------------  

## Input Variables

Study Description: {description_prompt}

Respondent Profile: {demographics}

Information About the Product (Image): {image}

Statements: {statements_with_scale_labels}

-----------------------------------------------------------------------------  
## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")


CONCEPT_TESTING_STATEMENT_LIKERT_SCORES_PROMPT = inspect.cleandoc("""
## Objectives
                                                        
In the context of this Study, we would like you to answer/rate few statements,
based on information about the product (image).

-----------------------------------------------------------------------------
## Input Format
                                                        
1. **Study Description**: A description of the product study to conduct.
    - For example: 'A new milk product is being launched in the market and we would like to understand people's perception towards the product.'

2. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

3. **Information about the product**: An image with information about the product to study.
                                                        
4. **Statements With Likert Scales**: Statements to rate, together with identifying indices (int values starting from 0) for each statement and their respective likert scales.
    - For example, a statement could be the following:
        'As I deal with the stress of today's new normal, milk does my body good with its vitamins, minerals and protein. Milk helps me wind down after a long day.'
    - Likert scale: Labels for the Likert scale for each statement. Likert scales typically range from 4-point to 9-point variants. Using a 5-point Likert scale as an example, common variations include:
        - Agreement: ('1 = Strongly Disagree', '2 = Disagree', '3 = Neutral', '4 = Agree', '5 = Strongly Agree')
        - Frequency: ('1 = Never', '2 = Rarely', '3 = Sometimes', '4 = Often', '5 = Always')
        - Satisfaction: ('1 = Very Dissatisfied', '2 = Dissatisfied', '3 = Neutral', '4 = Satisfied', '5 = Very Satisfied')
        - Severity: ('1 = Not Severe', '2 = Slightly Severe', '3 = Moderately Severe', '4 = Very Severe', '5 = Extremely Severe')
        - Importance: ('1 = Not Important', '2 = Slightly Important', '3 = Moderately Important', '4 = Important', '5 = Very Important')

-----------------------------------------------------------------------------
## Instructions for Scoring

Based on the provided demographic profile, the study description, and the information about the product, estimate the percentage score that this individual would give to each statement.
    - The scores should be informed by the individual's demographic characteristics and reflect behavioral patterns and preferences typically associated with the relevant traits for that demographic profile.
    - Avoid clustering around the middle value (e.g., 3 for a 5-point likert scale). Use the full range of the scale to capture the diversity of responses. If the individual leans towards disagreement, use a lower value; if they lean towards agreement, use a higher value.

-----------------------------------------------------------------------------  

## Input Variables

Study Description: {description_prompt}

Respondent Profile: {demographics}

Information About the Product (Image): {image}

Statements: {statements_with_scale_labels}

-----------------------------------------------------------------------------  
## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")


# TODO: Should there be a monotonicity constraint in the probability values?
# TODO: Is giving specific examples biasing the LLM?
CONCEPT_TESTING_STATEMENT_SOFT_SCORES_PROMPT = inspect.cleandoc("""
## Objectives
                                                        
In the context of this Study, we would like you to answer/rate few statements, based on information about the product (image).

-----------------------------------------------------------------------------
## Input Format
                                                        
1. **Study Description**: A description of the product study to conduct.
    - For example: 'A new milk product is being launched in the market and we would like to understand people's perception towards the product.'

2. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
            'Gender': Female
            'Income Level': $120,000
            'Location': 'San Francisco, California')

3. **Information about the product**: An image with information about the product to study.
                                                        
4. **Statements With Likert Scales**: Statements to rate, together with identifying indices (int values starting from 0) for each statement and their respective likert scale labels.
    - For example, a statement could be the following:
        'As I deal with the stress of today's new normal, milk does my body good with its vitamins, minerals and protein. Milk helps me wind down after a long day.'
    - Likert scale: Labels for the Likert scale for each statement. Likert scales typically range from 4-point to 9-point variants. Using a 5-point Likert scale as an example, common variations include:
        - Agreement: ('1 = Strongly Disagree', '2 = Disagree', '3 = Neutral', '4 = Agree', '5 = Strongly Agree')
        - Frequency: ('1 = Never', '2 = Rarely', '3 = Sometimes', '4 = Often', '5 = Always')
        - Satisfaction: ('1 = Very Dissatisfied', '2 = Dissatisfied', '3 = Neutral', '4 = Satisfied', '5 = Very Satisfied')
        - Severity: ('1 = Not Severe', '2 = Slightly Severe', '3 = Moderately Severe', '4 = Very Severe', '5 = Extremely Severe')
        - Importance: ('1 = Not Important', '2 = Slightly Important', '3 = Moderately Important', '4 = Important', '5 = Very Important')

-----------------------------------------------------------------------------
## Instructions for Scoring

For each statement and its associated Likert scale labels, provide a probability distribution over the available labels. Each distribution must satisfy the following:
    - All probability scores should be between 0 and 1.
    - The sum of the scores for each statement must equal 1 (i.e., a valid probability distribution).
    For example, for a five-point Likert scale:
        - Scale: ['Strongly Disagree', 'Disagree', 'Neutral', 'Agree', 'Strongly Agree']
        - Probability distribution: [0.18, 0.63, 0.12, 0.07, 0]
The probability scores should be based on the provided demographic profile, the study description, and the information about the product.
Avoid assigning high probabilities to middle values (e.g., "Neutral") by default. Instead, use the full range of the scale to reflect more distinct response tendencies. If the individual leans towards disagreement, assign higher probabilities to the lower end of the scale; if they lean towards agreement, assign higher probabilities to the upper end.

-----------------------------------------------------------------------------  

## Input Variables

Study Description: {description_prompt}

Respondent Profile: {demographics}

Information About the Product (Image): {image}

Statements: {statements_with_scale_labels}

-----------------------------------------------------------------------------  
## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.
Note that you should have a nested dictionary structure where the keys are, at the top level, the statement indices (int values starting from 0) and in the second level label indices (int values starting from 0).

{format_instructions}
""")

# TODO: Check Additional notes
# - Additional notes on the persona's trait and its relation to the research context, which include:
#     - Trait characteristics: An explanation of how the trait typically manifests in individuals with this demographic profile within the defined research context.
#     - Behavioral Tendencies: Insights into how demographic variables influence the individual's trait-related behaviors within the defined research context.
PERSONA_TRAITS_NL_SUMMARY_TEMPLATE = inspect.cleandoc("""
## Input Format

1. **Research Context**: A context from a question or topic area that involves human decision-making, preferences, or behavior.
    - For example: 'What factors affect venture capitalists' decisions to invest in early-stage startups?'

2. **Respondent Profile**: Demographic traits for a given individual.
    - For example:
        ('Age: 28
        'Gender': Female
        'Income Level': $120,000
        'Location':  'San Francisco, California')

3. **Respondent Pyshcological and Behavioral Trait Data**: Information about psychological/behavioral traits or characteristics of the individual that are relevant for this research context.
    - For example, for the research context 'What factors affect venture capitalists' decisions to invest in early-stage startups?', here are two examples of traits:
        - Risk Aversion
        - Due Diligence
    - The information for each trait includes trait measurement statements together with the scores given by the individual to these statements, a list of pain points associated with the trait, and pain point detection statements together with the scores given by the individual to these statements. The scores can be probability distributions over likert scale labels together with the mean score (weighted average), or percentage scores.


## Output Instructions

Given the research context, the respondent demographic profile, and the respondent psychological/behavioral trait information, provide a natural language summary of the persona. This summary should be vivid, yet concise, and embody the provided demographic, psychological and behvioral trait information, reflecting the individual's lifestyle, priorities, and constraints. Focus exclusively on those demographic, psychological and behavioral details that logically influence their preferences, needs, or decision-making related to the research context, automatically disregarding any traits that lack a clear connection to this context. Ensure that each relevant trait contributes to a unique perspective—even if some traits are subtle or similar to others. Keep the description detailed enough to capture their identity and motivations in this context, but brief enough to avoid unnecessary elaboration—aim for a natural length that feels complete without being overly verbose.

The natural language summary should contain the following points:
- **Demographics**: Demographic traits of the persona in natural language
- **Personality traits**
- **Pain points**: Persona-specific pain points.
- **Behaviors**: Persona-specific behaviors.                                                 
- **Goals**
- **Motivations**: Internal drivers that influence the persona's decisions and actions.
- **Trigger Points**: Specific situations or messages that prompt immediate action or emotional response.

## Input Variables

Research Context: {why_prompt}

Respondent Profile: {demographics}

Respondent Traits Data: {persona_traits_data}

## Example Response Structure

Please provide the response in a JSON format, following the structure shown below.
Please respond only in JSON format without any additional explanation or details.

{format_instructions}
""")


TRAITS_PREDICTABLE = inspect.cleandoc("""
You are a data scientist with expertise in demographic analysis and predictive modeling. You'll be evaluating whether certain new traits can be reliably predicted from existing demographic data.

EXISTING DEMOGRAPHIC TRAITS:
{known_traits}

NEW TRAITS TO EVALUATE:
{new_traits}

For each new trait, evaluate whether it can be reliably predicted from the existing demographic traits. Consider:
1. Known correlations from social science research
2. Statistical relationships between the traits
3. Predictive power of the existing variables
4. Availability of sufficient signal in the data

Provide your output in this format:
{{
  "new_trait_1": true or false,
  "new_trait_2": true or false,
  ...
}}

Focus on being precise and honest about what can actually be predicted reliably rather than making guesses with low confidence. If a trait cannot be reliably predicted from the available demographic data, indicate so with "predictable": false. Otherwise if the trait can be reliably predicted from the available demographic data, indicate so with "predictable": true.

Remember that correlation does not imply causation, and some traits may be partially predictable but not with high confidence

DO NOT RETURN ANYTHING ELSE BUT THE JSON OUTPUT I DO NOT WANT THE RATIONALE OR ANYTHING

""")
PREDICT_TRAITS = inspect.cleandoc("""
[Immersive Roleplay Experience]

You're about to step into someone else's life for a moment. As you read, imagine yourself AS this person:

{traits}

Take a deep breath and truly embody this individual. Feel what it's like living in their state, with their income, their family situation. Picture their daily routines, the neighborhood they might live in, their social circles.

Now, as you're living this person's life, I'm curious about something personal:

What would your {trait_to_predict} be?

This isn't about statistics or analysis - it's about what feels authentic to the life you're now experiencing. What feels right given everything about who you are in this moment?

Your options are: {possible_values}

While staying completely in character as this person, respond ONLY with:
{{
  "predicted_value": "Predicted Value of the trait to predict"
}}

Choose one value from the options provided. Stay in character, but return ONLY the JSON - no additional text.
""")
