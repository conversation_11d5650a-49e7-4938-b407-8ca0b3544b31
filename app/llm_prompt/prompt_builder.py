from typing import Dict, List

from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from app.api.v1.schemas.attributes_levels import (
    AttributesResponse,
    BrandRelevanceResponse,
    LeveledAttribute,
    LeveledAttributeBase,
    LeveledAttributeResponse,
    LeveledAttributeResponseClaude,
    MonetaryAttributeResponse,
    OrdinalResponse,
    ProcessedProducts,
    ProductAttributeResponse,
    ProductPriceLevelResponse,
    RealWorldProductsResponse,
    SpecificOrGenericAttributeCheckResponse,
    SpecificOrGenericAttributeCheckStrictResponse,
    contains_brand,
)
from app.api.v1.schemas.copilot import CausalityResponse
from app.api.v1.schemas.experiments import (
    CausalityCheckResponse,
    DependentVariableResponse,
    ExperimentIdea,
    ExperimentIdeationResponse,
    LikertLabelResponse,
    ObjectOfStudyResponse,
    OutcomePhraseResponse,
    RealWorldConjointStatementResponse,
    StudyContextResponse,
    TargetBehaviorResponse,
)
from app.api.v1.schemas.markekt_simulator import CheckProductRealWorldResponse
from app.api.v1.schemas.personas import (
    ConstraintResponse,
    HistoryResponse,
    MotivationResponse,
    PersonasResponse,
    QuestionList,
)
from app.api.v1.schemas.traits import (
    BinaryVariableToNaturalLanguageResponse,
    NaturalLanguageTraitResponse,
    TraitLevelsSuggestionResponse,
    TraitSuggestionResponse,
)
from app.api.v2.schemas.copilot import SingleStatementCausalityResponse
from app.api.v2.schemas.latent_variables import (
    AdditionalNotesLLMOutput,
    GenerationLLMOutput,
    NLSummaryLLMOutput,
    StatementLikertScoresLLMOutput,
    StatementPercentageScoresLLMOutput,
    StatementSoftScoresLLMOutput,
)
from app.core.utils.models import SurveyChoice, SurveyChoiceResponse
from app.core.utils.type_enums import PromptType
from app.llm_prompt.prompt_templates import (
    STATEMENT_MODIFY_TO_SUIT_GENERIC_OR_SPECIFIC_ATTRIBUTE_TEMPLATE,  # TODO: check
)
from app.llm_prompt.prompt_templates import (
    ADDITIONAL_NOTES_TEMPLATE,
    ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE,
    ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE_WITH_SCORE,
    ATTRIBUTE_LEVELS_TEMPLATE_V2,
    ATTRIBUTES_TEMPLATE,
    CAUSAL_EXPERIMENT_IDEATION_PROMPT,
    CAUSAL_EXPERIMENT_IDEATION_REFINEMENT_PROMPT,
    CAUSALITY_TEMPLATE,
    CAUSALITY_WITH_RATIONALE_TEMPLATE,
    CHECK_FOR_PRODUCT_TEMPLATE,
    CONCEPT_TESTING_STATEMENT_LIKERT_SCORES_PROMPT,
    CONCEPT_TESTING_STATEMENT_PERCENTAGE_SCORES_PROMPT,
    CONCEPT_TESTING_STATEMENT_SOFT_SCORES_PROMPT,
    CONSTRAINTS_PROMPT_TEMPLATE,
    CONTAINS_BRAND,
    DEPENDENT_VARIABLE_TEMPLATE_DISCRETE,
    DEPENDENT_VARIABLE_TEMPLATE_PROBABILITSTIC,
    DEPENDENT_VARIABLE_TEMPLATE_WITH_OPT_OUT,
    ENHANCE_ATTRIBUTE_LEVELS_TEMPLATE,
    EXPERIMENT_TEMPLATE,
    FETCH_PRODUCTS_PROMPT,
    GENERIC_OR_SPECIFIC_ATTRIBUTE_CHECK,
    HISTORY_PROMPT_TEMPLATE,
    LATENT_VARIABLE_GENERATION_TEMPLATE,
    LEVELS_PRICE_TEMPLATE,
    LEVELS_TEMPLATE,
    LIKERT_LABEL_PROMPT_TEMPLATE,
    LLM_ATTRIBUTES_TEMPLATE,
    LLM_LEVELS_TEMPLATE,
    MONETARY_ATTRIBUTES_LEVELS_TEMPLATE,
    MOTIVATION_TEMPLATE,
    OBJECT_OF_STUDY_TEMPLATE,
    ORDINALITY_CHECK_TEMPLATE,
    ORTHOGONAL_ATTRIBUTE_LEVELS_TEMPLATE,
    OUTCOME_PHRASE_TEMPLATE,
    PERSONA_EXTRACTION_PROMPT,
    PERSONA_IN_NATURAL_LANGUAGE_IMAGE_TEMPLATE,
    PERSONA_IN_NATURAL_LANGUAGE_TEMPLATE,
    PERSONA_TRAITS_NL_SUMMARY_TEMPLATE,
    PREDICT_TRAITS,
    PRICE_QUESTION_TEMPLATE,
    PRODUCT_ATTRIBUTE_LEVEL_SELECTION_TEMPLATE,
    PRODUCT_ATTRIBUTE_LEVELS_COMPLETE_TEMPLATE,
    PRODUCT_ATTRIBUTE_LEVELS_TEMPLATE,
    PRODUCT_ATTRIBUTE_RANKING_TEMPLATE,
    PRODUCT_ATTRIBUTES_TEMPLATE,
    PRODUCT_ATTRIBUTES_WITHBRAND_TEMPLATE,
    PRODUCT_BRAND_TEMPLATE_GENERAL,
    PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_BRANDS,
    PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_PRODUCTS,
    PRODUCT_BRAND_TEMPLATE_SPECIFIC,
    PRODUCT_LEVELS_ALIGNMENT_TEMPLATE,
    QUESTION_GENERATION_TEMPLATE,
    REAL_WORLD_CONJOINT_STATEMENTS_PRELIMINARY_TEMPLATE,
    REAL_WORLD_CONJOINT_STATEMENTS_TEMPLATE,
    SINGLE_STATEMENT_CAUSALITY_TEMPLATE,
    STANDARDIZE_NUMERICAL_ATTR,
    STATEMENT_LIKERT_SCORES_TEMPLATE,
    STATEMENT_PERCENTAGE_SCORES_TEMPLATE,
    STATEMENT_SOFT_SCORES_TEMPLATE,
    STUDY_CONTEXT_PROMPT,
    SURVEY_PROMPT_TEMPLATE,
    SURVEY_PROMPT_WITH_TARGET_BEHAVIOR_WITH_DECISION_STRATEGY_BOTH,
    TARGET_BEHAVIOUR_PROMPT,
    TRAIT_LEVELS_SUGGESTION_PROMPT,
    TRAIT_SUGGESTION_PROMPT,
    TRAITS_PREDICTABLE,
    VARIABLE_TO_NATURAL_LANGUAGE_TEMPLATE,
    VERIFY_BRAND_RELEVANCE_TEMPLATE,
)


class PromptBuilder:
    def __init__(self, pt: PromptType = PromptType.GENERIC):
        self.prompt_type = pt

        self._attributes_action_lookup = {
            PromptType.GENERIC: "for this why_prompt",
            PromptType.SERVICE: "when choosing options for this service",
            PromptType.PRODUCT: "when purchasing",
            PromptType.EXPERIENCE: "when considering this experience",
            PromptType.POLICY: "when weighing this policy",
        }
        self._experiment_prompt_type_lookup = {
            PromptType.GENERIC: "things I prefer",
            PromptType.SERVICE: "services I prefer",
            PromptType.PRODUCT: "products I prefer",
            PromptType.EXPERIENCE: "experiences I prefer",
            PromptType.POLICY: "policies I prefer",
        }
        self._experiment_decision_prompt_lookup = {
            PromptType.GENERIC: (
                "I most agree with the statement above, written as a single letter"
                " only, is Option"
            ),
            PromptType.SERVICE: (
                "The most desirable service, written as a single letter only, is Option"
            ),
            PromptType.PRODUCT: (
                "The most desirable product, written as a single letter only, is Option"
            ),
            PromptType.EXPERIENCE: (
                "The most desirable experience, written as a single letter only, is"
                " Option"
            ),
            PromptType.POLICY: (
                "The most desirable policy, written as a single letter only, is Option"
            ),
        }

    def attributes_prompt(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["why_prompt", "attribute_count", "max_length"],
            template=ATTRIBUTES_TEMPLATE.format(
                action=self._attributes_action_lookup[self.prompt_type]
            ),
        )

    def levels_prompt(self, attribute: str = "") -> PromptTemplate:
        """
        Modified the prompt to include "new lines" because of the below responses from OpenAI
            OpenAI Response:
            "Sailing in the Caribbean-Exploring the Mediterranean-Cruising in the Pacific-Racing around the lake-Fishing
            in the Gulf-Touring the Everglades-Rafting the Amazon-Diving the Great Barrier Reef"
        """
        if attribute == PRICE_QUESTION_TEMPLATE:
            return PromptTemplate(
                input_variables=[
                    "why_prompt",
                    "attribute",
                    "level_count",
                    "max_length",
                ],
                template=LEVELS_PRICE_TEMPLATE,
            )

        return PromptTemplate(
            input_variables=["why_prompt", "attribute", "level_count", "max_length"],
            template=LEVELS_TEMPLATE,
        )

    def attributes_levels_prompt(self) -> PromptTemplate:
        """
        Prompt tailored for LLM to create attributes and levels at the same time
        """
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=ATTRIBUTE_LEVELS_TEMPLATE_V2,
            input_variables=[
                "why_prompt",
                "country",
                "attribute_count",
                "max_length",
                "level_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def attributes_levels_prompt_claude(self) -> PromptTemplate:
        """
        Prompt tailored for LLM to create attributes and levels at the same time
        """
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponseClaude)
        return PromptTemplate(
            template=ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE,
            input_variables=[
                "why_prompt",
                "country",
                "year",
                "attribute_count",
                "level_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def attributes_levels_prompt_claude_with_score(self) -> PromptTemplate:
        """
        Prompt tailored for LLM to create attributes and levels at the same time
        """
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponseClaude)
        return PromptTemplate(
            template=ATTRIBUTE_LEVELS_TEMPLATE_CLAUDE_WITH_SCORE,
            input_variables=[
                "why_prompt",
                "country",
                "year",
                "attribute_count",
                "level_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def enhance_attributes_levels_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=ENHANCE_ATTRIBUTE_LEVELS_TEMPLATE,
            input_variables=[
                "why_prompt",
                "existing_attributes_levels",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def orthogonal_levels_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=ORTHOGONAL_ATTRIBUTE_LEVELS_TEMPLATE,
            input_variables=[
                "why_prompt",
                "country",
                "existing_attributes_levels",
                "new_attribute_count",
                "level_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def dependent_variable_prompt_discrete(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["why_prompt"],
            template=DEPENDENT_VARIABLE_TEMPLATE_DISCRETE,
        )

    def dependent_variable_prompt_probabilistic(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["why_prompt"],
            template=DEPENDENT_VARIABLE_TEMPLATE_PROBABILITSTIC,
        )

    def dependent_variable_prompt_with_opt_out(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=DependentVariableResponse)
        return PromptTemplate(
            input_variables=["why_prompt"],
            template=DEPENDENT_VARIABLE_TEMPLATE_WITH_OPT_OUT,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def experiment_prompt(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["why_prompt", "persona_description", "choices"],
            template=EXPERIMENT_TEMPLATE.format(
                prompt_type=self._experiment_prompt_type_lookup[self.prompt_type],
                decision_prompt=self._experiment_decision_prompt_lookup[
                    self.prompt_type
                ],
            ),
        )

    def causality_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=CausalityResponse)
        return PromptTemplate(
            template=CAUSALITY_TEMPLATE,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def single_statement_causality_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=SingleStatementCausalityResponse)
        return PromptTemplate(
            template=SINGLE_STATEMENT_CAUSALITY_TEMPLATE,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def llm_attributes_prompt(self) -> PromptTemplate:
        """
        Prompt to create a list of attributes
        """
        parser = JsonOutputParser(pydantic_object=AttributesResponse)
        return PromptTemplate(
            template=LLM_ATTRIBUTES_TEMPLATE,
            input_variables=["why_prompt", "attribute_count", "prompt_type"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def llm_levels_prompt(self) -> PromptTemplate:
        """
        Prompt to create a list of levels for a given attribute
        """
        parser = JsonOutputParser(pydantic_object=LeveledAttributeBase)
        return PromptTemplate(
            template=LLM_LEVELS_TEMPLATE,
            input_variables=[
                "why_prompt",
                "country",
                "existing_attributes",
                "attribute",
                "level_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def object_of_study_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ObjectOfStudyResponse)
        return PromptTemplate(
            input_variables=["why_prompt"],
            template=OBJECT_OF_STUDY_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def outcome_phrase_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=OutcomePhraseResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "outcome_phrase"],
            template=OUTCOME_PHRASE_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def create_survey_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=SurveyChoice)

        return PromptTemplate(
            input_variables=[
                "persona_traits",
                "country",
                "state",
                "year",
                "study_subject",
                "attributes_levels",
                "options",
                "none_option",
            ],
            template=SURVEY_PROMPT_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_attribute_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductAttributeResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "country", "attribute_count"],
            template=PRODUCT_ATTRIBUTES_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_attribute_withbrandinwhyprompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductAttributeResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "country", "attribute_count", "brands"],
            template=PRODUCT_ATTRIBUTES_WITHBRAND_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def fetch_product(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=RealWorldProductsResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "country", "product", "products_sofar"],
            template=FETCH_PRODUCTS_PROMPT,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_attribute_ranking_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductAttributeResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "attributes", "attribute_count"],
            template=PRODUCT_ATTRIBUTE_RANKING_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def ordinal_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=OrdinalResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "country", "attribute_levels"],
            template=ORDINALITY_CHECK_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_price_levels_withbrandinprompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductPriceLevelResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "attribute", "country"],
            template=PRODUCT_BRAND_TEMPLATE_SPECIFIC,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_price_levels_generalbrandprompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductPriceLevelResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "attribute", "country"],
            template=PRODUCT_BRAND_TEMPLATE_GENERAL,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_price_levels_multiplebrandprompt_getbrand(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductPriceLevelResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "attribute", "country"],
            template=PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_BRANDS,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_price_levels_multiplebrandprompt_getproducts(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProductPriceLevelResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "attribute", "country"],
            template=PRODUCT_BRAND_TEMPLATE_MULTIPLESPECIFIC_PRODUCTS,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_attribute_levels_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=LeveledAttribute)
        return PromptTemplate(
            input_variables=[
                "why_prompt",
                "attribute",
                "country",
                "min_price",
                "max_price",
                "units",
                "brand",
                "level_count",
            ],
            template=PRODUCT_ATTRIBUTE_LEVELS_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def align_levels(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ProcessedProducts)
        return PromptTemplate(
            input_variables=["product_data", "standardized_attribute"],
            template=PRODUCT_LEVELS_ALIGNMENT_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def process_levels_for_products(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=LeveledAttribute)
        return PromptTemplate(
            input_variables=[
                "why_prompt",
                "attribute",
                "country",
                "all_levels",
                "level_count",
            ],
            template=PRODUCT_ATTRIBUTE_LEVEL_SELECTION_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def product_attributes_levels_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=LeveledAttribute)
        return PromptTemplate(
            input_variables=[
                "why_prompt",
                "attributes",
                "country",
                "min_price",
                "max_price",
                "units",
                "brand_or_product",
            ],
            template=PRODUCT_ATTRIBUTE_LEVELS_COMPLETE_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def contains_brand(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=contains_brand)
        return PromptTemplate(
            input_variables=["why_prompt"],
            template=CONTAINS_BRAND,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def verify_brand_relevance_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=BrandRelevanceResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "brands"],
            template=VERIFY_BRAND_RELEVANCE_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def create_survey_prompt_with_target_behavior_with_decision_strategy(
        self,
    ) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=SurveyChoiceResponse)

        return PromptTemplate(
            input_variables=[
                "demographics_section",
                "guidance_section",
                "options",
                "target_behavior",
                "context",
                "optimization",
                "dependent_variable",
                "optimizing",
                "none_choice_instruction",
                "wording_for_study_context",
                "response_format",
                "probabilistic_response_instructions",
                "survey_time_proportion",
            ],
            template=SURVEY_PROMPT_WITH_TARGET_BEHAVIOR_WITH_DECISION_STRATEGY_BOTH,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def format_options(self, options: Dict[str, List[str]]) -> str:
        return "\n".join([
            f"Option {i + 1}: {', '.join(value)}"
            for i, (_, value) in enumerate(options.items())
        ])

    def history_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=HistoryResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "user_profile_attributes", "attributes"],
            template=HISTORY_PROMPT_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def constraint_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=ConstraintResponse)
        return PromptTemplate(
            input_variables=["why_prompt", "user_profile_attributes", "attributes"],
            template=CONSTRAINTS_PROMPT_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def question_generation_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=QuestionList)
        return PromptTemplate(
            input_variables=[
                "why_prompt",
                "user_profile_attributes",
                "model",
                "dimension",
            ],
            template=QUESTION_GENERATION_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def motivation_prompt(self) -> PromptTemplate:
        parser = PydanticOutputParser(pydantic_object=MotivationResponse)
        return PromptTemplate(
            input_variables=[
                "why_prompt",
                "user_profile_attributes",
                "attributes",
                "country",
                "num_questions",
            ],
            template=MOTIVATION_TEMPLATE,
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def check_for_product(self):
        parser = JsonOutputParser(pydantic_object=CheckProductRealWorldResponse)
        return PromptTemplate(
            template=CHECK_FOR_PRODUCT_TEMPLATE,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def generic_or_specific_attribute_check(self) -> PromptTemplate:
        parser = JsonOutputParser(
            pydantic_object=SpecificOrGenericAttributeCheckResponse
        )
        return PromptTemplate(
            template=GENERIC_OR_SPECIFIC_ATTRIBUTE_CHECK,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def generic_or_specific_attribute_check_strict(self) -> PromptTemplate:
        parser = JsonOutputParser(
            pydantic_object=SpecificOrGenericAttributeCheckStrictResponse
        )
        return PromptTemplate(
            template=STATEMENT_MODIFY_TO_SUIT_GENERIC_OR_SPECIFIC_ATTRIBUTE_TEMPLATE,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def suggest_traits(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=TraitSuggestionResponse)
        return PromptTemplate(
            template=TRAIT_SUGGESTION_PROMPT,
            input_variables=[
                "why_prompt",
                "selected_traits",
                "number_of_new_traits",
                "number_of_levels_per_trait",
                "max_length",
                "country",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def suggest_trait_levels(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=TraitLevelsSuggestionResponse)
        return PromptTemplate(
            template=TRAIT_LEVELS_SUGGESTION_PROMPT,
            input_variables=[
                "why_prompt",
                "existing_traits",
                "levels_count",
                "new_trait",
                "max_length",
                "country",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_target_behavior_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=TargetBehaviorResponse)
        return PromptTemplate(
            template=TARGET_BEHAVIOUR_PROMPT,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_study_context_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=StudyContextResponse)
        return PromptTemplate(
            template=STUDY_CONTEXT_PROMPT,
            input_variables=["target_behavior", "attributes"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_natural_language_trait_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=NaturalLanguageTraitResponse)
        return PromptTemplate(
            template=VARIABLE_TO_NATURAL_LANGUAGE_TEMPLATE,
            input_variables=["demo_variables"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def dict_to_natural_language_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(
            pydantic_object=BinaryVariableToNaturalLanguageResponse
        )
        return PromptTemplate(
            template=PERSONA_IN_NATURAL_LANGUAGE_TEMPLATE,
            input_variables=["demographic_data", "why_prompt", "attributes"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def dict_to_natural_language_prompt_image(self) -> PromptTemplate:
        parser = JsonOutputParser(
            pydantic_object=BinaryVariableToNaturalLanguageResponse
        )
        return PromptTemplate(
            template=PERSONA_IN_NATURAL_LANGUAGE_IMAGE_TEMPLATE,
            input_variables=["demographic_data", "image_description"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_recursive_experiment_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=ExperimentIdeationResponse)
        return PromptTemplate(
            template=CAUSAL_EXPERIMENT_IDEATION_PROMPT,
            input_variables=[
                "why_question",
                "respondent_dependant_variable",
                "respondent_context",
                "population_traits",
                "attributes_and_levels",
                "analysis_json_data",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def casuality_check_with_reasoning_experiment_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=CausalityCheckResponse)
        return PromptTemplate(
            template=CAUSALITY_WITH_RATIONALE_TEMPLATE,
            input_variables=["why_prompt"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def iterate_on_recursive_experiment_idea(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=ExperimentIdea)
        return PromptTemplate(
            template=CAUSAL_EXPERIMENT_IDEATION_REFINEMENT_PROMPT,
            input_variables=["context", "previous_ideas_and_feedback"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def check_monetary_attributes_levels(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=MonetaryAttributeResponse)
        return PromptTemplate(
            template=MONETARY_ATTRIBUTES_LEVELS_TEMPLATE,
            input_variables=[
                "existing_attributes_levels",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_conjoint_statements_preliminary_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=RealWorldConjointStatementResponse)
        return PromptTemplate(
            template=REAL_WORLD_CONJOINT_STATEMENTS_PRELIMINARY_TEMPLATE,
            input_variables=["why_prompt", "attribute_count", "statement_history"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_conjoint_statements_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=RealWorldConjointStatementResponse)
        return PromptTemplate(
            template=REAL_WORLD_CONJOINT_STATEMENTS_TEMPLATE,
            input_variables=[
                "why_prompt",
                "suggestion_count",
                "attribute_count",
                "statement_history",
                "context_message",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_persona_extraction_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=PersonasResponse)
        return PromptTemplate(
            template=PERSONA_EXTRACTION_PROMPT,
            input_variables=["text"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def get_likert_label_prompt(self) -> PromptTemplate:
        parser = JsonOutputParser(pydantic_object=LikertLabelResponse)
        return PromptTemplate(
            template=LIKERT_LABEL_PROMPT_TEMPLATE,
            input_variables=[
                "context",
                "statement",
                "scale",
                "scale_range",
                "image_details",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def latent_variables_generation_prompt(self) -> PromptTemplate:
        """
        Prompt to generate psychological traits, pain points, trait measurement
        statements, and pain point detection statements.
        """

        parser = JsonOutputParser(pydantic_object=GenerationLLMOutput)
        return PromptTemplate(
            template=LATENT_VARIABLE_GENERATION_TEMPLATE,
            input_variables=[
                "why_prompt",
                "traits_count",
                "measurement_count",
                "pain_point_count",
                "scale_labels_count",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def additional_notes_prompt(self) -> PromptTemplate:
        """
        Prompt to generate insights into how a trait is likely to manifest in an
        individual, based on the trait in study and a demographic profile.
        """

        parser = JsonOutputParser(pydantic_object=AdditionalNotesLLMOutput)
        return PromptTemplate(
            template=ADDITIONAL_NOTES_TEMPLATE,
            input_variables=["why_prompt", "demographics", "trait"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def statement_percentage_scores_prompt(self) -> PromptTemplate:
        """
        Prompt to score trait measurement and pain point detection statements for a
        given persona and trait in study.
        """

        parser = JsonOutputParser(pydantic_object=StatementPercentageScoresLLMOutput)
        return PromptTemplate(
            template=STATEMENT_PERCENTAGE_SCORES_TEMPLATE,
            input_variables=[
                "demographics",
                "trait",
                "statements_with_scale_labels",
                "additional_notes",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def statement_likert_scores_prompt(self) -> PromptTemplate:
        """
        Prompt to score trait measurement and pain point detection statements for a
        given persona and trait in study.
        """

        parser = JsonOutputParser(pydantic_object=StatementLikertScoresLLMOutput)
        return PromptTemplate(
            template=STATEMENT_LIKERT_SCORES_TEMPLATE,
            input_variables=[
                "demographics",
                "trait",
                "statements_with_scale_labels",
                "additional_notes",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def statement_soft_scores_prompt(self) -> PromptTemplate:
        """
        Prompt to score trait measurement and pain point detection statements for a
        given persona and trait in study.
        """

        parser = JsonOutputParser(pydantic_object=StatementSoftScoresLLMOutput)
        return PromptTemplate(
            template=STATEMENT_SOFT_SCORES_TEMPLATE,
            input_variables=[
                "demographics",
                "trait",
                "statements_with_scale_labels",
                "additional_notes",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def concept_testing_statement_percentage_scores_prompt(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""

        parser = JsonOutputParser(pydantic_object=StatementPercentageScoresLLMOutput)
        return PromptTemplate(
            template=CONCEPT_TESTING_STATEMENT_PERCENTAGE_SCORES_PROMPT,
            input_variables=[
                "description_prompt",
                "demographics",
                "image",
                "statements_with_scale_labels",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def concept_testing_statement_likert_scores_prompt(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""

        parser = JsonOutputParser(pydantic_object=StatementLikertScoresLLMOutput)
        return PromptTemplate(
            template=CONCEPT_TESTING_STATEMENT_LIKERT_SCORES_PROMPT,
            input_variables=[
                "description_prompt",
                "demographics",
                "image",
                "statements_with_scale_labels",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def concept_testing_statement_soft_scores_prompt(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""

        parser = JsonOutputParser(pydantic_object=StatementSoftScoresLLMOutput)
        return PromptTemplate(
            template=CONCEPT_TESTING_STATEMENT_SOFT_SCORES_PROMPT,
            input_variables=[
                "description_prompt",
                "demographics",
                "image",
                "statements_with_scale_labels",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def persona_traits_NL_summary_prompt(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""

        parser = JsonOutputParser(pydantic_object=NLSummaryLLMOutput)
        return PromptTemplate(
            template=PERSONA_TRAITS_NL_SUMMARY_TEMPLATE,
            input_variables=[
                "why_prompt",
                "demographics",
                "persona_traits_data",
            ],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def standarize_numerical_attr(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=STANDARDIZE_NUMERICAL_ATTR,
            input_variables=["al_lookup"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def traits_predictable(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=TRAITS_PREDICTABLE,
            input_variables=["known_traits", "new_traits"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

    def predict_traits(self) -> PromptTemplate:
        """Prompt to summarize the persona traits information in natural language."""
        parser = JsonOutputParser(pydantic_object=LeveledAttributeResponse)
        return PromptTemplate(
            template=PREDICT_TRAITS,
            input_variables=["traits", "trait_to_predict", "possible_values"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )
