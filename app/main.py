import os
from contextlib import asynccontextmanager

import sentry_sdk
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from redis import asyncio as aioredis
from sentry_sdk import capture_exception

from app.api.router import api_router
from app.core.global_config import settings
from app.core.utils.databases import check_database_connection
from app.core.utils.workers import worker_ping


@asynccontextmanager
async def lifespan(app: FastAPI):
    check_database_connection()
    redis = aioredis.from_url(
        url=os.getenv("REDIS_URL", "redis://localhost:6379"),
        encoding="utf8",
        decode_responses=True,
    )
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    yield


app = FastAPI(
    openapi_tags=settings.TAGS_METADATA,
    title=settings.TITLE,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    terms_of_service=settings.TOS,
    contact=settings.CONTACT,
    license_info=settings.LICENSE,
    lifespan=lifespan,
)
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router)

if os.getenv("ENVIRONMENT") != "local":
    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN"),
        traces_sample_rate=1.0,
        enable_tracing=True,
    )


@app.get("/", tags=["main"])
def root() -> str:
    """Root endpoint."""
    return "ok"


@app.get("/sentry-debug", tags=["main"])
def trigger_error():
    """Trigger a test error for Sentry debugging."""
    try:
        1 / 0
    except Exception as e:
        capture_exception(e)
        return JSONResponse(
            {"error": "Exception captured by Sentry. Check Sentry for more details."}
        )


@app.post("/ping-worker", tags=["main"])
def run_task():
    """
    Start a ping worker task.
    """
    try:
        task = worker_ping.delay()
        return JSONResponse({"task_id": task.id})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail="Failed to start ping worker task.")


if __name__ == "__main__":
    uvicorn.run("main:app", port=int(os.getenv("PORT", 8080)))
