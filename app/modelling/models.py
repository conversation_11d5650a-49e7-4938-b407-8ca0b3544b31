import os
import warnings
from collections import OrderedDict

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import pylogit as pl
import statsmodels.api as sm
from rich import print
from sklearn.metrics import classification_report

from app.modelling.data_objects import SurveyData

warnings.simplefilter(action="ignore", category=FutureWarning)
pd.set_option("display.chop_threshold", 10**-100)
repo = os.path.dirname(os.getcwd())


class Model:
    model_name = ""

    def __init__(self, seed: int = 1821):
        np.random.seed(seed)

    def fit(self, survey: SurveyData):
        raise NotImplementedError


class Ols(Model):
    model_name = "ols"

    def __init__(self, seed: int = 1821):
        super().__init__(seed)

    def fit(self, survey: SurveyData):
        data = survey.survey_setup
        """Run OLS model"""
        y = data["chosen_choice_letter"]
        x = data.iloc[:, 7:]
        # drop reference levels
        x = pd.get_dummies(x, columns=list(x.columns), drop_first=True)
        x = sm.add_constant(x)

        """Fit model"""
        mod = sm.OLS(y, x)
        res = mod.fit()

        keys = [x.split("_")[1] for x in res.params.index[1:]]
        levels = ["intercept"] + survey.get_attributes_by_keys(keys)
        print("OLS: res.summary:", res.summary(xname=levels))

        return res


class Clm(Model):
    model_name = "clm"

    def __init__(self, ridge: bool = True, seed: int = 1821):
        super().__init__(seed)
        self.ridge = ridge

    def fit(self, survey: SurveyData):
        data = survey.survey_setup
        basic_specification = OrderedDict()
        basic_names = OrderedDict()
        data = data[[c for c in data.columns if "Reference" not in c]]

        variables = data.columns[3:]
        for col in variables:
            basic_specification[col] = [list(range(1, len(np.unique(data.alt_id)) + 1))]
            basic_names[col] = [col]

        parameter_dimension = len(basic_names.values())

        model = pl.create_choice_model(
            data=data,
            alt_id_col="alt_id",
            obs_id_col="chid",
            choice_col="choice",
            specification=basic_specification,
            model_type="MNL",
            names=basic_names,
        )

        if self.ridge:
            model.fit_mle(np.zeros(parameter_dimension), ridge=0.8)
        else:
            model.fit_mle(np.zeros(parameter_dimension))

        stat_model_summary = model.get_statsmodels_summary()
        print(stat_model_summary)
        return model


class MixedClm(Clm):
    model_name = "mixed_clm"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def fit(self):
        self.pre_process_data()

        print(self.data)

        basic_specification = OrderedDict()
        basic_names = OrderedDict()
        # basic_specification["intercept"] = list(range(1, len(np.unique(self.data.alt_id))))
        # basic_names["intercept"] = list(self.categories[:-1])
        # parameter_dimension = len(list(self.categories[:-1])) - 1

        index_var_names = self.data.columns[3:]
        # Transform all of the index variable columns to have float dtypes
        for col in index_var_names:
            self.data[col] = self.data[col].astype(float)

        # Note that the names used below are simply for consistency with
        # the coefficient names given in the mlogit vignette.
        for col in index_var_names:
            basic_specification[col] = [[1, 2]]
            basic_names[col] = [col]

        model = pl.create_choice_model(
            data=self.data,
            alt_id_col="alt_id",
            obs_id_col="chid",
            choice_col="choice",
            specification=basic_specification,
            model_type="Mixed Logit",
            names=basic_names,
            mixing_id_col="chid",
            mixing_vars=index_var_names,
        )

        model.fit_mle(
            init_vals=np.zeros(2 * len(index_var_names)), num_draws=600, seed=123
        )

        # Look at the estimated results
        stat_model_summary = model.get_statsmodels_summary()
        model_summary = model.print_summaries()
        print(stat_model_summary)
        print(model_summary)
        df_res = pd.DataFrame({
            "param_name": model.params.index[1:],
            "param_w": model.params.values[1:],
            "pval": model.pvalues[1:],
        })
        print(model.params.index)

        # adding field for absolute of parameters
        df_res["abs_param_w"] = np.abs(df_res["param_w"])
        # marking field is significant under 95% confidence interval
        df_res["is_sig_95"] = df_res["pval"] < 0.05
        # constructing color naming for each param
        df_res["c"] = ["blue" if x else "red" for x in df_res["is_sig_95"]]

        f, ax = plt.subplots(figsize=(20, 8))
        plt.title("Attribute Levels Part Worth")
        pwu = df_res["param_w"]
        xbar = np.arange(len(pwu))
        plt.barh(xbar, pwu, color=df_res["c"])
        plt.yticks(xbar, labels=df_res["param_name"])
        # f.tight_layout()
        if self.store_results:
            plt.savefig(
                os.path.join(
                    self.data_path,
                    f"{self.experiment_title}-Attribute Levels Part Worth",
                )
            )
        if self.show_summaries:
            plt.show()
        plt.clf()
        plt.close()

        # predictive report
        self.data["preds"] = model.predict(self.data)
        for i in range(0, self.data.shape[0], 3):
            pred_choice = np.where(
                np.array(self.data.loc[i : i + 2, "preds"])
                >= max(self.data.loc[i : i + 2, "preds"]),
                int(1),
                int(0),
            )
            self.data.loc[i : i + 2, "pred_choice"] = pred_choice
        self.data["pred_choice"] = self.data["pred_choice"].astype("int64")
        self.class_report = classification_report(
            self.data.choice, self.data.pred_choice, output_dict=True
        )
        class_report_t = pd.DataFrame(self.class_report).transpose()
        self.f1_score_clm = class_report_t["f1-score"].mean()
        print(self.class_report)

        return model, self.class_report, model.rho_squared, self.f1_score_clm
