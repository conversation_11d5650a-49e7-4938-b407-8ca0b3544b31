from dataclasses import dataclass
from pathlib import Path

import numpy as np
import pandas as pd


@dataclass
class RunnerConfig:
    title: str
    run_name: str
    run_id: str
    model: str
    survey_setup_path: Path
    survey_path: Path
    output_dir: Path
    store_metrics: bool = True
    store_plots: bool = True
    show_plots: bool = False

    def get_file_path(self, summary_name: str, extension: str = "csv") -> Path:
        return self.output_dir / Path(
            f"{self.title}-{summary_name}-{self.run_name}.{extension}"
        )


@dataclass
class SurveyData:
    survey_setup: pd.DataFrame
    survey_choices: dict

    @property
    def attribute_names(self):
        return list(
            self.survey_choices["attributes_and_levels_lookup"][
                "get_attribute_text"
            ].values()
        )

    @property
    def attributes_dict(self) -> dict:
        return self.survey_choices["attributes_and_levels_lookup"]["get_level_text"]

    @property
    def attributes_levels(self) -> list:
        return list(
            self.survey_choices["attributes_and_levels_lookup"][
                "get_level_text"
            ].values()
        )

    @property
    def attribute_ids(self) -> list:
        return list(
            self.survey_choices["attributes_and_levels_lookup"][
                "get_attribute_id"
            ].values()
        )

    @property
    def attribute_text(self) -> dict:
        return self.survey_choices["attributes_and_levels_lookup"]["get_attribute_text"]

    @property
    def categories(self) -> list:
        return list(np.unique(self.survey_setup.chosen_choice_letter))

    @property
    def prompt(self) -> str:
        return self.survey_choices["experimentor why question prompt"]

    def is_data_valid(self):
        if self.survey_setup.empty:
            print(f"Warning: Dataset is Empty, Terminating analysis")
            return False
        return True

    def get_attributes_by_keys(self, keys: list[str]) -> list[str]:
        return [self.attributes_dict[key] for key in keys]
