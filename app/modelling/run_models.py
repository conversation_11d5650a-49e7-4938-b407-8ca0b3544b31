from pandas import DataFrame

from app.api.v1.schemas.runs import RunOutput


class OlsRun(RunOutput):
    model_metrics: DataFrame
    parameter_summary: DataFrame
    diagnostics_table: DataFrame
    r2: float

    def log(self, logger, run_id, run_name):
        """
        prints messages to terminal
        """
        labels = {
            "wandb_run_id": f"{run_id}",
            "wandb_run_name": f"{run_name}",
        }

        sig_ratio = self.num_sig_vars / self.num_vars

        logger.info(
            f"OLS R Squared: {self.r2}",
            extra={
                "http.request.body.content": f"OLS R Squared: {self.r2}",
                "labels": labels,
            },
        )

        logger.info(
            f"OLS # Significant Variables: {self.num_sig_vars}",
            extra={
                "http.request.body.content": (
                    f"OLS # Significant Variables: {self.num_sig_vars}"
                ),
                "labels": labels,
            },
        )

        logger.info(
            f"OLS # of variables: {self.num_vars}",
            extra={
                "http.request.body.content": f"OLS # of variables: {self.num_vars}",
                "labels": labels,
            },
        )

        logger.info(
            f"OLS Ratio of Significant Variables: {sig_ratio}",
            extra={
                "http.request.body.content": (
                    f"OLS Ratio of Significant Variables: {sig_ratio}"
                ),
                "labels": labels,
            },
        )


class ClmRun(RunOutput):
    model_metrics: DataFrame
    coefficients: DataFrame
    rho2: float
    f1: float

    def log(self, logger, run_id, run_name):
        """
        prints results to terminal
        """
        labels = {
            "wandb_run_id": f"{run_id}",
            "wandb_run_name": f"{run_name}",
        }

        sig_ratio = self.num_sig_vars / self.num_vars

        logger.info(
            f"CLM Pseudo R Squared: {self.rho2}",
            extra={
                "http.request.body.content": f"CLM Pseudo R Squared: {self.rho2}",
                "labels": labels,
            },
        )

        logger.info(
            f"CLM # Significant Variables: {self.num_sig_vars}",
            extra={
                "http.request.body.content": (
                    f"CLM # Significant Variables: {self.num_sig_vars}"
                ),
                "labels": labels,
            },
        )

        logger.info(
            f"CLM # of variables: {self.num_vars}",
            extra={
                "http.request.body.content": f"CLM # of variables: {self.num_vars}",
                "labels": labels,
            },
        )

        logger.info(
            f"CLM F1 Statistic: {self.f1}",
            extra={
                "http.request.body.content": f"CLM F1 Statistic: {self.f1}",
                "labels": labels,
            },
        )

        logger.info(
            f"CLM Ratio of Significant Variables: {sig_ratio}",
            extra={
                "http.request.body.content": (
                    f"CLM Ratio of Significant Variables: {sig_ratio}"
                ),
                "labels": labels,
            },
        )
