"""
Defines the Randomness Inspector Class

Input : an array containing the numerical sequences to be tested for randomness

Output : a list of hypothesis test results for each randonmenss test performed

References
https://www.cs.rice.edu/~johnmc/comp528/lecture-notes/Lecture22.pdf
http://www.cs.fsu.edu/~mascagni/Testing.pdf

"""

import math

import numpy as np
import pandas as pd
import scipy
from rich.padding import Padding
from rich.progress import track
from scipy import stats
from scipy.stats import kstest


# It's a class that implements a number of randomness tests and random number generator
class Randomness_Inspector:
    def __init__(self, X, alpha=0.001):
        """
        :param X: the input data
        :param alpha: Margin of Significance. Probability of rejecting the null hypothesis when it is true.
        """

        super(Randomness_Inspector, self).__init__()
        self.X = X
        self.L = len(X)
        self.alpha = alpha

    def chisquare_test(self):
        chi_statistic, p_val = scipy.stats.chisquare(self.X)

        if p_val >= self.alpha:
            # the null hypothesis is failed to be rejected hence False
            return False, p_val
        else:
            return True, p_val

    def cusum_test(self):
        # https://blogs.sas.com/content/iml/2019/04/22/cusum-test-randomness-binary-sequence.html

        L = len(self.X)
        x = np.array(self.X)
        x = 2 * x - 1
        abs_cum_sum = np.max(abs(np.cumsum(x)))

        z_statistic = abs_cum_sum / math.sqrt(L)

        first_term_start = int(((-L / float(z_statistic)) + 1) / 4.0)
        first_term_end = int(((L / float(z_statistic)) - 1) / 4.0)

        first_term_sum = 0
        for k in range(first_term_start, first_term_end + 1):
            first_term_sum += scipy.stats.norm.cdf(
                (4 * k + 1) * z_statistic
            ) - scipy.stats.norm.cdf((4 * k - 1) * z_statistic)

        second_term_start = int(((-L / float(z_statistic)) - 3) / 4.0)
        second_term_end = int(((L / float(z_statistic)) - 1) / 4.0)

        second_term_sum = 0
        for k in range(second_term_start, second_term_end + 1):
            second_term_sum += scipy.stats.norm.cdf(
                (4 * k + 3) * z_statistic
            ) - scipy.stats.norm.cdf((4 * k + 1) * z_statistic)

        p_val = 1 - first_term_sum + second_term_sum

        if p_val >= self.alpha:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, p_val
        else:
            # print(True, p_val)
            return True, p_val

    def turning_point_test(self):
        """mainly tests for cyclicality of data points/Checks for the number of inflection points in the data.
        https://dspace.mit.edu/bitstream/handle/1721.1/100851/18-443-spring-2009/contents/assignments/MIT18_443s09_assn03_res03.pdf
        """

        n = len(self.X)
        # compute number of turning points
        dx = np.diff(self.X)
        tp = np.sum(dx[1:] * dx[:-1] < 0)

        # compute the test statistic
        # E(n) : expected turning points
        E_tp = (2 * n - 4) / 3.0  # Number of Turning Points Expecded
        V_tp = (16 * n - 29) / 90.0  # Variance Expected
        z_statistic = (tp - E_tp) / math.sqrt(V_tp)

        # compute the critical values for the given alpha
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        # p-value
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            return False, p_val
        else:
            return True, p_val

    def wald_wolfowitz_runs_test(self):
        """
        The function takes in a list of numbers and returns a boolean value and a p-value. The boolean
        value is True if the list of numbers is random and False if the list of numbers is not random. The
        :return: a tuple of two values. The first value is a boolean value which is True if the null
        hypothesis is rejected and False if the null hypothesis is not rejected. The second value is the
        p-value.
        """

        # convert input into a run sequence
        run_sequence = list()
        L = len(self.X)
        x = np.array(self.X)
        # L = len(self.X)
        run_sequence = 2 * x - 1
        # print(run_sequence)

        # for i in range(1, L):
        #   if self.X[i] > self.X[i-1]:
        #     run_sequence.append(-1)
        #   else:
        #     run_sequence.append(1)

        # for i in track(range(1, L), description="wald_wolfowitz_runs_test"):
        #   if self.X[i] > np.median(self.X):
        #     run_sequence.append(1)
        #   else:
        #     run_sequence.append(-1)

        pos_counts, neg_counts, run_counts = 1, 1, 1
        # count run sequence
        for i in range(0, L - 1):
            if run_sequence[i] > 0:
                pos_counts += 1
            elif run_sequence[i] < 0:
                neg_counts += 1
            if i > 1 and run_sequence[i] != run_sequence[i - 1]:
                run_counts += 1
        n = pos_counts + neg_counts

        # compute run sequence statistics
        E_r = (2 * (pos_counts * neg_counts) / n) + 1
        V_r = ((E_r - 1) * (E_r - 2)) / (n - 1)

        if V_r == 0.0:
            return None, np.nan
        z_statistic = (run_counts - E_r) / math.sqrt(V_r)
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            return False, p_val
        else:
            return True, p_val

    def mann_kendal_rank_test(self):
        """
        The Mann-Kendall test is a non-parametric test that determines if there is a monotonic upward or
        downward trend of the variable of interest over time. A monotonic upward (downward) trend means
        that the variable consistently increases (decreases) through time, but the trend may or may not be
        linear. The null hypothesis of the test is that there is no monotonic trend
        :return: The return value is a tuple of two values. The first value is a boolean value which is
        True if the null hypothesis is rejected and False if the null hypothesis is not rejected. The
        second value is the p-value.
        """

        # compute mann kendal sum
        s = 0
        L = len(self.X)
        d = np.ones(L)

        for i in track(range(L - 1), description="mann_kendal_rank_test"):
            s = (
                s
                + np.sum(d[i + 1 : L][self.X[i + 1 : L] > self.X[i]])
                - np.sum(d[i + 1 : L][self.X[i + 1 : L] < self.X[i]])
            )

        # compute asymptotic variance
        u_x = np.unique(self.X)
        g = len(u_x)
        # if no ties in the sequence
        if L == g:
            V_s = (L * (L - 1) * (2 * L + 5)) / 18
        # if ties exist in sequence
        else:
            tp = np.zeros(u_x.shape)
            demo = np.ones(L)

            for i in range(g):
                tp[i] = np.sum(demo[x == u_x[i]])

            V_s = (
                L * (L - 1) * (2 * L + 5) - np.sum(tp * (tp - 1) * (2 * tp + 5))
            ) / 18

        # compute statistic
        # print(V_s)

        z_statistic = s / math.sqrt(V_s)
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            return False, p_val
        else:
            return True, p_val

    def difference_sign_test(self):
        """
        > The function computes the number of times the differenced sequence is positive, and then
        compares that number to the expected number of positive values under the null hypothesis
        :return: a tuple of two values. The first value is a boolean value, which is True if the null
        hypothesis is rejected and False if the null hypothesis is not rejected. The second value is the
        p-value.
        """

        # count the number of times the differenced sequence is positive
        L = len(self.X)
        c = 0
        for i in track(range(1, L), description="difference_sign_test"):
            if self.X[i] - self.X[i - 1] > 0:
                c += 1

        # compute statistics
        E_c = 0.5 * (L - 1)
        V_c = (L + 1) / 12.0

        z_statistic = (c - E_c) / math.sqrt(V_c)
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, p_val

        else:
            # print(True, p_val)
            return True, p_val

    def cox_stuart_test(self):
        """
        ???? Box-Cox test for homogeneity of regression slopes
        """

        # form the paired sequence
        L = len(self.X)
        if L % 2 == 0:
            c = int(L / 2.0)
        else:
            c = int((L + 1) / 2.0)

        p_seq = list()
        for i in range(0, c):
            p_seq.append(self.X[i])
            p_seq.append(self.X[i + c])

        # perform sign test on new sequence
        L = len(p_seq)
        count = 0
        for i in track(range(1, L), description="cox_stuart_test"):
            if p_seq[i] - p_seq[i - 1] > 0:
                count += 1

        # compute statistics
        E_c = L / 2.0
        V_c = L / 4.0

        z_statistic = (count - E_c) / math.sqrt(V_c)
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, p_val
        else:
            # print(True, p_val)
            return True, p_val

    def bartels_rank_test(self):
        # https://www.jstor.org/stable/2287767

        # compute sample ranks of the sequence
        R = scipy.stats.rankdata(self.X)
        L = len(self.X)

        rank_difference = list()
        rank_factor = list()
        # compute test_statistic
        for i in range(0, L - 1):
            rank_difference.append((R[i] - R[i + 1]) ** 2)
            rank_factor.append((R[i] - ((L + 1) / 2.0)) ** 2)

        rank_difference = np.sum(rank_difference)
        rank_factor = np.sum(rank_factor)

        RVN_statistic = rank_difference / rank_factor

        if L == 1:
            V_rvn = np.nan
        else:
            V_rvn = (4 * (L - 2) * ((5 * L) ** 2 - 2 * L - 9)) / (
                5 * L * (L + 1) * (L - 1) ** 2
            )

        z_statistic = (RVN_statistic - 2) / math.sqrt(V_rvn)
        z_critical = scipy.stats.norm.ppf(1 - (self.alpha / 2))
        p_val = scipy.stats.norm.sf(abs(z_statistic)) * 2

        if z_statistic <= z_critical and z_statistic >= -z_critical:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, p_val
        else:
            # print(True, p_val)
            return True, p_val

    def categorical_runs_test(self):
        "Reference https://github.com/psinger/RunsTest"
        from statsmodels.sandbox.stats.runs import runstest_1samp

        z_statistic, pval = runstest_1samp(self.X, correction=False)

        if pval >= self.alpha:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, pval
        else:
            # print(True, p_val)
            return True, pval

    def Kolmogorov_Smirnov_test(self):
        statistic, pval = kstest(self.X, stats.randint.cdf)

        if pval >= self.alpha:
            # the null hypothesis is failed to be rejected hence False
            # print(False, p_val)
            return False, pval
        else:
            # print(True, p_val)
            return True, pval

    def randonmness_test(self):
        # iid denotes independent and identically distributed random variables
        test_results = pd.DataFrame(
            columns=["Test", "Null Hypothesis Rejected", "p-value"]
        )

        if len(np.unique(self.X)) <= 2:
            # if len(np.unique(self.X)) != 2:
            #     print('Warning: input is not a series of binary observations')
            #     return None
            # Pair session with Vasili: Returning Detailed Message results
            if np.size(self.X) == 0:
                return (
                    None,
                    (
                        "Warning: Empty Array given to Randomness Inspector, cannot"
                        " conduct tests"
                    ),
                )
            if np.size(self.X) < 30:
                return (
                    None,
                    (
                        "Warning: Empty Array given to Randomness Inspector, cannot"
                        " conduct tests"
                    ),
                )

            # perform turning point test
            # TODO: Vasili, please implement this test once we fix the error.  Sent in your signal
            # hypothesis, p_val = self.cusum_test()
            # test_results.loc[0] = ['Cusum',hypothesis, p_val]

            # perform Wald Wolfowitz Runs test
            # TODO: Vasili, please implement this test once we check for V_r == 0 in line 162 of randomness_inspector.py
            hypothesis, p_val = self.wald_wolfowitz_runs_test()
            test_results.loc[0] = ["Wald Wolfowitz", hypothesis, p_val]

            # perform Bartels Rank test
            hypothesis, p_val = self.bartels_rank_test()
            test_results.loc[1] = ["Bartels Rank", hypothesis, p_val]
            test_results_formatted = Padding(
                test_results.to_string(index=False),
                (1, 2),
                style="on blue",
                expand=False,
            )

            return test_results

        if len(np.unique(self.X)) > 2:
            # perform chi-square frequency test
            # hypothesis, p_val = self.chisquare_test()
            # test_results.loc[0] = ['Chi-Square', hypothesis, p_val]

            # perform Wald Wolfowitz Runs test for categorical sequences
            hypothesis, p_val = self.categorical_runs_test()
            test_results.loc[0] = ["Wald Wolfowitz", hypothesis, p_val]

            # perform Bartels Rank test
            hypothesis, p_val = self.bartels_rank_test()
            test_results.loc[1] = ["Bartels Rank", hypothesis, p_val]

            return test_results


class Randomness_Generator:
    # TODO: consider more random number generators

    def __init__(self, samples=10000, seed=7335):
        super(Randomness_Generator, self).__init__()
        self.samples = samples
        self.seed = seed

    def Wichmann_Hill(self, s1, s2, s3):
        seq = []
        for i in range(self.samples):
            s1 = 171 * s1 % 30269
            s2 = 172 * s2 % 30307
            s3 = 170 * s3 % 30323
            seq.append(
                (float(s1) / 30269.0 + float(s2) / 30307.0 + float(s3) / 30323.0) % 1.0
            )

        return np.array(seq)

    def Marsaglia_Multicarry(self, mult=23712, mod=5):
        # TODO: check fixed variables

        X = np.random.uniform(low=0.0, high=1.0, size=self.samples)
        C = np.random.uniform(low=0.0, high=1.0, size=self.samples)

        for i in range(1, self.samples):
            X[i] = (mult * X[i - 1] + C[i - 1]) % mod
            C[i] = math.floor((mult * X[i - 1] + C[i - 1]) / mod)

        return X

    def Mersenne_Twister(
        self,
        seed,
        w=32,
        n=624,
        m=397,
        r=31,
        a=0x99080DF,
        u=11,
        d=0xFFFFFFFFF,
        s=7,
        b=0x9D2C5680,
        t=15,
        c=0xEFC60000,
        l=18,
        f=1812433253,
    ):
        # TODO: add multiple sample generation

        lower_mask = (1 << 31) - 1
        upper_mask = 1 << 31
        state[0] = seed
        index = n

        def int_32(number):
            return int(0xFFFFFFFF & number)

        for i in range(1, n):
            state[i] = int_32(f * (state[i - 1] ^ (state[i - 1] >> 30)) + i)

        def twist():
            for i in range(624):
                temp = int_32(
                    (state[i] & upper_mask) + (state[(i + 1) % 624] & lower_mask)
                )
                temp_shift = temp >> 1
                if temp % 2 != 0:
                    temp_shift = temp_shift ^ 0x9908B0DF
                state[i] = state[(i + m) % 624] ^ temp_shift
                index = 0

        def get_random_number():
            if index >= n:
                twist()
            y = state[index]
            y = y ^ (y >> u)
            y = y ^ ((y << s) & b)
            y = y ^ ((y << t) & c)
            y = y ^ (y >> l)
            index += 1
            return int_32(y)


# x = Randomness_Generator(samples = 10000).Wichmann_Hill(300, 500, 1000)
# x = np.array(np.random.choice([0, 1], size=(1000,), p=[7./10, 3./10]))
# x = np.array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1])
# x = np.array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1])
# x = np.array([0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1])
# x = np.ones(1000)

# x = [0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2]
# x = [0,0,0,1,1,0,0,0,1,0,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,0,1,2,1,0,0,1,0,0,1,0,0,1,1,1,1,0,0,0,0,1,0,0,1,1,1,1,0,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,0,1,2,0,2,2,2,2,1,2,1,1,2,2,1,1,0,2,2,2,1,0,0,2,1,2,2,0,0,2,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]
# x = [0,0,0,1,1,0,0,0,1,0,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,0,1,1,0,0,1,0,0,1,0,0,1,1,1,1,0,0,0,0,1,0,0,1,1,1,1,0,1,1,0,1,0,1,1,1,1,1,0,1,0,0,1,0,0,1,1]

# print(type(Randomness_Inspector(X = x).randonmness_test(var_type = 'binary')))
# Randomness_Inspector(X = x).randonmness_test(var_type = 'binary')
