import json
import os
from pathlib import Path

import pandas as pd
from sentry_sdk import capture_exception

from app.core.utils.logging import app_logger
from app.modelling.data_objects import RunnerConfig, SurveyData
from app.modelling.metrics import (
    ClmCoefficients,
    ClmModelSummary,
    OlsDiagnostics,
    OlsModelSummary,
    OlsParameterSummary,
)
from app.modelling.metrics_strategy import ClmMetricsStrategy, OlsMetricsStrategy
from app.modelling.models import Clm, Ols
from app.modelling.preprocessing import ClmPreprocessing, OlsPreprocessing
from app.modelling.run_models import ClmRun, OlsRun

log = app_logger.get_logger(__name__)


class OlsRunner:
    def __init__(self, config: RunnerConfig):
        self.config = config

    def run(self) -> OlsRun:
        data = self.load_data()
        data = OlsPreprocessing().preprocess_data(data)
        model = Ols().fit(data)
        metrics = OlsMetricsStrategy().get_model_metrics(model, data)
        for name, metric in metrics.items():
            if self.config.store_metrics:
                metric.to_csv(self.config.get_file_path(name, extension="csv"))
                metric.to_json(self.config.get_file_path(name, extension="json"))
            if self.config.store_plots:
                path = self.config.get_file_path(name, extension="png")
                metric.plot(show=self.config.show_plots, path=path)
            else:
                path = Path()

        return OlsRun(
            model=model,
            parameter_summary=metrics[OlsParameterSummary.name].metric,
            model_metrics=metrics[OlsModelSummary.name].metric,
            r2=model.rsquared,
            num_obs=model.nobs,
            num_vars=metrics[OlsParameterSummary.name].ols_num_variables,
            num_sig_vars=metrics[
                OlsParameterSummary.name
            ].ols_num_significant_variables,
            diagnostics_table=metrics[OlsDiagnostics.name].metric,
        )

    def load_data(self) -> SurveyData:
        try:
            survey_setup_path = self.config.survey_setup_path
            if not os.path.exists(survey_setup_path):
                raise FileNotFoundError(
                    f"Survey setup file not found: {survey_setup_path}"
                )

            survey_setup = pd.read_csv(survey_setup_path)

            survey_path = self.config.survey_path
            if not os.path.exists(survey_path):
                raise FileNotFoundError(f"Survey file not found: {survey_path}")

            with open(survey_path, "r") as survey_file:
                survey_choices = json.load(survey_file)

            return SurveyData(survey_setup, survey_choices)

        except Exception as e:
            log.error(f"Error loading data: {e}")
            capture_exception(e)
            raise


class ClmRunner:
    def __init__(self, config: RunnerConfig):
        self.config = config

    def run(self) -> ClmRun:
        data = self.load_data()
        data = ClmPreprocessing().preprocess_data(data)
        model = Clm().fit(data)
        metrics = ClmMetricsStrategy().get_model_metrics(model, data)
        for name, metric in metrics.items():
            if self.config.store_metrics:
                metric.to_csv(self.config.get_file_path(name, extension="csv"))
                metric.to_json(self.config.get_file_path(name, extension="json"))
            if self.config.store_plots:
                path = self.config.get_file_path(name, extension="png")
                metric.plot(show=self.config.show_plots, path=path)
            else:
                path = Path()

        return ClmRun(
            model=model,
            coefficients=metrics[ClmCoefficients.name].metric,
            model_metrics=metrics[ClmModelSummary.name].metric,
            f1=metrics[ClmModelSummary.name].f1_score,
            rho2=model.rho_squared,
            num_obs=len(data.survey_setup.index),
            num_vars=metrics[ClmCoefficients.name].clm_num_variables,
            num_sig_vars=metrics[ClmCoefficients.name].clm_num_significant_variables,
        )

    def load_data(self) -> SurveyData:
        try:
            survey_setup_path = self.config.survey_setup_path
            if not os.path.exists(survey_setup_path):
                raise FileNotFoundError(
                    f"Survey setup file not found: {survey_setup_path}"
                )

            survey_setup = pd.read_csv(survey_setup_path)

            survey_path = self.config.survey_path
            if not os.path.exists(survey_path):
                raise FileNotFoundError(f"Survey file not found: {survey_path}")

            with open(survey_path, "r") as survey_file:
                survey_choices = json.load(survey_file)

            return SurveyData(survey_setup, survey_choices)

        except Exception as e:
            log.error(f"Error loading data: {e}")
            capture_exception(e)
            raise
