import pandera as pa
from pandera.typing import Index, Series


class Coefficients(pa.DataFrameModel):
    index: Index[int]
    group: Series[str]
    varname: Series[str]
    coef: Series[float]
    err: Series[float]
    p_value: Series[float]
    significance: Series[str]


class Diagnostics(pa.DataFrameModel):
    index: Index[int]
    maximum_VIF_factor: Series[float]
    pearsons_normality: Series[float]
    pearsons_normality_p_value: Series[float]
    influential_points_to_outliers: Series[int]


class Parameters(pa.DataFrameModel):
    index: Index[int]
    Attributes: Series[str]
    varname: Series[str]
    coef: Series[str]
    err: Series[float]
    p_value: Series[float]
    significance: Series[str]


class OlsMetrics(pa.DataFrameModel):
    index: Index[int]
    r_squared: Series[float]
    adj_r_squared: Series[float]
    log_likelihood: Series[float]
    aic: Series[float]
    bic: Series[float]
    f_statistic_value: Series[float]
    residual_df: Series[int]
    model_df: Series[int]


class ClmMetrics(pa.DataFrameModel):
    index: Index[int]
    psuedo_r_squared: Series[float]
    psuedo_r_bar_squared: Series[float]
    log_likelihood: Series[float]
    aic: Series[float]
    bic: Series[float]
