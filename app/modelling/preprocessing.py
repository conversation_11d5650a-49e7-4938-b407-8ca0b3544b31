from abc import ABC, abstractmethod
from dataclasses import replace

import numpy as np
import pandas as pd

from app.modelling.data_objects import SurveyData


class PreprocessingStrategy(ABC):
    @abstractmethod
    def preprocess_data(self, survey_data: SurveyData):
        """Implement preprocessing of the data"""


class OlsPreprocessing(PreprocessingStrategy):
    def preprocess_data(self, survey_data: SurveyData) -> SurveyData:
        data = survey_data.survey_setup
        answer_num = data.shape[0]
        alternatives = [col[7] for col in data.columns[7:]]
        """Get number of alternatives and attributes"""
        alternative_num = len(np.unique(alternatives))
        attribute_num = int(len(alternatives) / alternative_num)

        # convert to long format
        attributes = data.iloc[:, 7:]
        attribute_dfs = list()
        rest_dfs = list()
        """Split data into alternative_num number of dataframes and concatenate them later"""

        for a in range(1, alternative_num + 1):
            attribute_dfs.append(attributes.iloc[:, :attribute_num].values)
            attributes = attributes.drop(
                columns=attributes.iloc[:, :attribute_num].columns
            )
            rest_dfs.append(data.iloc[:, :7])

        """Concatenate dataframes (Columns 7-n)"""
        rest_df = np.concatenate(rest_dfs, axis=0)
        attribute_df = np.concatenate(attribute_dfs, axis=0)
        attribute_df = pd.DataFrame(attribute_df, columns=survey_data.attribute_names)

        """Add rest of the columns to the dataframe (columns 0-5)"""
        rest_df = pd.DataFrame(rest_df, columns=data.iloc[:, :7].columns)
        data = pd.concat([rest_df, attribute_df], axis=1)

        choice = np.array(data.chosen_choice_letter.values)
        for i in range(answer_num):
            if choice[i] == "A":
                choice[i] = 1
                choice[i + answer_num] = 0
            if data.chosen_choice_letter[i] == "B":
                choice[i] = 0
                choice[i + answer_num] = 1
            if data.chosen_choice_letter[i] == "C":
                choice[i] = 0
                choice[i + answer_num] = 0

        data["chosen_choice_letter"] = choice

        for col in data.columns:
            if col == "persona_id":
                continue
            data[col] = pd.to_numeric(data[col])

        return replace(survey_data, survey_setup=data)


class ClmPreprocessing(PreprocessingStrategy):
    def preprocess_data(self, survey_data: SurveyData):
        data = survey_data.survey_setup
        data.rename(columns={"Unnamed: 0": "id"}, inplace=True)
        alternatives = [col[7] for col in data.columns[7:]]
        """Get number of alternatives and attributes"""
        alternative_num = len(np.unique(alternatives))
        attribute_num = int(len(alternatives) / alternative_num)

        # make choice column
        if len(np.unique(data["chosen_choice_letter"])) <= 1:
            possible_categories = list("ABC")
        else:
            possible_categories = list(np.unique(data["chosen_choice_letter"]))
        dtype = pd.CategoricalDtype(categories=possible_categories)
        choice_letters = pd.Series(list(data["chosen_choice_letter"]), dtype=dtype)
        choice_ = pd.get_dummies(choice_letters)
        choice_["id"] = range(1, len(choice_) + 1)
        choice_.set_index("id", inplace=True)
        choice = pd.concat([choice_[col] for col in choice_])
        choice = pd.DataFrame(choice)
        choice["alt_id"] = np.arange(choice.shape[0]) // choice_.shape[0] + 1
        choice["ids"] = choice.index
        choice = choice.sort_values(by=["ids", "alt_id"])
        choice = choice.set_axis(["choice", "alt_id", "ids"], axis="columns")
        choice = choice.reset_index()
        choice = choice.drop(["ids"], axis=1)

        # make attribute df
        attributes = data.iloc[:, 7:]
        dfs = list()
        for a in range(1, alternative_num + 1):
            # convert categories to dummies
            df = attributes.iloc[:, :attribute_num]
            df = df.values
            # add obs_id to attribute dfs
            df = np.insert(df, 0, np.arange(1, df.shape[0] + 1, 1, dtype=int), axis=1)
            # add alt_id to attribute dfs
            df = np.insert(df, 0, a * np.ones(df.shape[0]), axis=1)
            dfs.append(df)
            attributes = attributes.drop(
                columns=attributes.iloc[:, :attribute_num].columns
            )

        df = np.concatenate(dfs, axis=0)

        levels_ids_to_remove = []
        updated_level_ids = {}
        for i in range(2, df.shape[1]):
            level = np.unique(df[:, i])
            updated_level_ids[str(i - 2)] = []
            for id in survey_data.survey_choices["attributes_and_levels_lookup"][
                "get_level_ids"
            ][str(i - 2)]:
                if id not in level:
                    levels_ids_to_remove.append(id)
                else:
                    updated_level_ids[str(i - 2)].append(id)

        print("Removing Level IDs:", levels_ids_to_remove)

        # Remove first element
        first_items_rem_level_ids = {}
        for key, value in updated_level_ids.items():
            first_items_rem_level_ids[key] = value

        keys = list(first_items_rem_level_ids.values())
        keys = [str(item) for sublist in keys for item in sublist]
        attribute_levels_names = survey_data.get_attributes_by_keys(keys)

        df = pd.DataFrame(df)
        df = pd.get_dummies(df, columns=df.columns[2:])

        if len(np.unique(data.chosen_choice_letter)) != alternative_num:
            no_choice_df = pd.DataFrame({
                0: (alternative_num + 1) * np.ones(data.shape[0]),
                1: np.arange(1, data.shape[0] + 1, 1, dtype=int),
            })
            for col in df.columns[2:]:
                no_choice_df[col] = 0
            df = pd.concat([df, no_choice_df])
            df["None"] = np.where(df.iloc[:, 0] == (alternative_num + 1), 1, 0)

        df = df.sort_values(by=[1, 0])
        df = df.reset_index(drop=True)
        df = df.drop(columns=[0, 1])
        data = pd.concat([choice, df], axis=1)
        col_names = ["chid"] + ["choice"] + ["alt_id"] + attribute_levels_names
        if "None" in data.columns:
            data = data.drop("None", axis=1)
        data = data.set_axis(
            [col for col in col_names if col != "None"], axis="columns"
        )
        return replace(survey_data, survey_setup=data)
