from abc import ABC, abstractmethod

from app.modelling.data_objects import SurveyData
from app.modelling.metrics import (
    ClmCoefficients,
    ClmImportanceFrame,
    ClmModelSummary,
    Metric,
    OlsCoefficients,
    OlsDiagnostics,
    OlsModelSummary,
    OlsParameterSummary,
)


class MetricsStrategy(ABC):
    @abstractmethod
    def get_model_metrics(self, res, data: SurveyData) -> dict[str, Metric]:
        """Implements metrics"""


class OlsMetricsStrategy(MetricsStrategy):
    def get_model_metrics(self, res, data: SurveyData) -> dict[str, Metric]:
        metrics = {
            OlsCoefficients.name: OlsCoefficients(res, data),
            OlsDiagnostics.name: OlsDiagnostics(res, data),
            OlsParameterSummary.name: OlsParameterSummary(res, data),
            OlsModelSummary.name: OlsModelSummary(res, data),
        }
        return metrics


class ClmMetricsStrategy(MetricsStrategy):
    def get_model_metrics(self, res, data: SurveyData) -> dict[str, Metric]:
        clm_coefs = ClmCoefficients(res, data)
        importance_frame = ClmImportanceFrame(clm_coefs.metric, data)
        return {
            ClmCoefficients.name: clm_coefs,
            importance_frame.name: importance_frame,
            ClmModelSummary.name: ClmModelSummary(res, data),
        }
