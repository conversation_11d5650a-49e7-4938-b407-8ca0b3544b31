import math
import os
import textwrap
from abc import ABC, abstractmethod
from pathlib import Path

import matplotlib.cbook as cbook
import matplotlib.image as image
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from scipy import stats
from sklearn.metrics import classification_report
from statsmodels.graphics.gofplots import ProbPlot
from statsmodels.stats.outliers_influence import variance_inflation_factor
from statsmodels.tools.tools import maybe_unwrap_results

from app.core.utils.common import is_reference_level
from app.modelling.data_objects import SurveyData

PROJECT_PATH = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

plt.switch_backend("agg")


class Metric(ABC):
    def __init__(self, res, survey: SurveyData):
        self.metric = self._get_metric(res, survey)

    @abstractmethod
    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        """Implements metric"""

    @abstractmethod
    def plot(self, show: bool = False, path: Path = Path()):
        """Implements plotting of the metric"""

    @staticmethod
    def _wrap_text(element, width=50):
        # helper function to wrap labels for plots
        wrapped_text = "\n".join(textwrap.wrap(str(element), width))
        return wrapped_text

    def to_csv(self, path: Path):
        """Implements saving the metric to csv"""
        self.metric.to_csv(path)

    def to_json(self, path: Path):
        """Implements saving the metric to json"""
        self.metric.to_json(path)


class CoefficientMetric(Metric):
    MAX_VAR_CHARS = 30

    def __init__(self, res, survey: SurveyData):
        super(CoefficientMetric, self).__init__(res, survey)

    def optimize_amce_height(self, coef_df):
        """
        Calculate the height for the plot based on the number of rows that are created based on the
        varname coulmn. Each row should be multiplied by 0.3 height.
        """

        height = sum([len(x) // self.MAX_VAR_CHARS + 1 for x in coef_df.varname])
        return 20 + height * 0.3


class OlsCoefficients(CoefficientMetric):
    name = "ols_coefficients"

    def __init__(self, res, survey: SurveyData):
        super(OlsCoefficients, self).__init__(res, survey)
        self.prompt = survey.prompt

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        group_id = survey.attribute_ids
        group_dict = survey.attribute_text
        group_dict = dict(map(lambda kv: (int(kv[0]), kv[1]), group_dict.items()))
        groups_list = [group_dict[i] for i in group_id]

        err_series = res.params - res.conf_int()[0]
        keys = [x.split("_")[1] for x in res.params.index[1:]]
        levels = survey.get_attributes_by_keys(keys)
        coefs, errors, p_value = list(), list(), list()
        i = 0
        for param in survey.attributes_levels:
            if param in levels:
                coefs.append(res.params.values[1:][i])
                errors.append(err_series.values[1:][i])
                p_value.append(res.pvalues[1:][i])
                i += 1
            else:
                coefs.append(0)
                errors.append(0)
                p_value.append(np.nan)

        coef_df = pd.DataFrame({
            "group": groups_list,
            "varname": survey.attributes_levels,
            "coef": coefs,
            "err": errors,
            "p_value": p_value,
        })

        coef_df["significance"] = np.where(
            coef_df["p_value"] <= 0.001,
            "***",
            np.where(
                coef_df["p_value"] <= 0.01,
                "**",
                np.where(coef_df["p_value"] <= 0.05, "*", "n.s."),
            ),
        )
        coef_df.reset_index(inplace=True)

        coef_df["group"] = coef_df["group"].replace(
            to_replace=r"[_]", value="", regex=True
        )
        coef_df["group"] = coef_df["group"].astype("category")
        # Coefficients.validate(coef_df)
        return coef_df

    def plot(self, show: bool = False, path: Path = Path()):
        coef_df = self.metric
        unique_groups, ind = np.unique(coef_df.group, return_index=True)
        unique_groups = unique_groups[np.argsort(ind)]

        plt.style.context("fivethirtyeight")
        colors = plt.rcParams["axes.prop_cycle"].by_key()["color"]
        # makes the word wrapped labels have a smaller line space
        plt.rcParams["ytick.major.pad"] = 0.2
        # Creates a bar plot for each group
        label_width = math.floor(100 / len(unique_groups))
        wrapped_unique_groups = [
            self._wrap_text(l, width=label_width) for l in unique_groups
        ]
        g = None
        for i in range(coef_df.shape[0] + len(unique_groups)):
            if coef_df.loc[i].group != g and not is_reference_level(
                coef_df.loc[i].group
            ):
                line = pd.DataFrame(
                    {
                        "group": coef_df.loc[i].group,
                        "varname": "Attribute: " + coef_df.loc[i].group,
                        "coef": np.nan,
                        "err": np.nan,
                        "p_value": np.nan,
                        "significance": np.nan,
                    },
                    index=[i - 0.5],
                )
                coef_df = coef_df.append(line, ignore_index=False)
                coef_df = coef_df.sort_index().reset_index(drop=True)
            g = coef_df.loc[i].group

        coef_df["group"] = coef_df["group"].astype("category")
        coef_df = coef_df.iloc[::-1]
        coef_df = coef_df[~coef_df["varname"].apply(is_reference_level)]

        f, ax = plt.subplots(figsize=(15, self.optimize_amce_height(coef_df)))

        coef_df["varname"] = coef_df["varname"].apply(
            lambda x: self._wrap_text(x, self.MAX_VAR_CHARS)
        )
        coef_df.plot(
            x="varname",
            y="coef",
            kind="barh",
            ax=ax,
            color="none",
            xerr="err",
            legend=False,
            zorder=1,
        )

        ax.scatter(
            y=np.arange(coef_df.shape[0]),
            marker="o",
            s=100,
            x=coef_df["coef"],
            c=coef_df["group"].cat.codes,
            zorder=2,
        )

        ax.spines["top"].set_visible(False)
        ax.spines["right"].set_visible(False)
        ax.spines["bottom"].set_visible(False)
        ax.spines["left"].set_visible(False)
        # legend_title_left(legend)
        prompt = self._wrap_text(self.prompt, width=100)
        plt.title(
            "Cause and effect response for the question prompt: \n" + prompt,
            fontsize=20,
            wrap=True,
            fontweight="bold",
            y=1.05,
        )
        plt.xlabel(
            "Estimated Average Marginal Component Effects (AMCE)",
            fontsize=24,
            fontweight="bold",
        )
        with cbook.get_sample_data(PROJECT_PATH / Path("assets/watermark.png")) as file:
            im = image.imread(file)
        f.figimage(im, 0, 0, zorder=3, alpha=0.25)
        ax.set_facecolor("lightgrey")
        ax.grid(color="white")
        plt.axvline(x=0, linestyle="--")
        plt.tight_layout()
        plt.ylabel("Attributes and Levels", fontsize=24, fontweight="bold")
        plt.tick_params(axis="y", which="major", labelsize=18)
        xabs_max = abs(max(ax.get_xlim(), key=abs))
        ax.set_xlim(xmin=-xabs_max, xmax=xabs_max)
        caption = (
            f"Casual relationship estimates (x-axis) among the attribute levels"
            f" (y-axis) as estimated via the average component effect of each"
            f" attributes level"
        )
        caption = self._wrap_text(caption, 100)
        f.text(
            0.5, -0.05, caption, horizontalalignment="center", wrap=True, fontsize=24
        )

        gs = list(unique_groups)
        gs = [self._wrap_text("Attribute: " + x, self.MAX_VAR_CHARS) for x in gs]
        ax.set_yticklabels(list(coef_df["varname"]))
        for label in ax.get_yticklabels():
            if label.get_text() in gs:
                label.set_weight("bold")
        if path != Path():
            plt.savefig(path, bbox_inches="tight", dpi=60)
        if show:
            plt.show()
        plt.clf()
        plt.close()


class OlsDiagnostics(Metric):
    name = "ols_diagnostics_table"

    def __init__(self, res, survey: SurveyData):
        self.results = maybe_unwrap_results(res)
        super(OlsDiagnostics, self).__init__(res, survey)

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        # 1) Vif calculation
        vifs = [
            variance_inflation_factor(self.results.model.exog, i)
            for i in range(self.results.model.exog.shape[1])
        ]
        max_vif = max(vifs)

        # 2) Residual Normality test
        influence = self.results.get_influence()
        residual_norm = np.array(influence.resid_studentized_internal)
        # Null hypothesis: The input data are normaly distributed
        # (requires at least 8 observations)
        if res.params.shape[0] > 8:
            k2, p_norm = stats.normaltest(residual_norm)
            if p_norm < 1e-3:
                norm = False
            else:
                norm = True

            # 3) Compute the number of outliers using the leverage of the fitted model
            # Compute leverage values
            leverage = influence.hat_matrix_diag
            # Identify influential points based on leverage
            n = leverage.shape[0]
            influential_points = [
                i for i in range(n) if leverage[i] > 2 * (res.df_model + 1) / n
            ]
        else:
            norm = "Test not conducted due to low sample size"
            p_norm = np.nan
            influential_points = list()

        ols_diagnostics_summary = pd.DataFrame(
            {
                "maximum_VIF_factor": max_vif,
                "pearsons_normality": norm,
                "pearsons_normality_p_value": p_norm,
                "influential_points_to_outliers_ratio": len(influential_points),
            },
            index=[0],
        )
        return ols_diagnostics_summary

    def plot(self, show: bool = False, path: Path = Path()):
        with plt.style.context("seaborn-paper"):
            fig, ax = plt.subplots(nrows=2, ncols=2, figsize=(15, 15))
            self._plot_residual(ax=ax[0, 0])
            self._plot_qq(ax=ax[0, 1])
            self._plot_scale_location(ax=ax[1, 0])
            self._plot_leverage(ax=ax[1, 1])
            if path != Path():
                fig.savefig(path, dpi=60)
            if show:
                plt.show()
            plt.clf()
            plt.close()

    def _plot_residual(self, ax=None):
        """
        Residual vs Fitted Plot

        Graphical tool to identify non-linearity.
        (Roughly) Horizontal red line is an indicator that the residual has a linear
        pattern
        """
        residual = np.array(self.results.resid)
        predicted = self.results.fittedvalues
        if ax is None:
            fig, ax = plt.subplots()

        sns.residplot(
            x=predicted,
            y=residual,
            lowess=True,
            scatter_kws={"alpha": 0.5},
            line_kws={"color": "red", "lw": 1, "alpha": 0.8},
            ax=ax,
        )

        # annotations
        abs_resid = np.flip(np.sort(np.abs(residual)))
        for i, _ in enumerate(abs_resid[:3]):
            ax.annotate(i, xy=(predicted[i], residual[i]), color="C3")

        ax.set_title("Residuals vs Fitted", fontweight="bold")
        ax.set_xlabel("Fitted values")
        ax.set_ylabel("Residuals")
        return ax

    def _plot_qq(self, ax=None):
        """
        Standarized Residual vs Theoretical Quantile plot

        Used to visually check if residuals are normally distributed.
        Points spread along the diagonal line will suggest so.
        """
        if ax is None:
            fig, ax = plt.subplots()

        influence = self.results.get_influence()
        residual_norm = influence.resid_studentized_internal

        qq = ProbPlot(residual_norm)
        qq.qqplot(line="45", alpha=0.5, lw=1, ax=ax)

        # annotations
        abs_norm_resid = np.flip(np.argsort(np.abs(residual_norm)), 0)
        abs_norm_resid_top_3 = abs_norm_resid[:3]
        for r, i in enumerate(abs_norm_resid_top_3):
            ax.annotate(
                i,
                xy=(np.flip(qq.theoretical_quantiles, 0)[r], residual_norm[i]),
                ha="right",
                color="C3",
            )

        ax.set_title("Normal Q-Q", fontweight="bold")
        ax.set_xlabel("Theoretical Quantiles")
        ax.set_ylabel("Standardized Residuals")
        return ax

    def _plot_scale_location(self, ax=None):
        """
        Sqrt(Standarized Residual) vs Fitted values plot

        Used to check homoscedasticity of the residuals.
        Horizontal line will suggest so.
        """
        if ax is None:
            fig, ax = plt.subplots()

        influence = self.results.get_influence()
        residual_norm = influence.resid_studentized_internal
        residual_norm_abs_sqrt = np.sqrt(np.abs(residual_norm))

        ax.scatter(self.results.fittedvalues, residual_norm_abs_sqrt, alpha=0.5)
        sns.regplot(
            x=self.results.fittedvalues,
            y=residual_norm_abs_sqrt,
            scatter=False,
            ci=False,
            lowess=True,
            line_kws={"color": "red", "lw": 1, "alpha": 0.8},
            ax=ax,
        )

        # annotations
        abs_sq_norm_resid = np.flip(np.argsort(residual_norm_abs_sqrt), 0)
        abs_sq_norm_resid_top_3 = abs_sq_norm_resid[:3]
        for i in abs_sq_norm_resid_top_3:
            ax.annotate(
                i,
                xy=(self.results.fittedvalues[i], residual_norm_abs_sqrt[i]),
                color="C3",
            )
        ax.set_title("Scale-Location", fontweight="bold")
        ax.set_xlabel("Fitted values")
        ax.set_ylabel(r"$\sqrt{|\mathrm{Standardized\ Residuals}|}$")
        return ax

    def _plot_leverage(self, ax=None):
        """
        Residual vs Leverage plot

        Points falling outside Cook's distance curves are considered observation that
        can sway the fit
        aka are influential.
        Good to have none outside the curves.
        """
        if ax is None:
            fig, ax = plt.subplots()

        influence = self.results.get_influence()
        residual_norm = influence.resid_studentized_internal
        leverage = influence.hat_matrix_diag
        cooks_distance = influence.cooks_distance[0]
        nparams = len(self.results.params)

        ax.scatter(leverage, residual_norm, alpha=0.5)

        sns.regplot(
            x=leverage,
            y=residual_norm,
            scatter=False,
            ci=False,
            lowess=True,
            line_kws={"color": "red", "lw": 1, "alpha": 0.8},
            ax=ax,
        )

        # annotations
        leverage_top_3 = np.flip(np.argsort(cooks_distance), 0)[:3]
        for i in leverage_top_3:
            ax.annotate(i, xy=(leverage[i], residual_norm[i]), color="C3")

        xtemp, ytemp = self._cooks_dist_line(nparams, leverage, 0.5)  # 0.5 line
        ax.plot(xtemp, ytemp, label="Cook's distance", lw=1, ls="--", color="red")
        xtemp, ytemp = self._cooks_dist_line(nparams, leverage, 1)  # 1 line
        ax.plot(xtemp, ytemp, lw=1, ls="--", color="red")

        ax.set_xlim(0, max(leverage) + 0.01)
        ax.set_title("Residuals vs Leverage", fontweight="bold")
        ax.set_xlabel("Leverage")
        ax.set_ylabel("Standardized Residuals")
        ax.legend(loc="upper right")
        return ax

    @staticmethod
    def _cooks_dist_line(nparms, leverage, factor):
        """
        Helper function for plotting Cook's distance curves
        """
        x = np.linspace(0.001, max(leverage), 50)
        y = np.sqrt((factor * nparms * (1 - x)) / x)
        return x, y


class OlsParameterSummary(Metric):
    name = "ols_parameter_summary"

    def __init__(self, res, survey: SurveyData):
        super(OlsParameterSummary, self).__init__(res, survey)
        self.prompt = survey.prompt

    @property
    def ols_num_variables(self):
        return self.metric.shape[0]

    @property
    def ols_num_significant_variables(self):
        return self.metric.loc[self.metric["significance"] != "n.s."].shape[0]

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        keys = [x.split("_")[1] for x in res.params.index[1:]]
        levels = ["intercept"] + survey.get_attributes_by_keys(keys)
        err_series = res.params - res.conf_int()[0]
        # make regression parameter output table
        parameter_summary_table = pd.DataFrame({
            "varname": levels,
            "coef": res.params.values,
            "err": err_series.values,
            "p_value": res.pvalues,
        })

        """Add significance"""
        parameter_summary_table["significance"] = np.where(
            parameter_summary_table["p_value"] <= 0.001,
            "***",
            np.where(
                parameter_summary_table["p_value"] <= 0.01,
                "**",
                np.where(parameter_summary_table["p_value"] <= 0.05, "*", "n.s."),
            ),
        )
        parameter_summary_table.reset_index(inplace=True)
        parameter_summary_table = parameter_summary_table.rename(
            columns={"index": "Attributes"}
        )
        return parameter_summary_table

    def plot(self, show: bool = False, path: Path = Path()):
        # make dataframe for calculating importance
        parameter_summary_table_ = self.metric.iloc[1:, :]
        parameter_summary_table_["Attributes"] = [
            x.rsplit("_", 1)[0] for x in parameter_summary_table_["Attributes"]
        ]
        #    "Attributes"
        # ].replace(to_replace=r"[_]", value="", regex=True)
        max_part_worth = parameter_summary_table_.groupby(["Attributes"]).max()
        min_part_worth = parameter_summary_table_.groupby(["Attributes"]).min()
        importance = max_part_worth.coef - min_part_worth.coef
        importance_err = np.sqrt(
            np.square(max_part_worth.err) + np.square(min_part_worth.err)
        )
        importance_frame = pd.DataFrame(
            {"importance": importance, "importance_err": importance_err}
        )
        importance_frame = importance_frame.sort_values(by=["importance"])
        importance_frame = importance_frame.fillna(0)
        importance_frame.index = [
            self._wrap_text(x, 30) for x in importance_frame.index
        ]

        f, ax = plt.subplots(figsize=(20, 10))

        with cbook.get_sample_data(PROJECT_PATH / Path("assets/watermark.png")) as file:
            im = image.imread(file)
        f.figimage(im, 0, 0, zorder=3, alpha=0.25)

        self.prompt = self._wrap_text(self.prompt, width=100)
        plt.title(
            f"Attribute Importance for prompt:\n {self.prompt}",
            fontsize=25,
            fontweight="bold",
            wrap=True,
            y=1.05,
        )

        pwu = importance_frame.importance
        xbar = np.arange(len(pwu))
        plt.barh(
            xbar,
            pwu,
            xerr=importance_frame.importance_err,
            color=np.random.rand(importance_frame.shape[0], 3),
        )
        plt.yticks(xbar, labels=importance_frame.index, fontsize=18)
        plt.xlim(left=0)
        plt.xlabel("Importance", fontsize=20, fontweight="bold")
        ax.set_facecolor("lightgrey")
        plt.grid(color="white")
        caption = "Importance (x-axis) of each attribute (y-axis)"
        caption = self._wrap_text(caption, 150)
        f.text(
            0.5, -0.05, caption, horizontalalignment="center", wrap=True, fontsize=16
        )
        f.tight_layout()
        if path != Path():
            plt.savefig(path, bbox_inches="tight", dpi=60)
        if show:
            plt.show()
        plt.clf()
        plt.close()


class OlsModelSummary(Metric):
    name = "model_summary"

    def __init__(self, res, survey: SurveyData):
        super(OlsModelSummary, self).__init__(res, survey)

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        model_results_table = pd.DataFrame(
            {
                "r_squared": res.rsquared,
                "adj_r_squared": res.rsquared_adj,
                "log_likelihood": res.llf,
                "aic": res.aic,
                "bic": res.bic,
                "f_statistic_value": res.fvalue,
                "residual_df": res.df_resid,
                "model_df": res.df_model,
            },
            index=[0],
        )
        return model_results_table

    def plot(self, show: bool = False, path: Path = Path()):
        print(f"Plotting not implemented for {self.name}")


class ClmCoefficients(CoefficientMetric):
    name = "clm_coefficients"

    def __init__(self, res, survey: SurveyData):
        super(ClmCoefficients, self).__init__(res, survey)
        self.prompt = survey.prompt

    @property
    def clm_num_significant_variables(self):
        return self.metric.loc[self.metric["significance"] != "n.s."].shape[0]

    @property
    def clm_num_variables(self):
        return self.metric.shape[0]

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        group_id = survey.attribute_ids
        group_dict = survey.attribute_text
        group_dict = dict(map(lambda kv: (int(kv[0]), kv[1]), group_dict.items()))
        groups_list = [group_dict[i] for i in group_id]

        full_param_name = survey.attributes_levels
        coef_df = pd.DataFrame({
            "coef": res.params.values,
            "err": res.summary.std_err.values,
            "varname": res.params.index,
            "pvalue": res.pvalues,
        })

        coefs, errors, p_value = list(), list(), list()
        x, y = 0, 0
        while len(coefs) < len(full_param_name):
            if full_param_name[x] == coef_df.varname[y]:
                coefs.append(coef_df.coef[y])
                errors.append(coef_df.err[y])
                p_value.append(coef_df.pvalue[y])
                x += 1
                y += 1
            else:
                coefs.append(0)
                errors.append(0)
                p_value.append(np.nan)
                x += 1

        coef_df = pd.DataFrame({
            "group": groups_list,
            "varname": full_param_name,
            "coef": coefs,
            "err": errors,
            "p_value": p_value,
        })

        coef_df["group"] = coef_df["group"].replace(
            to_replace=r"[_]", value="", regex=True
        )
        coef_df["group"] = coef_df["group"].astype("category")
        # sort by order in experiment definition
        sorter = list(survey.attribute_text.values())
        coef_df["group"] = coef_df["group"].cat.set_categories(sorter)

        coef_df["significance"] = np.where(
            coef_df["p_value"] <= 0.001,
            "***",
            np.where(
                coef_df["p_value"] <= 0.01,
                "**",
                np.where(coef_df["p_value"] <= 0.05, "*", "n.s."),
            ),
        )
        return coef_df

    def plot(self, show: bool = False, path: Path = Path()):
        coef_df = self.metric
        unique_groups, ind = np.unique(coef_df.group, return_index=True)
        unique_groups = unique_groups[np.argsort(ind)]

        plt.style.context("fivethirtyeight")
        # makes the word wrapped labels have a smaller line space

        plt.rcParams["ytick.major.pad"] = 0.2
        # Creates a bar plot for each group

        g = None
        for i in range(coef_df.shape[0] + len(unique_groups)):
            if coef_df.loc[i].group != g:
                line = pd.DataFrame(
                    {
                        "group": coef_df.loc[i].group,
                        "varname": "Attribute: " + coef_df.loc[i].group,
                        "coef": np.nan,
                        "err": np.nan,
                        "p_value": np.nan,
                        "significance": np.nan,
                    },
                    index=[i - 0.5],
                )
                coef_df = coef_df.append(line, ignore_index=False)
                coef_df = coef_df.sort_index().reset_index(drop=True)
            g = coef_df.loc[i].group

        coef_df["group"] = coef_df["group"].astype("category")
        coef_df = coef_df.iloc[::-1]
        coef_df = coef_df[~coef_df["varname"].apply(is_reference_level)]

        # compute maximum string length in attribute levels
        f, ax = plt.subplots(figsize=(15, self.optimize_amce_height(coef_df)))

        # Add a column 'group' to the dataframe
        coef_df["varname"] = coef_df["varname"].apply(
            lambda x: self._wrap_text(x, self.MAX_VAR_CHARS)
        )
        coef_df.plot(
            x="varname",
            y="coef",
            kind="barh",
            ax=ax,
            color="none",
            xerr="err",
            legend=False,
            zorder=1,
        )
        # Pass the 'group' column to the color parameter
        ax.scatter(
            y=np.arange(coef_df.shape[0]),
            marker="o",
            s=100,
            x=coef_df["coef"],
            c=coef_df["group"].cat.codes,
            zorder=2,
        )
        ax.spines["top"].set_visible(False)
        ax.spines["right"].set_visible(False)
        ax.spines["bottom"].set_visible(False)
        ax.spines["left"].set_visible(False)

        # legend_title_left(legend)
        prompt = self._wrap_text(self.prompt, width=100)
        plt.title(
            "Cause and effect response for the question prompt: \n" + prompt,
            fontsize=20,
            wrap=True,
            fontweight="bold",
            y=1.01,
        )
        plt.xlabel(
            "Estimated Average Marginal Component Effects (AMCE)",
            fontsize=24,
            fontweight="bold",
        )
        with cbook.get_sample_data(PROJECT_PATH / Path("assets/watermark.png")) as file:
            im = image.imread(file)
        f.figimage(im, 0, 0, zorder=3, alpha=0.25)
        ax.set_facecolor("lightgrey")
        ax.grid(color="white")
        plt.axvline(x=0, linestyle="--")
        plt.tight_layout()
        plt.ylabel("Attributes and Levels", fontsize=24, fontweight="bold")
        plt.tick_params(axis="y", which="major", labelsize=18)
        xabs_max = abs(max(ax.get_xlim(), key=abs))
        ax.set_xlim(xmin=-xabs_max, xmax=xabs_max)
        caption = (
            f"Casual relationship estimates (x-axis) among the attribute levels"
            f" (y-axis) as estimated via the average component effect of each"
            f" attributes level"
        )
        caption = self._wrap_text(caption, 100)
        f.text(
            0.5, -0.05, caption, horizontalalignment="center", wrap=True, fontsize=24
        )

        gs = list(unique_groups)
        gs = [self._wrap_text("Attribute: " + x, self.MAX_VAR_CHARS) for x in gs]
        ax.set_yticklabels(list(coef_df["varname"]))
        for label in ax.get_yticklabels():
            if label.get_text() in gs:
                label.set_weight("bold")

        if path != Path():
            plt.savefig(path, bbox_inches="tight")
        if show:
            plt.show()
        plt.clf()
        plt.close()


class ClmImportanceFrame(Metric):
    name = "clm_importance_frame"

    def __init__(self, res, survey: SurveyData):
        super(ClmImportanceFrame, self).__init__(res, survey)
        self.prompt = survey.prompt

    def _get_metric(self, coef_df, survey: SurveyData) -> pd.DataFrame:
        df_res = pd.DataFrame({
            "Attributes": coef_df.group,
            "param_name": coef_df.varname,
            "coef": coef_df.coef,
            "stderr": coef_df.err,
        })
        max_part_worth = df_res.groupby(["Attributes"]).max()
        min_part_worth = df_res.groupby(["Attributes"]).min()
        importance = max_part_worth.coef - min_part_worth.coef
        importance_err = np.sqrt(
            np.square(max_part_worth.stderr) + np.square(min_part_worth.stderr)
        )
        importance_frame = pd.DataFrame(
            {"importance": importance, "importance_err": importance_err}
        )
        importance_frame = importance_frame.sort_values(by=["importance"])
        importance_frame.index = [
            self._wrap_text(x, 30) for x in importance_frame.index
        ]

        return importance_frame

    def plot(self, show: bool = False, path: Path = Path()):
        importance_frame = self.metric
        f, ax = plt.subplots(figsize=(20, 10))

        with cbook.get_sample_data(PROJECT_PATH / Path("assets/watermark.png")) as file:
            im = image.imread(file)
        f.figimage(im, 0, 0, zorder=3, alpha=0.25)

        prompt = self._wrap_text(self.prompt, width=100)
        plt.title(
            f"Attribute Importance for prompt:\n {prompt}",
            fontsize=30,
            fontweight="bold",
            wrap=True,
            y=1.05,
        )

        pwu = importance_frame.importance
        xbar = np.arange(len(pwu))
        plt.barh(
            xbar,
            pwu,
            xerr=importance_frame.importance_err,
            color=np.random.rand(importance_frame.shape[0], 3),
        )
        plt.yticks(xbar, labels=importance_frame.index, fontsize=24)
        plt.xlim(left=0)
        plt.xlabel("Importance", fontsize=20, fontweight="bold")
        ax.set_facecolor("lightgrey")
        plt.grid(color="white")
        caption = f"Importance (x-axis) of each attribute (y-axis)"
        caption = self._wrap_text(caption, 150)
        f.text(
            0.5, -0.05, caption, horizontalalignment="center", wrap=True, fontsize=24
        )
        f.tight_layout()
        if path != Path():
            plt.savefig(path, bbox_inches="tight", dpi=60)
        if show:
            plt.show()
        plt.clf()
        plt.close()


class ClmModelSummary(Metric):
    name = "model_summary"

    def __init__(self, res, survey: SurveyData):
        super(ClmModelSummary, self).__init__(res, survey)
        self.survey = survey
        self.res = res

    @property
    def f1_score(self):
        data = self.survey.survey_setup.copy()
        data["preds"] = self.res.predict(data)
        for i in range(0, data.shape[0], 3):
            pred_choice = np.where(
                np.array(data.loc[i : i + 2, "preds"])
                >= max(data.loc[i : i + 2, "preds"]),
                int(1),
                int(0),
            )
            data.loc[i : i + 2, "pred_choice"] = pred_choice
        data["pred_choice"] = data["pred_choice"].astype("int64")
        class_report = classification_report(
            data.choice, data.pred_choice, output_dict=True
        )
        class_report_t = pd.DataFrame(class_report).transpose()
        return class_report_t["f1-score"].mean()

    def _get_metric(self, res, survey: SurveyData) -> pd.DataFrame:
        return pd.DataFrame(
            {
                "psuedo_r_squared": res.rho_squared,
                "psuedo_r_bar_squared": res.rho_bar_squared,
                "log_likelihood": res.llf,
                "aic": res.aic,
                "bic": res.bic,
            },
            index=[0],
        )

    def plot(self, show: bool = False, path: Path = Path()):
        print(f"Plotting not implemented for {self.name}")
