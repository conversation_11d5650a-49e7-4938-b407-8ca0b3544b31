from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

# --- Models for Chat ---


class ChatRequest(BaseModel):
    """
    Request model for the chat endpoint.
    Requires the experiment run ID and a list of messages.
    """

    run_id: str
    messages: List[Dict[str, Any]]  # e.g., [{"role": "user", "content": "Hello"}]


class ChatResponse(BaseModel):
    """
    Response model for the chat endpoint.
    Indicates success and returns the assistant's message or an error.
    """

    success: bool
    message: Optional[Dict[str, Any]] = (
        None  # e.g., {"role": "assistant", "content": "..."}
    )
    error: Optional[str] = None


# --- Models for Artifacts, Processing, Generation, Reports ---
# Based on the structure in interactive_chat_agent.py


class ArtifactData(BaseModel):
    """Data structure holding fetched artifact details and content."""

    success: bool
    experiment_definition_file: str
    analytics_output_file: str
    experiment_definition_data: Dict[str, Any]
    analytics_output_data: Optional[Dict[str, Any]] = None  # Can be None if not found


class ExtractedExperimentDetails(BaseModel):
    """Structured details extracted from the experiment definition."""

    title: str = "Not available"
    context: str = "Not available"
    target_behavior: str = "Not available"
    why_prompt: str = "Not available"
    country: str = "Not available"
    state: str = ""
    year: str = "Not available"
    respondent_dependent_variable: str = "Not available"


class AmceFeature(BaseModel):
    """Represents a single feature's AMCE value."""

    attribute: str
    level: str
    amce: float


class ExtractedAmceData(BaseModel):
    """Structured AMCE data extracted from analytics output."""

    DependentVariable: str
    Features: List[AmceFeature]


class ExtractedMindsetData(BaseModel):
    """Structured Mindset data extracted from analytics output."""

    mindset: str
    attribute_text: str
    level_text: str
    row: int
    AMCE: float = Field(alias="AMCE")  # Handle potential case sensitivity if needed
    lower_bound: float
    upper_bound: float
    desc: str


class CausalSentence(BaseModel):
    """A single generated causal sentence."""

    sentence: str


class CausalMindsetSentence(BaseModel):
    """A generated causal explanation linked to a specific mindset."""

    mindset: str
    attribute_text: str
    level_text: str
    causal_explanation: str


class TextReportResponse(BaseModel):
    """Response model for the text report generation."""

    success: bool
    format: str = "text"
    run_id: str
    report_text: str


class SummaryResponse(BaseModel):
    """Response model for the generated summary."""

    summary: str


# Note: PDF reports are typically returned as FileResponse, not a Pydantic model.
