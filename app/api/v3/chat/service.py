import asyncio
import json
import logging
import os
import re
import shutil
from datetime import datetime
from typing import Any, Dict, List, Optional

import aiohttp

# Add these environment variable defaults
os.environ["API_USERNAME"] = os.getenv("API_USERNAME", "<EMAIL>")
os.environ["API_PASSWORD"] = os.getenv("API_PASSWORD", "wd8BPYz9e2kPnn")
os.environ["CLIENT_ID"] = os.getenv("CLIENT_ID", "MR5gS0QSe3boMNAtnR0t1t2ctNpzSHsd")
os.environ["CLIENT_SECRET"] = os.getenv(
    "CLIENT_SECRET", "c1GGYa9U7gbX5dvDR8pRHvmmY067InwLi0HYZMNXzKg9zn99RvNf13ibsDzT2jKV"
)
os.environ["AUTH_URL"] = os.getenv("AUTH_URL", "https://auth.subconscious.ai")
os.environ["AUDIENCE"] = os.getenv(
    "AUDIENCE", "https://dev-5qhuxyzkmd8cku6i.us.auth0.com/api/v2/"
)
os.environ["API_BASE_URL"] = os.getenv(
    "API_BASE_URL", "https://api.dev.subconscious.ai"
)


# Third-party libraries (Ensure these are in your main pyproject.toml)
import google.generativeai as genai
from cachetools import TTLCache
from fastapi import HTTPException
from openai import AsyncOpenAI, OpenAIError
from pydantic import BaseModel  # Needed for internal models
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer

# Local imports
# Import all models needed by the service layer functions now
from .models import (
    AmceFeature,
    CausalMindsetSentence,
    CausalSentence,
    ExtractedAmceData,
    ExtractedExperimentDetails,
    ExtractedMindsetData,
)

# --- Configuration & Initialization ---

logger = logging.getLogger(__name__)

# TODO: Refactor LLM client initialization to use centralized configuration
# For now, replicate the setup from interactive_chat_agent.py
openai_client: Optional[AsyncOpenAI] = None


def get_openai_client():
    global openai_client
    if openai_client is None:
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key:
            openai_client = AsyncOpenAI(api_key=api_key)
            logger.info("OpenAI client initialized for chat service.")
        else:
            logger.warning(
                "OPENAI_API_KEY not set. OpenAI functionality will be unavailable in"
                " chat service."
            )
    return openai_client


# Configure Gemini API
try:
    gemini_api_key = os.getenv("GOOGLE_API_KEY")
    if gemini_api_key:
        genai.configure(api_key=gemini_api_key)
        logger.info("Google Gemini API configured for chat service.")
    else:
        logger.warning(
            "GOOGLE_API_KEY not set. Gemini functionality will be unavailable."
        )
        genai = None  # Indicate Gemini is unavailable
except Exception as e:
    logging.warning(f"Could not configure Google Gemini API: {e}")
    genai = None

# TODO: Refactor Auth token logic to use centralized app.auth module
# Replicating cache and token fetching for now
token_cache = TTLCache(maxsize=1, ttl=3540)  # ~59 min cache


# WARNING: Avoid direct requests usage in services if possible. Prefer a dedicated HTTP client utility.
# This function is temporarily copied for functionality.
def get_access_token() -> str:
    """Get Auth0 access token with caching (Temporary - Should be in app.auth)"""
    if "token" in token_cache:
        return token_cache["token"]
    try:
        # Use environment variables directly - TODO: Use centralized config (e.g., settings object)
        auth_url = os.getenv("AUTH_URL")
        api_username = os.getenv("API_USERNAME")
        api_password = os.getenv("API_PASSWORD")
        audience = os.getenv("AUDIENCE")
        client_id = os.getenv("CLIENT_ID")
        client_secret = os.getenv("CLIENT_SECRET")

        if not all(
            [auth_url, api_username, api_password, audience, client_id, client_secret]
        ):
            logger.error(
                "Missing required Auth0 environment variables for token retrieval."
            )
            raise HTTPException(
                status_code=500,
                detail="Server configuration error: Auth credentials missing.",
            )

        import requests  # Temporary import - avoid in async services

        response = requests.post(
            url=f"{auth_url}/oauth/token",
            data={
                "grant_type": "password",
                "username": api_username,
                "password": api_password,
                "audience": audience,
                "client_id": client_id,
                "client_secret": client_secret,
            },
            headers={"content-type": "application/x-www-form-urlencoded"},
        )
        response.raise_for_status()
        token = response.json().get("access_token")
        if not token:
            raise ValueError("Access token not found in response.")
        token_cache["token"] = token
        logger.info("Successfully obtained new Auth0 token (Chat Service).")
        return token
    except requests.exceptions.RequestException as e:
        logger.error(
            "Auth HTTP error (Chat Service):"
            f" {e.response.text if e.response else str(e)}"
        )
        # Use FastAPI's HTTPException
        raise HTTPException(
            status_code=503, detail=f"Authentication service error: {e}"
        )
    except Exception as e:
        logger.error(f"Auth unexpected error (Chat Service): {str(e)}")
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")


# --- API Data Fetching (Temporary - Should be in a core client/utility) ---


async def fetch_api_data(
    session: aiohttp.ClientSession, url: str, token: str
) -> Dict[str, Any]:
    """Generic function to fetch data from the Subconscious API."""
    logger.debug(f"Fetching URL (Chat Service): {url}")
    try:
        async with session.get(
            url, headers={"Authorization": f"Bearer {token}"}
        ) as response:
            response_text = await response.text(encoding="utf-8-sig")  # Handle BOM
            logger.debug(f"Response status: {response.status}")
            if response.status != 200:
                logger.error(
                    f"API error fetching {url}: {response.status}, {response_text}"
                )
                raise HTTPException(
                    status_code=response.status,
                    detail=(
                        f"API error ({response.status}) fetching {url}: {response_text}"
                    ),
                )
            if not response_text:
                logger.warning(f"Received empty response from {url}")
                return {}
            return json.loads(response_text)
    except aiohttp.ClientError as e:
        logger.error(f"Network error fetching {url}: {str(e)}")
        raise HTTPException(
            status_code=503, detail=f"Network error accessing API: {str(e)}"
        )
    except json.JSONDecodeError as e:
        logger.error(
            f"JSON decode error for {url}: {str(e)}. Response text:"
            f" {response_text[:500]}"
        )
        raise HTTPException(
            status_code=500, detail=f"Failed to parse API response as JSON from {url}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error fetching {url}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error retrieving data from {url}: {str(e)}",
        )


async def fetch_run_details(run_id: str, token: str) -> Dict[str, Any]:
    """Retrieve run details from the API using the run_id"""
    # TODO: Use centralized config for API_BASE_URL
    api_base_url = os.getenv("API_BASE_URL", "https://api.dev.subconscious.ai")
    url = f"{api_base_url}/api/v1/runs/{run_id}"
    async with aiohttp.ClientSession() as session:
        return await fetch_api_data(session, url, token)


async def fetch_artifact(file_name: str, token: str) -> Dict[str, Any]:
    """Retrieve artifact content from the API and return as JSON"""
    # TODO: Use centralized config for API_BASE_URL
    api_base_url = os.getenv("API_BASE_URL", "https://api.dev.subconscious.ai")
    url = f"{api_base_url}/api/v1/runs/artifact/{file_name}"
    async with aiohttp.ClientSession() as session:
        return await fetch_api_data(session, url, token)


# Internal ArtifactData structure (not exposed via API model here)
class _ArtifactDataInternal(BaseModel):
    success: bool
    experiment_definition_file: str
    analytics_output_file: str
    experiment_definition_data: Dict[str, Any]
    analytics_output_data: Optional[Dict[str, Any]]


async def _get_experiment_artifacts_internal(
    run_id: str, token: str
) -> _ArtifactDataInternal:
    """Internal logic to fetch and structure main artifacts."""
    logger.info(f"Fetching artifacts for run_id (Chat Service): {run_id}")
    run_details = await fetch_run_details(run_id, token)

    try:
        artifacts_list = (
            run_details.get("run_details", {}).get("configs", {}).get("artifacts", [])
        )
        experiment_def_file = None
        for artifact_entry in artifacts_list:
            if (
                isinstance(artifact_entry, dict)
                and "experiment_definition" in artifact_entry
            ):
                experiment_def_file = artifact_entry["experiment_definition"]
                break

        if not experiment_def_file:
            raise HTTPException(
                status_code=404,
                detail=(
                    "Experiment definition file key not found in run details artifacts."
                ),
            )

        experiment_definition_data = await fetch_artifact(experiment_def_file, token)

        run_name = run_id
        if experiment_def_file.startswith("experiment_definition_"):
            run_name = experiment_def_file[len("experiment_definition_") :]
        analytics_output_file = f"Analytics_output_{run_name}"

        analytics_output_data = None
        try:
            analytics_output_data = await fetch_artifact(analytics_output_file, token)
        except HTTPException as ex:
            if ex.status_code == 404:
                logger.warning(
                    f"Analytics output file not found: {analytics_output_file}."
                    " Proceeding without it."
                )
                analytics_output_data = {
                    "error": f"Analytics output file not found: {analytics_output_file}"
                }
            else:
                raise

        return _ArtifactDataInternal(
            success=True,
            experiment_definition_file=experiment_def_file,
            analytics_output_file=analytics_output_file,
            experiment_definition_data=experiment_definition_data,
            analytics_output_data=analytics_output_data,
        )
    except HTTPException:
        raise
    except KeyError as e:
        logger.error(f"Missing key in run details structure for run {run_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Invalid run details structure: missing key {e}"
        )
    except Exception as e:
        logger.error(f"Error processing artifacts for run {run_id}: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error processing artifact details: {str(e)}"
        )


# --- Data Processing Functions (Specific to Experiment Structure) ---
# These functions are tightly coupled to the expected artifact structure.
# Consider moving to a dedicated processing module if reused elsewhere.

# Use the models imported from models.py directly now
# No need for internal underscore versions anymore


def extract_experiment_details(data: Dict) -> ExtractedExperimentDetails:
    """Extract key experimental design details."""
    if not data:
        return ExtractedExperimentDetails()  # Return default values
    # Map directly to the imported model
    return ExtractedExperimentDetails(
        title=data.get("title", "Not available"),
        context=data.get("context", "Not available"),
        target_behavior=data.get("target_behavior", "Not available"),
        why_prompt=data.get("why_prompt", "Not available"),
        country=data.get("country", "Not available"),
        state=data.get("state", ""),
        year=data.get("year", "Not available"),
        respondent_dependent_variable=data.get(
            "respondent_dependent_variable", "Not available"
        ),
    )


def extract_amce_data(data: Optional[Dict]) -> Optional[ExtractedAmceData]:
    """Extract AMCE-related insights."""
    if not data or "Ind_Est_2" not in data:
        return None
    try:
        utility_calc = (
            data["Ind_Est_2"]
            .get("error_handling_calc", {})
            .get("Utility_Estimation_calc", {})
        )
        if not utility_calc:
            return None
        amce_list = utility_calc.get("AMCE_Data", [])
        dependent_var = utility_calc.get(
            "dependent_variable", "Unknown Dependent Variable"
        )
        if isinstance(dependent_var, list):
            dependent_var = (
                dependent_var[0] if dependent_var else "Unknown Dependent Variable"
            )
        if not amce_list or not isinstance(amce_list, list):
            return None

        features = []
        for feat in amce_list:
            if not isinstance(feat, dict):
                continue
            try:
                # Map directly to the imported AmceFeature model
                features.append(
                    AmceFeature(
                        attribute=str(feat.get("attribute_text", "")).strip(),
                        level=str(feat.get("level_text", "")).strip(),
                        amce=float(feat.get("AMCE", 0.0)),
                    )
                )
            except (ValueError, TypeError) as e:
                logger.warning(
                    f"Skipping invalid AMCE feature data: {feat}, error: {e}"
                )
                continue

        # Map directly to the imported ExtractedAmceData model
        return (
            ExtractedAmceData(DependentVariable=str(dependent_var), Features=features)
            if features
            else None
        )
    except Exception as e:
        logger.error(f"Error extracting AMCE data: {e}")
        return None


def extract_mindset_data(data: Optional[Dict]) -> List[ExtractedMindsetData]:
    """Extract mindset partworth data."""
    extracted_items = []
    if not data or "Ind_Est_2" not in data:
        return extracted_items
    try:
        mindset_partworth = data["Ind_Est_2"].get("mindset_partworth", [])
        if not isinstance(mindset_partworth, list):
            return extracted_items

        for item_group in mindset_partworth:
            items_to_process = (
                item_group if isinstance(item_group, list) else [item_group]
            )
            for item in items_to_process:
                if not isinstance(item, dict):
                    continue
                try:
                    # Map directly to the imported ExtractedMindsetData model
                    extracted_items.append(
                        ExtractedMindsetData(
                            mindset=str(item.get("mindset", "")).strip(),
                            attribute_text=str(item.get("attribute_text", "")).strip(),
                            level_text=str(item.get("level_text", "")).strip(),
                            row=int(item.get("row", 0)),
                            AMCE=float(
                                item.get("AMCE", 0.0)
                            ),  # Use alias if needed via Field
                            lower_bound=float(item.get("lower_bound", 0.0)),
                            upper_bound=float(item.get("upper_bound", 0.0)),
                            desc=str(item.get("desc", "")),
                        )
                    )
                except (ValueError, TypeError) as e:
                    logger.warning(
                        f"Skipping invalid mindset data item: {item}, error: {e}"
                    )
                    continue
        return extracted_items
    except Exception as e:
        logger.error(f"Error extracting mindset data: {str(e)}")
        return []


# --- NLP Generation Functions ---


async def generate_with_llm(prompt: str) -> str:
    """Generate text using available LLMs (Gemini preferred, OpenAI fallback)."""
    # TODO: Refactor to use centralized LLM execution logic (e.g., from app.llm_prompt)
    # Try Gemini first
    if genai:
        try:
            # Use a known stable model or the latest recommended one
            model = genai.GenerativeModel(
                "gemini-1.5-flash"
            )  # Simplified config for now
            response = await model.generate_content_async(prompt)
            if response and response.text:
                logger.info("Generated content using Gemini (Chat Service).")
                return response.text
            else:
                logger.warning("Gemini response was empty (Chat Service).")
        except Exception as e:
            logger.warning(
                f"Gemini generation failed (Chat Service): {e}. Trying OpenAI fallback."
            )

    # Fallback to OpenAI
    client = get_openai_client()
    if client:
        try:
            response = await client.chat.completions.create(
                model="gpt-4-turbo",  # Consider making model configurable
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an AI assistant analyzing experimental data."
                        ),
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.7,
                max_tokens=1024,
            )
            if response and response.choices and response.choices[0].message:
                logger.info("Generated content using OpenAI (Chat Service).")
                return (
                    response.choices[0].message.content
                    or "OpenAI returned empty content."
                )
            else:
                logger.warning(
                    "OpenAI response structure invalid or empty (Chat Service)."
                )
                return "Error: OpenAI returned an unusable response."
        except OpenAIError as openai_error:  # Catch specific OpenAI errors
            logger.error(f"OpenAI API error during chat generation: {openai_error}")
            return f"Error: OpenAI API request failed: {openai_error}"
        except Exception as openai_error:  # Catch other potential errors
            logger.error(
                f"OpenAI fallback unexpected error (Chat Service): {openai_error}"
            )
            return f"Error: OpenAI generation failed: {openai_error}"
    else:
        logger.error(
            "LLM generation failed: Neither Gemini nor OpenAI configured/available"
            " (Chat Service)."
        )
        # Raise an exception or return a clear error message
        raise HTTPException(
            status_code=503, detail="Language model service unavailable."
        )


async def generate_experiment_summary(exp_details: ExtractedExperimentDetails) -> str:
    """Generate a description of the experimental study using an LLM."""
    if not exp_details or exp_details.title == "Not available":
        return "No experiment details available to generate summary."
    prompt = f"""
    Generate a concise summary paragraph for the following experimental study:

    Title: {exp_details.title}
    Context: {exp_details.context}
    Target Behavior Measured: {exp_details.target_behavior}
    Reasoning ("Why"): {exp_details.why_prompt}
    Location: {exp_details.country}{f', {exp_details.state}' if exp_details.state else ''}
    Year: {exp_details.year}
    Primary Outcome Variable: {exp_details.respondent_dependent_variable}

    Focus on the study's objective, what was tested, and the primary outcome measured. Keep it brief (1-2 sentences).
    """
    return await generate_with_llm(prompt)


async def generate_single_causal_sentence(
    feature: AmceFeature, dependent_var: str
) -> str:
    """Generate a single clean causal impact sentence for a feature."""
    try:
        amce_pct = round(feature.amce * 100, 1)
        sign = "+" if amce_pct > 0 else ""
        formatted_amce = f"{sign}{amce_pct}%"
        attr = feature.attribute
        lvl = feature.level
        dep_var_short = "preference"  # Simplified term

        if abs(amce_pct) < 0.1:  # Threshold for "no significant impact"
            return (
                f"Showing '{lvl}' for the '{attr}' attribute had no significant impact"
                f" on {dep_var_short} (Effect: {formatted_amce})."
            )
        elif amce_pct > 0:
            return (
                f"Showing '{lvl}' for the '{attr}' attribute *increased*"
                f" {dep_var_short} by {formatted_amce}."
            )
        else:
            return (
                f"Showing '{lvl}' for the '{attr}' attribute *decreased*"
                f" {dep_var_short} by {formatted_amce}."
            )
    except Exception as e:
        logger.error(f"Error generating sentence for feature {feature}: {e}")
        return f"Error generating sentence for attribute '{feature.attribute}'."


async def generate_bulk_causal_sentences(
    amce_data: Optional[ExtractedAmceData],
) -> List[CausalSentence]:
    """Generate clean causal impact sentences for all features."""
    if not amce_data or not amce_data.Features:
        return [
            CausalSentence(
                sentence="No AMCE data available to generate causal sentences."
            )
        ]

    dependent_var = amce_data.DependentVariable
    tasks = [
        generate_single_causal_sentence(feature, dependent_var)
        for feature in amce_data.Features
    ]
    results = await asyncio.gather(*tasks)
    # Use the imported CausalSentence model
    return [CausalSentence(sentence=s) for s in results]


async def generate_single_mindset_sentence(data_point: ExtractedMindsetData) -> str:
    """Generate clean causal mindset sentence."""
    try:
        mindset = data_point.mindset
        attribute = data_point.attribute_text
        level = data_point.level_text
        amce_pct = round(data_point.AMCE * 100, 1)
        sign = "+" if amce_pct > 0 else ""
        formatted_amce = f"{sign}{amce_pct}%"
        dep_var_short = "preference"  # Simplified term

        if abs(amce_pct) < 0.1:
            return (
                f"For consumers with the '{mindset}' mindset, showing '{level}' for"
                f" '{attribute}' had no significant impact on {dep_var_short} (Effect:"
                f" {formatted_amce})."
            )
        elif amce_pct > 0:
            return (
                f"For consumers with the '{mindset}' mindset, showing '{level}' for"
                f" '{attribute}' *increased* {dep_var_short} by {formatted_amce}."
            )
        else:
            return (
                f"For consumers with the '{mindset}' mindset, showing '{level}' for"
                f" '{attribute}' *decreased* {dep_var_short} by {formatted_amce}."
            )
    except Exception as e:
        logger.error(f"Error generating mindset sentence for {data_point}: {e}")
        return (
            f"Error generating explanation for mindset '{data_point.mindset}',"
            f" attribute '{data_point.attribute_text}'."
        )


async def generate_bulk_mindset_sentences(
    mindset_data: List[ExtractedMindsetData],
) -> List[CausalMindsetSentence]:
    """Generate causal explanations for mindset data."""
    if not mindset_data:
        return []  # Return empty list, not a sentence saying none available

    tasks = [generate_single_mindset_sentence(item) for item in mindset_data]
    generated_sentences = await asyncio.gather(*tasks)

    # Combine original data with generated sentence using imported CausalMindsetSentence model
    results = []
    for i, item in enumerate(mindset_data):
        results.append(
            CausalMindsetSentence(
                mindset=item.mindset,
                attribute_text=item.attribute_text,
                level_text=item.level_text,
                causal_explanation=generated_sentences[i],
            )
        )
    return results


# --- Full Report Generation Functions (Adapted from original script) ---


def create_text_report(
    summary: str,
    causal_sentences: List[CausalSentence],
    mindset_explanations: List[CausalMindsetSentence],
    experiment_data: Optional[Dict] = None,
    analytics_data: Optional[Dict] = None,  # Keep for potential future use
) -> str:
    """Create a comprehensive text report (Markdown format)."""
    logger.info("Creating enhanced text report")
    text_report = []
    current_time = datetime.now().strftime("%B %d, %Y %H:%M")
    text_report.append(f"# CAUSAL INSIGHTS REPORT\n*Generated on {current_time}*\n")

    if experiment_data and "title" in experiment_data:
        experiment_title = experiment_data["title"].replace("_", " ").title()
        text_report.append(f"## Experiment: {experiment_title}\n")

    text_report.append("## Executive Summary\n")
    text_report.append(summary if summary else "*No summary generated.*")
    text_report.append("\n\n")

    if experiment_data:
        text_report.append("## Experiment Design Details\n")
        details = extract_experiment_details(experiment_data)  # Use extracted details
        text_report.append(f"**Context:** {details.context}\n")
        text_report.append(f"**Target Behavior:** {details.target_behavior}\n")
        text_report.append(
            f"**Primary Outcome Measure:** {details.respondent_dependent_variable}\n"
        )
        location = f"{details.country}{', '+details.state if details.state else ''}"
        text_report.append(f"**Location:** {location}\n")
        if details.year != "Not available":
            text_report.append(f"**Year:** {details.year}\n")

        attributes_lookup = experiment_data.get(
            "pre_cooked_attributes_and_levels_lookup"
        )
        if attributes_lookup and isinstance(attributes_lookup, list):
            text_report.append("\n### Tested Attributes and Levels\n")
            for attr_entry in attributes_lookup:
                if isinstance(attr_entry, list) and len(attr_entry) >= 2:
                    attr_name, attr_levels = attr_entry[0], attr_entry[1]
                    if isinstance(attr_levels, list):
                        text_report.append(f"**{attr_name}:**\n")
                        for level in attr_levels:
                            text_report.append(f"- {level}\n")
                        text_report.append("\n")
        text_report.append("\n")

    text_report.append("## Key Causal Insights (AMCE)\n")
    effect_data = []  # For sorting and recommendations
    if (
        causal_sentences
        and "No AMCE data available" not in causal_sentences[0].sentence
    ):
        significant_findings = []
        insignificant_findings = []
        for cs in causal_sentences:
            sentence = cs.sentence
            pct_match = re.search(r"by ([+-]?\d+\.?\d*%)", sentence)
            no_impact_match = re.search(
                r"no significant impact", sentence, re.IGNORECASE
            )
            if no_impact_match:
                insignificant_findings.append(sentence)
            elif pct_match:
                significant_findings.append(sentence)
                try:
                    pct_num = float(pct_match.group(1).strip("%"))
                    attr_match = re.search(
                        r"Showing '([^']+)' for the '([^']+)' attribute", sentence
                    )
                    if attr_match:
                        effect_data.append({
                            "percentage": pct_num,
                            "attribute": attr_match.group(2),
                            "level": attr_match.group(1),
                            "sentence": sentence,
                        })
                except Exception as e:
                    logger.error(
                        f"Error parsing impact data from sentence '{sentence}': {e}"
                    )
            else:
                significant_findings.append(sentence)  # Unquantified significant

        if effect_data:
            effect_data.sort(key=lambda x: abs(x["percentage"]), reverse=True)
            text_report.append("### Top Findings (by impact magnitude)\n")
            text_report.append(
                "| Attribute | Level | Impact |\n|-----------|-------|--------|\n"
            )
            for effect in effect_data[:5]:
                sign_disp = "+" if effect["percentage"] > 0 else ""
                text_report.append(
                    f"| {effect['attribute']} | {effect['level']} |"
                    f" {sign_disp}{effect['percentage']:.1f}% |\n"
                )
            text_report.append("\n")

        if significant_findings:
            text_report.append("### Significant Effects\n")
            for sentence in significant_findings:
                pct_match = re.search(r"([+-]?\d+\.?\d*%)", sentence)
                highlighted = (
                    sentence.replace(pct_match.group(1), f"**{pct_match.group(1)}**")
                    if pct_match
                    else sentence
                )
                text_report.append(f"- {highlighted}\n")
            text_report.append("\n")
        if insignificant_findings:
            text_report.append("### Non-Significant Effects\n")
            for sentence in insignificant_findings:
                text_report.append(f"- {sentence}\n")
            text_report.append("\n")
    else:
        text_report.append(
            "*No causal insights (AMCE) were found or could be generated.*\n\n"
        )

    if mindset_explanations:
        text_report.append("## Consumer Mindset Insights\n")
        mindsets = {}
        for item in mindset_explanations:
            mindset = item.mindset
            if mindset not in mindsets:
                mindsets[mindset] = []
            mindsets[mindset].append(item.causal_explanation)
        for mindset, explanations in mindsets.items():
            text_report.append(f"### Mindset: {mindset}\n")
            for explanation in explanations:
                text_report.append(f"- {explanation}\n")
            text_report.append("\n")
    else:
        text_report.append(
            "## Consumer Mindset Insights\n*No mindset insights found.*\n\n"
        )

    text_report.append("## Recommendations\n")
    if effect_data:
        text_report.append(
            "Based on the causal insights from this experiment, consider the"
            " following:\n"
        )
        recs = []
        positive_effects = sorted(
            [e for e in effect_data if e["percentage"] > 0],
            key=lambda x: x["percentage"],
            reverse=True,
        )
        negative_effects = sorted(
            [e for e in effect_data if e["percentage"] < 0],
            key=lambda x: x["percentage"],
        )
        if positive_effects:
            recs.append(
                f"**Leverage '{positive_effects[0]['level']}' for"
                f" '{positive_effects[0]['attribute']}':** Positive impact of"
                f" **+{positive_effects[0]['percentage']:.1f}%**."
            )
        if negative_effects:
            recs.append(
                f"**Re-evaluate '{negative_effects[0]['level']}' for"
                f" '{negative_effects[0]['attribute']}':** Negative impact of"
                f" **{negative_effects[0]['percentage']:.1f}%**."
            )
        if mindset_explanations:
            recs.append(
                "**Consider Consumer Segmentation:** Responses varied across mindsets."
            )
        if not recs:
            recs.append("*No strong positive or negative effects identified.*")
        for i, r in enumerate(recs, 1):
            text_report.append(f"{i}. {r}\n")
    else:
        text_report.append(
            "*No clear quantitative effects identified for specific recommendations.*\n"
        )

    text_report.append("\n## Next Steps\n")
    text_report.append(
        "1. Use the chat feature for follow-up questions.\n2. Request further analysis"
        " if needed.\n3. Discuss implementation strategies.\n"
    )
    return "\n".join(text_report)


def create_pdf_report(
    summary: str,
    causal_sentences: List[CausalSentence],
    mindset_explanations: List[CausalMindsetSentence],
    output_file: str,
    experiment_title: Optional[str] = "Experiment Report",
) -> None:
    """Create a PDF report."""
    logger.info(f"Creating PDF report: {output_file}")
    try:
        doc = SimpleDocTemplate(
            output_file,
            pagesize=letter,
            rightMargin=0.75 * inch,
            leftMargin=0.75 * inch,
            topMargin=0.75 * inch,
            bottomMargin=0.75 * inch,
        )
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            "CustomTitle", parent=styles["h1"], alignment=1, fontSize=16, spaceAfter=14
        )
        subtitle_style = ParagraphStyle(
            "CustomSubtitle",
            parent=styles["h2"],
            fontSize=13,
            spaceAfter=8,
            spaceBefore=10,
            textColor="#34495E",
        )
        normal_style = ParagraphStyle(
            "CustomNormal",
            parent=styles["Normal"],
            fontSize=10,
            leading=14,
            spaceAfter=6,
        )
        list_style = ParagraphStyle(
            "CustomListItem",
            parent=styles["Normal"],
            fontSize=10,
            leading=14,
            leftIndent=20,
            firstLineIndent=-10,
            spaceBefore=3,
            spaceAfter=3,
        )
        mindset_title_style = ParagraphStyle(
            "MindsetTitle",
            parent=styles["h3"],
            fontSize=11,
            spaceBefore=8,
            spaceAfter=4,
            textColor="#16A085",
        )
        content = []

        content.append(
            Paragraph(experiment_title.replace("_", " ").title(), title_style)
        )
        content.append(Spacer(1, 0.1 * inch))
        content.append(
            Paragraph(
                f"<i>Report generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}</i>",
                styles["Italic"],
            )
        )
        content.append(Spacer(1, 0.2 * inch))

        content.append(Paragraph("Executive Summary", subtitle_style))
        summary_text = summary if summary else "No summary available."
        for paragraph_text in summary_text.split("\n"):
            if paragraph_text.strip():
                content.append(Paragraph(paragraph_text.strip(), normal_style))
        content.append(Spacer(1, 0.2 * inch))

        content.append(Paragraph("Key Causal Insights (AMCE)", subtitle_style))
        if (
            causal_sentences
            and "No AMCE data available" not in causal_sentences[0].sentence
        ):
            sig_sentences = [
                s.sentence
                for s in causal_sentences
                if "no significant impact" not in s.sentence.lower()
            ]
            non_sig_sentences = [
                s.sentence
                for s in causal_sentences
                if "no significant impact" in s.sentence.lower()
            ]
            if sig_sentences:
                content.append(
                    Paragraph(
                        "<u>Significant Effects:</u>",
                        ParagraphStyle(
                            name="sig_label", parent=normal_style, spaceBefore=6
                        ),
                    )
                )
                for idx, sentence in enumerate(sig_sentences, 1):
                    content.append(Paragraph(f"{idx}. {sentence}", list_style))
                content.append(Spacer(1, 0.1 * inch))
            if non_sig_sentences:
                content.append(
                    Paragraph(
                        "<u>Non-Significant Effects:</u>",
                        ParagraphStyle(
                            name="nonsig_label", parent=normal_style, spaceBefore=6
                        ),
                    )
                )
                for idx, sentence in enumerate(non_sig_sentences, 1):
                    content.append(Paragraph(f"{idx}. {sentence}", list_style))
        else:
            content.append(
                Paragraph("No AMCE data available or processed.", normal_style)
            )
        content.append(Spacer(1, 0.2 * inch))

        content.append(Paragraph("Consumer Mindset Insights", subtitle_style))
        if mindset_explanations:
            mindsets_dict = {}
            for item in mindset_explanations:
                if item.mindset not in mindsets_dict:
                    mindsets_dict[item.mindset] = []
                mindsets_dict[item.mindset].append(item.causal_explanation)
            if mindsets_dict:
                for mindset, explanations in mindsets_dict.items():
                    content.append(
                        Paragraph(f"Mindset: {mindset}", mindset_title_style)
                    )
                    for idx, explanation in enumerate(explanations, 1):
                        content.append(Paragraph(f"- {explanation}", list_style))
                    content.append(Spacer(1, 0.1 * inch))
            else:
                content.append(Paragraph("No mindset data processed.", normal_style))
        else:
            content.append(
                Paragraph("No mindset data available or processed.", normal_style)
            )

        doc.build(content)
        logger.info(f"PDF report created successfully: {output_file}")
    except Exception as e:
        logger.error(
            f"Failed to create PDF report {output_file}: {str(e)}", exc_info=True
        )
        raise RuntimeError(f"PDF Generation Error: {str(e)}")


# --- Background Task for Temp Directory Cleanup ---
def cleanup_temp_dir(temp_dir: str):
    """Safely remove a temporary directory."""
    # This function is needed by the report endpoint
    try:
        if os.path.exists(temp_dir):  # Check if dir exists before removing
            shutil.rmtree(temp_dir)
            logger.info(f"Successfully cleaned up temporary directory: {temp_dir}")
        else:
            logger.warning(
                f"Attempted to clean up non-existent temp directory: {temp_dir}"
            )
    except Exception as e:
        logger.error(f"Error cleaning up temporary directory {temp_dir}: {e}")


# --- Core Chat Processing Logic ---


async def process_experiment_chat(
    run_id: str, user_message: str, previous_messages: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Handles the core logic for chat interaction regarding a specific experiment run.
    Fetches data, generates initial context if needed, and calls LLM.
    """
    logger.info(f"Processing chat message for run ID (Service): {run_id}")
    # Get token internally - raises HTTPException on failure
    token = get_access_token()

    # Step 1: Fetch experiment artifacts
    try:
        artifacts = await _get_experiment_artifacts_internal(run_id, token)
        if not artifacts.success:
            # This case might be handled by exceptions in _get_experiment_artifacts_internal
            logger.error(f"Artifact fetching reported failure for run {run_id}")
            raise HTTPException(
                status_code=500, detail="Failed to retrieve artifact data."
            )
    except HTTPException as he:
        # Pass through HTTP exceptions from fetching
        logger.error(
            f"HTTP Error fetching artifacts for chat context (run {run_id}):"
            f" {he.detail}"
        )
        # Return a user-friendly error message in the chat response format
        return {
            "role": "assistant",
            "content": (
                "Sorry, I couldn't retrieve the experiment data needed (Error:"
                f" {he.status_code}). Please check the run ID or try again later."
            ),
        }
    except Exception as e:
        logger.error(
            f"Unexpected error fetching artifacts for chat context (run {run_id}):"
            f" {str(e)}",
            exc_info=True,
        )
        return {
            "role": "assistant",
            "content": (
                "Sorry, an unexpected error occurred while retrieving experiment data."
            ),
        }

    experiment_data = artifacts.experiment_definition_data
    analytics_data = artifacts.analytics_output_data

    # Step 2: Check for/Generate initial report context for the LLM prompt
    initial_report_text = ""
    # Simple check: if history is empty or first message isn't assistant, generate context.
    # This context is *only* for the LLM prompt, not returned directly unless an error occurs.
    if not previous_messages or previous_messages[0].get("role") != "assistant":
        logger.info(f"Generating initial context report for LLM prompt (run {run_id}).")
        try:
            # Generate components async needed for the context report
            exp_details_ctx = extract_experiment_details(experiment_data)
            summary_ctx_task = asyncio.create_task(
                generate_experiment_summary(exp_details_ctx)
            )
            amce_data_ctx = extract_amce_data(analytics_data)
            causal_sentences_ctx_task = asyncio.create_task(
                generate_bulk_causal_sentences(amce_data_ctx)
            )
            mindset_data_ctx = extract_mindset_data(analytics_data)
            mindset_explanations_ctx_task = asyncio.create_task(
                generate_bulk_mindset_sentences(mindset_data_ctx)
            )

            summary_ctx, causal_sentences_ctx, mindset_explanations_ctx = (
                await asyncio.gather(
                    summary_ctx_task,
                    causal_sentences_ctx_task,
                    mindset_explanations_ctx_task,
                )
            )
            # Use the full text report function now available
            initial_report_text = create_text_report(
                summary_ctx,
                causal_sentences_ctx,
                mindset_explanations_ctx,
                experiment_data,
            )
            logger.info(
                f"Successfully generated context report for LLM prompt (run {run_id})."
            )

        except Exception as e:
            logger.error(
                f"Error generating context report for LLM prompt (run {run_id}):"
                f" {str(e)}",
                exc_info=True,
            )
            initial_report_text = (
                "Error: Failed to generate the analysis context for this experiment."
            )
    else:
        # If history exists and starts with assistant, assume it's the context report.
        initial_report_text = previous_messages[0].get("content", "")
        logger.debug(
            f"Using existing context from chat history for LLM prompt (run {run_id})."
        )

    # Step 3: Build the detailed prompt for the LLM
    def build_chat_prompt(
        user_msg: str, history: list, context_report: str, exp_data: dict
    ) -> str:
        title = exp_data.get("title", "Unknown Title").replace("_", " ")
        # Use only the *actual* conversation history, excluding the prepended context report
        actual_history = [
            msg for msg in history if msg.get("content") != context_report
        ]
        history_limit = 6  # Limit history context sent to LLM
        formatted_history = "\n".join([
            f"{msg.get('role', '').upper()}: {msg.get('content', '')}"
            for msg in actual_history[-history_limit:]
        ])

        prompt = f"""You are an AI assistant analyzing results from a causal experiment.
        Answer the user's query based *only* on the provided context report and conversation history.
        Be concise. If the information isn't present in the report, state that clearly. Do not make up information.

        EXPERIMENT TITLE: {title}

        CAUSAL INSIGHTS REPORT CONTEXT:
        --- START REPORT ---
        {context_report}
        --- END REPORT ---

        CONVERSATION HISTORY (recent):
        --- START HISTORY ---
        {formatted_history}
        --- END HISTORY ---

        CURRENT USER QUERY: {user_msg}

        YOUR CONCISE RESPONSE:"""
        return prompt

    # Prepare history for prompt generation - include context if generated/found
    history_for_prompt = previous_messages
    if initial_report_text and (
        not previous_messages
        or previous_messages[0].get("content") != initial_report_text
    ):
        # If context was generated and not already the first message, prepend it for the prompt
        history_for_prompt = [
            {"role": "assistant", "content": initial_report_text}
        ] + previous_messages

    final_prompt = build_chat_prompt(
        user_message, history_for_prompt, initial_report_text, experiment_data
    )

    # Step 4: Call LLM to generate the response
    try:
        logger.info(f"Sending prompt to LLM for chat (run {run_id})...")
        ai_response_text = await generate_with_llm(final_prompt)
        logger.info(f"Received LLM response for chat (run {run_id}).")
    except HTTPException as he:
        # Handle specific errors from generate_with_llm (like 503 Service Unavailable)
        logger.error(
            f"LLM generation failed during chat processing (run {run_id}): {he.detail}"
        )
        ai_response_text = (
            "I encountered an error while trying to process your request with the"
            f" language model ({he.detail}). Please try again later."
        )
    except Exception as e:
        logger.error(
            f"Unexpected error during LLM generation for chat (run {run_id}): {str(e)}",
            exc_info=True,
        )
        ai_response_text = (
            "I encountered an unexpected error while trying to process your request."
            " Please try again."
        )

    # Step 5: Return the assistant's message (and potentially the context if it was generated)
    # The endpoint will handle the final ChatResponse structure.
    # This service function returns the core result.
    response_content = {"role": "assistant", "content": ai_response_text}

    # Optionally, return the generated context if it was created in this call
    # This allows the endpoint to decide if it needs to update the client-side history
    # if 'initial_report_text_generated' in locals() and initial_report_text_generated:
    #     response_content["generated_context"] = initial_report_text

    return response_content
