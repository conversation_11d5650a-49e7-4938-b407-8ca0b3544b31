import asyncio  # Add this import
import logging
import os
import tempfile
from typing import Any, Dict, List, Optional

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Body,
    Depends,
    HTTPException,
    Path,
    Query,
)
from fastapi.responses import FileResponse, JSONResponse

# Local imports
from .models import (
    ArtifactData,
    CausalMindsetSentence,
    CausalSentence,
    ChatRequest,
    ChatResponse,
    ExtractedAmceData,
    ExtractedExperimentDetails,
    ExtractedMindsetData,
    SummaryResponse,
    TextReportResponse,
)
from .service import _ArtifactDataInternal  # Internal model for dependency type hint
from .service import (
    _get_experiment_artifacts_internal,  # Internal function for dependency
)
from .service import get_access_token  # Assuming temporary location in service.py
from .service import (
    cleanup_temp_dir,
    create_pdf_report,
    create_text_report,
    extract_amce_data,
    extract_experiment_details,
    extract_mindset_data,
    fetch_artifact,
    fetch_run_details,
    generate_bulk_causal_sentences,
    generate_bulk_mindset_sentences,
    generate_experiment_summary,
    process_experiment_chat,
)

logger = logging.getLogger(__name__)

# Define router - all v3 endpoints related to chat/insights will go here
router = APIRouter(
    # Prefixing at the main router level (api/router.py) with /v3
    # prefix="/v3", # No prefix here, handled in api/router.py
    # No default tags here, assign per endpoint
)

# --- Dependencies ---
# TODO: Refactor auth dependency to use centralized app.auth module


async def get_valid_token(token: str = Depends(get_access_token)):
    """FastAPI dependency to get a valid token via the service function."""
    # The get_access_token in service.py already handles caching and errors (raising HTTPException)
    if not token:
        # This case should ideally be handled by get_access_token raising HTTPException
        logger.error("Failed to retrieve a valid token (dependency).")
        raise HTTPException(status_code=401, detail="Authentication failed")
    return token


async def get_run_artifacts_data(
    run_id: str = Path(
        ..., description="The unique identifier for the experiment run."
    ),
    token: str = Depends(get_valid_token),
) -> _ArtifactDataInternal:
    """
    Dependency to fetch core experiment/analytics artifacts using the service function.
    Used by endpoints that need this data to avoid redundant fetches.
    Returns the internal data structure used by the service layer.
    """
    # Calls the internal service function which handles fetching and structuring
    # It also raises HTTPException on errors during fetching/processing artifacts
    return await _get_experiment_artifacts_internal(run_id, token)


# --- Chat Endpoint ---


@router.post(
    "/chat",
    response_model=ChatResponse,
    summary="Process Chat Message",
    tags=["v3.chat"],  # Use new tag format
    description="""
    Accepts a user message and conversation history for a specific experiment run ID.
    Retrieves relevant experiment data, generates context if necessary (using internal report generation),
    and uses an LLM to provide a contextual response based on the experiment's findings.
    """,
)
async def handle_chat_message(request: ChatRequest = Body(...)):
    """Handles incoming chat requests."""
    logger.info(f"Received request for /v3/chat for run_id: {request.run_id}")

    # Basic validation (already present)
    if not request.run_id or not request.messages:
        raise HTTPException(status_code=400, detail="Missing 'run_id' or 'messages'.")
    if request.messages[-1].get("role") != "user":
        raise HTTPException(status_code=400, detail="Last message must be from 'user'.")

    try:
        assistant_message_content = await process_experiment_chat(
            run_id=request.run_id,
            user_message=request.messages[-1]["content"],
            previous_messages=request.messages[:-1],
        )
        # Service function now returns the assistant message dict directly or raises HTTPException
        return ChatResponse(success=True, message=assistant_message_content)

    except HTTPException as he:
        logger.warning(
            f"HTTPException during chat processing for run {request.run_id}:"
            f" {he.status_code} - {he.detail}"
        )
        return ChatResponse(
            success=False, error=f"Error ({he.status_code}): {he.detail}"
        )
    except Exception as e:
        logger.error(
            f"Unexpected error processing chat request for run {request.run_id}:"
            f" {str(e)}",
            exc_info=True,
        )
        return ChatResponse(
            success=False, error=f"An internal server error occurred: {str(e)}"
        )


# --- Artifact & Run Detail Endpoints ---


@router.get(
    "/runs/{run_id}/details",
    response_model=Dict[str, Any],  # Assuming raw details are returned as dict
    summary="Get Run Details",
    tags=["v3.runs"],  # Use new tag format
    description="Fetch the detailed configuration and metadata for a specific run.",
)
async def get_run_details_endpoint(
    run_id: str = Path(
        ..., description="The unique identifier for the experiment run."
    ),
    token: str = Depends(get_valid_token),
):
    logger.info(f"Endpoint requested details for run_id: {run_id}")
    # Directly call the service function, which handles potential HTTPExceptions
    details = await fetch_run_details(run_id, token)
    return JSONResponse(content=details)  # Return as JSON


@router.get(
    "/artifacts/{file_name}",
    response_model=Dict[str, Any],  # Assuming artifact content is JSON
    summary="Get Single Artifact",
    tags=["v3.artifacts"],  # Use new tag format
    description="Fetch the content of a specific raw artifact file by its name.",
)
async def get_single_artifact_endpoint(
    file_name: str = Path(
        ..., description="The exact name of the artifact file to fetch."
    ),
    token: str = Depends(get_valid_token),
):
    logger.info(f"Endpoint requested artifact: {file_name}")
    try:
        if "/" in file_name or "\\" in file_name or ".." in file_name:
            raise HTTPException(
                status_code=400, detail="Invalid characters in file name."
            )
        # Call service function, handles HTTPExceptions
        artifact_data = await fetch_artifact(file_name, token)
        return JSONResponse(content=artifact_data)
    except HTTPException as he:
        raise he  # Re-raise known HTTP errors
    except Exception as e:
        logger.error(
            f"Unexpected error in get_single_artifact endpoint for {file_name}:"
            f" {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred fetching artifact {file_name}.",
        )


@router.get(
    "/runs/{run_id}/artifacts",
    response_model=ArtifactData,  # Use the Pydantic model defined in models.py
    summary="Get Primary Artifacts",
    tags=["v3.runs"],  # Use new tag format (related to runs)
    description=(
        "Fetch the primary experiment definition and analytics output artifacts for a"
        " run."
    ),
)
async def get_experiment_main_artifacts_endpoint(
    # Use the dependency to fetch data; it returns the internal service model
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint returning artifacts for run_id associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    # Convert the internal service data model to the public API response model
    # This assumes the fields match directly or requires manual mapping if they differ
    return ArtifactData(
        success=artifacts_internal_data.success,
        experiment_definition_file=artifacts_internal_data.experiment_definition_file,
        analytics_output_file=artifacts_internal_data.analytics_output_file,
        experiment_definition_data=artifacts_internal_data.experiment_definition_data,
        analytics_output_data=artifacts_internal_data.analytics_output_data,
    )


# --- Processing Endpoints ---


@router.get(
    "/runs/{run_id}/processed/details",
    response_model=ExtractedExperimentDetails,
    summary="Get Processed Experiment Details",
    tags=["v3.processing"],  # Use new tag format
    description=(
        "Extract and return key details from the experiment definition artifact."
    ),
)
async def get_processed_experiment_details_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint processing experiment details for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if not artifacts_internal_data.experiment_definition_data:
        raise HTTPException(
            status_code=404, detail="Experiment definition data not found or empty."
        )
    # Call the service function for extraction
    return extract_experiment_details(
        artifacts_internal_data.experiment_definition_data
    )


@router.get(
    "/runs/{run_id}/processed/amce",
    response_model=Optional[ExtractedAmceData],  # Response can be null if no data
    summary="Get Processed AMCE Data",
    tags=["v3.processing"],  # Use new tag format
    description=(
        "Extract and return structured AMCE data from the analytics output artifact."
    ),
)
async def get_processed_amce_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint processing AMCE data for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if (
        not artifacts_internal_data.analytics_output_data
        or artifacts_internal_data.analytics_output_data.get("error")
    ):
        logger.warning(f"Cannot process AMCE: Analytics data unavailable for run.")
        return None  # Return null JSON response

    # Call the service function for extraction
    extracted_data = extract_amce_data(artifacts_internal_data.analytics_output_data)
    return extracted_data  # Return the extracted data or None


@router.get(
    "/runs/{run_id}/processed/mindset",
    response_model=List[ExtractedMindsetData],  # Response is a list (can be empty)
    summary="Get Processed Mindset Data",
    tags=["v3.processing"],  # Use new tag format
    description=(
        "Extract and return structured Mindset data from the analytics output artifact."
    ),
)
async def get_processed_mindset_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint processing Mindset data for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if (
        not artifacts_internal_data.analytics_output_data
        or artifacts_internal_data.analytics_output_data.get("error")
    ):
        logger.warning(f"Cannot process Mindset: Analytics data unavailable for run.")
        return []  # Return empty list

    # Call the service function for extraction
    extracted_data = extract_mindset_data(artifacts_internal_data.analytics_output_data)
    return extracted_data  # Return the list (possibly empty)


# --- Generation Endpoints ---


@router.post(
    "/runs/{run_id}/generate/summary",
    response_model=SummaryResponse,  # Use the specific response model
    summary="Generate Experiment Summary",
    tags=["v3.generation"],  # Use new tag format
    description="Generate an LLM-based summary of the experiment based on its details.",
)
async def generate_summary_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint generating summary for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if not artifacts_internal_data.experiment_definition_data:
        raise HTTPException(
            status_code=404, detail="Experiment definition data not found."
        )

    exp_details = extract_experiment_details(
        artifacts_internal_data.experiment_definition_data
    )
    # Call the service function for generation (handles LLM errors)
    summary_text = await generate_experiment_summary(exp_details)
    return SummaryResponse(summary=summary_text)


@router.post(
    "/runs/{run_id}/generate/causal-sentences",
    response_model=List[CausalSentence],
    summary="Generate Causal Sentences (AMCE)",
    tags=["v3.generation"],  # Use new tag format
    description="Generate LLM-based causal sentences from the extracted AMCE data.",
)
async def generate_causal_sentences_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint generating causal sentences for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if (
        not artifacts_internal_data.analytics_output_data
        or artifacts_internal_data.analytics_output_data.get("error")
    ):
        logger.warning(f"Cannot generate causal sentences: Analytics data unavailable.")
        # Return appropriate response based on model (list)
        return [CausalSentence(sentence="Analytics data not available.")]

    amce_data = extract_amce_data(artifacts_internal_data.analytics_output_data)
    # Call the service function for generation (handles LLM errors)
    sentences = await generate_bulk_causal_sentences(amce_data)
    return sentences


@router.post(
    "/runs/{run_id}/generate/mindset-sentences",
    response_model=List[CausalMindsetSentence],
    summary="Generate Mindset Sentences",
    tags=["v3.generation"],  # Use new tag format
    description="Generate LLM-based causal explanations for extracted Mindset data.",
)
async def generate_mindset_sentences_endpoint(
    artifacts_internal_data: _ArtifactDataInternal = Depends(get_run_artifacts_data),
):
    logger.info(
        "Endpoint generating mindset sentences for run associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )
    if (
        not artifacts_internal_data.analytics_output_data
        or artifacts_internal_data.analytics_output_data.get("error")
    ):
        logger.warning(
            f"Cannot generate mindset sentences: Analytics data unavailable."
        )
        return []  # Return empty list

    mindset_data = extract_mindset_data(artifacts_internal_data.analytics_output_data)
    # Call the service function for generation (handles LLM errors)
    mindset_sentences = await generate_bulk_mindset_sentences(mindset_data)
    return mindset_sentences


# --- Report Endpoint ---


@router.post(
    "/runs/{run_id}/report",
    # The response can be JSON (for text) or PDF, so don't set a fixed response_model
    summary="Generate Report (PDF/Text)",
    tags=["v3.reports"],  # Use new tag format
    description="""
    Generate a consolidated report (PDF or Text) with summary, causal insights, and mindset analysis.
    Defaults to PDF format. Use query parameter `?format=text` for text report.
    Optionally specify `?output_filename=your_name.pdf` for the downloaded PDF.
    """,
)
async def generate_report_endpoint(
    background_tasks: BackgroundTasks,  # Inject background tasks for cleanup
    run_id: str = Path(
        ..., description="The unique identifier for the experiment run."
    ),
    format: str = Query("pdf", description="Report format ('pdf' or 'text')"),
    output_filename: Optional[str] = Query(
        None, description="Desired filename for PDF download"
    ),
    artifacts_internal_data: _ArtifactDataInternal = Depends(
        get_run_artifacts_data
    ),  # Use dependency
):
    report_format = format.lower()
    logger.info(
        f"Endpoint generating '{report_format}' report for run {run_id} associated with"
        f" {artifacts_internal_data.experiment_definition_file}"
    )

    if not artifacts_internal_data.success:
        raise HTTPException(
            status_code=404,
            detail="Could not retrieve necessary artifacts to generate report.",
        )

    # --- Data Extraction and Generation ---
    try:
        # 1. Extract Details & Start Summary Generation
        exp_details = extract_experiment_details(
            artifacts_internal_data.experiment_definition_data
        )
        summary_task = asyncio.create_task(generate_experiment_summary(exp_details))

        # 2. Extract AMCE & Start Causal Sentence Generation
        amce_data = extract_amce_data(artifacts_internal_data.analytics_output_data)
        causal_sentences_task = asyncio.create_task(
            generate_bulk_causal_sentences(amce_data)
        )

        # 3. Extract Mindset & Start Mindset Sentence Generation
        mindset_data = extract_mindset_data(
            artifacts_internal_data.analytics_output_data
        )
        mindset_explanations_task = asyncio.create_task(
            generate_bulk_mindset_sentences(mindset_data)
        )

        # --- Await Results ---
        experiment_summary = await summary_task
        causal_sentences = await causal_sentences_task
        mindset_explanations = await mindset_explanations_task

    except Exception as e:
        logger.error(
            f"Error during data processing/generation for report on run {run_id}: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"Failed during report data preparation: {e}"
        )

    # --- Report Creation ---
    if report_format == "text":
        try:
            # Call the service function to create the text content
            report_text = create_text_report(
                experiment_summary,
                causal_sentences,
                mindset_explanations,
                artifacts_internal_data.experiment_definition_data,
                artifacts_internal_data.analytics_output_data,
            )
            # Return as JSON using the TextReportResponse model
            return TextReportResponse(
                success=True, run_id=run_id, report_text=report_text
            )
        except Exception as e:
            logger.error(
                f"Failed to create text report for run {run_id}: {e}", exc_info=True
            )
            raise HTTPException(
                status_code=500, detail=f"Failed to generate text report content: {e}"
            )

    elif report_format == "pdf":
        temp_dir = None
        try:
            temp_dir = tempfile.mkdtemp()
            pdf_filename = output_filename or f"causal_insights_report_{run_id}.pdf"
            if not pdf_filename.lower().endswith(".pdf"):
                pdf_filename += ".pdf"
            # Sanitize filename slightly (basic example)
            pdf_filename = pdf_filename.replace("/", "_").replace("\\", "_")
            output_path = os.path.join(temp_dir, pdf_filename)

            exp_title_for_pdf = artifacts_internal_data.experiment_definition_data.get(
                "title", f"Run {run_id}"
            )

            # Call the service function to create the PDF file
            create_pdf_report(
                experiment_summary,
                causal_sentences,
                mindset_explanations,
                output_path,
                exp_title_for_pdf,
            )

            # Add cleanup task *before* returning the response
            background_tasks.add_task(cleanup_temp_dir, temp_dir)

            # Return the PDF file using FileResponse
            return FileResponse(
                path=output_path, filename=pdf_filename, media_type="application/pdf"
            )
        except (
            RuntimeError
        ) as pdf_error:  # Catch specific PDF generation errors from service
            logger.error(
                f"PDF generation failed for run {run_id}: {pdf_error}", exc_info=True
            )
            if temp_dir:
                cleanup_temp_dir(temp_dir)  # Attempt cleanup
            raise HTTPException(
                status_code=500, detail=f"Failed to generate PDF report: {pdf_error}"
            )
        except Exception as e:
            logger.error(
                f"Unexpected error during PDF report creation for run {run_id}: {e}",
                exc_info=True,
            )
            if temp_dir:
                cleanup_temp_dir(temp_dir)  # Attempt cleanup
            raise HTTPException(
                status_code=500, detail=f"Unexpected error creating PDF report: {e}"
            )
    else:
        raise HTTPException(
            status_code=400,
            detail="Invalid report format specified. Use 'pdf' or 'text'.",
        )
