from pydantic import BaseModel

from app.api.v1.schemas.attributes_levels import LeveledAttribute
from app.core.utils.type_enums import LLMModel


class EnhanceAttributesLevelsRequest(BaseModel):
    why_prompt: str
    existing_attributes_levels: list[LeveledAttribute]
    llm_model: LLMModel | None = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What are the factors that influence the purchase of an electric"
                    " vehicle?"
                ),
                "existing_attributes_levels": [
                    {
                        "attribute": "Battery Range per Charge",
                        "levels": [
                            "Less than 150 miles",
                            "150 to 250 miles",
                        ],
                    },
                    {
                        "attribute": "Vehicle Warranty",
                        "levels": [
                            "3 years or 36,000 miles",
                            "4 years or 50,000 miles",
                        ],
                    },
                ],
            }
        }
