from pydantic import BaseModel


class SingleStatementCausalityResponse(BaseModel):
    is_causal: bool = False
    suggestion: str | None

    class Config:
        json_schema_extra = {
            "example": {
                "is_causal": False,
                "suggestion": (
                    "What factors contribute to the evolutionary advantage of"
                    " having two eyes?"
                ),
            },
        }
