"""Schemas that support endpoint requests and responses"""

import inspect
from abc import ABC, abstractmethod
from enum import Enum
from typing import (
    Annotated,
    Generic,
    Protocol,
    TypeAlias,
    TypeVar,
    Union,
    runtime_checkable,
)

from fastapi import Query
from pydantic import BaseModel, Field, field_validator

from app.api.v2.helpers.latent_variables import (
    convert_traits_data_to_percentage_scales,
    validate_unique_personas,
)
from app.api.v2.schemas.latent_variables_core import Demographics
from app.api.v2.schemas.latent_variables_core import Examples as CoreExamples
from app.api.v2.schemas.latent_variables_core import (
    IdType,
    LikertScoreType,
    PercentageScoreType,
    Persona,
    ProbabilityDistribution,
    ProbabilityType,
    StatementWithLikertScale,
    StatementWithLikertScaleLabels,
    StatementWithLikertScaleScore,
    StatementWithLikertScaleSoftScores,
    StatementWithPercentageScaleLabels,
    StatementWithPercentageScaleScore,
    TraitsLikertScaleDictType,
    TraitsPercentageScaleDictType,
    TraitType,
    TraitWithMeasurementsWithLikertScale,
    TraitWithMeasurementsWithLikertScore,
    TraitWithMeasurementsWithPercentageScale,
    TraitWithMeasurementsWithPercentageScore,
    TraitWithMeasurementsWithSoftScores,
    WhyPromptType,
)
from app.core.utils.type_enums import LLMModel


class Examples:
    TRAIT_CHARACTERISTICS = [
        "Ella's due diligence, given her age, tech-savviness, and commitment to "
        "sustainability, will likely involve extensive online research.  She'll "
        "prioritize verifying claims made by EV brands regarding their "
        "environmental impact and ethical sourcing.  Her due diligence will go "
        "beyond simply checking specifications; it will involve scrutinizing brand "
        "transparency, examining supply chains, and researching the company's "
        "overall sustainability initiatives.  She'll actively seek out independent "
        "reviews and information from trusted sources within her online "
        "communities, showing a higher level of skepticism towards marketing "
        "claims than someone less engaged in ethical consumption."
    ]
    BEHAVIORAL_TENDENCIES = [
        "Ella's young age and budget constraints will influence her due diligence "
        "process by focusing her research on cost-effective ways to verify "
        "information.  She'll likely utilize free online resources, social media "
        "communities, and independent review sites. Her location in an urban area "
        "provides access to a wider range of information and potentially "
        "opportunities to interact with other EV owners and enthusiasts, allowing "
        "her to gather firsthand experiences and feedback.  Her commitment to "
        "sustainability will drive her to prioritize brands with verifiable "
        "environmental credentials, potentially leading to a longer and more "
        "thorough due diligence process than someone less concerned with ethical "
        "considerations.  She'll be particularly wary of greenwashing and will "
        "actively seek out evidence to counter marketing claims. This could "
        "involve contacting companies directly, examining certifications, and "
        "comparing information from multiple sources."
    ]


###############
# Configuration


# TODO: Test different model speeds and qualities
class LLMConfig(BaseModel, ABC):
    llm_model: str = LLMModel.GCP_SONNET


class Config(LLMConfig, ABC):
    """LLM configuration and batch size configuration"""

    batch_size: int = 20


############
# Generation


class GenerationInput(BaseModel):
    """Input to the endpoint"""

    why_prompt: WhyPromptType
    traits_count: int = Field(
        default=2,
        description="Number of psychological traits or characteristics to identify.",
        example=[2],
    )
    measurement_count: int = Field(
        default=2,
        description="Number of trait measurement statements to generate.",
        examples=[2, 4],
    )
    pain_point_count: int = Field(
        default=2,
        description="Number of pain points measurement statements to generate.",
        examples=[2, 4],
    )
    scale_labels_count: int = Field(
        default=5,
        ge=4,
        le=9,
        description=(
            "Number of labels in the Likert scale (e.g., 5 for a 5-point Likert scale)."
        ),
        examples=[5, 9],
    )


class GenerationRequest(GenerationInput, LLMConfig):
    pass


class GenerationLLMOutput(BaseModel):
    """Expected output from the LLM"""

    traits_data: list[TraitWithMeasurementsWithLikertScale] = Field(
        ...,
        description=(
            "List of traits with their measurement statements, pain points, and "
            "pain point detection statements to be evaluated using the associated "
            "Likert scales."
        ),
    )


class GenerationResponse(BaseModel):
    """Endpoint response.

    Fields:
    : why_prompt: str
    : traits_data: list[TraitWithMeasurementsWithLikertScale]
    """

    why_prompt: WhyPromptType
    traits_data: list[TraitWithMeasurementsWithLikertScale]

    @staticmethod
    def validate_response(
        input_data: GenerationInput, llm_output_data: GenerationLLMOutput
    ) -> None:
        """Check response counts and trait uniqueness."""

        traits_list: list[TraitType] = [
            trait_data.trait for trait_data in llm_output_data.traits_data
        ]

        # checking if traits are unique so that they can be used as dictionary keys
        unique_traits = len(traits_list) == len(set(traits_list))
        if not unique_traits:
            raise ValueError("The output traits are not unique.")

        if len(traits_list) != input_data.traits_count:
            raise ValueError(
                "The number of traits in the response does not match the expected "
                "number."
            )

        for trait_data in llm_output_data.traits_data:
            mismatch: bool = (
                (
                    len(trait_data.trait_measurement_statements)
                    != input_data.measurement_count
                )
                or (
                    len(trait_data.associated_pain_points)
                    != input_data.pain_point_count
                )
                or (
                    len(trait_data.pain_point_detection_statements)
                    != input_data.pain_point_count
                )
            )
            if mismatch:
                raise ValueError(
                    "The trait measurement statements, associated pain points or pain "
                    "point detection statements in the response do not match the "
                    "expected number."
                )

    @classmethod
    def build(
        cls,
        input_data: GenerationInput,
        llm_output_data: GenerationLLMOutput,
    ) -> "GenerationResponse":
        cls.validate_response(input_data, llm_output_data)

        return cls(
            why_prompt=input_data.why_prompt, traits_data=llm_output_data.traits_data
        )


##################
# Additional Notes


class AdditionalNotesInput(BaseModel):
    """Same as GenerationResponse"""

    why_prompt: WhyPromptType
    traits_data: list[TraitWithMeasurementsWithLikertScale]
    personas: list[Persona]

    @field_validator("personas")
    @classmethod
    def validate_unique_personas(cls, personas: list[Persona]):
        validate_unique_personas(personas)
        return personas


class AdditionalNotesRequest(AdditionalNotesInput, Config):
    pass


# ----------
# LLM Output


class AdditionalNotesLLMOutput(BaseModel):
    """LLM additional notes output for a single persona-trait combination"""

    trait_characteristics: Annotated[
        str,
        Field(
            description=(
                "How a trait typically manifests in individuals from this demographic "
                "within the research context."
            ),
            examples=Examples.TRAIT_CHARACTERISTICS,
        ),
    ]
    behavioral_tendencies: Annotated[
        str,
        Field(
            description=(
                "Insights into how demographic variables influence an individual's"
                " behaviorrelated to a trait within a given research context."
            ),
            examples=Examples.BEHAVIORAL_TENDENCIES,
        ),
    ]


AdditionalNotesOrNone: TypeAlias = Annotated[
    AdditionalNotesLLMOutput | None,
    Field(
        description=(
            "Trait characteristics and behavioral tendencies for a single "
            "persona-trait combination, or None if there was an error in "
            "the generation."
        )
    ),
]


AdditionalNotesDictType: TypeAlias = Annotated[
    dict[IdType, dict[TraitType, AdditionalNotesOrNone]],
    Field(
        description=(
            "Nested Dictionary where the first level is the persona id, the second "
            "level is the trait (key) and corresponding additional notes (value), "
            "or None if there was an error in the generation."
        )
    ),
]


# -----------------------------
# Protocol With AdditionalNotes
@runtime_checkable
class BasePersonaTraitData(Protocol):
    trait: TraitType
    additional_notes: AdditionalNotesOrNone


@runtime_checkable
class BasePersonaWithTraitsData(Protocol):
    id: IdType
    demographics: Demographics
    persona_traits_data: list[BasePersonaTraitData]


PWT = TypeVar("PWT", bound=BasePersonaWithTraitsData)  # Persona With Traits


class BaseResponse(Generic[PWT], ABC):
    personas_with_traits_data: list[PWT]
    traits_data: list[TraitWithMeasurementsWithLikertScale]

    def get_traits_dict(self) -> TraitsLikertScaleDictType:
        return {trait_data.trait: trait_data for trait_data in self.traits_data}

    def get_additional_notes_dict(self) -> AdditionalNotesDictType:
        additional_notes_dict = {}

        # persona_wtd = persona_with_traits_data
        for persona_wtd in self.personas_with_traits_data:
            additional_notes_dict[persona_wtd.id] = {
                persona_trait_data.trait: persona_trait_data.additional_notes
                for persona_trait_data in persona_wtd.persona_traits_data
            }

        return additional_notes_dict

    def get_personas(self) -> list[Persona]:
        return [  # persona_wtd = persona_with_traits_data
            Persona(id=persona_wtd.id, demographics=persona_wtd.demographics)
            for persona_wtd in self.personas_with_traits_data
        ]


# --------
# Response


class PersonaTraitDataM1(BaseModel):
    """Persona Trait Data: Model 1 (Additional Notes)."""

    trait: TraitType
    additional_notes: AdditionalNotesOrNone = None


class PersonaWithTraitsDataM1(Persona):
    """Persona With Traits Data: Model 1 (Additional Notes).

    Fields:
    : id: IdType
    : demographics: Demographics
    : persona_traits_data: list[PersonaTraitDataM1]
    """

    persona_traits_data: list[PersonaTraitDataM1] = Field(
        ..., description="All traits and their additional notes for this persona."
    )


class AdditionalNotesResponse(BaseModel, BaseResponse[PersonaWithTraitsDataM1]):

    why_prompt: WhyPromptType
    traits_data: list[TraitWithMeasurementsWithLikertScale]
    personas_with_traits_data: list[PersonaWithTraitsDataM1] = Field(
        ...,
        description=(
            "Data for all personas and additional notes for all traits of each persona."
        ),
    )

    @classmethod
    def build(
        cls,
        input_data: AdditionalNotesInput,
        additional_notes_dict: AdditionalNotesDictType,
    ) -> "AdditionalNotesResponse":

        personas_with_traits_data: list[PersonaWithTraitsDataM1] = []
        for persona in input_data.personas:
            persona_traits_data: list[PersonaTraitDataM1] = [
                PersonaTraitDataM1(trait=trait, additional_notes=additional_notes)
                for trait, additional_notes in additional_notes_dict[persona.id].items()
            ]
            personas_with_traits_data.append(
                PersonaWithTraitsDataM1(
                    id=persona.id,
                    demographics=persona.demographics,
                    persona_traits_data=persona_traits_data,
                )
            )

        return cls(
            why_prompt=input_data.why_prompt,
            traits_data=input_data.traits_data,
            personas_with_traits_data=personas_with_traits_data,
        )


####################
# Statement Scores


class ScoringType(str, Enum):
    PERCENTAGE = "percentage_scores"
    LIKERT = "likert_scores"
    SOFT = "soft_scores"


ScoringTypeQuery: TypeAlias = Annotated[
    ScoringType,
    Query(description="""Choose a scoring type:

        - percentage_scores: Integer between 0 and 100
        - likert_scores: Integer between 1 and n, where n is the end-range of the likert scale
        - soft_scores: Probability distribution over the likert scale labels
        """),
]


class StatementScoresInput(AdditionalNotesResponse):
    """Same as AdditionalNotesResponse.

    Fields:
    : why_prompt: WhyPromptType
    : traits_data: list[TraitWithMeasurementsWithLikertScale]
    : personas_with_traits_data: list[PersonaWithTraitsDataM1]
    """

    pass


class StatementScoresRequest(StatementScoresInput, Config):
    pass


# ---------
# LLM Input

# SWSL = statement with scale labels
SWSL = TypeVar(
    "SWSL",
    bound=Union[StatementWithPercentageScaleLabels, StatementWithLikertScaleLabels],
)

StatementsWithPercentageScaleLabelsDictType: TypeAlias = Annotated[
    dict[int, StatementWithPercentageScaleLabels],
    Field(
        description=(
            "The key is the statement index and the values are the statements together "
            "with the respective percentage scale labels."
        )
    ),
]


StatementsWithLikertScaleLabelsDictType: TypeAlias = Annotated[
    dict[int, StatementWithLikertScaleLabels],
    Field(
        description=(
            "The key is the statement index and the values are the statements together "
            "with the respective Likert scale labels."
        )
    ),
]


class BaseStatementScoresLLMInput(BaseModel, Generic[SWSL], ABC):
    demographics: Demographics
    trait: TraitType
    statements_with_scale_labels: dict[int, SWSL] = Field(
        ...,
        description=(
            "Statements designed to measure the trait/detect pain points, and "
            "their respectice indices and percentage/likert scale labels."
            "The keys of the dictionary are the statement indices and the values "
            "are the statements together with their scale labels."
        ),
    )
    additional_notes: AdditionalNotesLLMOutput

    @classmethod
    def _join_measurement_statements(
        cls,
        trait: TraitType,
        traits_dict: TraitsLikertScaleDictType,
    ) -> list[StatementWithLikertScale]:
        trait_with_measurements = traits_dict[trait]
        # list of trait_measurement statements followed by list of pain_point
        # detection statements
        all_measurement_statements = (
            trait_with_measurements.trait_measurement_statements
            + trait_with_measurements.pain_point_detection_statements
        )

        return all_measurement_statements

    @staticmethod
    @abstractmethod
    def build_statements_with_scale_labels(
        all_measurement_statements: list[StatementWithLikertScale],
    ) -> dict[int, SWSL]:
        raise NotImplementedError("Subclasses must implement this method")

    @classmethod
    @abstractmethod
    def build(
        cls,
        persona: Persona,
        trait: TraitType,
        traits_dict: list[TraitWithMeasurementsWithLikertScale],
        additional_notes_dict: AdditionalNotesDictType,
    ) -> "BaseStatementScoresLLMInput[SWSL]":
        raise NotImplementedError("Subclasses must implement this method")


class StatementPercentageScoresLLMInput(
    BaseStatementScoresLLMInput[StatementWithPercentageScaleLabels]
):
    @staticmethod
    def build_statements_with_scale_labels(
        all_measurement_statements: list[StatementWithLikertScale],
    ) -> StatementsWithPercentageScaleLabelsDictType:
        statements_with_scale_labels = {
            idx: StatementWithPercentageScaleLabels(
                statement=statement_ws.statement,
                scale_labels=statement_ws.scale.to_percentage_scale().to_scale_labels(),
            )
            for idx, statement_ws in enumerate(all_measurement_statements)
        }

        return statements_with_scale_labels

    @classmethod
    def build(
        cls,
        persona: Persona,
        trait: TraitType,
        traits_dict: list[TraitWithMeasurementsWithLikertScale],
        additional_notes_dict: AdditionalNotesDictType,
    ) -> "StatementPercentageScoresLLMInput":
        additional_notes = additional_notes_dict[persona.id][trait]
        # Persona-trait combinations where additional_notes is None were filtered out
        assert additional_notes is not None

        all_measurement_statements = cls._join_measurement_statements(
            trait, traits_dict
        )
        statements_with_scale_labels = cls.build_statements_with_scale_labels(
            all_measurement_statements
        )

        return cls(
            demographics=persona.demographics,
            trait=trait,
            statements_with_scale_labels=statements_with_scale_labels,
            additional_notes=additional_notes,
        )


class StatementLikertOrSoftScoresLLMInput(
    BaseStatementScoresLLMInput[StatementWithLikertScaleLabels]
):
    @staticmethod
    def build_statements_with_scale_labels(
        all_measurement_statements: list[StatementWithLikertScale],
    ) -> StatementsWithLikertScaleLabelsDictType:
        statements_with_scale_labels = {
            idx: StatementWithLikertScaleLabels(
                statement=statement_ws.statement,
                scale_labels=statement_ws.scale.to_scale_labels(),
            )
            for idx, statement_ws in enumerate(all_measurement_statements)
        }

        return statements_with_scale_labels

    @classmethod
    def build(
        cls,
        persona: Persona,
        trait: TraitType,
        traits_dict: list[TraitWithMeasurementsWithLikertScale],
        additional_notes_dict: AdditionalNotesDictType,
    ) -> "StatementLikertOrSoftScoresLLMInput":
        additional_notes = additional_notes_dict[persona.id][trait]
        # Persona-trait combinations where additional_notes is None were filtered out
        assert additional_notes is not None

        all_measurement_statements = cls._join_measurement_statements(
            trait, traits_dict
        )
        statements_with_scale_labels = cls.build_statements_with_scale_labels(
            all_measurement_statements
        )

        return cls(
            demographics=persona.demographics,
            trait=trait,
            statements_with_scale_labels=statements_with_scale_labels,
            additional_notes=additional_notes,
        )


# ----------
# LLM Output

PercentageScoresDictType: TypeAlias = Annotated[
    dict[int, PercentageScoreType],
    Field(
        description=(
            "The key is the statement index and the values are the percentage scores "
            "given by a persona to each of the statements."
        )
    ),
]

LikertScoresDictType: TypeAlias = Annotated[
    dict[int, LikertScoreType],
    Field(
        description=(
            "The key is the statement index and the values are the Likert scores "
            "given by a persona to each of the statements."
        )
    ),
]


SoftScoresDictType: TypeAlias = Annotated[
    dict[int, dict[int, ProbabilityType]],
    Field(
        description=(
            "This is a nested dictionary with two levels. The keys at the top level "
            "are the statement indices (integers) and the values are dictionaries. "
            "In the second level, th keys are the label indices (integers) and the "
            "values are the probability scores given by a persona to each Likert scale "
            "label of each of the statements."
        )
    ),
]


class StatementPercentageScoresLLMOutput(BaseModel):
    """Percentage scores.

    Each key-value pair corresponds to a statement index (int) and its score.
    """

    statement_scores: PercentageScoresDictType


class StatementLikertScoresLLMOutput(BaseModel):
    """Likert scores.

    Each key-value pair corresponds to a statement index (int) and its score.
    """

    statement_scores: LikertScoresDictType


class StatementSoftScoresLLMOutput(BaseModel):
    """List of probability distributions.

    Each key-value pair corresponds to a statement index and its score dictionary.
    Each key-value pair of the scores dictionary corresponds to a label index (int)
    and the corresponding label probability score.
    """

    statement_scores: SoftScoresDictType


# --------
# Response


Score = TypeVar(
    "Score", bound=Union[PercentageScoreType, LikertScoreType, ProbabilityDistribution]
)

IN = TypeVar(
    "IN",
    bound=Union[
        StatementsWithPercentageScaleLabelsDictType,
        StatementsWithLikertScaleLabelsDictType,
    ],
)

OUT = TypeVar(
    "OUT",
    bound=Union[
        StatementPercentageScoresLLMOutput,
        StatementLikertScoresLLMOutput,
        StatementSoftScoresLLMOutput,
    ],
)

ScoresDict = TypeVar(
    "ScoreDict",
    bound=Union[PercentageScoresDictType, LikertScoresDictType, SoftScoresDictType],
)


# ------------------------
# Ordered Statement Scores


class BaseStatementScores(BaseModel, Generic[IN, OUT, ScoresDict], ABC):
    """Properly ordered statement score responses"""

    @classmethod
    def _order_response_statement_scores(
        cls,
        llm_output_data: OUT,
        statements_with_scale_labels: IN,
    ) -> ScoresDict:

        statement_indices = list(statements_with_scale_labels.keys())
        response_statement_indices = list(llm_output_data.statement_scores.keys())

        if len(response_statement_indices) != len(set(response_statement_indices)):
            raise ValueError(
                "Duplicate statement scores found in the response.\n"
                f":response statement indices: {response_statement_indices}"
            )

        if set(statement_indices) != set(response_statement_indices):
            raise ValueError(
                "Statements and response statements indices don't match.\n"
                f":statement indices: {statement_indices};\n"
                f":response statement indices: {response_statement_indices}"
            )

        # Get indices of statements in response statements
        # Example:
        # statement_indices = [0, 1, 2, 3, 4],
        # response_indices = [3, 0, 1, 2, 4]
        # indices (result) = [1, 2, 3, 0, 4]
        # reordering: [response_indices[i] for i in indices] = [0, 1, 2, 3, 4]
        index_map = {
            response_idx: idx
            for idx, response_idx in enumerate(response_statement_indices)
        }
        indices = [index_map[idx] for idx in range(len(statement_indices))]
        ordered_statement_scores = {
            j: llm_output_data.statement_scores[idx] for j, idx in enumerate(indices)
        }

        return ordered_statement_scores


class StatementPercentageScores(
    BaseStatementScores[
        StatementsWithPercentageScaleLabelsDictType,
        StatementPercentageScoresLLMOutput,
        PercentageScoresDictType,
    ]
):
    scores: list[PercentageScoreType]

    @classmethod
    def build(
        cls,
        llm_output_data: StatementPercentageScoresLLMOutput,
        statements_with_scale_labels: StatementsWithPercentageScaleLabelsDictType,
    ) -> "StatementPercentageScores":
        """Put the responses in the correct order"""

        ordered_statement_scores = cls._order_response_statement_scores(
            llm_output_data, statements_with_scale_labels
        )

        # python3.7+ preserves insertion order
        scores: list[PercentageScoreType] = list(ordered_statement_scores.values())

        return cls(scores=scores)


class StatementLikertScores(
    BaseStatementScores[
        StatementsWithLikertScaleLabelsDictType,
        StatementLikertScoresLLMOutput,
        LikertScoresDictType,
    ]
):
    scores: list[LikertScoreType]

    @classmethod
    def build(
        cls,
        llm_output_data: StatementSoftScoresLLMOutput,
        statements_with_scale_labels: StatementsWithLikertScaleLabelsDictType,
    ) -> "StatementLikertScores":
        """Put the responses in the correct order"""

        ordered_statement_scores = cls._order_response_statement_scores(
            llm_output_data, statements_with_scale_labels
        )

        # python3.7+ preserves insertion order
        scores: list[LikertScoreType] = list(ordered_statement_scores.values())

        return cls(scores=scores)


class StatementSoftScores(
    BaseStatementScores[
        StatementsWithLikertScaleLabelsDictType,
        StatementSoftScoresLLMOutput,
        SoftScoresDictType,
    ]
):
    scores: list[ProbabilityDistribution]

    @classmethod
    def get_label_order_indices(
        cls, label_indices: list[int], response_label_indices: list[int]
    ) -> list[int]:
        """Get indices of response labels in original labels list"""

        if len(response_label_indices) != len(set(response_label_indices)):
            raise ValueError(
                "Duplicate labels found in the response.\n"
                f":response label indices: {response_label_indices}"
            )

        if set(label_indices) != set(response_label_indices):
            raise ValueError(
                "Label and response label indices don't match.\n"
                f":label indices: {label_indices};\n"
                f":response label indices: {response_label_indices}"
            )

        label_index_map = {
            response_label_idx: idx
            for idx, response_label_idx in enumerate(response_label_indices)
        }
        indices = [label_index_map[idx] for idx in range(len(label_indices))]

        return indices

    @classmethod
    def build(
        cls,
        llm_output_data: StatementSoftScoresLLMOutput,
        statements_with_scale_labels: StatementsWithLikertScaleLabelsDictType,
    ) -> "StatementSoftScores":
        """Put the responses in the correct order"""

        ordered_statement_scores = cls._order_response_statement_scores(
            llm_output_data, statements_with_scale_labels
        )

        scores: list[ProbabilityDistribution] = []

        for statement_wsl, soft_scores in zip(
            statements_with_scale_labels.values(), ordered_statement_scores.values()
        ):
            label_indices = list(range(len(statement_wsl.scale_labels)))
            response_label_indices = list(soft_scores.keys())

            indices = cls.get_label_order_indices(label_indices, response_label_indices)
            probability_distribution: ProbabilityDistribution = [
                soft_scores[idx] for idx in indices
            ]
            ProbabilityDistribution.check_valid_probability_distribution(
                probability_distribution
            )

            scores.append(probability_distribution)

        return cls(scores=scores)


# ----------------------
# Trait statement scores


class BaseTraitStatementScores(BaseModel, Generic[Score]):
    """Statement scores split as trait measurement and pain point detection scores."""

    trait_measurement_statement_scores: list[Score] = Field(
        ..., description="Scores for trait measurement statements."
    )
    pain_point_detection_statement_scores: list[Score] = Field(
        ..., description="Scores for pain point detection statements."
    )

    @classmethod
    def get_split_scores(
        cls,
        scores: list[Score],
        trait_with_measurements: TraitWithMeasurementsWithLikertScale,
    ) -> tuple[list[Score], list[Score]]:
        """Build the statement scores model for a trait-persona combination.

        : trait_with_measurements:
            trait with measurement statements.
        : M2_model:
            percentage scores or probability distribution given by the persona to the
            trait measurement statements.
        """
        # tms = trait measurement statements, ppds = pain point detection statements
        number_of_tms = len(trait_with_measurements.trait_measurement_statements)
        number_of_ppds = len(trait_with_measurements.pain_point_detection_statements)

        if len(scores) != (number_of_tms + number_of_ppds):
            raise ValueError(
                "Total number of percentage scores does not match expected number."
            )

        tms_scores = scores[0:number_of_tms]
        ppds_scores = scores[number_of_tms:]

        return tms_scores, ppds_scores


class TraitStatementPercentageScores(BaseTraitStatementScores[PercentageScoreType]):
    """Statement percentage scores split into statement type."""

    @classmethod
    def build(
        cls,
        llm_output_data: StatementPercentageScoresLLMOutput,
        statements_with_scale_labels: StatementsWithPercentageScaleLabelsDictType,
        trait_with_measurements: TraitWithMeasurementsWithLikertScale,
    ) -> "TraitStatementPercentageScores":
        m2_model = StatementPercentageScores.build(
            llm_output_data, statements_with_scale_labels
        )
        scores = m2_model.scores

        tms_scores, ppds_scores = cls.get_split_scores(scores, trait_with_measurements)

        return cls(
            trait_measurement_statement_scores=tms_scores,
            pain_point_detection_statement_scores=ppds_scores,
        )

    model_config = {
        "json_schema_extra": {
            "examples": [{
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_PERCENTAGE_SCORE
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_PERCENTAGE_SCORE
                ),
            }]
        },
    }


class TraitStatementLikertScores(BaseTraitStatementScores[LikertScoreType]):
    """Statement likert scores split into statement type."""

    @classmethod
    def build(
        cls,
        llm_output_data: StatementLikertScoresLLMOutput,
        statements_with_scale_labels: StatementsWithLikertScaleLabelsDictType,
        trait_with_measurements: TraitWithMeasurementsWithLikertScale,
    ) -> "TraitStatementLikertScores":
        m2_model = StatementLikertScores.build(
            llm_output_data, statements_with_scale_labels
        )
        scores = m2_model.scores

        tms_scores, ppds_scores = cls.get_split_scores(scores, trait_with_measurements)

        return cls(
            trait_measurement_statement_scores=tms_scores,
            pain_point_detection_statement_scores=ppds_scores,
        )

    model_config = {
        "json_schema_extra": {
            "examples": [{
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_LIKERT_SCORE
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_LIKERT_SCORE
                ),
            }]
        },
    }


class TraitStatementSoftScores(BaseTraitStatementScores[ProbabilityDistribution]):
    """Statement label probability scores split into statement type."""

    @classmethod
    def build(
        cls,
        llm_output_data: StatementSoftScoresLLMOutput,
        statements_with_scale_labels: StatementsWithLikertScaleLabelsDictType,
        trait_with_measurements: TraitWithMeasurementsWithLikertScale,
    ):
        m2_model = StatementSoftScores.build(
            llm_output_data, statements_with_scale_labels
        )
        scores = m2_model.scores

        tms_scores, ppds_scores = cls.get_split_scores(scores, trait_with_measurements)

        return cls(
            trait_measurement_statement_scores=tms_scores,
            pain_point_detection_statement_scores=ppds_scores,
        )

    model_config = {
        "json_schema_extra": {
            "examples": [{
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_SOFT_SCORES
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_SOFT_SCORES
                ),
            }]
        },
    }


StatementScoresDictType: TypeAlias = Annotated[
    dict[
        IdType,
        TraitStatementPercentageScores
        | TraitStatementLikertScores
        | TraitStatementSoftScores
        | None,
    ],
    Field(
        description=(
            "Dictionary where the key is the persona id and the value is the "
            "statement scores, or None if there was an error in the generation."
        )
    ),
]


class PersonaTraitDataM2(PersonaTraitDataM1):
    """Persona Trait Data: Model 2 (Additional Notes + Statement Scores)."""

    scores: (
        TraitStatementPercentageScores
        | TraitStatementLikertScores
        | TraitStatementSoftScores
        | None
    ) = Field(
        default=None,
        description=(
            "Percentage scores or label probability scores for all concept testing "
            "statements for a single persona, or None if there was an error in the "
            "generation."
        ),
        examples=[
            {
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_PERCENTAGE_SCORE
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_PERCENTAGE_SCORE
                ),
            },
            {
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_LIKERT_SCORE
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_LIKERT_SCORE
                ),
            },
            {
                "trait_measurement_statement_scores": (
                    CoreExamples.TRAIT_MEASUREMENTS_SOFT_SCORES
                ),
                "pain_point_detection_statement_scores": (
                    CoreExamples.PAIN_POINTS_SOFT_SCORES
                ),
            },
        ],
    )


class PersonaWithTraitsDataM2(Persona):
    """Persona With Traits Data: Model 2 (Additional Notes + TraitStatementScores).

    Fields:
    : id: IdType
    : demographics: Demographics
    : persona_traits_data: list[PersonaTraitDataM2]
    """

    persona_traits_data: list[PersonaTraitDataM2] = Field(
        ...,
        description=(
            "All traits and their additional notes and statement scores for this "
            "persona."
        ),
    )


class StatementScoresResponse(BaseModel, BaseResponse[PersonaWithTraitsDataM2]):

    why_prompt: WhyPromptType
    traits_data: (
        list[TraitWithMeasurementsWithPercentageScale]
        | list[TraitWithMeasurementsWithLikertScale]
    )
    personas_with_traits_data: list[PersonaWithTraitsDataM2] = Field(
        ...,
        description=(
            "Data for all personas and additional notes + statement scores for all "
            "traits of each persona."
        ),
    )

    @classmethod
    def build(
        cls,
        input_data: StatementScoresInput,
        statement_scores_dict: StatementScoresDictType,
        scoring_type: ScoringType,
    ) -> "StatementScoresResponse":
        new_personas_with_traits_data = []

        # persona_wtd = persona_with_traits_data
        # persona_td = persona_trait_data
        for persona_wtd in input_data.personas_with_traits_data:
            persona_id = persona_wtd.id

            new_persona_traits_data = [
                PersonaTraitDataM2(
                    trait=persona_td.trait,
                    additional_notes=persona_td.additional_notes,
                    scores=statement_scores_dict[persona_id][persona_td.trait],
                )
                for persona_td in persona_wtd.persona_traits_data
            ]
            new_personas_with_traits_data.append(
                PersonaWithTraitsDataM2(
                    id=persona_wtd.id,
                    demographics=persona_wtd.demographics,
                    persona_traits_data=new_persona_traits_data,
                )
            )

        if scoring_type == ScoringType.PERCENTAGE:
            traits_data = convert_traits_data_to_percentage_scales(
                input_data.traits_data
            )
        else:
            traits_data = input_data.traits_data

        return cls(
            why_prompt=input_data.why_prompt,
            traits_data=traits_data,
            personas_with_traits_data=new_personas_with_traits_data,
        )

    def get_statement_scores_dict(self) -> StatementScoresDictType:
        statement_scores_dict = {}

        # persona_wtd = persona_with_traits_data
        for persona_wtd in self.personas_with_traits_data:
            statement_scores_dict[persona_wtd.id] = {
                persona_trait_data.trait: persona_trait_data.scores
                for persona_trait_data in persona_wtd.persona_traits_data
            }

        return statement_scores_dict


#########################################
# Natural Language Persona Traits Summary


class NLSummaryInput(StatementScoresResponse):
    """Same as StatementScoresResponse

    Fields:
    : why_prompt: str
    : traits_data: list[TraitWithMeasurementsWithLikertPercentageScales]
    : persona_with_traits_data: list[PersonaWithTraitsDataM2]
    """

    pass


class NLSummaryRequest(NLSummaryInput, Config):
    pass


class TraitWithMeasurementsWithPercentageScoreAndAdditionalNotes(
    TraitWithMeasurementsWithPercentageScore
):
    additional_notes: AdditionalNotesOrNone = None


class TraitWithMeasurementsWithLikertScoreAndAdditionalNotes(
    TraitWithMeasurementsWithLikertScore
):
    additional_notes: AdditionalNotesOrNone = None


class TraitWithMeasurementsWithSoftScoresAndAdditionalNotes(
    TraitWithMeasurementsWithSoftScores
):
    additional_notes: AdditionalNotesOrNone = None


SinglePersonaTraitsData: TypeAlias = Annotated[
    TraitWithMeasurementsWithPercentageScoreAndAdditionalNotes
    | TraitWithMeasurementsWithLikertScoreAndAdditionalNotes
    | TraitWithMeasurementsWithSoftScoresAndAdditionalNotes
    | None,
    Field(
        description=(
            "Persona traits, statements, scales and scores, and additional notes."
        )
    ),
]


class PersonaWithTraitsDataM3(Persona):
    """Persona with traits, trait statements, and their respective scales and scores, and additional notes.

    Fields:
    : id: IdType
    : demographics: Demographics
    : persona_traits_data: list[SinglePersonaTraitsData]
    """

    persona_traits_data: list[SinglePersonaTraitsData] = Field(
        ...,
        description=(
            "All persona traits, statements, scales and scores, and additional notes."
        ),
    )

    def get_exclude_pattern(self):
        """Exclude additional notes from LLM Input; for soft scores, keep only the mean_score."""

        # TODO: Try not excluding additional notes
        exclude_pattern: dict = {"additional_notes": True}

        if isinstance(
            next(iter(self.persona_traits_data)), TraitWithMeasurementsWithSoftScores
        ):
            # applies to all items in all statement lists
            exclude_pattern["__all__"] = {
                "__all__": {  # Applies to all items in those lists
                    "score": True,
                }
            }

        return exclude_pattern

    @classmethod
    def build(
        cls,
        persona_with_traits_data: PersonaWithTraitsDataM2,
        traits_dict: TraitsLikertScaleDictType | TraitsPercentageScaleDictType,
    ) -> "PersonaWithTraitsDataM3":

        persona_traits_data = persona_with_traits_data.persona_traits_data
        new_persona_traits_data: list[SinglePersonaTraitsData] = []

        for persona_trait_data in persona_traits_data:
            trait = persona_trait_data.trait
            trait_with_measurements = traits_dict[trait]
            additional_notes = persona_trait_data.additional_notes
            scores = persona_trait_data.scores

            trait_wms: SinglePersonaTraitsData = None  # trait with measurement scores
            if scores is not None:
                if isinstance(scores, TraitStatementSoftScores):
                    trait_wms = TraitWithMeasurementsWithSoftScoresAndAdditionalNotes(
                        trait=trait,
                        trait_measurement_statements=[
                            StatementWithLikertScaleSoftScores(
                                statement=statement_with_scale.statement,
                                scale=statement_with_scale.scale,
                                score=scores.trait_measurement_statement_scores[i],
                                mean_score=(
                                    scores.trait_measurement_statement_scores[
                                        i
                                    ].weighted_average()
                                ),
                            )
                            for i, statement_with_scale in enumerate(
                                trait_with_measurements.trait_measurement_statements
                            )
                        ],
                        associated_pain_points=(
                            trait_with_measurements.associated_pain_points
                        ),
                        pain_point_detection_statements=[
                            StatementWithLikertScaleSoftScores(
                                statement=statement_with_scale.statement,
                                scale=statement_with_scale.scale,
                                score=scores.pain_point_detection_statement_scores[i],
                                mean_score=(
                                    scores.pain_point_detection_statement_scores[
                                        i
                                    ].weighted_average()
                                ),
                            )
                            for i, statement_with_scale in enumerate(
                                trait_with_measurements.pain_point_detection_statements
                            )
                        ],
                        additional_notes=additional_notes,
                    )
                else:
                    if isinstance(
                        trait_with_measurements,
                        TraitWithMeasurementsWithPercentageScale,
                    ):
                        TraitMeasurementsCls = (
                            TraitWithMeasurementsWithPercentageScoreAndAdditionalNotes
                        )
                        StatementCls = StatementWithPercentageScaleScore
                    else:
                        TraitMeasurementsCls = (
                            TraitWithMeasurementsWithLikertScoreAndAdditionalNotes
                        )
                        StatementCls = StatementWithLikertScaleScore

                    trait_wms = TraitMeasurementsCls(
                        trait=trait,
                        trait_measurement_statements=[
                            StatementCls(
                                statement=statement_with_scale.statement,
                                scale=statement_with_scale.scale,
                                score=scores.trait_measurement_statement_scores[i],
                            )
                            for i, statement_with_scale in enumerate(
                                trait_with_measurements.trait_measurement_statements
                            )
                        ],
                        associated_pain_points=(
                            trait_with_measurements.associated_pain_points
                        ),
                        pain_point_detection_statements=[
                            StatementCls(
                                statement=statement_with_scale.statement,
                                scale=statement_with_scale.scale,
                                score=scores.pain_point_detection_statement_scores[i],
                            )
                            for i, statement_with_scale in enumerate(
                                trait_with_measurements.pain_point_detection_statements
                            )
                        ],
                        additional_notes=additional_notes,
                    )

            # Add trait data for this persona to the complete trait data for this
            # persona
            new_persona_traits_data.append(trait_wms)

        return cls(
            id=persona_with_traits_data.id,
            demographics=persona_with_traits_data.demographics,
            persona_traits_data=new_persona_traits_data,
        )


class NLSummaryLLMOutput(BaseModel):

    demographics: str = Field(
        ...,
        description="Demographic traits of the persona",
        examples=[
            "Ella is a tech-savvy, socially conscious young woman aged 18–25, living "
            "in urban areas."
        ],
    )
    personality_traits: str = Field(
        ...,
        description="Personality traits of the persona",
        examples=[
            "Ella is analytical, tech-savvy, socially responsible, and driven by "
            "authenticity and transparency, often balancing her introverted and "
            "extroverted tendencies to engage meaningfully with others."
        ],
    )
    pain_points: str = Field(
        ...,
        description="Pain points of the persona",
        examples=[
            "Ella struggles with balancing ethical consumption "
            "with budget constraints, finding authentic and sustainable "
            "luxury brands, and dealing with inauthentic marketing and "
            "greenwashing."
        ],
    )
    behaviors: str = Field(
        ...,
        description="Behaviors of the persona",
        examples=[
            "Ella actively researches and evaluates brands "
            "online, engages with social media communities, values "
            "influencer insights, and consistently seeks transparency and "
            "authenticity in brand communications."
        ],
    )
    goals: str = Field(
        ...,
        description="Persona goals",
        examples=[
            "Ella aims to maintain a health-conscious and sustainable "
            "lifestyle, make ethically responsible purchasing decisions, and "
            "inspire others to adopt similar values, all while balancing luxury "
            "with ethical consumption."
        ],
    )
    motivations: str = Field(
        ...,
        description=(
            "Internal drivers that influence the persona's decisions and actions"
        ),
        examples=[
            "Ella is motivated by a desire to make a positive environmental impact,"
            " align her purchases with her values, and be recognized as an early"
            " adopter of sustainable innovations within her social circles."
        ],
    )
    trigger_points: str = Field(
        description=(
            "Specific situations or messages that prompt immediate action or emotional "
            "response"
        ),
        examples=[
            "Seeing concrete data about climate change impacts triggers Ella to"
            " research sustainable alternatives. Encounters with greenwashing claims"
            " prompt immediate brand reevaluation. Peer recommendations from trusted"
            " sources often trigger purchasing decisions."
        ],
    )

    @staticmethod
    def get_format_str() -> str:
        persona_NL_format_str = inspect.cleandoc("""
        Demographics: {demographics}

        Personality Traits: {personality_traits}

        Pain Points: {pain_points}

        Behaviors: {behaviors}

        Goals: {goals}

        Motivations: {motivations}
        
        Trigger Points: {trigger_points}
        """)

        return persona_NL_format_str


NLSummaryOrNone: TypeAlias = Annotated[
    NLSummaryLLMOutput | None,
    Field(
        description=(
            "Natural language summary of persona traits, or None if there was an error "
            "in the generation."
        )
    ),
]


NLSummaryDictType: TypeAlias = Annotated[
    dict[IdType, NLSummaryOrNone],
    Field(
        description=(
            "Key: persona id; Value: persona traits natural language summary, or None "
            "if there was an error in the generation."
        )
    ),
]


class PersonaWithTraitsDataM4(PersonaWithTraitsDataM3):
    """Persona With Traits Data: Model 4 (PersonaWithTraitsDataM3 + NLSummary).

    Fields:
    : id: IdType
    : demographics: Demographics
    : persona_traits_data: list[SinglePersonaTraitsData]
    : NL_summary: NLSummaryOrNone
    """

    NL_summary: NLSummaryOrNone = None


# TODO: Solve PERSONA prompt failing issue
class NLSummaryResponse(BaseModel):

    why_prompt: WhyPromptType
    traits_data: (
        list[TraitWithMeasurementsWithLikertScale]
        | list[TraitWithMeasurementsWithPercentageScale]
    )
    personas_with_traits_data: list[PersonaWithTraitsDataM4] = Field(
        ...,
        description=(
            "Data for all personas with traits, statement scores, additional "
            "notes, and natural language summary for all traits of each persona."
        ),
    )

    @classmethod
    def build(
        cls,
        input_data: NLSummaryInput,
        NL_summary_dict: NLSummaryDictType,
        personas_with_traits_data: list[PersonaWithTraitsDataM3],
    ) -> "NLSummaryResponse":

        new_personas_with_traits_data = [
            PersonaWithTraitsDataM4(
                id=persona_wtd.id,
                demographics=persona_wtd.demographics,
                persona_traits_data=persona_wtd.persona_traits_data,
                NL_summary=NL_summary_dict[persona_wtd.id],
            )
            for persona_wtd in personas_with_traits_data
        ]

        return cls(
            why_prompt=input_data.why_prompt,
            traits_data=input_data.traits_data,
            personas_with_traits_data=new_personas_with_traits_data,
        )


#########


class FullRunRequest(GenerationInput, Config):
    """Model for full run with all endpoint calls

    Fields:
    : personas: list[Persona]
    : why_prompt: str
    : traits_count: int = 5
    : measurement_count: int = 2
    : pain_point_count: int = 2
    : scale_labels_count: int = 5
    : batch_size: int = 20
    """

    personas: list[Persona]


class FullRunResponse(NLSummaryResponse):
    pass
