"""Schemas with basic latent-variable building blocks"""

import math
from abc import ABC
from typing import Annotated, Generic, Mapping, TypeAlias, TypeVar

from pydantic import BaseModel, Field, RootModel, conlist, field_validator
from pydantic.types import confloat


class Examples:
    WHY_PROMPT = [
        "What factors affect venture capitalists' decisions to invest in "
        "early-stage startups?"
    ]
    ID_TYPE = [1, 2, 3, 4]
    DEMOGRAPHICS = [{
        "name": "<PERSON>",
        "age": "18-25",
        "gender": "Female",
        "location": "Urban areas",
        "background": (
            "Tech-savvy, socially responsible, driven by authenticity and transparency"
        ),
        "personalityTraits": "Analytical, tech-savvy, socially responsible",
        "behaviors": (
            "Actively researches and evaluates brands online, engages with social "
            "media communities, values influencer insights, seeks transparency and "
            "authenticity in brand communications"
        ),
        "goals": (
            "Maintain a health-conscious and sustainable lifestyle, make "
            "ethically responsible purchasing decisions, inspire others to adopt "
            "similar values"
        ),
    }]
    DEMOGRAPHICS_NL = [
        "I'm <PERSON>, a tech-savvy young woman between 18-25 living in an urban area. "
        "As someone deeply committed to sustainability and ethical consumption, "
        "I'm constantly researching brands online and engaging with social media "
        "communities to make informed decisions. When it comes to adopting an "
        "electric vehicle, I'm particularly interested in brands that align with "
        "my values of authenticity and transparency. While price is a significant "
        "factor due to my budget constraints, I'm willing to invest more in a "
        "brand that genuinely demonstrates environmental responsibility. I'm "
        "always on the lookout for ways to balance my desire for sustainable "
        "luxury with my financial limitations, and I'm wary of greenwashing "
        "tactics in marketing."
    ]
    LIKERT_SCALE = [
        {
            1: "Strongly Disagree",
            2: "Disagree",
            3: "Neither agree nor disagree",
            4: "Agree",
            5: "Strongly Agree",
        },
        {1: "Never", 2: "Rarely", 3: "Sometimes", 4: "Often", 5: "Always"},
    ]
    LIKERT_SCALE_LABELS = [[
        "1 = Strongly Disagree",
        "2 = Disagree",
        "3 = Neither agree nor disagree",
        "4 = Agree",
        "5 = Strongly Agree",
    ]]
    PERCENTAGE_SCALE = [
        {0: "Strongly Disagree", 100: "Strongly Agree"},
        {0: "Never", 100: "Always"},
    ]
    PERCENTAGE_SCALE_LABELS = [
        ["0 = Strongly Disagree", "100 = Strongly Agree"],
        ["0 = Never", "100 = Always"],
    ]
    LIKERT_SCORE = [2, 5]
    FLOAT_LIKERT_SCORE = [2.2, 4.3]
    PERCENTAGE_SCORE = [21, 84]
    PROBABILITY = [0.6, 0.1, 0.5]
    PROBABILITY_DISTRIBUTION = [
        [0.05, 0.1, 0.15, 0.5, 0.2],
        [0.02, 0.4, 0.24, 0.2, 0.14],
    ]
    TRAITS = ["Due Diligence", "Risk Tolerance"]
    PAIN_POINT_NAME = ["Information Overload", "Inaccurate Market Assessment"]
    PAIN_POINTS_DESCRIPTION = [
        (
            "Difficulty in filtering relevant information from the vast amount of "
            "data available on the market."
        ),
        (
            "Misjudging the market potential of a startup due to incomplete or biased "
            "information."
        ),
        (
            "The fear of significant financial losses prevents investment in "
            "higher-risk, higher-reward opportunities."
        ),
        (
            "A low risk tolerance leads to missing out on potentially lucrative "
            "investments due to excessive caution."
        ),
    ]
    TRAIT_MEASUREMENT_STATEMENTS = [
        (
            "I thoroughly research the market and the team before making an investment "
            "decision."
        ),
        (
            "I rely heavily on gut feeling and intuition when evaluating investment "
            "opportunities."
        ),
    ]
    TRAIT_MEASUREMENTS_LIKERT_SCALE = [LIKERT_SCALE[1], LIKERT_SCALE[0]]
    TRAIT_MEASUREMENTS_LIKERT_SCORE = [4, 1]
    TRAIT_MEASUREMENTS_PERCENTAGE_SCALE = [PERCENTAGE_SCALE[1], PERCENTAGE_SCALE[0]]
    TRAIT_MEASUREMENTS_PERCENTAGE_SCORE = [84, 21]
    TRAIT_MEASUREMENTS_SOFT_SCORES = [
        PROBABILITY_DISTRIBUTION[1],
        PROBABILITY_DISTRIBUTION[0],
    ]
    TRAIT_MEASUREMENTS_FLOAT_LIKERT_SCORE = [3.04, 3.7]
    PAIN_POINT_DETECTION_STATEMENT = [
        (
            "I often feel overwhelmed by the amount of information I need to process"
            " when evaluating startups."
        ),
        "I've made investment decisions based on incomplete or inaccurate information.",
    ]
    PAIN_POINTS_LIKERT_SCALE = [LIKERT_SCALE[0], LIKERT_SCALE[0]]
    PAIN_POINTS_LIKERT_SCORE = [4, 2]
    PAIN_POINTS_PERCENTAGE_SCALE = [PERCENTAGE_SCALE[0], PERCENTAGE_SCALE[0]]
    PAIN_POINTS_PERCENTAGE_SCORE = [76, 31]
    PAIN_POINTS_SOFT_SCORES = [
        PROBABILITY_DISTRIBUTION[1],
        PROBABILITY_DISTRIBUTION[0],
    ]
    PAIN_POINTS_FLOAT_LIKERT_SCORE = [3.04, 3.7]


#####################
# Base Root dict type

# Define generic type variables for key and value
K = TypeVar("K")  # Key type
V = TypeVar("V")  # Value type


class DictModel(RootModel[dict[K, V]], Mapping[K, V], Generic[K, V]):
    """Generic dictionary model with Mapping support."""

    def __getitem__(self, key: K) -> V:
        return self.root[key]

    def __iter__(self):
        return iter(self.root)

    def __len__(self) -> int:
        return len(self.root)


############
# Why-prompt

WhyPromptType: TypeAlias = Annotated[
    str,
    Field(
        ...,
        description=(
            "A question or topic area that involves human decision-making, "
            "preferences, or behavior."
        ),
        examples=Examples.WHY_PROMPT,
    ),
]

#########
# Persona

IdType: TypeAlias = Annotated[
    int, Field(description="Unique identifier", examples=Examples.ID_TYPE)
]


class Demographics(DictModel[str, str]):
    model_config = {
        "json_schema_extra": {
            "description": "Persona demographics",
            "examples": Examples.DEMOGRAPHICS,
        }
    }


Demographics_NL: TypeAlias = Annotated[
    str,
    Field(
        description="Natural language description of the Persona",
        examples=Examples.DEMOGRAPHICS_NL,
    ),
]


class IdModel(BaseModel, ABC):
    id: IdType  # even with exclude=True, still gets printed in LLM Output


class Persona(IdModel):
    demographics: Demographics


class Persona_NL(IdModel):
    demographics: Demographics_NL


###################
# Scales and Scores

LikertScoreType: TypeAlias = Annotated[
    int,
    Field(
        ge=1,
        le=9,
        description="Likert score for a given statement.",
        examples=Examples.LIKERT_SCORE,
    ),
]


FloatLikertScoreType: TypeAlias = Annotated[
    float,
    Field(
        ge=1,
        le=9,
        description="Float likert score value for a given statement.",
        examples=Examples.FLOAT_LIKERT_SCORE,
    ),
]


PercentageScoreType: TypeAlias = Annotated[
    int,
    Field(
        ge=0,
        le=100,
        description="Percentage score for a given statement.",
        examples=Examples.PERCENTAGE_SCORE,
    ),
]

LikertScaleLabelsType = Annotated[
    conlist(  # Constrained list
        str,
        min_length=4,
        max_length=9,
    ),
    Field(description="Likert scale labels.", examples=Examples.LIKERT_SCALE_LABELS),
]

PercentageScaleLabelsType = Annotated[
    conlist(  # Constrained list
        str,
        min_length=2,
        max_length=2,
    ),
    Field(
        description="Percentage scale labels.",
        examples=Examples.PERCENTAGE_SCALE_LABELS,
    ),
]


class PercentageScale(DictModel[int, str]):
    """Model for percentage scale.

    Access self.root to get scale directly"""

    model_config = {
        "json_schema_extra": {
            "description": "A percentage scale with labels for 0 and 100.",
            "examples": Examples.PERCENTAGE_SCALE,
        }
    }

    def to_scale_labels(self) -> PercentageScaleLabelsType:
        return [f"{key} = {value}" for key, value in self.root.items()]

    @field_validator("root")
    def check_keys(cls, percentage_scale: dict[int, str]):
        percentage_scale = dict(sorted(percentage_scale.items()))
        keys = list(percentage_scale.keys())
        expected_keys = [0, 100]
        if keys != expected_keys:
            raise ValueError(
                f"Expected percentage scale keys to be 0 and 100.\nGot {keys}."
            )

        return percentage_scale


class LikertScale(DictModel[int, str]):
    """Model for Likert scale.

    Access self.root to get scale directly."""

    model_config = {
        "json_schema_extra": {
            "description": "A 4-point to 9-point likert scale.",
            "examples": Examples.LIKERT_SCALE,
        },
        "populate_by_name": True,
    }

    def to_percentage_scale(self) -> PercentageScale:
        """
        Convert a likert scale with labels (1-[4/5/9]) into a percentage scale with labels
        for 0 and 100.
        """
        scale_ordered = dict(sorted(self.root.items()))
        labels = list(scale_ordered.values())

        return PercentageScale({0: labels[0], 100: labels[-1]})

    def to_scale_labels(self) -> LikertScaleLabelsType:
        return [f"{key} = {value}" for (key, value) in self.root.items()]

    @field_validator("root")
    def check_keys(cls, scale: dict[int, str]):
        scale = dict(sorted(scale.items()))
        keys = list(scale.keys())
        expected_keys = list(range(1, 1 + len(keys)))
        if keys != expected_keys:
            raise ValueError(f"Keys must be sequential starting from 1. Got: {keys}.")
        if not (4 <= len(keys) <= 9):
            raise ValueError(
                f"Number of likert scale keys must be between 4 and 9.\nkeys: {keys}."
            )

        return scale


ProbabilityType = Annotated[
    confloat(ge=0.0, le=1.0),
    Field(description="A probability value (between 0 and 1)."),
]


class ProbabilityDistribution(RootModel[list[ProbabilityType]]):
    """Model for probability distribution over the Likert scale labels.

    Access self.root to get distribution directly."""

    model_config = {
        "json_schema_extra": {
            "description": "A valid probability distribution.",
            "examples": Examples.PROBABILITY_DISTRIBUTION,
        }
    }

    def weighted_average(self) -> FloatLikertScoreType:
        """Get weighted average of distribution with Likert scale labels as weights."""
        mean_score = sum([(i + 1) * self.root[i] for i in range(len(self.root))])

        return round(mean_score, 2)

    @field_validator("root")
    def check_valid_probability_distribution(cls, value: list[ProbabilityType]):
        if not math.isclose(sum(value), 1.0, abs_tol=1e-2):
            raise ValueError(f"Encountered invalid probability distribution: {value}.")

        return value


############
# Statements

StatementIdxType: TypeAlias = Annotated[int, Field(..., ge=0)]


class StatementWithLikertScale(BaseModel):
    statement: str
    scale: LikertScale


class StatementWithPercentageScale(BaseModel):
    statement: str
    scale: PercentageScale


class StatementWithLikertScaleLabels(BaseModel):
    statement: str
    scale_labels: LikertScaleLabelsType


class StatementWithPercentageScaleLabels(BaseModel):
    statement: str
    scale_labels: PercentageScaleLabelsType


class StatementWithPercentageScaleScore(BaseModel):
    statement: str
    scale: PercentageScale
    score: PercentageScoreType


class StatementWithLikertScaleScore(BaseModel):
    statement: str
    scale: LikertScale
    score: LikertScoreType


class StatementWithLikertScaleSoftScores(BaseModel):
    statement: str
    scale: LikertScale
    score: ProbabilityDistribution
    mean_score: FloatLikertScoreType


class StatementWithIdxScale(BaseModel):
    idx: StatementIdxType
    statement: str
    scale: LikertScale | PercentageScale


########
# Traits

TraitType: TypeAlias = Annotated[
    str,
    Field(
        description="Psychological trait or characteristic",
        examples=Examples.TRAITS,
    ),
]


class Trait(BaseModel, ABC):
    trait: TraitType


# TODO: Remove examples where they are not required for swagger (such as here?)
class PainPoint(BaseModel):
    name: str = Field(
        ...,
        description="Name of the specific pain point.",
        examples=Examples.PAIN_POINT_NAME,
    )
    description: str = Field(
        ...,
        description="Brief explanation of how the trait relates to this pain point.",
        examples=Examples.PAIN_POINTS_DESCRIPTION,
    )


class PainPointWithStatementIdx(BaseModel):
    # statement_idx corresponds to the idx in StatementWithIdxScale for pain poin
    # detection statements
    statement_idx: StatementIdxType
    name: str
    description: str


AssociatedPainPointsType: TypeAlias = Annotated[
    list[PainPoint],
    Field(
        description="Pain points associated with the trait.",
        examples=[[
            {
                "name": Examples.PAIN_POINT_NAME[i],
                "description": Examples.PAIN_POINTS_DESCRIPTION[i],
            }
            for i in range(2)
        ]],
    ),
]


class TraitWithMeasurementsWithLikertScale(Trait):
    """Trait with its measurements with the associated Likert scales.

    Trait measurements include trait measurement statements, pain points, and pain
    point detection statements.
    """

    trait_measurement_statements: list[StatementWithLikertScale] = Field(
        ...,
        description=(
            "Statements designed to measure the trait, and the associated "
            "Likert scales."
        ),
        examples=[[
            {
                "statement": Examples.TRAIT_MEASUREMENT_STATEMENTS[i],
                "scale": Examples.TRAIT_MEASUREMENTS_LIKERT_SCALE[i],
            }
            for i in range(2)
        ]],
    )
    associated_pain_points: AssociatedPainPointsType
    pain_point_detection_statements: list[StatementWithLikertScale] = Field(
        ...,
        description=(
            "Statements designed to detect pain points, and the associated "
            "Likert scales."
        ),
        examples=[[
            {
                "statement": Examples.PAIN_POINT_DETECTION_STATEMENT[i],
                "scale": Examples.PAIN_POINTS_LIKERT_SCALE[i],
            }
            for i in range(2)
        ]],
    )


class TraitWithMeasurementsWithPercentageScale(Trait):
    """Trait with its measurements with the associated Percentage scales.

    Trait measurements include trait measurement statements, pain points, and pain
    point detection statements.
    """

    trait_measurement_statements: list[StatementWithPercentageScale] = Field(
        ...,
        description=(
            "Statements designed to measure the trait, and the associated "
            "Percentage scales."
        ),
        examples=[[
            {
                "statement": Examples.TRAIT_MEASUREMENT_STATEMENTS[i],
                "scale": Examples.TRAIT_MEASUREMENTS_PERCENTAGE_SCALE[i],
            }
            for i in range(2)
        ]],
    )
    associated_pain_points: AssociatedPainPointsType
    pain_point_detection_statements: list[StatementWithPercentageScale] = Field(
        ...,
        description=(
            "Statements designed to detect pain points, and the associated "
            "Percentage scales."
        ),
        examples=[[
            {
                "statement": Examples.PAIN_POINT_DETECTION_STATEMENT[i],
                "scale": Examples.PAIN_POINTS_PERCENTAGE_SCALE[i],
            }
            for i in range(2)
        ]],
    )


class TraitsPercentageScaleDictType(
    DictModel[TraitType, TraitWithMeasurementsWithPercentageScale]
):
    model_config = {
        "json_schema_extra": {
            "description": (
                "Dictionary of trait:(trait measurements) key:value pairs, where the"
                " trait measurements include trait measurement statements, pain points,"
                " and point detection statements to be evaluated using the associated"
                " Percentage scales."
            )
        }
    }


class TraitsLikertScaleDictType(
    DictModel[TraitType, TraitWithMeasurementsWithLikertScale]
):
    model_config = {
        "json_schema_extra": {
            "description": (
                "Dictionary of trait:(trait measurements) key:value pairs, where the"
                " trait measurements include trait measurement statements, pain points,"
                " and point detection statements to be evaluated using the associated"
                " Likert scales."
            )
        }
    }


class TraitWithMeasurementsWithPercentageScore(Trait):
    """Trait with its measurements with the associated percentage scales and scores.

    Trait measurements include trait measurement statements, pain points, and pain
    point detection statements.
    """

    trait_measurement_statements: list[StatementWithPercentageScaleScore] = Field(
        ...,
        description=(
            "Statements designed to measure the trait, and the associated "
            "percentage scales and scores."
        ),
        examples=[[
            {
                "statement": Examples.TRAIT_MEASUREMENT_STATEMENTS[i],
                "scale": Examples.TRAIT_MEASUREMENTS_PERCENTAGE_SCALE[i],
                "score": Examples.TRAIT_MEASUREMENTS_PERCENTAGE_SCORE[i],
            }
            for i in range(2)
        ]],
    )
    associated_pain_points: AssociatedPainPointsType
    pain_point_detection_statements: list[StatementWithPercentageScaleScore] = Field(
        ...,
        description=(
            "Statements designed to detect pain points, and the associated "
            "percentage scales and scores."
        ),
        examples=[[
            {
                "statement": Examples.PAIN_POINT_DETECTION_STATEMENT[i],
                "scale": Examples.PAIN_POINTS_PERCENTAGE_SCALE[i],
                "score": Examples.PAIN_POINTS_PERCENTAGE_SCORE[i],
            }
            for i in range(2)
        ]],
    )


class TraitWithMeasurementsWithLikertScore(Trait):
    """Trait with its measurements with the associated likert scales and scores.

    Trait measurements include trait measurement statements, pain points, and pain
    point detection statements.
    """

    trait_measurement_statements: list[StatementWithLikertScaleScore] = Field(
        ...,
        description=(
            "Statements designed to measure the trait, and the associated "
            "likert scales and scores."
        ),
        examples=[[
            {
                "statement": Examples.TRAIT_MEASUREMENT_STATEMENTS[i],
                "scale": Examples.TRAIT_MEASUREMENTS_LIKERT_SCALE[i],
                "score": Examples.TRAIT_MEASUREMENTS_LIKERT_SCORE[i],
            }
            for i in range(2)
        ]],
    )
    associated_pain_points: AssociatedPainPointsType
    pain_point_detection_statements: list[StatementWithLikertScaleScore] = Field(
        ...,
        description=(
            "Statements designed to detect pain points, and the associated "
            "likert scales and scores."
        ),
        examples=[[
            {
                "statement": Examples.PAIN_POINT_DETECTION_STATEMENT[i],
                "scale": Examples.PAIN_POINTS_LIKERT_SCALE[i],
                "score": Examples.PAIN_POINTS_LIKERT_SCORE[i],
            }
            for i in range(2)
        ]],
    )


class TraitWithMeasurementsWithSoftScores(Trait):
    """Trait with its measurements with the associated Likert scales and soft scores.

    Trait measurements include trait measurement statements, pain points, and pain point detection statements.
    """

    trait_measurement_statements: list[StatementWithLikertScaleSoftScores] = Field(
        ...,
        description=(
            "Statements designed to measure the trait, and the associated "
            "likert scales and soft scores and weight avg. score."
        ),
        examples=[[
            {
                "statement": Examples.TRAIT_MEASUREMENT_STATEMENTS[i],
                "scale": Examples.TRAIT_MEASUREMENTS_PERCENTAGE_SCALE[i],
                "score": Examples.TRAIT_MEASUREMENTS_SOFT_SCORES[i],
                "mean_score": Examples.TRAIT_MEASUREMENTS_FLOAT_LIKERT_SCORE[i],
            }
            for i in range(2)
        ]],
    )
    associated_pain_points: AssociatedPainPointsType
    pain_point_detection_statements: list[StatementWithLikertScaleSoftScores] = Field(
        ...,
        description=(
            "Statements designed to detect pain points, and the associated "
            "likert scales and soft scores and weight avg. score."
        ),
        examples=[[
            {
                "statement": Examples.PAIN_POINT_DETECTION_STATEMENT[i],
                "scale": Examples.PAIN_POINTS_LIKERT_SCALE[i],
                "score": Examples.PAIN_POINTS_SOFT_SCORES[i],
                "mean_score": Examples.PAIN_POINTS_FLOAT_LIKERT_SCORE[i],
            }
            for i in range(2)
        ]],
    )


class TraitWithMeasurementsForAnalytics(BaseModel):
    trait_measurement_statements: list[StatementWithIdxScale]
    pain_point_detection_statements: list[StatementWithIdxScale]
    associated_pain_points: list[PainPointWithStatementIdx]


# the keys are the traits
TraitsDataForAnalytics: TypeAlias = dict[str, TraitWithMeasurementsForAnalytics]
