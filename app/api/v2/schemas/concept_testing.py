from typing import Annotated, Any, Type<PERSON>lias

from pydantic import BaseModel, Field, field_validator

from app.api.v2.helpers.latent_variables import validate_unique_personas
from app.api.v2.schemas.latent_variables import (
    Config,
    ScoringType,
    StatementLikertOrSoftScoresLLMInput,
    StatementLikertScores,
    StatementPercentageScores,
    StatementPercentageScoresLLMInput,
    StatementSoftScores,
    StatementsWithLikertScaleLabelsDictType,
    StatementsWithPercentageScaleLabelsDictType,
    StatementWithLikertScaleScore,
)
from app.api.v2.schemas.latent_variables_core import Examples as CoreExamples
from app.api.v2.schemas.latent_variables_core import (
    IdModel,
    IdType,
    Persona_NL,
    StatementWithLikertScale,
    StatementWithLikertScaleSoftScores,
    StatementWithPercentageScaleScore,
)


class Examples:
    AGE = [["20", "26"]]
    EDUCATION_LEVEL = [[
        "Less than high school",
        "High School but no diploma",
        "High School Diploma",
        "Some College",
        "Associates",
        "Bachelors",
        "Masters",
        "PhD",
    ]]
    GENDER = [["Female", "Male"]]
    HOUSEHOLD_INCOME = [["50000", "70000"]]
    NUMBER_OF_CHILDREN = [["0", "1", "2", "3", "4+"]]
    RACIAL_GROUP = [[
        "White",
        "African American",
        "Asian or Pacific Islander",
        "Mixed race",
        "Other race",
    ]]
    LACTOSE_INTOLERANT = [["Yes", "No"]]
    HEALTH_CONSCIOUS = [["Yes", "No"]]
    COUNTRY = ["United States of America"]
    STATE = ["New York", "Ohio"]
    YEAR = ["2025"]
    DESCRIPTION_PROMPT = [
        "A new milk product is being launched in the market and we would like to "
        "understand people's perception towards the product."
    ]
    STATEMENT = [
        "As I deal with the stress of today's new normal, milk does my "
        "body good with its vitamins, minerals and protein. Milk helps me wind "
        "down after a long day."
    ]


CountryType: TypeAlias = Annotated[str, Field(examples=Examples.COUNTRY)]

StateType: TypeAlias = Annotated[
    str | None, Field(default=None, examples=Examples.STATE)
]

YearType: TypeAlias = Annotated[str, Field(examples=Examples.YEAR)]

DescriptionPromptType: TypeAlias = Annotated[
    str, Field(examples=Examples.DESCRIPTION_PROMPT)
]

ImageType: TypeAlias = str


class PopulationRequest(BaseModel):
    target_population: dict[str, Any]
    population_traits: dict[str, Any]
    country: CountryType
    state: StateType
    year: YearType
    description_prompt: DescriptionPromptType


#######
# Input


class CTStatementScoresInputBase(BaseModel):  # CT = Concept Testing
    personas: list[Persona_NL]
    description_prompt: DescriptionPromptType
    image: ImageType


class CTStatementScoresInput(CTStatementScoresInputBase):
    statements_with_scales: list[StatementWithLikertScale] = Field(
        ...,
        examples=[[{
            "statement": Examples.STATEMENT[0],
            "scale": CoreExamples.LIKERT_SCALE[0],
        }]],
    )

    @field_validator("personas")
    @classmethod
    def validate_unique_personas(cls, personas: list[Persona_NL]):
        validate_unique_personas(personas)
        return personas

        return personas

    @field_validator("statements_with_scales")
    @classmethod
    def check_unique_statements(
        cls, statement_with_scales: list[StatementWithLikertScale]
    ):
        statements = [model.statement for model in statement_with_scales]
        if len(statements) != len(set(statements)):
            raise ValueError("Duplicate statements are not allowed.")

        return statement_with_scales


class CTStatementScoresRequest(CTStatementScoresInput, Config):
    pass


###########
# LLM Input


class CTStatementPercentageScoresLLMInput(BaseModel):
    statements_with_scale_labels: StatementsWithPercentageScaleLabelsDictType

    @classmethod
    def build(
        cls, statements_with_scales: list[StatementWithLikertScale]
    ) -> "CTStatementPercentageScoresLLMInput":

        return cls(
            statements_with_scale_labels=(
                StatementPercentageScoresLLMInput.build_statements_with_scale_labels(
                    statements_with_scales
                )
            )
        )


class CTStatementLikertOrSoftScoresLLMInput(BaseModel):
    statements_with_scale_labels: StatementsWithLikertScaleLabelsDictType

    @classmethod
    def build(
        cls, statements_with_scales: list[StatementWithLikertScale]
    ) -> "CTStatementLikertOrSoftScoresLLMInput":

        return cls(
            statements_with_scale_labels=(
                StatementLikertOrSoftScoresLLMInput.build_statements_with_scale_labels(
                    statements_with_scales
                )
            )
        )


##########
# Response


CTStatementScoresDictType: TypeAlias = Annotated[
    dict[
        IdType,
        StatementPercentageScores | StatementLikertScores | StatementSoftScores | None,
    ],
    Field(
        description=(
            "Dictionary where the key is the persona id and the value is the concept "
            "testing statement scores, or None if there was an error in the generation."
        )
    ),
]


# class PersonaWithStatementScores(Persona)
class PersonaWithStatementScores(IdModel):
    statements_with_scores: (
        list[StatementWithPercentageScaleScore]
        | list[StatementWithLikertScaleScore]
        | list[StatementWithLikertScaleSoftScores]
    ) = Field(
        ...,
        examples=[[{
            "statement": Examples.STATEMENT[0],
            "scale": CoreExamples.PERCENTAGE_SCALE[0],
            "score": CoreExamples.PERCENTAGE_SCORE[0],
        }]],
    )


class CTStatementScoresResponse(BaseModel):
    personas_with_statement_scores: list[PersonaWithStatementScores]

    @classmethod
    def build(
        cls,
        data: CTStatementScoresInput,
        concept_testing_scores_dict: CTStatementScoresDictType,
        scoring_type: ScoringType,
    ) -> "CTStatementScoresResponse":
        personas_with_statement_scores = []

        for persona in data.personas:
            statements_with_scales = data.statements_with_scales
            scores = concept_testing_scores_dict[persona.id].scores

            match scoring_type:
                case ScoringType.PERCENTAGE:
                    statements_with_scores = [
                        StatementWithPercentageScaleScore(
                            statement=sws.statement,
                            scale=sws.scale.to_percentage_scale(),
                            score=scores[idx],
                        )
                        for idx, sws in enumerate(statements_with_scales)
                    ]
                case ScoringType.LIKERT:
                    statements_with_scores = [
                        StatementWithLikertScaleScore(
                            statement=sws.statement,
                            scale=sws.scale,
                            score=scores[idx],
                        )
                        for idx, sws in enumerate(statements_with_scales)
                    ]
                case ScoringType.SOFT:
                    statements_with_scores = [
                        StatementWithLikertScaleSoftScores(
                            statement=sws.statement,
                            scale=sws.scale,
                            score=scores[idx],
                            mean_score=scores[idx].weighted_average(),
                        )
                        for idx, sws in enumerate(statements_with_scales)
                    ]

            personas_with_statement_scores.append(
                PersonaWithStatementScores(
                    id=persona.id,
                    statements_with_scores=statements_with_scores,
                )
            )

        return cls(
            personas_with_statement_scores=personas_with_statement_scores,
        )
