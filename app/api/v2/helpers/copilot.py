from app.api.v1.helpers.attributes_levels import check_generic_or_specific_attribute
from app.api.v1.schemas.attributes_levels import (
    SpecificOrGenericAttributeCheckStrictResponse,
)
from app.api.v1.schemas.markekt_simulator import (
    CheckProductRealWorldResponse,
    CheckProductRequest,
)
from app.llm_prompt.prompt_builder import Prompt<PERSON>uilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor


def check_for_product_strict(req: CheckProductRequest) -> str:
    """
    Checks if a why_prompt can correspond to a real-world product.
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model)
    pb = PromptBuilder()
    prompt = pb.check_for_product()

    try:
        response = pe.execute(
            prompt=prompt,
            args={"why_prompt": req.why_prompt},
            description="Checking if the product exists in the real world",
            output_object=CheckProductRealWorldResponse,
        )

        product_exists = response.get("product_exists")
        score = response.get("score")
        product_exists = True if product_exists and score == 5 else False
        if product_exists:
            attribute_reason = check_generic_or_specific_attribute(
                why_prompt=req.why_prompt
            )
            attribute_type = attribute_reason.get("attribute_type")
            if attribute_type == "generic":
                res = pe.execute(
                    prompt=pb.generic_or_specific_attribute_check_strict(),
                    args={"why_prompt": req.why_prompt},
                    description=(
                        "Strictly checking if the product exists in the real world"
                    ),
                    output_object=SpecificOrGenericAttributeCheckStrictResponse,
                )
                modified_statement = res.get("modified_statement")
                return modified_statement
            else:
                return req.why_prompt
        else:
            return req.why_prompt
    except Exception as e:
        raise
