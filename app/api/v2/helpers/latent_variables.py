import json
from typing import Generator, Type, TypeVar

from pydantic import BaseModel

from app.api.v2.schemas.latent_variables_core import (
    Persona,
    Persona_NL,
    StatementWithPercentageScale,
    TraitType,
    TraitWithMeasurementsWithLikertScale,
    TraitWithMeasurementsWithPercentageScale,
)
from app.llm_prompt.prompt_executor import LCELPromptExecutor

#########
# TypeVar

ItemType = TypeVar("Item")
ModelType = TypeVar("Model", bound=BaseModel)


########


def get_persona_trait_combinations(
    personas: list[Persona],
    traits_data: list[TraitWithMeasurementsWithLikertScale],
    batch_size: int,
) -> Generator[list[tuple[Persona, TraitType]], None, None]:
    """Generate batches of all persona-trait combinations."""

    current_batch = []
    for persona in personas:
        for trait_with_measurements in traits_data:
            current_batch.append((persona, trait_with_measurements.trait))
            if len(current_batch) >= batch_size:
                yield current_batch
                current_batch = []

    # Yield any remaining items in the final batch
    if current_batch:
        yield current_batch


def get_list_batches(
    list_: list[ItemType], batch_size: int
) -> Generator[list[ItemType], None, None]:
    """Generate batches from a list."""

    current_batch = []
    for item in list_:
        current_batch.append(item)
        if len(current_batch) >= batch_size:
            yield current_batch
            current_batch = []

    # Yield any remaining items in the final batch
    if current_batch:
        yield current_batch


def json_dump_model(model: BaseModel, exclude: dict | None = None) -> str:
    return json.dumps(model.model_dump(exclude=exclude), indent=4)


def json_dump_model_list(
    model_list: list[BaseModel], exclude: dict | None = None
) -> str:
    return json.dumps(
        [model.model_dump(exclude=exclude) for model in model_list], indent=4
    )


def json_dump_model_dict(
    model_dict: dict[int | str, BaseModel], exclude: dict | None = None
):
    return json.dumps(
        {str(k): model.model_dump(exclude=exclude) for k, model in model_dict.items()},
        indent=4,
    )


def get_first_model_field(ModelClass: Type[BaseModel]) -> str:
    return next(iter(ModelClass.model_fields))


def get_llm_batch_results(
    prompt_executor: LCELPromptExecutor,
    batch_prompts_with_args: list,
    output_model: Type[ModelType],
    description: str,
) -> list[dict | None]:
    """LLM batch execution with graceful handling of failures.

    Returns None for the prompts that failed.
    """

    try:
        results = prompt_executor.batch_execute(  # list of dictionaries
            prompts=batch_prompts_with_args,
            args=None,
            output_object=output_model,
            description=description,
        )
        first_field = get_first_model_field(output_model)
        # Assigns None to the result of failing prompts
        llm_batch_outputs = [
            (result if (isinstance(result, dict) and (first_field in result)) else None)
            for result in results
        ]
    except Exception:
        # TODO: Recreate the llm_batch_outputs in the same format as the one in line
        # 139 and set all probability scores to 0
        llm_batch_outputs = [None] * len(batch_prompts_with_args)

    return llm_batch_outputs


def convert_traits_data_to_percentage_scales(
    traits_data: list[TraitWithMeasurementsWithLikertScale],
) -> list[TraitWithMeasurementsWithPercentageScale]:
    new_traits_data: list[TraitWithMeasurementsWithPercentageScale] = []

    for trait_data in traits_data:
        new_traits_data.append(
            TraitWithMeasurementsWithPercentageScale(
                trait=trait_data.trait,
                trait_measurement_statements=[
                    StatementWithPercentageScale(
                        statement=statement_ws.statement,
                        scale=statement_ws.scale.to_percentage_scale(),
                    )
                    for statement_ws in trait_data.trait_measurement_statements
                ],
                associated_pain_points=trait_data.associated_pain_points,
                pain_point_detection_statements=[
                    StatementWithPercentageScale(
                        statement=statement_ws.statement,
                        scale=statement_ws.scale.to_percentage_scale(),
                    )
                    for statement_ws in trait_data.pain_point_detection_statements
                ],
            )
        )

    return new_traits_data


def validate_unique_personas(personas: list[Persona] | list[Persona_NL]) -> None:
    ids = [persona.id for persona in personas]
    unique_ids = len(ids) == len(set(ids))
    if not unique_ids:
        raise ValueError("Duplicate personas detected in the input...")
