from typing import Any

from app.api.v2.schemas.latent_variables import SinglePersonaTraitsData
from app.api.v2.schemas.latent_variables_core import (
    PainPointWithStatementIdx,
    StatementWithIdxScale,
    TraitsDataForAnalytics,
    TraitWithMeasurementsForAnalytics,
    TraitWithMeasurementsWithLikertScale,
    TraitWithMeasurementsWithPercentageScale,
)


def get_traits_data_for_analytics(
    traits_data: (
        list[TraitWithMeasurementsWithLikertScale]
        | list[TraitWithMeasurementsWithPercentageScale]
    ),
) -> TraitsDataForAnalytics:
    """Get traits data for analytics.

    Returns a dictionary of the following form, where the statement indices are globally unique across traits.
    {
        '{{trait name}}': {
            'trait_measurement_statements': [
                {
                    'idx': 1
                    'statement': {{statement}},
                    'scale': {{scale}},
                },
                ...
                {
                    'idx': i
                    'statement': {{statement}},
                    'scale': {{scale}},
                },
            ]
            'pain_point_detection_statements': [
                {
                    'idx': i + 1
                    'statement': {{statement}},
                    'scale': {{scale}},
                },
                ...,
                {
                    'idx': n
                    'statement': {{statement}},
                    'scale': {{scale}},
                }
            ],
            'associated_pain_points': [
                {
                    'statement_idx': i+1,  # corresponds to idx of pain point statements
                    'name': {{name}},
                    'description': {{description}}
                }
            ],
        }
    ]
    """
    traits_dict: TraitsDataForAnalytics = {
        trait_data.trait: {} for trait_data in traits_data
    }

    idx = 0  # global statement index
    for trait_data in traits_data:
        tms_data: list[StatementWithIdxScale] = []
        for tms in trait_data.trait_measurement_statements:
            tms_data.append(
                StatementWithIdxScale(idx=idx, statement=tms.statement, scale=tms.scale)
            )
            idx += 1

        ppds_data: list[StatementWithIdxScale] = []
        pain_points_data: list[PainPointWithStatementIdx] = []
        assert len(trait_data.pain_point_detection_statements) == len(
            trait_data.associated_pain_points
        )

        for ppds, pain_point in zip(
            trait_data.pain_point_detection_statements,
            trait_data.associated_pain_points,
        ):
            ppds_data.append(
                StatementWithIdxScale(
                    idx=idx, statement=ppds.statement, scale=ppds.scale
                )
            )
            pain_points_data.append(
                PainPointWithStatementIdx(
                    statement_idx=idx,
                    name=pain_point.name,
                    description=pain_point.description,
                )
            )
            idx += 1

        traits_dict[trait_data.trait] = TraitWithMeasurementsForAnalytics(
            trait_measurement_statements=tms_data,
            pain_point_detection_statements=ppds_data,
            associated_pain_points=pain_points_data,
        )

    return traits_dict


def get_persona_statement_scores_info(
    persona_traits_data: list[SinglePersonaTraitsData],
    analytics_traits_data: TraitsDataForAnalytics,
) -> list[str]:
    """Returns a list of statement scores in the form ['statement_i: {{score}}']."""

    statement_scores_dict: dict[int, str] = {}
    for traits_data in persona_traits_data:
        trait = traits_data.trait
        analytics_trait_data = analytics_traits_data[trait]
        for analytics_tms, tms in zip(
            analytics_trait_data.trait_measurement_statements,
            traits_data.trait_measurement_statements,
        ):
            assert analytics_tms.statement == tms.statement
            statement_scores_dict[analytics_tms.idx] = (
                f"statement_{analytics_tms.idx}: {tms.mean_score}"
            )

        for analytics_ppds, ppds in zip(
            analytics_trait_data.pain_point_detection_statements,
            traits_data.pain_point_detection_statements,
        ):
            assert analytics_ppds.statement == ppds.statement
            statement_scores_dict[analytics_ppds.idx] = (
                f"statement_{analytics_ppds.idx}: {ppds.mean_score}"
            )

    statement_scores_info = [
        statement_scores_dict[key] for key in sorted(statement_scores_dict)
    ]

    return statement_scores_info


def convert_analytics_traits_data_to_dict(
    analytics_traits_data: TraitsDataForAnalytics,
) -> dict[str, Any]:
    analytics_trait_dict: dict[str, Any] = {
        trait: model.model_dump() for trait, model in analytics_traits_data.items()
    }

    return analytics_trait_dict
