from app.api.v1.schemas.attributes_levels import (
    LeveledAttribute,
    LeveledAttributeBase,
    LeveledAttributeResponse,
    MonetaryAttribute,
    MonetaryAttributeResponse,
)
from app.core.utils.common import chunk_list
from app.core.utils.type_enums import LLMModel
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor


def get_enhanced_attributes_levels(
    why_prompt: str,
    existing_attributes_levels: list[LeveledAttribute],
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> list[LeveledAttributeBase]:
    """
    Enhances the attributes and levels for the given why_prompt.
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.enhance_attributes_levels_prompt()
    args = {
        "why_prompt": why_prompt,
        "existing_attributes_levels": existing_attributes_levels,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeResponse,
        description="Enhancing attributes and levels",
    )
    return [
        LeveledAttributeBase(**attr) for attr in response.get("attributes_levels", [])
    ]


def check_for_monetary_attributes_levels(
    existing_attributes_levels: list[LeveledAttribute],
    llm_model: LLMModel = LLMModel.AZURE_GPTO3_MINI,
) -> list[MonetaryAttribute]:
    """
    Enhances the attributes and levels for the given why_prompt.
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.check_monetary_attributes_levels()
    batched_attributes_levels = list(chunk_list(existing_attributes_levels, 3))
    prompts_with_args = [
        prompt.format(existing_attributes_levels=attr)
        for attr in batched_attributes_levels
    ]
    responses = pe.batch_execute(
        prompts=prompts_with_args,
        args=None,
        output_object=MonetaryAttributeResponse,
        description="Batch-Checking monetary attributes",
    )
    return [
        MonetaryAttribute(**attr)
        for response in responses
        for attr in response.get("attributes_levels", [])
        if attr.get("levels") and len(set(attr.get("levels"))) >= 2
    ]
