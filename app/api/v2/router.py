from fastapi import APIRouter

from app.api.v2.endpoints import (
    attributes_levels,
    concept_testing,
    copilot,
    latent_variables,
)

router = APIRouter()

router.include_router(copilot.router, prefix="/copilot", tags=["v2.copilot"])
router.include_router(
    attributes_levels.router, prefix="/attributes_levels", tags=["v2.attributes_levels"]
)
router.include_router(
    latent_variables.router, prefix="/latent-variables", tags=["v2.latent-variables"]
)
router.include_router(
    concept_testing.router, prefix="/concept-testing", tags=["v2.concept-testing"]
)
