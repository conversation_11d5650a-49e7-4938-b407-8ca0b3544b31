from fastapi import APIRout<PERSON>, Depends, HTTPException
from fastapi.security import HTTPBearer

from app.api.v1.endpoints.attributes_levels import (
    create_attributes_and_levels_claude_with_score,
    product_attribute_levels,
)
from app.api.v1.endpoints.market_simulator import check_for_product
from app.api.v1.schemas.attributes_levels import (
    AttributesLevelsRequest,
    AttributesLevelsRequestClaude,
    AttributesLevelsResponse,
    ProductAttributeRequest,
)
from app.api.v1.schemas.markekt_simulator import CheckProductRequest
from app.api.v2.helpers.attributes_levels import (
    check_for_monetary_attributes_levels,
    get_enhanced_attributes_levels,
)
from app.api.v2.schemas.attributes_levels import EnhanceAttributesLevelsRequest
from app.core.utils.logging import app_logger

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/consolidated", response_model=dict)
async def create_attributes_and_levels(
    request: AttributesLevelsRequest,
) -> AttributesLevelsResponse | HTTPException | dict:
    """
    Create attributes and levels for a given why_prompt, automatically
    determining if it's a real-world product.
    If it is a real-world product, generates product-specific attributes and levels.
    If not, generates regular attributes and levels.

    Parameters:
    - `req`: AttributesLevelsRequest object containing:
        - `why_prompt`: The main concept or prompt
        - `country`: Target country
        - `level_count`: Number of levels per attribute (optional)
        - `max_length`: Maximum length for levels (optional)
        - `llm_model`: Language model to use (optional)
        - `attribute_count`: Number of attributes to generate (optional)

    Returns:
    - List of attributes with their levels.
    """
    try:
        check_product_request = CheckProductRequest(
            why_prompt=request.why_prompt,
            llm_model=request.llm_model,
        )
        product_response = check_for_product(check_product_request)
        is_real_product = product_response.product_exists

        if is_real_product:
            try:
                product_attribute_request = ProductAttributeRequest(
                    why_prompt=request.why_prompt,
                    attribute_count=request.attribute_count,
                    country=request.country,
                    level_count=request.level_count,
                )
                attributes_levels_products = await product_attribute_levels(
                    product_attribute_request
                )
                attributes_levels = attributes_levels_products.attributes_levels
                monetary_attributes_levels = check_for_monetary_attributes_levels(
                    existing_attributes_levels=attributes_levels,
                )
                return {
                    "attributes_levels": monetary_attributes_levels,
                    "brand_attribute_combinations": (
                        attributes_levels_products.brand_attribute_combinations
                    ),
                }
            except Exception as e:
                attributes_levels_request_claude = AttributesLevelsRequestClaude(
                    why_prompt=request.why_prompt,
                    country=request.country,
                    level_count=request.level_count,
                    llm_model=request.llm_model,
                    attribute_count=request.attribute_count,
                )
                attributes_levels = create_attributes_and_levels_claude_with_score(
                    req=attributes_levels_request_claude
                )
                monetary_attributes_levels = check_for_monetary_attributes_levels(
                    existing_attributes_levels=attributes_levels,
                )
                return {
                    "attributes_levels": monetary_attributes_levels,
                    "brand_attribute_combinations": [],
                }
        else:
            attributes_levels_request_claude = AttributesLevelsRequestClaude(
                why_prompt=request.why_prompt,
                country=request.country,
                level_count=request.level_count,
                llm_model=request.llm_model,
                attribute_count=request.attribute_count,
            )
            attributes_levels = create_attributes_and_levels_claude_with_score(
                req=attributes_levels_request_claude
            )
            monetary_attributes_levels = check_for_monetary_attributes_levels(
                existing_attributes_levels=attributes_levels,
            )
            return {
                "attributes_levels": monetary_attributes_levels,
                "brand_attribute_combinations": [],
            }
    except Exception as e:
        logger.error(f"Error in create_attributes_and_levels: {str(e)}")
        raise HTTPException(
            status_code=404,
            detail=f"Error creating attributes and levels: {str(e)}",
        )


@router.post("/enhance", response_model=AttributesLevelsResponse)
async def enhance_attributes_and_levels(
    request: EnhanceAttributesLevelsRequest,
) -> dict | HTTPException:
    try:
        attributes_levels = get_enhanced_attributes_levels(
            why_prompt=request.why_prompt,
            existing_attributes_levels=request.existing_attributes_levels,
        )
        return {
            "attributes_levels": attributes_levels,
            "brand_attribute_combinations": [],
        }
    except Exception as e:
        logger.error(f"Error in enhance_attributes_and_levels: {str(e)}")
        raise HTTPException(
            status_code=404,
            detail=f"Error enhancing attributes and levels: {str(e)}",
        )
