from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON>er
from sentry_sdk import capture_exception

from app.api.v1.schemas.copilot import (
    CausalityRequest,
    CausalityResponse,
    FinalCausalityResponse,
)
from app.api.v1.schemas.markekt_simulator import CheckProductRequest
from app.api.v2.helpers.copilot import check_for_product_strict
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/causality", response_model=FinalCausalityResponse)
def check_causality(req: CausalityRequest) -> FinalCausalityResponse:
    """
    Check whether a why_prompt is causal or not.

    Raises:
    - HTTPException 500: If an error occurs during the causality check
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model)
    pb = PromptBuilder()
    causality_prompt = pb.causality_prompt()
    retry_count = 2
    new_statements = []

    try:
        causality_info = pe.execute(
            prompt=causality_prompt,
            args={"why_prompt": req.why_prompt},
            output_object=CausalityResponse,
            description="Checking causality",
        )
        logger.success(
            f"Causality check executed successfully for prompt: {req.why_prompt}"
        )
        suggestions = causality_info.get("suggestions", [])
        is_harmful = causality_info.get("is_harmful", False)

        if is_harmful is False and len(suggestions) == 0:
            while retry_count > 0:
                causality_info = pe.execute(
                    prompt=causality_prompt,
                    args={"why_prompt": req.why_prompt},
                    output_object=CausalityResponse,
                    description="Checking causality",
                )
                if len(causality_info.get("suggestions", [])) > 0:
                    suggestions = causality_info.get("suggestions", [])
                    for suggestion in suggestions:
                        check_product_request = CheckProductRequest(
                            why_prompt=suggestion
                        )
                        new_statement = check_for_product_strict(check_product_request)
                        new_statements.append(new_statement)
                    return FinalCausalityResponse(
                        is_causal=causality_info.get("is_causal", False),
                        suggestions=new_statements,
                    )
                else:
                    retry_count -= 1
                    logger.warning(
                        f"Retrying causality check, attempts left: {retry_count}"
                    )
            logger.warning(
                f"No suggestions returned after retries for prompt: {req.why_prompt}"
            )
            return FinalCausalityResponse(
                is_causal=causality_info.get("is_causal", False),
                suggestions=[],
            )
        else:
            return FinalCausalityResponse(
                is_causal=causality_info.get("is_causal", False),
                suggestions=causality_info.get("suggestions", []),
            )
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check causality: {str(e)}",
        )
