import math

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from sentry_sdk import capture_exception

from app.api.v2.helpers.latent_variables import (
    get_list_batches,
    get_llm_batch_results,
    json_dump_model_dict,
)
from app.api.v2.schemas.concept_testing import (
    CTStatementLikertOrSoftScoresLLMInput,
    CTStatementPercentageScoresLLMInput,
    CTStatementScoresDictType,
    CTStatementScoresRequest,
    CTStatementScoresResponse,
)
from app.api.v2.schemas.latent_variables import (
    ScoringType,
    ScoringTypeQuery,
    StatementLikertScores,
    StatementLikertScoresLLMOutput,
    StatementPercentageScores,
    StatementPercentageScoresLLMOutput,
    StatementSoftScores,
    StatementSoftScoresLLMOutput,
)
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


def generate_statement_scores(
    req: CTStatementScoresRequest,
    scoring_type: ScoringTypeQuery = ScoringType.SOFT,
) -> CTStatementScoresResponse:
    """Score concept testing statements with a single percentage or likert score or
    probability distribution.

    Args:
        req: see CTStatementScoresRequest

    Returns:
        CTStatementScoresResponse: All the information received, plus a percentage
        score or probability distribution for each measurement statement, depending
        on the :soft_scores flag.

    Raises:
        HTTPException 500: If an error occurs during generation
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model, max_tokens=8192)
    pb = PromptBuilder()

    match scoring_type:  # Select appropriate prompt based on score type
        case ScoringType.PERCENTAGE:
            statement_scores_prompt = (
                pb.concept_testing_statement_percentage_scores_prompt()
            )
        case ScoringType.LIKERT:
            statement_scores_prompt = (
                pb.concept_testing_statement_likert_scores_prompt()
            )
        case ScoringType.SOFT:
            statement_scores_prompt = pb.concept_testing_statement_soft_scores_prompt()

    statement_scores_dict: CTStatementScoresDictType = {
        persona.id: None for persona in req.personas
    }

    nb_batches = math.ceil(len(req.personas) / req.batch_size)

    for batch_idx, persona_batch in enumerate(
        get_list_batches(req.personas, batch_size=req.batch_size)
    ):
        logger.info(
            f"Concept testing statement scores: batch {batch_idx + 1}/{nb_batches}."
        )

        batch_statement_score_prompts_with_args = []

        llm_input_models: list[
            CTStatementPercentageScoresLLMInput | CTStatementLikertOrSoftScoresLLMInput
        ] = []

        for persona in persona_batch:
            match scoring_type:
                case ScoringType.PERCENTAGE:
                    llm_input_model = CTStatementPercentageScoresLLMInput.build(
                        req.statements_with_scales
                    )
                case _:
                    llm_input_model = CTStatementLikertOrSoftScoresLLMInput.build(
                        req.statements_with_scales
                    )

            llm_input_models.append(llm_input_model)

            args = {
                "description_prompt": req.description_prompt,
                "demographics": persona.demographics,
                "image": req.image,
                "statements_with_scale_labels": json_dump_model_dict(
                    llm_input_model.statements_with_scale_labels
                ),
            }
            batch_statement_score_prompts_with_args.append(
                statement_scores_prompt.format_prompt(**args)
            )

        match scoring_type:
            case ScoringType.PERCENTAGE:
                OutputType = StatementPercentageScoresLLMOutput
            case ScoringType.LIKERT:
                OutputType = StatementLikertScoresLLMOutput
            case ScoringType.SOFT:
                OutputType = StatementSoftScoresLLMOutput

        llm_batch_outputs = get_llm_batch_results(
            prompt_executor=pe,
            batch_prompts_with_args=batch_statement_score_prompts_with_args,
            output_model=OutputType,
            description="Batch-generating concept-testing statement scores",
        )
        for idx, llm_output in enumerate(llm_batch_outputs):
            persona = persona_batch[idx]
            if llm_output is not None:
                llm_input_model = llm_input_models[idx]
                match scoring_type:
                    case ScoringType.PERCENTAGE:
                        assert isinstance(
                            llm_input_model, CTStatementPercentageScoresLLMInput
                        )
                        llm_output_model = StatementPercentageScoresLLMOutput(
                            **llm_output
                        )
                        statement_scores = StatementPercentageScores.build(
                            llm_output_model,
                            llm_input_model.statements_with_scale_labels,
                        )
                    case ScoringType.LIKERT:
                        assert isinstance(
                            llm_input_model, CTStatementLikertOrSoftScoresLLMInput
                        )
                        llm_output_model = StatementLikertScoresLLMOutput(**llm_output)
                        statement_scores = StatementLikertScores.build(
                            llm_output_model,
                            llm_input_model.statements_with_scale_labels,
                        )
                    case ScoringType.SOFT:
                        assert isinstance(
                            llm_input_model, CTStatementLikertOrSoftScoresLLMInput
                        )
                        llm_output_model = StatementSoftScoresLLMOutput(**llm_output)
                        statement_scores = StatementSoftScores.build(
                            llm_output_model,
                            llm_input_model.statements_with_scale_labels,
                        )

                statement_scores_dict[persona.id] = statement_scores

    response = CTStatementScoresResponse.build(req, statement_scores_dict, scoring_type)

    logger.success(
        f"Statement scores generated successfully for prompt: {req.description_prompt}"
    )

    return response


@router.post("/concept-testing", response_model=CTStatementScoresResponse)
async def generate_concept_testing_statement_scores(
    req: CTStatementScoresRequest,
    scoring_type: ScoringTypeQuery = ScoringType.SOFT,
) -> CTStatementScoresResponse:
    try:
        response = generate_statement_scores(req, scoring_type=scoring_type)
        logger.success(
            "Statement scores generated successfully for prompt: "
            f"{req.description_prompt}"
        )

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500, detail=f"Failed to generate statement scores: {str(e)}."
        )
