import math
import time
from typing import cast

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import HTT<PERSON><PERSON>earer
from sentry_sdk import capture_exception

from app.api.v2.helpers.latent_variables import (
    get_list_batches,
    get_llm_batch_results,
    get_persona_trait_combinations,
    json_dump_model,
    json_dump_model_dict,
    json_dump_model_list,
)
from app.api.v2.schemas.latent_variables import (
    AdditionalNotesDictType,
    AdditionalNotesLLMOutput,
    AdditionalNotesRequest,
    AdditionalNotesResponse,
    FullRunRequest,
    FullRunResponse,
    GenerationLLMOutput,
    GenerationRequest,
    GenerationResponse,
    NLSummaryDictType,
    NLSummaryLLMOutput,
    NLSummaryRequest,
    NLSummaryResponse,
    PersonaWithTraitsDataM3,
    ScoringType,
    ScoringTypeQuery,
    StatementLikertOrSoftScoresLLMInput,
    StatementLikertScoresLLMOutput,
    StatementPercentageScoresLLMInput,
    StatementPercentageScoresLLMOutput,
    StatementScoresDictType,
    StatementScoresRequest,
    StatementScoresResponse,
    StatementSoftScoresLLMOutput,
    TraitStatementLikertScores,
    TraitStatementPercentageScores,
    TraitStatementSoftScores,
)
from app.api.v2.schemas.latent_variables_core import (
    TraitsLikertScaleDictType,
    TraitsPercentageScaleDictType,
)
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/generate", response_model=GenerationResponse)
async def generate_latent_variables(req: GenerationRequest) -> GenerationResponse:
    """Generate psychological traits with their measurement statements, pain points,
    and point detection statements based on a research context (why-prompt).

    Args:
        req: The request containing the research question (why-prompt) and
        configuration.

    Returns:
        GenerationResponse: The list of traits with their associated measurements,
        and the why prompt from which they were generated.

    Raises:
        HTTPException 500: If an error occurs during generation.
        HTTPException 422: Output validation failed.
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model, max_tokens=8192)
    pb = PromptBuilder()
    latent_variables_prompt = pb.latent_variables_generation_prompt()

    # TODO: Add tests
    try:
        llm_output = pe.execute(
            prompt=latent_variables_prompt,
            args={
                "why_prompt": req.why_prompt,
                "traits_count": req.traits_count,
                "measurement_count": req.measurement_count,
                "pain_point_count": req.pain_point_count,
                "scale_labels_count": req.scale_labels_count,
            },
            output_object=GenerationLLMOutput,
            description="Generating latent variables",
        )
        llm_output_model = GenerationLLMOutput(**llm_output)

        try:
            response = GenerationResponse.build(req, llm_output_model)
        except ValueError as exc:
            capture_exception()
            raise HTTPException(
                status_code=422, detail="Encountered data validation errors"
            ) from exc

        logger.success(
            f"Latent variables generated successfully for prompt: {req.why_prompt}"
        )

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500, detail=f"Failed to generate latent variables: {str(e)}."
        )


@router.post("/additional-notes", response_model=AdditionalNotesResponse)
async def generate_additional_notes(
    req: AdditionalNotesRequest,
) -> AdditionalNotesResponse:
    """Generate persona traits additional notes.

    Based on a list of traits and a list of personas, generate insights
    into how each trait is likely to manifest in each individual.

    Args:
        req: The information from GenerationResponse in generate_latent_variables(),
        and a list of individual demographic information (personas).

    Returns:
        AdditionalNotesResponse: For each persona and trait, the associated trait
        characteristics and behavioral tendencies, together with the remaining
        information in the request.

    Raises:
        HTTPException 500: If an error occurs during generation
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model, max_tokens=8192)
    pb = PromptBuilder()
    additional_notes_prompt = pb.additional_notes_prompt()

    try:
        start_time = time.time()
        # Initialize with None
        additional_notes_dict: AdditionalNotesDictType = {
            persona.id: {trait_data.trait: None}
            for persona in req.personas
            for trait_data in req.traits_data
        }

        total_combinations = len(req.personas) * len(req.traits_data)
        nb_batches = math.ceil(total_combinations / req.batch_size)

        # TODO: Try increasing batch size
        for batch_idx, persona_trait_batch in enumerate(
            get_persona_trait_combinations(
                req.personas, req.traits_data, batch_size=req.batch_size
            )
        ):
            logger.info(f"Additional notes: batch {batch_idx + 1}/{nb_batches}.")

            batch_additional_notes_prompts_with_args = []
            for persona, trait in persona_trait_batch:
                args = {
                    "why_prompt": req.why_prompt,
                    "demographics": persona.demographics,
                    "trait": trait,
                }
                batch_additional_notes_prompts_with_args.append(
                    additional_notes_prompt.format_prompt(**args)
                )

            llm_batch_outputs = get_llm_batch_results(
                prompt_executor=pe,
                batch_prompts_with_args=batch_additional_notes_prompts_with_args,
                output_model=AdditionalNotesLLMOutput,
                description="Batch-generating additional notes",
            )
            for idx, llm_output in enumerate(llm_batch_outputs):
                if llm_output is None:
                    additional_notes_dict[persona.id][trait] = None
                    continue

                persona, trait = persona_trait_batch[idx]
                additional_notes_dict[persona.id][trait] = AdditionalNotesLLMOutput(
                    **llm_output
                )

        response = AdditionalNotesResponse.build(req, additional_notes_dict)

        logger.success(
            f"Additional notes generated successfully for prompt: {req.why_prompt}"
        )
        elapsed_seconds = time.time() - start_time
        minutes, seconds = divmod(int(elapsed_seconds), 60)
        logger.info(f"Additional Notes took {minutes} min {seconds} sec to complete")

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500, detail=f"Failed to generate additional notes: {str(e)}."
        )


@router.post("/statement-scores", response_model=StatementScoresResponse)
async def generate_statement_scores(
    req: StatementScoresRequest,
    scoring_type: ScoringTypeQuery = ScoringType.SOFT,
) -> StatementScoresResponse:
    """Score trait measurement and pain point detection statements with a single
    score or a probability distribution.

    Receives a list of personas, traits and their measurements, and the additional
    notes. For each persona-trait combination, it assigns the percentage score (0-100),
    likert score (1 to n, where n is the endpoint of the likert scale),
    or probability distribution over the likert scale labels that corresponds to how
    the persona rates the trait measurement statements and pain point detection
    statements, based on the additional notes of how this trait is likely to manifest
    for a given individual.

    Args:
        req: The information in AdditionalNotesResponse (generate_additional_notes())

    Returns:
        StatementScoresResponse: All the information received, plus a percentage
        scale and score for each measurement statement.

    Raises:
        HTTPException 500: If an error occurs during generation
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model, max_tokens=8192)
    pb = PromptBuilder()

    match scoring_type:  # Select appropriate prompt based on score type
        case ScoringType.PERCENTAGE:
            statement_scores_prompt = pb.statement_percentage_scores_prompt()
        case ScoringType.LIKERT:
            statement_scores_prompt = pb.statement_likert_scores_prompt()
        case ScoringType.SOFT:
            statement_scores_prompt = pb.statement_soft_scores_prompt()

    try:
        start_time = time.time()
        traits_dict = req.get_traits_dict()
        additional_notes_dict = req.get_additional_notes_dict()
        personas = req.get_personas()

        # initialize with None
        statement_scores_dict: StatementScoresDictType = {
            persona.id: {trait: None} for persona in personas for trait in traits_dict
        }

        total_combinations = len(personas) * len(req.traits_data)
        nb_batches = math.ceil(total_combinations / req.batch_size)

        for batch_idx, persona_trait_batch in enumerate(
            get_persona_trait_combinations(
                personas, req.traits_data, batch_size=req.batch_size
            )
        ):
            logger.info(f"Statement scores: batch {batch_idx + 1}/{nb_batches}.")

            # Remove elements for which additional_notes is None (LLM failure)
            filtered_persona_trait_batch = [
                (persona, trait)
                for (persona, trait) in persona_trait_batch
                if additional_notes_dict[persona.id][trait] is not None
            ]

            batch_statement_score_prompts_with_args = []
            llm_input_models: list[
                StatementPercentageScoresLLMInput | StatementLikertOrSoftScoresLLMInput
            ] = []

            for persona, trait in filtered_persona_trait_batch:
                match scoring_type:
                    case ScoringType.PERCENTAGE:
                        llm_input_model = StatementPercentageScoresLLMInput.build(
                            persona, trait, traits_dict, additional_notes_dict
                        )
                    case _:
                        llm_input_model = StatementLikertOrSoftScoresLLMInput.build(
                            persona, trait, traits_dict, additional_notes_dict
                        )

                llm_input_models.append(llm_input_model)

                args = {
                    "demographics": llm_input_model.demographics,
                    "trait": llm_input_model.trait,
                    "statements_with_scale_labels": json_dump_model_dict(
                        llm_input_model.statements_with_scale_labels
                    ),
                    "additional_notes": json_dump_model(
                        llm_input_model.additional_notes
                    ),
                }
                batch_statement_score_prompts_with_args.append(
                    statement_scores_prompt.format_prompt(**args)
                )

            match scoring_type:
                case ScoringType.PERCENTAGE:
                    OutputType = StatementPercentageScoresLLMOutput
                case ScoringType.LIKERT:
                    OutputType = StatementLikertScoresLLMOutput
                case ScoringType.SOFT:
                    OutputType = StatementSoftScoresLLMOutput

            llm_batch_outputs = get_llm_batch_results(
                prompt_executor=pe,
                batch_prompts_with_args=batch_statement_score_prompts_with_args,
                output_model=OutputType,
                description="Batch-generating statement scores",
            )
            for idx, llm_output in enumerate(llm_batch_outputs):
                if llm_output is None:
                    statement_scores_dict[persona.id][trait] = None
                    continue

                llm_input_model = llm_input_models[idx]
                persona, trait = filtered_persona_trait_batch[idx]
                trait_with_measurements = traits_dict[trait]

                try:
                    match scoring_type:
                        case ScoringType.PERCENTAGE:
                            assert isinstance(
                                llm_input_model, StatementPercentageScoresLLMInput
                            )
                            llm_output_model = StatementPercentageScoresLLMOutput(
                                **llm_output
                            )
                            statement_scores = TraitStatementPercentageScores.build(
                                llm_output_model,
                                llm_input_model.statements_with_scale_labels,
                                trait_with_measurements,
                            )
                        case ScoringType.LIKERT:
                            assert isinstance(
                                llm_input_model, StatementLikertOrSoftScoresLLMInput
                            )
                            llm_output_model = StatementLikertScoresLLMOutput(
                                **llm_output
                            )
                            statement_scores = TraitStatementLikertScores.build(
                                llm_output_model,
                                llm_input_model.statements_with_scale_labels,
                                trait_with_measurements,
                            )
                        case ScoringType.SOFT:
                            assert isinstance(
                                llm_input_model, StatementLikertOrSoftScoresLLMInput
                            )
                            llm_output_model = StatementSoftScoresLLMOutput(
                                **llm_output
                            )
                            statement_scores = TraitStatementSoftScores.build(
                                llm_output_model,
                                llm_input_model.statements_with_scale_labels,
                                trait_with_measurements,
                            )

                    statement_scores_dict[persona.id][trait] = statement_scores

                except ValueError as exc:
                    capture_exception()
                    raise HTTPException(
                        status_code=422,
                        detail="Encountered data validation errors",
                    ) from exc

        response = StatementScoresResponse.build(
            req, statement_scores_dict, scoring_type
        )

        logger.success(
            f"Statement scores generated successfully for prompt: {req.why_prompt}"
        )
        elapsed_seconds = time.time() - start_time
        minutes, seconds = divmod(int(elapsed_seconds), 60)
        logger.info(f"Statement Scores took {minutes} min {seconds} sec to complete")

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500, detail=f"Failed to generate statement scores: {str(e)}."
        )


@router.post("/generate-nl-summary", response_model=NLSummaryResponse)
async def generate_natural_language_summary(req: NLSummaryRequest) -> NLSummaryResponse:
    """Generate natural language personas summary based on the traits data for each
    persona.

    Args:
        req: The information in StatementScoresResponse (generate_statement_scores())

    Returns:
        NLSummaryResponse: List of personas with demographics, natural language
        summarization of the persona trait information, and the remaining trait
        information.

    Raises:
        HTTPException 500: If an error occurs during generation.
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model, max_tokens=8192)
    pb = PromptBuilder()
    NL_summary_prompt = pb.persona_traits_NL_summary_prompt()

    try:
        start_time = time.time()
        traits_dict: TraitsLikertScaleDictType | TraitsPercentageScaleDictType = {
            trait_data.trait: trait_data for trait_data in req.traits_data
        }
        statement_scores_dict = req.get_statement_scores_dict()
        # prepare data by joining statements and scales with scores
        new_personas_with_traits_data: list[PersonaWithTraitsDataM3] = [
            PersonaWithTraitsDataM3.build(persona_wtd, traits_dict)
            for persona_wtd in req.personas_with_traits_data
        ]

        # initialize with None
        NL_summary_dict: NLSummaryDictType = {
            persona_wtd.id: None for persona_wtd in req.personas_with_traits_data
        }
        nb_batches = math.ceil(len(new_personas_with_traits_data) / req.batch_size)

        # persona_wtd = persona_with_traits_data
        for batch_idx, persona_wtd_batch in enumerate(
            get_list_batches(new_personas_with_traits_data, batch_size=req.batch_size)
        ):
            persona_wtd_batch = cast(list[PersonaWithTraitsDataM3], persona_wtd_batch)
            logger.info(f"Generate NL Summary: batch {batch_idx + 1}/{nb_batches}.")

            # Remove elements for which no statement scores were correctly produced
            filtered_persona_wtd_batch: list[PersonaWithTraitsDataM3] = []
            for persona_wtd in persona_wtd_batch:
                if any(
                    (
                        statement_scores_dict[persona_wtd.id][persona_td.trait]
                        is not None
                    )
                    for persona_td in persona_wtd.persona_traits_data
                ):
                    filtered_persona_wtd_batch.append(persona_wtd)

            batch_NL_summary_prompts_with_args = []
            for persona_wtd in filtered_persona_wtd_batch:
                exclude_pattern = persona_wtd.get_exclude_pattern()
                args = {
                    "why_prompt": req.why_prompt,
                    "demographics": persona_wtd.demographics,
                    "persona_traits_data": json_dump_model_list(
                        persona_wtd.persona_traits_data, exclude=exclude_pattern
                    ),
                }
                batch_NL_summary_prompts_with_args.append(
                    NL_summary_prompt.format_prompt(**args)
                )

            llm_batch_outputs = get_llm_batch_results(
                prompt_executor=pe,
                batch_prompts_with_args=batch_NL_summary_prompts_with_args,
                output_model=NLSummaryLLMOutput,
                description="Batch-generating NL persona-traits summary",
            )
            for persona_wtd, llm_output in zip(
                filtered_persona_wtd_batch, llm_batch_outputs
            ):
                if llm_output is None:
                    NL_summary_dict[persona_wtd.id] = None
                    continue

                NL_summary_dict[persona_wtd.id] = NLSummaryLLMOutput(**llm_output)

        response = NLSummaryResponse.build(
            req, NL_summary_dict, new_personas_with_traits_data
        )

        logger.success(
            "Natural language summary for persona traits data generated successfully "
            f"for prompt: {req.why_prompt}"
        )
        elapsed_seconds = time.time() - start_time
        minutes, seconds = divmod(int(elapsed_seconds), 60)
        logger.info(f"NL Summary took {minutes} min {seconds} sec to complete.")

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate natural language summary: {str(e)}.",
        )


@router.post("/full-run", response_model=FullRunResponse)
async def latent_variables_full_run(
    req: FullRunRequest, scoring_type: ScoringTypeQuery = ScoringType.SOFT
) -> FullRunResponse:
    """Full latent variables run.

    Args:
        req: The request containing the research question (why-prompt), the list of
        personas and configuration.

    Returns:
        FullRunResponse: Same as NLSummaryResponse.

    Raises:
        HTTPException 500: If an error occurs during generation.
    """
    try:
        start_time = time.time()
        generation_request = GenerationRequest(**req.model_dump())
        generation_response: GenerationResponse = await generate_latent_variables(
            generation_request
        )

        additional_notes_request: AdditionalNotesResponse = AdditionalNotesRequest(
            personas=req.personas,
            batch_size=req.batch_size,
            **generation_response.model_dump(),
        )
        additional_notes_response = await generate_additional_notes(
            additional_notes_request
        )

        statement_scores_request = StatementScoresRequest(
            batch_size=req.batch_size, **additional_notes_response.model_dump()
        )
        statement_scores_response: StatementScoresResponse = (
            await generate_statement_scores(
                statement_scores_request, scoring_type=scoring_type
            )
        )

        natural_language_summary_request = NLSummaryRequest(
            batch_size=req.batch_size, **statement_scores_response.model_dump()
        )
        response: NLSummaryResponse = await generate_natural_language_summary(
            natural_language_summary_request
        )

        logger.success(
            f"latent_variables_full_run() successfully run for prompt: {req.why_prompt}"
        )
        elapsed_seconds = time.time() - start_time
        minutes, seconds = divmod(int(elapsed_seconds), 60)
        logger.info(f"Latent Variables took {minutes} min {seconds} sec to complete.")

        return response

    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to run latent_variables_full_run(): {str(e)}.",
        )
