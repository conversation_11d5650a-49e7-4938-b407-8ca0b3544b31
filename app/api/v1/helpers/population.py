import copy
from typing import Optional

import numpy as np
import pandas as pd

from app.api.v1.schemas.population import PopulationRequest
from app.core.utils.constants import (
    AGE_LOWER_THRESHOLD,
    AGE_UPPER_THRESHOLD,
    INCOME_LOWER_THRESHOLD,
    INCOME_UPPER_THRESHOLD,
    numerical,
)
from app.core.utils.databases import get_supabase_client
from app.core.utils.logging import app_logger

logger = app_logger.get_logger(__name__)

INCOME_INTERVAL = 1000
AGE_INTERVAL = 1
EXPONENTIATED_SEARCH_RATIO = 0.1
MAX_RECORDS_TO_FETCH = 4000
CHANGE_AVG_THRESHOLD = 10
PAGE_SIZE = 500


class QueryConfig:
    def __init__(self, config, table_name, supabase_client):
        self.supabase_client = supabase_client
        self.table_name = table_name
        self.update_config(config)

    def __deepcopy__(self, memo):
        # Skip deepcopy of supabase_client, and reassign it to a new instance if needed
        new_copy = type(self)(
            copy.deepcopy(self.config, memo),  # Deepcopy only the config
            self.table_name,  # You can safely deepcopy other fields if needed
            self.supabase_client,  # Do not copy the supabase_client, or reassign it to a new instance
        )
        return new_copy

    # write method to convert config from enum to value when pulled in and whenever config set
    def _convert_config_to_values(self, config):
        """Convert all enum values in the config to their corresponding values."""
        for key, value in config.items():
            if isinstance(value, list):
                config[key] = [convertEnumToValue(item) for item in value]
            else:
                config[key] = convertEnumToValue(value)
        return config

    def _get_count(self, config):

        # Build the query based on the config
        query = self._build_query(config, False)

        # Execute the query to get the count
        response = query.execute()
        # Return the count of matching records
        return response.count

    def _get_data(self, config, limit=None):
        # Build the query based on the config
        count_response = self.count
        logger.info(f"Getting {count_response} records from Supabase")
        # Add a limit in how much data we can fetch
        if limit is not None and count_response > limit:
            total_records = limit
        else:
            total_records = count_response
        # Get the total number of records
        page_size = PAGE_SIZE
        total_pages = (
            total_records + page_size - 1
        ) // page_size  # Calculate total pages

        all_data = []
        # Create an array of random values for pagination
        if count_response <= page_size:
            random_offsets = [0]  # Only one page, start at the beginning
        else:
            random_offsets = np.sort(
                np.random.choice(
                    range(0, count_response, page_size), total_pages, replace=False
                )
            )
        # Scramble the random offsets
        np.random.shuffle(random_offsets)
        logger.info(f"Random offsets for pagination: {random_offsets}")

        # Iterate over each page
        for page in range(1, total_pages + 1):
            # Build the query again for the data retrieval
            logger.info(f"Fetching page {page} of {total_pages}")
            query = self._build_query(config, True)

            # Apply pagination using range() for each page
            start_index = int(random_offsets[page - 1])
            end_index = int(start_index + page_size)
            # Ensure the end index does not exceed the total records
            if end_index > count_response:
                end_index = count_response
            logger.info(f"Page {page} range: {start_index} to {end_index}")
            query = query.range(start_index, end_index)
            # Execute the query to get the data

            try:
                response = query.execute()

                # Append the data from the current page
                all_data.extend(response.data)
            except Exception as e:

                logger.info(f"Failed to fetch data for page {page}: {e}")
                logger.info("falling back to RPC")
                try:
                    response = self.call_query_ipums_get(
                        timeout=45, limit_rows=page_size, offset=start_index
                    )
                    all_data.extend(response)
                except Exception as rpc_error:
                    logger.info(f"RPC fallback failed for page {page}: {rpc_error}")

        return all_data

    def _build_query(self, config, get_data):
        count_accuracy = "exact"
        if config.get("state") is None:
            count_accuracy = "estimated"

        if get_data:
            query = self.supabase_client.table(self.table_name).select(
                "*", count=count_accuracy
            )
        else:
            query = (
                self.supabase_client.table(self.table_name)
                .select("id", count=count_accuracy)
                .limit(0)
            )

        # Check for 'state' string (using .eq() for exact match)
        if config.get("state") is not None:
            state = config["state"]
            query = query.eq("state", state)
        else:
            # Define major geographic regions and their corresponding states
            regions = {
                "Northeast": ["New York", "Pennsylvania", "Massachusetts"],
                "Midwest": ["Illinois", "Ohio", "Michigan"],
                "South": ["Texas", "Florida", "Georgia"],
                "West": ["California", "Washington", "Colorado"],
            }

            # Create a single array with one state from each region
            state_values = [state for states in regions.values() for state in states]
            query = query.in_("state", state_values)

        # Check for 'age' range using .ge() and .le()
        if config.get("age") is not None:
            min_age, max_age = config["age"]

            # Ensure the min_age and max_age are integers
            min_age = int(min_age)
            max_age = int(max_age)

            query = query.gte("age", min_age).lte("age", max_age)

        # Check for 'household_income' range using .ge() and .le()
        if config.get("household_income") is not None:
            min_income, max_income = config["household_income"]

            # Ensure the min_income and max_income are integers
            min_income = int(min_income)
            max_income = int(max_income)

            query = query.gte("household_income", min_income).lte(
                "household_income", max_income
            )

        # Check for 'gender' array (using .in_())
        if config.get("gender") is not None:
            gender_values = config["gender"]
            query = query.in_("gender", gender_values)

        # Check for 'education_level' array
        if config.get("education_level") is not None:
            education_values = config["education_level"]
            query = query.in_("education_level", education_values)

        # Check for 'racial_group' array (using .in_())
        if config.get("racial_group") is not None:
            racial_group_values = config["racial_group"]
            query = query.in_("racial_group", racial_group_values)

        # Check for 'number_of_children' array
        if config.get("number_of_children") is not None:
            children_values = config["number_of_children"]
            query = query.in_("number_of_children", children_values)

        return query

    def update_config(self, new_config):
        # Update the config
        self.config = self._convert_config_to_values(new_config)
        # Update the count based on the new config

        try:
            self.count = self._get_count(self.config)
        except Exception as e:
            logger.info(
                f"Failed to get count using _get_count: {e}. Falling back to"
                " call_query_ipums_count."
            )
            self.count = self.call_query_ipums_count(timeout=20)  # 20 second timeout

    def get_count(self):
        return self.count

    def get_config(self):
        return self.config

    def get_data(self, records_to_fetch=None):
        return self._get_data(self.config, records_to_fetch)

    def get_column_possible_values(self, column):
        """Get the unique values for a column with caching."""
        # Check if the values are cached
        # Query Supabase to get the distinct values for the column
        response = self.supabase_client.rpc(
            "get_unique_values",
            params={"p_table_name": "ipums", "p_column_name": column},
        ).execute()
        # Convert the response data to ensure each element is an int or string
        converted_data = response.data  # convert_to_type(response.data)
        # Cache the result for future use
        unique_values = sorted(converted_data)
        return unique_values

    def call_query_ipums_count(self, timeout=10):
        # Extract the parameters
        config = self.get_config()
        state = config.get("state")
        min_age, max_age = config.get("age", (None, None))
        min_income, max_income = config.get("household_income", (None, None))
        gender = config.get("gender", None)
        education_level = config.get("education_level", None)
        racial_group = config.get("racial_group", None)
        number_of_children = config.get("number_of_children", None)

        config_rpc = {
            "state": state,
            "min_age": min_age,
            "max_age": max_age,
            "min_income": min_income,
            "max_income": max_income,
            "gender": gender,
            "education_level": education_level,
            "racial_group": racial_group,
            "number_of_children": number_of_children,
            "timeout": timeout,  # Pass the timeout value
        }

        # Call the RPC function to get the count
        try:
            response = self.supabase_client.rpc(
                "query_ipums_count", config_rpc
            ).execute()

            # Handle the response
            return response.data
        except Exception as e:
            logger.info(f"Failed to execute RPC query_ipums_count: {e}")
            raise RuntimeError(
                "Error occurred while executing query_ipums_count RPC"
            ) from e

    def call_query_ipums_get(self, timeout=30, limit_rows=1000, offset=0):
        # Extract the parameters
        config = self.get_config()
        state = config.get("state")
        min_age, max_age = config.get("age", (None, None))
        min_income, max_income = config.get("household_income", (None, None))
        gender = config.get("gender", None)
        education_level = config.get("education_level", None)
        racial_group = config.get("racial_group", None)
        number_of_children = config.get("number_of_children", None)

        config_rpc = {
            "state": state,
            "min_age": min_age,
            "max_age": max_age,
            "min_income": min_income,
            "max_income": max_income,
            "gender": gender,
            "education_level": education_level,
            "racial_group": racial_group,
            "number_of_children": number_of_children,
            "timeout": timeout,  # Pass the timeout value
            "limit_rows": limit_rows,
            "offset_rows": offset,
        }
        try:
            response = self.supabase_client.rpc(
                "query_ipums_get_data", config_rpc
            ).execute()
            cleaned_data = _clean_for_json(response.data)
            return cleaned_data
        except Exception as e:
            logger.info(f"Failed to execute RPC query_ipums_get_data: {e}")
            raise RuntimeError(
                "Error occurred while executing query_get_data RPC"
            ) from e


def _clean_for_json(data):
    if isinstance(data, list):
        return [_clean_for_json(d) for d in data]
    elif isinstance(data, dict):
        return {k: _clean_for_json(v) for k, v in data.items()}
    elif isinstance(data, (np.integer, np.floating)):
        return data.item()
    elif hasattr(data, "tolist"):
        return data.tolist()
    return data


def expand_range_until_limit(query_config, key, limit):
    """
    Expand the range of a column gradually until the count exceeds the limit.

    Args:
    query_config: The QueryConfig object
    key: The key in the config to expand
    limit: The limit to exceed

    Returns:
    The updated QueryConfig object
    """
    # Make a copy of the query config object
    query_config_copy = copy.deepcopy(query_config)
    config = query_config_copy.get_config()
    if key in numerical:
        min_value, max_value = config[key]

        # FOR AGE
        lower_bound = AGE_LOWER_THRESHOLD
        upper_bound = AGE_UPPER_THRESHOLD
        interval = AGE_INTERVAL

        if key == "household_income":
            lower_bound = INCOME_LOWER_THRESHOLD
            upper_bound = INCOME_UPPER_THRESHOLD
            interval = INCOME_INTERVAL

        original_interval = interval
        query_count = 0
        age_range = max_value - min_value + 1
        density = query_config_copy.get_count() / (age_range / interval)
        density = np.ceil(density)

        if key == "household_income":
            income_range = max_value - min_value
            density = query_config_copy.get_count() / (income_range / interval)
            density = np.ceil(density)

        # if density is 0, use default interval
        if density != 0:
            # try to guess how many more people needed using avg density
            density_to_split_between_max_min = 2
            interval = int(
                interval
                * np.ceil(
                    ((limit - query_config_copy.get_count()))
                    / density
                    / density_to_split_between_max_min
                )
            )

        change_rolling_five = []
        while query_config_copy.get_count() <= limit:
            prev_count = query_config_copy.get_count()
            min_value, min_reached = safe_decrease_value(
                min_value, lower_bound, interval
            )
            max_value, max_reached = safe_increase_value(
                max_value, upper_bound, interval
            )

            config[key] = [min_value, max_value]
            query_config_copy.update_config(config)
            query_count += 1

            if min_reached and max_reached:
                break
            current_count = query_config_copy.get_count()

            if len(change_rolling_five) >= 5:
                change_rolling_five.pop(0)
            change_rolling_five.append(current_count - prev_count)
            len_rolling_five = len(change_rolling_five)

            if len_rolling_five == 0:
                continue

            if (abs(current_count - limit) > limit * EXPONENTIATED_SEARCH_RATIO) or (
                sum(change_rolling_five) / len_rolling_five < CHANGE_AVG_THRESHOLD
            ):  # if far from goal or change avg is low
                interval = interval * 2
            else:
                interval = original_interval
    else:
        possible_values = query_config_copy.get_column_possible_values(key)
        current_values = set(config[key])
        remaining_values = [v for v in possible_values if v not in current_values]
        for value in remaining_values:
            current_values.add(value)
            config[key] = list(current_values)
            query_config_copy.update_config(config)

            if query_config_copy.get_count() >= limit:
                break

    return query_config_copy


def safe_decrease_value(value, lower_bound, interval):
    new_value = max(value - interval, lower_bound)
    return new_value, new_value == lower_bound


def safe_increase_value(value, upper_bound, interval):
    new_value = min(value + interval, upper_bound)
    return new_value, new_value == upper_bound


def create_population(
    request: PopulationRequest,
    limit: Optional[int] = None,
) -> pd.DataFrame:
    db = get_supabase_client()
    if request["age"] is None or request["age"] == []:
        request["age"] = [18, 100]
    if request["age"][0] is None:
        request["age"][0] = 18
    if request["age"][1] is None:
        request["age"][1] = 100
    if request["household_income"] is None or request["household_income"] == []:
        request["household_income"] = [0, 3_000_000]
    if request["household_income"][0] is None:
        request["household_income"][0] = 0
    if request["household_income"][1] is None:
        request["household_income"][1] = 3_000_000
    config = {
        "age": [request["age"][0], request["age"][1]],
        "number_of_children": request["number_of_children"],
        "education_level": request["education_level"],
        "household_income": [
            request["household_income"][0],
            request["household_income"][1],
        ],
        "racial_group": request["racial_group"],
        "gender": request["gender"],
        "state": request["state"],
    }
    query_config = QueryConfig(config, "ipums", db)
    fetch_limit = MAX_RECORDS_TO_FETCH
    if limit is not None:
        fetch_limit = limit
    res_data = query_config.get_data(fetch_limit)
    df_res = pd.DataFrame(res_data)
    df_res["ID"] = range(1, len(df_res) + 1)
    return df_res


def convertTraitLevelEnumsToValue(trait_levels):
    """Convert enum values to their string representation."""
    if trait_levels is None:
        return None
    if isinstance(trait_levels, list):
        return [convertEnumToValue(item) for item in trait_levels]
    return convertEnumToValue(trait_levels)


def convertEnumToValue(data):
    """Convert a single enum value to its string representation."""
    if hasattr(data, "value"):
        return data.value
    return data
