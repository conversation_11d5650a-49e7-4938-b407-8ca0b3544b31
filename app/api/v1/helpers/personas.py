import asyncio
import itertools
import os
import tempfile
from collections import defaultdict
from concurrent.futures import Thr<PERSON>PoolExecutor
from contextvars import copy_context
from dataclasses import dataclass
from functools import partial
from typing import Dict, List, Optional, Union

import numpy as np
import uuid6
from fastapi import HTT<PERSON>Exception
from langchain_community.utils.math import cosine_similarity
from langchain_openai import OpenAIEmbeddings
from langsmith import traceable
from pydantic import BaseModel, ValidationError
from pymupdf import pymupdf
from sentry_sdk import capture_exception
from uuid6 import UUID

import app.core.utils.databases as databases
from app.api.v1.schemas.personas import (
    ConstraintRequest,
    ConstraintResponse,
    HistoryRequest,
    HistoryResponse,
    MotivationRequest,
    MotivationResponse,
    PersonasResponse,
    QuestionList,
    QuestionResponse,
)
from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel
from app.llm_prompt.prompt_builder import Prompt<PERSON>uilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

logger = app_logger.get_logger(__name__)


class TraitLevel(BaseModel):
    id: str
    set_type: Optional[str]  # Big Five, 8 Values
    type: str  # religion
    # Not an actual enum type because of Firestore serialization issues.
    measurement_type: str  # enum: nominal, ordinal, interval, ratio
    ordinal_rank: Optional[int]
    short_description: str  # Satanist
    long_description: str  # My religion is Satanism

    class Config:
        from_attributes = True


@dataclass
class Persona:
    id: UUID
    traits: list[TraitLevel]


# traits_to_poc_format converts the given dictionary of traits into the PoC format, to be consumed downstream.
# Example input format: ('age' -> ['10-15', '16-20', ...])
def traits_to_poc_format(
    traits: dict[str, list[TraitLevel]],
) -> dict[str, dict[int, tuple[str, str]]]:
    """
    Converts traits to a specific format.

    This function takes a dictionary of traits, where each trait is a list of TraitLevel objects,
    and converts it to a dictionary where each trait is a dictionary mapping from level id to a tuple
    of short and long descriptions.

    Args:
        traits: A dictionary where the keys are trait names and the values are lists of TraitLevel objects.

    Returns:
        A dictionary where the keys are trait names and the values are dictionaries. Each inner dictionary
        maps from level id to a tuple of short and long descriptions.
    """
    m = {}
    for trait_name, levels in traits.items():
        levels = sorted(levels, key=lambda l: l.ordinal_rank if l.ordinal_rank else 0)
        levels = list(
            filter(
                lambda level: (
                    (
                        level.ordinal_rank + 1
                        if level.ordinal_rank is not None
                        else level.ordinal_rank
                    )
                    if level.measurement_type == "ordinal"
                    else True
                ),
                levels,
            )
        )
        m[trait_name] = {
            level.id: (
                f"{level.type.capitalize()}-{level.short_description}",
                level.long_description,
            )
            for level in levels
        }
    return m


# personas_to_poc_formats converts a list of Personas to the existing PoC format, to be consumed downstream.
def personas_to_poc_formats(personas: list[Persona]) -> tuple:
    all_combinations_index = [tuple(trait.id for trait in p.traits) for p in personas]
    persona_trait_descriptions = [
        [t.long_description for t in persona.traits] for persona in personas
    ]
    personas_full_string_definition = [
        ". ".join(descriptions) for descriptions in persona_trait_descriptions
    ]
    return all_combinations_index, personas_full_string_definition


# TODO: Some permutations here are impossible, e.g. "I am under 5. I have a PhD.". How can we eliminate those?
def choose_trait_levels(
    trait_levels: defaultdict[str, list[TraitLevel]], levels_per_trait: int
) -> dict[str, list[TraitLevel]]:
    chosen_traits = defaultdict(list)
    for trait, levels in trait_levels.items():
        # Need to sort levels per trait, can't rely on database / pre-existing sort order.
        levels = sorted(levels, key=lambda l: l.ordinal_rank if l.ordinal_rank else 0)
        # Business logic - if a trait level's ordinal rank is None, we filter it out. This lets us specify in Firestore
        # certain trait levels to be ignored, e.g. "I am under 5" for age.
        levels = list(
            filter(
                lambda level: (
                    (
                        level.ordinal_rank + 1
                        if level.ordinal_rank is not None
                        else level.ordinal_rank
                    )
                    if level.measurement_type == "ordinal"
                    else True
                ),
                levels,
            )
        )
        level_count = len(levels)
        # Ensure we don't exceed available levels.
        levels_per_trait_clamped = min(levels_per_trait, level_count)
        if levels and levels[0].measurement_type == "ordinal":
            # Special case: select middle trait if we only want one.
            if levels_per_trait_clamped == 1:
                chosen_traits[trait] = [levels[level_count // 2]]
            # Default: use numpy to select N indexes, evenly spaced in a range, e.g. 3 within [0, 10] -> [0, 5, 10]
            else:
                ixes = np.round(
                    np.linspace(0, level_count - 1, levels_per_trait_clamped)
                ).astype(int)
                chosen_traits[trait] = [levels[ix] for ix in ixes]
        else:  # Is not ordinal, e.g. nominal.
            chosen_traits[trait] = levels[:levels_per_trait_clamped]
    return chosen_traits


def generate_personas(levels_per_trait: int, trait_keys: list[str]):
    full_trait_levels = defaultdict(list)
    for item in databases.get_firebase_collection("traits").stream():
        t = TraitLevel(**item.to_dict() | {"id": item.id})
        if t.type in trait_keys:
            full_trait_levels[t.type].append(t)

    chosen_levels = choose_trait_levels(full_trait_levels, levels_per_trait)

    traits_product = itertools.product(
        *chosen_levels.values()
    )  # A cartesian product generator of traits
    personas_list = [
        Persona(id=uuid6.uuid7(), traits=list(ts)) for ts in traits_product
    ]
    traits_lookup_map = traits_to_poc_format(full_trait_levels)
    all_combinations_index, personas_full_string_definition = personas_to_poc_formats(
        personas_list
    )
    return all_combinations_index, traits_lookup_map, personas_full_string_definition


@traceable(
    run_type="chain",
    name="History API",
    tags=["Personas", "History", "Get History"],
    metadata={"Personas": "History"},
)
def get_history(
    req: HistoryRequest,
) -> HistoryResponse:

    pe = LCELPromptExecutor(llm_model=req.llm_model)

    prompt_builder = PromptBuilder()
    attribute_prioritization_prompt = prompt_builder.history_prompt()

    try:
        response = pe.execute(
            prompt=attribute_prioritization_prompt,
            args={
                "why_prompt": req.why_prompt,
                "user_profile_attributes": req.user_profile_attributes,
                "attributes": req.attributes,
            },
            output_object=HistoryResponse,
        )

        return HistoryResponse(**response)

    except ValidationError as ve:
        capture_exception(ve)
        raise HTTPException(
            status_code=422,
            detail=f"Validation error in History response: {str(ve)}",
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get History : {str(e)}",
        )


@traceable(
    run_type="chain",
    name="Constraint API",
    tags=[
        "Personas",
        "Constraints",
        "Get Constraints",
    ],
    metadata={"Personas": "Constraints"},
)
def get_constraints(
    req: ConstraintRequest,
) -> ConstraintResponse:

    pe = LCELPromptExecutor(llm_model=req.llm_model)

    prompt_builder = PromptBuilder()
    attribute_prioritization_prompt = prompt_builder.constraint_prompt()

    try:
        response = pe.execute(
            prompt=attribute_prioritization_prompt,
            args={
                "why_prompt": req.why_prompt,
                "user_profile_attributes": req.user_profile_attributes,
                "attributes": req.attributes,
            },
            output_object=ConstraintResponse,
        )
        return ConstraintResponse(**response)

    except ValidationError as ve:
        capture_exception(ve)
        raise HTTPException(
            status_code=422,
            detail=f"Validation error in Constraints response: {str(ve)}",
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get Constraints : {str(e)}",
        )


def sort_dict_by_orthogonality(input_dict: Dict[str, str]) -> Dict[str, str]:
    embedding_model = OpenAIEmbeddings(model="text-embedding-3-small")
    combined_key_value = [f"{key} :- {value}" for key, value in input_dict.items()]
    combined_embeddings = embedding_model.embed_documents(combined_key_value)
    similarity_matrix = cosine_similarity(combined_embeddings, combined_embeddings)
    similarity = np.var(similarity_matrix, axis=1)
    key_similarity_mapping = {
        list(input_dict.keys())[i]: similarity[i] for i in range(len(input_dict))
    }
    sorted_keys = sorted(
        input_dict.keys(), key=lambda key: key_similarity_mapping[key], reverse=True
    )
    return {key: input_dict[key] for key in sorted_keys}


def get_orthogonal_dimensions(
    request: Union[HistoryResponse, ConstraintResponse, MotivationResponse]
) -> Union[HistoryResponse, ConstraintResponse, MotivationResponse]:
    """
    Generate orthogonal history levels by assessing the orthogonality of both keys and values.

    Args:
        request Union[HistoryResponse, ConstraintResponse, MotivationResponse]: The request object containing the initial dimensions of the model.

    Returns:
        Union[HistoryResponse, ConstraintResponse, MotivationResponse]: Sorted dimensions based on orthogonality.
    """

    if isinstance(request, HistoryResponse):
        sorted_dict = sort_dict_by_orthogonality(request.history)
        return HistoryResponse(history=sorted_dict)
    elif isinstance(request, ConstraintResponse):
        sorted_dict = sort_dict_by_orthogonality(request.constraints)
        return ConstraintResponse(constraints=sorted_dict)
    elif isinstance(request, MotivationResponse):
        sorted_dict = sort_dict_by_orthogonality(request.motivation)
        return MotivationResponse(constraints=sorted_dict)
    else:
        logger.error(
            "Invalid input type - Expected HistoryResponse or ConstraintResponse or"
            f" MotivationResponse: {type(request)}"
        )
        raise ValueError(
            "Invalid input type. Expected HistoryResponse or ConstraintResponse."
        )


async def run_in_threadpool(func, *args, **kwargs):
    loop = asyncio.get_running_loop()
    context = copy_context()

    def wrapped_func(*args, **kwargs):
        # runs the original function with copied context - makes sure that whenever a
        # new thread is created it has access to all the context variables for tracing
        return context.run(func, *args, **kwargs)

    with ThreadPoolExecutor() as executor:
        return await loop.run_in_executor(
            executor, partial(wrapped_func, *args, **kwargs)
        )


@traceable(
    run_type="chain",
    name="Dimension wise question generation",
    tags=["Personas", "Responses"],
    metadata={"Personas": "QnA"},
)
async def get_question_for_dimension(
    pe: LCELPromptExecutor,
    prompt: str,
    why_prompt: str,
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]],
    model: str,
    dimension: str,
) -> QuestionList:
    try:
        response = await run_in_threadpool(
            pe.execute,
            prompt=prompt,
            args={
                "why_prompt": why_prompt,
                "user_profile_attributes": user_profile_attributes,
                "model": model,
                "dimension": dimension,
            },
            output_object=QuestionList,
            description=(
                f"Generating questions for Model - {model} and Dimension - {dimension}"
            ),
        )
        logger.success(f"Successfully generated QnA for {model} - {dimension}")
        return QuestionList(root=response)
    except Exception as e:
        logger.error(f"Error generating QnA for {model} - {dimension}: {str(e)}")
        return None


@traceable(
    run_type="chain",
    name="Question Generation",
    tags=["Personas", "Question", "Responses"],
    metadata={"Personas": "QnA"},
)
async def get_question(
    why_prompt: str,
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]],
    model_dimensions: Dict[str, List[str]],
    llm_model: Optional[str] = "gpt4",
) -> QuestionResponse:
    pe = LCELPromptExecutor(llm_model=llm_model)
    prompt_builder = PromptBuilder()
    prompt = prompt_builder.question_generation_prompt()
    master_mapping = {}

    try:
        tasks = []
        for model, dimensions_list in model_dimensions.items():
            for dimension in dimensions_list:
                task = asyncio.create_task(
                    get_question_for_dimension(
                        pe,
                        prompt,
                        why_prompt,
                        user_profile_attributes,
                        model,
                        dimension,
                    )
                )
                tasks.append((model, dimension, task))

        for model, dimension, task in tasks:
            try:
                result = await task
                if result:
                    if model not in master_mapping:
                        master_mapping[model] = {}
                    master_mapping[model][dimension] = result
            except Exception as exc:
                print(f"{model} - {dimension} generated an exception: {exc}")

        if not master_mapping:
            raise ValueError("No questions were generated")

        result = QuestionResponse(**master_mapping)
        return result
    except Exception as e:
        capture_exception(e)
        logger.error(f"Failed to generate questions for {model}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate questions for {model}: {str(e)}",
        )


@traceable(
    run_type="chain",
    name="Motivation API",
    tags=[
        "Personas",
        "Motivation",
        "Get Motivation",
    ],
    metadata={"Personas": "Motivation"},
)
def get_motivation(
    req: MotivationRequest,
) -> MotivationResponse:
    pe = LCELPromptExecutor(llm_model=req.llm_model)
    prompt_builder = PromptBuilder()
    motivation_prompt = prompt_builder.motivation_prompt()
    try:
        response = pe.execute(
            prompt=motivation_prompt,
            args={
                "why_prompt": req.why_prompt,
                "user_profile_attributes": req.user_profile_attributes,
                "attributes": req.attributes,
                "country": req.country,
                "num_questions": req.num_questions,
            },
            output_object=MotivationResponse,
        )
        return MotivationResponse(**response)

    except Exception as e:
        capture_exception(e)
        logger.error(f"Failed to get motivation : {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get motivation : {str(e)}",
        )


async def extract_personas_from_pdf(file) -> PersonasResponse:
    """
    Extracts personas from a PDF file and returns the extracted personas.
    """
    pe = LCELPromptExecutor(llm_model=LLMModel.AZURE_GPTO3_MINI)
    pb = PromptBuilder()
    prompt = pb.get_persona_extraction_prompt()
    persona_str = extract_text_from_pdf(file)
    logger.info("Extracting personas from PDF.")
    try:
        response = pe.execute(
            prompt=prompt,
            args={"text": persona_str},
            output_object=PersonasResponse,
            description="Extracting personas from PDF",
        )
        logger.info("Successfully extracted personas from PDF.")
        personas = response.get("personas", [])
        if not personas:
            raise ValueError("No personas found in the PDF.")
        return PersonasResponse(personas=personas)
    except Exception as e:
        logger.error(f"Error extracting personas from PDF: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting personas from PDF: {str(e)}",
        )


async def extract_text_from_pdf(file) -> str:
    """
    Extracts text from a PDF file and returns the extracted text.
    """
    try:
        logger.info("Extracting text from PDF.")
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(file.file.read())
            tmp_file_path = tmp_file.name

        pdf_document = pymupdf.open(tmp_file_path)
        text_data = []
        for page_number in range(pdf_document.page_count):
            page = pdf_document[page_number]
            text = page.get_text("text")
            text_data.append(text)
        pdf_document.close()
    finally:
        logger.info("Cleaning up temporary PDF file.")
        if os.path.exists(tmp_file_path):
            os.remove(tmp_file_path)
    logger.info("Successfully extracted text from PDF.")
    return "".join([data for data in text_data])
