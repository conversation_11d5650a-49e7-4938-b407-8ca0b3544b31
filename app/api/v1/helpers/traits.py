from typing import Dict, List, Set

from app.api.v1.schemas.traits import (
    NaturalLanguageTraitResponse,
    TraitLevelsSuggestionResponse,
    TraitSuggestionResponse,
)
from app.core.utils.common import chunk_list
from app.core.utils.type_enums import LLMModel
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor


def get_suggested_traits(
    why_prompt: str,
    selected_traits: List[str],
    number_of_new_traits: int,
    number_of_levels_per_trait: int,
    llm_model: LLMModel,
    max_length: int,
    country: str,
) -> Dict[str, Set[str]]:
    """
    Get trait suggestions with their possible levels based on why prompt and selected traits.

    Args:
        why_prompt: The use case explanation
        selected_traits: List of already selected trait names

    Returns:
        Dictionary mapping trait names to their possible levels
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.suggest_traits()
    args = {
        "why_prompt": why_prompt,
        "selected_traits": selected_traits,
        "number_of_new_traits": number_of_new_traits,
        "number_of_levels_per_trait": number_of_levels_per_trait,
        "country": country,
        "max_length": max_length,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=TraitSuggestionResponse,
        description="Getting suggested traits",
    )
    return response.get("suggested_traits", {})


def get_suggested_trait_levels(
    why_prompt: str,
    existing_traits: List[str],
    levels_count: int,
    new_trait: str,
    max_length: int,
    country: str,
    llm_model: LLMModel,
) -> TraitLevelsSuggestionResponse:
    """Get levels for a given trait."""
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.suggest_trait_levels()
    args = {
        "why_prompt": why_prompt,
        "existing_traits": existing_traits,
        "new_trait": new_trait,
        "levels_count": levels_count,
        "max_length": max_length,
        "country": country,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=TraitLevelsSuggestionResponse,
        description="Getting levels for a given trait",
    )
    return TraitLevelsSuggestionResponse(
        trait=response.get("trait", ""), levels=response.get("levels", [])
    )


def get_trait_language_level_description(traits: list[dict]) -> dict:
    """Get the description for a given trait level in batches."""
    pe = LCELPromptExecutor(llm_model=LLMModel.GCP_SONNET)
    pb = PromptBuilder()
    prompt = pb.get_natural_language_trait_prompt()

    batched_traits = list(chunk_list(traits, 5))

    prompts_with_args = [
        prompt.format_prompt(demo_variables=batch) for batch in batched_traits
    ]

    responses = pe.batch_execute(
        prompts=prompts_with_args,
        args=None,
        output_object=NaturalLanguageTraitResponse,
        description="Batch-Retrieving trait level descriptions",
    )

    mappings = {}
    for response in responses:
        traits = response.get("traits", [])
        for trait in traits:
            mappings[trait.get("variable")] = {
                "demo": trait.get("demo"),
                "guide": trait.get("guide"),
            }

    return mappings
