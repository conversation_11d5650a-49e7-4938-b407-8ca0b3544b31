import csv
import json
import os
import time
from io import String<PERSON>
from typing import Any, Literal, Optional

import firebase_admin
import pandas as pd
import requests
from fastapi import HTTPException
from firebase_admin import credentials, firestore
from langgraph.graph import END, START, StateGraph
from langsmith import trace
from pydantic import BaseModel
from sentry_sdk import capture_exception

from app.api.v1.schemas.experiments import (
    CausalityCheckResponse,
    ChildrenEnum,
    EducationLevelEnum,
    Experiment,
    ExperimentIdea,
    ExperimentIdeationResponse,
    GenderEnum,
    RacialGroupEnum,
    RealWorldConjointStatementResponse,
)
from app.core.controller import execute
from app.core.experiment_config import ExperimentConfig
from app.core.global_config import settings
from app.core.utils.common import (
    cleanup_old_wandb_runs,
    delete_local_dir,
    delete_subfolders,
)
from app.core.utils.constants import context_message
from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel
from app.core.utils.wandb_api import (
    HumanBaselineDefinitionError,
    MissingHumanBaselineDefinition,
    WandbAPI,
)
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

log = app_logger.get_logger(__name__)


class ExperimentLimitChecker:
    """
    Check if user can run a new experiment.
    Allow employees, admins, and customers to run experiments.

    For free users:
    1. If WandB completed runs >= 2: deny
    2. If WandB < 2: check Firestore, if count > 2: deny
    3. Otherwise: allow
    """

    def __init__(self):
        if not firebase_admin._apps:
            cred = credentials.Certificate(
                json.loads(os.getenv("FIREBASE_CONFIG_WHY_EARTH"))
            )
            firebase_admin.initialize_app(cred)
        self.db = firestore.client()
        self.collection = self.db.collection("free_user_experiment_counts")

        self.wandb_api = WandbAPI()
        self.FREE_TIER_LIMIT = 2
        self.EXEMPT_ROLES = {"admin", "employee", "customer"}

    def can_run_experiment(self, user_token: dict) -> bool:
        """
        Check if user can run a new experiment.
        For free users:
        1. If WandB completed runs >= 2: deny
        2. If WandB < 2: check Firestore, if count > 2: deny
        3. Otherwise: allow
        """
        user_roles = set(user_token.get("https://www.api.subconscious.ai/roles", []))
        if user_roles & self.EXEMPT_ROLES:
            return True

        user_id = user_token["sub"]

        wandb_filters = {"config.user": {"$eq": user_id}}
        completed_experiments = self.wandb_api.get_wandb_runs(filters=wandb_filters)

        if len(completed_experiments) >= self.FREE_TIER_LIMIT:
            return False

        doc = self.collection.document(user_id).get()
        if doc.exists and doc.get("count", 0) >= self.FREE_TIER_LIMIT:
            return False

        return True

    def record_experiment(self, user_token: dict) -> None:
        """Record experiment run for free users."""
        user_roles = set(user_token.get("https://www.api.subconscious.ai/roles", []))
        if not user_roles & self.EXEMPT_ROLES:
            user_id = user_token["sub"]
            doc_ref = self.collection.document(user_id)
            if not doc_ref.get().exists:
                doc_ref.set({"count": 1})
            else:
                doc_ref.set({"count": self.db.increment(1)}, merge=True)


class ExperimentRunner:
    def __init__(
        self,
        expr_conf: ExperimentConfig,
        tags: list,
        user_id: str = "",
        wandb_id: str = "",
        wandb_name: str = "",
    ):
        self.start_time = None
        self.expr = expr_conf
        if settings.USE_WANDB_STORAGE:
            log.info("Creating new wandb run")
            self.expr.make_wandb_run(
                tags=tags,
                user_id=user_id,
                id=wandb_id,
                name=wandb_name,
                is_hb_run=self.expr.is_hb_run,
            )

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # log total time
        assert self.start_time is not None
        elapsed_seconds = time.time() - self.start_time
        minutes, seconds = divmod(int(elapsed_seconds), 60)
        log.info(f"Experiment took {minutes} min {seconds} sec to complete")

        if self.expr.wandb_run:
            if exc_type and "TimeLimitExceeded" in exc_type.__name__:
                log.warning("Experiment timed out, marking as failed")
                self.expr.wandb_run.mark_as_failed(reason="timeout")
            else:
                log.info("Finishing wandb run")
                self.expr.wandb_run.finish_run()

            delete_subfolders(os.path.join(settings.EXPERIMENT_DATA_FOLDER_NAME, "tmp"))
            wandb_dir = os.path.join(settings.EXPERIMENT_DATA_FOLDER_NAME, "wandb")
            cleanup_old_wandb_runs(wandb_dir, days=1)
            delete_local_dir(
                os.path.join(settings.PROJECT_ROOT_DIRECTORY, "app", "logs"),
                "analytics",
            )


def run_experiment(
    experiment: Experiment,
    user_id: str = "",
    wandb_id: str = "",
    wandb_name: str = "",
):
    # TODO: this workflow needs to be changed.
    #  currently, even if we have error it still returns wandb name and run name which is not correct.
    #  add proper status code and error messages.
    try:
        expr_conf = ExperimentConfig(
            experiment=experiment,
            use_api=True,
        )
        tags = [
            f"exp_model_{experiment.expr_llm_model.value}",
            f"dv_model_{experiment.dv_llm_model.value}",
            f"lv_model_{experiment.levels_llm_model.value}",
        ]

        if expr_conf.hb_run_id:
            expr_conf.add_prefix_to_title("hb")
            tags.extend([expr_conf.title, "replication_study"])

        with ExperimentRunner(
            expr_conf,
            user_id=user_id,
            tags=tags,
            wandb_id=wandb_id,
            wandb_name=wandb_name,
        ):
            execute(expr_conf)
    except Exception as e:
        capture_exception(e)
        log.error(
            f"Error in running experiment for wandb_id {wandb_id}: {str(e)}",
            exc_info=True,
        )
        raise e


def get_hb_experiment_definition_from_wandb(
    hb_run_id: str,
    expr_llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> Experiment:
    """Fetch and process experiment definition from Weights & Biases using the run ID."""
    try:
        exper_def_path = WandbAPI().get_wandb_experiment_definition(hb_run_id)
        with open(exper_def_path, mode="r") as file:
            data = json.load(file)
        return process_experiment_data(
            raw_data=data, hb_run_id=hb_run_id, expr_llm_model=expr_llm_model
        )
    except MissingHumanBaselineDefinition as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"{e}")
    except Exception as e:
        capture_exception(e)
        log.error(
            f"Error fetching experiment definition from Weights & Biases: {str(e)}"
        )
        raise HumanBaselineDefinitionError(
            "Failed to load experiment definition from Weights & Biases."
        )


def get_hb_experiment_definition_from_github(
    hb_folder: str,
    expr_llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH,
) -> Experiment:
    """Fetch and process experiment definition from GitHub."""
    try:
        raw_data = get_raw_definition_from_github(hb_folder)
        return process_experiment_data(
            raw_data=raw_data,
            hb_folder=hb_folder,
            expr_llm_model=expr_llm_model,
        )
    except MissingHumanBaselineDefinition as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"{e}")
    except Exception as e:
        capture_exception(e)
        log.error(f"Error fetching experiment definition from GitHub: {str(e)}")
        raise HumanBaselineDefinitionError(
            "Failed to load experiment definition from GitHub."
        )


def get_raw_definition_from_github(hb_folder: str) -> dict:
    """Fetch the raw experiment definition JSON from GitHub."""
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/{settings.TRANSCRIPTIONS_PATH}/{hb_folder}"
    headers = {
        "Authorization": f"token {settings.GITHUB_PAT}",
        "Accept": "application/json",
    }

    response = requests.get(github_api_url, headers=headers)

    if response.status_code != 200:
        raise Exception(f"Failed to access GitHub folder: {hb_folder}")

    contents = response.json()
    for file_info in contents:
        if file_info["name"].endswith(".json"):
            file_response = requests.get(file_info["download_url"], headers=headers)
            if file_response.status_code == 200:
                return file_response.json()

    raise Exception(f"No JSON file (HB definition) found in GitHub folder: {hb_folder}")


def get_hb_amce_from_github(hb_folder: str) -> dict:
    """Fetch the raw experiment definition JSON from GitHub."""
    github_api_url = f"https://api.github.com/repos/{settings.GITHUB_OWNER}/{settings.GITHUB_HB_REPO}/contents/{settings.TRANSCRIPTIONS_PATH}/{hb_folder}"
    headers = {
        "Authorization": f"token {settings.GITHUB_PAT}",
        "Accept": "application/json",
    }

    response = requests.get(github_api_url, headers=headers)

    if response.status_code != 200:
        raise Exception(f"Failed to access GitHub folder: {hb_folder}")

    contents = response.json()
    for file_info in contents:
        if file_info["name"].endswith(".csv"):
            file_response = requests.get(file_info["download_url"], headers=headers)
            if file_response.status_code == 200:
                csv_data = StringIO(file_response.text)
                df = pd.read_csv(csv_data, index_col=0)
                df = df[["group", "varname", "coef", "err"]]
                df.fillna(0, inplace=True)
                return df.to_dict(orient="records")

    raise Exception(f"No CSV file (HB AMCE) found in GitHub folder: {hb_folder}")


def process_experiment_data(
    raw_data: dict,
    hb_run_id: Optional[str] = None,
    hb_folder: Optional[str] = None,
    expr_llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> Experiment:
    """Process raw experiment data and return an Experiment instance."""
    processed_data = raw_data.copy()
    num_attr = len(processed_data["pre_cooked_attributes_and_levels_lookup"])
    processed_data["country"] = processed_data.pop("where_preamble")
    processed_data["year"] = processed_data.pop("when_preamble")
    processed_data["attribute_count"] = num_attr if num_attr <= 7 else 7
    processed_data["level_count"] = 5
    # processed_data["population_traits"] = {}
    processed_data["why_prompt"] = processed_data.pop(
        "experimentor_why_question_prompt"
    )

    experiment = Experiment(**processed_data)
    if not experiment.title:
        experiment.title = (
            processed_data.get("experiment") or experiment.why_prompt[:50]
        )

    experiment.hb_run_id = hb_run_id
    experiment.hb_folder = hb_folder
    experiment.is_hb_run = bool(hb_run_id or hb_folder)
    experiment.expr_llm_model = expr_llm_model

    return experiment


def get_experiment_io(wandb_id: str) -> dict:
    """Get experiment metadata from Weights & Biases."""
    try:
        metadata = {}
        run_details = WandbAPI().get_wandb_run_details(wandb_id)

        # experiment AMCE table json
        artifacts = run_details.get("artifacts", [])
        amce_artifact_path = extract_artifact_name(
            artifacts, "analytics_output", "experiment_beta_amce"
        )[:-3]
        if not amce_artifact_path:
            raise HTTPException(
                status_code=404, detail="AMCE table file not found in the run files."
            )

        amce_artifact_path = WandbAPI().get_wandb_run_artifact(amce_artifact_path)[2]
        with open(amce_artifact_path, mode="r") as file:
            amce_data = json.load(file)

        artifacts = run_details["configs"]["artifacts"]
        csv_choices_path = next(
            (file for file in artifacts if "experiment_survey_results" in file), None
        )
        if not csv_choices_path:
            raise HTTPException(
                status_code=404,
                detail="CSV results file not found in the artifact files.",
            )

        csv_choices_path = csv_choices_path["experiment_survey_results"]
        csv_file_path = WandbAPI().get_wandb_run_artifact(csv_choices_path)[2]

        with open(csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)  # Read CSV as dictionaries
            csv_json_data = list(reader)  # Convert to JSON

        metadata["experiment_results_summary"] = run_details["summary"]
        metadata["experiment_results_json"] = csv_json_data
        metadata["experiment_analytics_json"] = amce_data
        metadata["experiment_design"] = run_details["configs"]["experiment_design"]
        return metadata
    except Exception as e:
        capture_exception(e)
        log.error(
            f"Error fetching experiment input output from Weights & Biases: {str(e)}"
        )
        raise HTTPException(status_code=404, detail="Failed to fetch experiment data.")


def get_recursive_experiment(
    data: dict, llm_model: LLMModel, run_id: Optional[str]
) -> str:
    """Get recursive experiment data."""
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()

    get_recursive_experiment_prompt = pb.get_recursive_experiment_prompt()

    why_question = data.get("experiment_design", {}).get("why_prompt", "")
    respondent_dependant_variable = data.get("experiment_design", {}).get(
        "respondent_dependent_variable", ""
    )
    respondent_context = data.get("experiment_design", {}).get("context", "")
    population_traits = data.get("experiment_design", {}).get("target_population", {})
    attributes_and_levels = data.get("experiment_design", {}).get(
        "pre_cooked_attributes_and_levels_lookup", {}
    )
    json_of_votes = data.get("experiment_results_json", "")
    analysis_json_data = data.get("experiment_analytics_json", {})
    analysis_json_data_formatted = formatAMCEDataToNaturalLanguage(analysis_json_data)
    educationLevelEnumValues = [e.value for e in EducationLevelEnum]
    childrenEnumValues = [e.value for e in ChildrenEnum]
    racialGroupEnumValues = [e.value for e in RacialGroupEnum]
    genderEnumValues = [e.value for e in GenderEnum]

    try:
        with trace(
            name="Next experiment idea generation",
            project_name=run_id,
            run_type="llm",
            metadata={
                "ls_model_name": llm_model.value,
                "prompt_type": "next_experiment",
            },
        ) as run:
            ideas = pe.execute(
                prompt=get_recursive_experiment_prompt,
                args={
                    "why_question": why_question,
                    "population_traits": population_traits,
                    "attributes_and_levels": attributes_and_levels,
                    "respondent_dependant_variable": respondent_dependant_variable,
                    "respondent_context": respondent_context,
                    "analysis_json_data": analysis_json_data_formatted,
                    "educationLevelEnumValues": educationLevelEnumValues,
                    "childrenEnumValues": childrenEnumValues,
                    "racialGroupEnumValues": racialGroupEnumValues,
                    "genderEnumValues": genderEnumValues,
                },
                output_object=ExperimentIdeationResponse,
                description="Generating ideas for next experiment",
            )
            run.add_metadata({"result": ideas})
            run.end()
    except Exception as e:
        log.error(f"Error in idea generation: {str(e)}", exc_info=True)
        raise e
    context = {
        "population_traits": population_traits,
        "attributes_and_levels": attributes_and_levels,
        "respondent_dependant_variable": respondent_dependant_variable,
        "respondent_context": respondent_context,
        "analysis_json_data": analysis_json_data_formatted,
    }
    return ideas, context


def iterate_over_experiment_idea(
    idea: str, rationale: str, context: Any, run_id: Optional[str], llm_model: LLMModel
):
    graph = build_iteration_graph()
    try:
        final_state = graph.invoke({
            "llm_model": llm_model,
            "run_id": run_id,
            "context": context,
            "is_causal": False,
            "why_questions_refined": [{"why_question": idea, "rationale": rationale}],
            "causal_evaluations": [],
        })
    except Exception as e:
        log.error(f"Error in idea generation: {str(e)}", exc_info=True)
        return None
    return final_state


# write causality check prompt to get check and rationale
def execute_causality_check_bot(state, llm_model: LLMModel):
    why_question = state.why_questions_refined[-1].why_question
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    casuality_check_with_reasoning_experiment_prompt = (
        pb.casuality_check_with_reasoning_experiment_prompt()
    )
    try:
        with trace(
            name="Causality check for experiment idea generation",
            project_name=state.run_id,
            run_type="llm",
            metadata={
                "ls_model_name": llm_model.value,
                "prompt_type": "next_experiment",
            },
        ) as run:
            causality_check = pe.execute(
                prompt=casuality_check_with_reasoning_experiment_prompt,
                args={"why_prompt": why_question},
                output_object=CausalityCheckResponse,
                description="Causality check with rationale",
            )
            run.add_metadata({"result": causality_check})
            run.end()
    except Exception as e:
        log.error(
            f"Error in causality check of idea generation: {str(e)}", exc_info=True
        )
        raise e
    print(causality_check)
    return causality_check


# write next experiment idea prompt to get next experiment idea with state vals and context of previous iterations
def execute_next_experiment_bot(state, llm_model: LLMModel):
    context = state.context
    previous_iterations = state.why_questions_refined  # + causal evaluation
    recent_causal_evaluation_feedback = state.causal_evaluations
    combined_iterations = []
    for i in range(
        max(len(previous_iterations), len(recent_causal_evaluation_feedback))
    ):
        if i < len(previous_iterations):
            combined_iterations.append(previous_iterations[i])
        if i < len(recent_causal_evaluation_feedback):
            combined_iterations.append(recent_causal_evaluation_feedback[i])
    # print(f"combined_iterations: {combined_iterations}")
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    iterate_on_recursive_experiment_idea = pb.iterate_on_recursive_experiment_idea()

    try:
        with trace(
            name="Next experiment idea generation",
            project_name=state.run_id,
            run_type="llm",
            metadata={
                "ls_model_name": llm_model.value,
                "prompt_type": "next_experiment",
            },
        ) as run:
            idea = pe.execute(
                prompt=iterate_on_recursive_experiment_idea,
                args={
                    "context": context,
                    "previous_ideas_and_feedback": combined_iterations,
                },
                output_object=ExperimentIdea,
                description="Generating ideas for next experiment",
            )
            run.add_metadata({"result": idea})
            run.end()
    except Exception as e:
        log.error(f"Error in idea generation: {str(e)}", exc_info=True)
        raise e
    print(f"idea in next experiment bot {idea}")
    return idea


def build_iteration_graph():
    class IdeaStep(BaseModel):
        why_question: str
        rationale: str

    class CausalityCheck(BaseModel):
        is_causal: bool
        rationale: str

    # load up initial idea
    class State(BaseModel):
        llm_model: LLMModel
        run_id: Optional[str]
        context: Any
        why_questions_refined: list[IdeaStep] = []
        causal_evaluations: list[CausalityCheck] = []

        def add_message(
            self,
            channel: Literal["why_questions_refined", "causal_evaluations"],
            new_message: Literal[IdeaStep, CausalityCheck, str],
        ) -> "State":
            # print(f"Adding message: {new_message}")
            if channel == "why_questions_refined":
                self.why_questions_refined.append(new_message)
            elif channel == "causal_evaluations":
                self.causal_evaluations.append(new_message)
            # print(f"State: {self}")
            return self

    def route(state: State) -> Literal["next_experiment_bot", END]:
        print("Executing route")
        if (
            len(state.causal_evaluations) < 3
            and state.causal_evaluations[-1].is_causal == False
        ):  # refinements
            print("Needs further refinement")
            return "next_experiment_bot"
        elif state.causal_evaluations[-1].is_causal:
            print("Causal found!")
            return END
        else:
            return END

    graph_builder = StateGraph(State)

    def next_experiment_bot(state: State):
        print("Executing next experiment bot")
        idea_refined = execute_next_experiment_bot(state, state.llm_model)
        idea_refined_pydantic = IdeaStep.parse_obj(idea_refined)
        return State.add_message(state, "why_questions_refined", idea_refined_pydantic)

    def causality_check_bot(state: State):
        print("Executing causality check bot")
        causality_check = execute_causality_check_bot(state, state.llm_model)
        causality_check_pydantic = CausalityCheck.parse_obj(causality_check)
        return State.add_message(state, "causal_evaluations", causality_check_pydantic)

    graph_builder.add_node(next_experiment_bot)
    graph_builder.add_node(causality_check_bot)
    graph_builder.add_edge(START, "causality_check_bot")
    graph_builder.add_conditional_edges("causality_check_bot", route)
    graph_builder.add_edge("next_experiment_bot", "causality_check_bot")

    graph = graph_builder.compile()
    # final_state = graph.invoke({"base_why_question": message_in})
    return graph


def extract_artifact_name(artifacts, target_type, name_prefix):
    for artifact in artifacts:
        if artifact["type"] == target_type and artifact["name"].startswith(name_prefix):
            return artifact["name"]
    return None


def formatAMCEDataToNaturalLanguage(data):
    amce_results = data["AMCE_Results"]["AMCE_Results"]
    amce_sentences = [
        f"{entry['attribute_text']}: {entry['level_text']} has an average marginal"
        f" contribution effect of {entry['AMCE']} and significance:"
        f" {entry['significance']}"
        for entry in amce_results
        if entry["attribute_text"] != "Intercept"
    ]

    # Extract Attribute Importance
    importance_results = data["AMCE_Results"]["Attribute_Importance"]
    importance_sentences = [
        f"{entry['attribute_text']} had an overall importance of {entry['importance']}"
        for entry in importance_results
    ]

    # Combine and Print Results
    output = "\n".join(amce_sentences + importance_sentences)
    print(output)
    return output


def generate_conjoint_statements_preliminary(
    why_prompt: str,
    attribute_count: int,
    statement_history: list[str],
):
    llm_model = LLMModel.AZURE_GPTO3_MINI
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    conjoint_statements_preliminary_prompt = (
        pb.get_conjoint_statements_preliminary_prompt()
    )

    response = pe.execute(
        prompt=conjoint_statements_preliminary_prompt,
        args={
            "why_prompt": why_prompt,
            "attribute_count": attribute_count,
            "statement_history": statement_history,
        },
        output_object=RealWorldConjointStatementResponse,
        description="Validating preliminary checks for real-world conjoint statements",
    )
    return response


def generate_conjoint_statements(
    why_prompt: str,
    suggestion_count: int,
    attribute_count: int,
    statement_history: list[str],
):
    llm_model = LLMModel.AZURE_GPTO3_MINI
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()

    # check preliminary checks using generate_conjoint_statements_preliminary
    preliminary_checks_response = generate_conjoint_statements_preliminary(
        why_prompt, attribute_count, statement_history
    )
    if preliminary_checks_response.get("flag") == "Error":
        return preliminary_checks_response
    else:
        conjoint_statements_prompt = pb.get_conjoint_statements_prompt()

        try:
            statement_history.remove(context_message)
        except Exception:
            pass

        response = pe.execute(
            prompt=conjoint_statements_prompt,
            args={
                "why_prompt": why_prompt,
                "suggestion_count": suggestion_count,
                "attribute_count": attribute_count,
                "statement_history": statement_history,
                "context_message": context_message,
            },
            output_object=RealWorldConjointStatementResponse,
            description="Generating real-world conjoint statements",
        )
        return response
