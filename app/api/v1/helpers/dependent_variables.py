from langsmith import traceable
from sentry_sdk import capture_exception

from app.api.v1.schemas.experiments import SurveyResponseType
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

logger = app_logger.get_logger(__name__)


@traceable(
    run_type="chain",
    name="Generate Dependent Variable",
    tags=[
        "Ideation",
        "Experiment Design",
        "Dependent Variable",
    ],
    metadata={
        "Question Type": "Experiment Focus",
        "LLM Model": "User Selected Model",
        "Output": "Survey Prompt",
    },
)
def generate_dependent_variable(prompt_type, why_prompt, dv_llm_model, response_type):
    """
    Using a general LLM, not OpenAI specifically.
    Args:
        1. 'prompt_type': string Enum value from Ideation page, where user selects experiment focus.
        2. 'why_prompt': string input from Ideation page, where user inputs question to ask.
        3. 'dv_llm_model': Enum from LLM model family, where user selects model type.

    Returns:
        Dependent variable string (the "Survey Prompt" that synthetic respondents will respond to).
    """
    try:
        pe = LCELPromptExecutor(llm_model=dv_llm_model)
        pb = PromptBuilder(pt=prompt_type)

        if response_type == SurveyResponseType.DISCRETE:
            dependent_prompt = pb.dependent_variable_prompt_discrete()
        elif response_type == SurveyResponseType.PROBABILISTIC:
            dependent_prompt = pb.dependent_variable_prompt_probabilistic()

        dependent_variable = pe.execute(
            prompt=dependent_prompt,
            args={"why_prompt": why_prompt},
            output_object=None,
            description="Generating dependent variable...",
        )
        logger.success("Dependent variable generated successfully.")
        return dependent_variable
    except Exception as e:
        capture_exception(e)
        raise e
