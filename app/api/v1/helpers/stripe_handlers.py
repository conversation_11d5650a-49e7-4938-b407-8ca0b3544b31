import os

import stripe

from app.core.utils.auth0 import auth0_client
from app.core.utils.logging import app_logger

log = app_logger.get_logger(__name__)


class StripeEventHandler:
    def __init__(self):
        self.auth0 = auth0_client
        self.auth0_premium_user_role_id = os.getenv("AUTH0_PREMIUM_USER_ROLE_ID")

    def handle_event(self, event):
        """
        Dispatches Stripe events to appropriate handlers.
        """
        event_type = event["type"]
        event_handlers = {
            "customer.subscription.created": self.handle_customer_subscription_created,
            "customer.subscription.updated": self.handle_customer_subscription_updated,
            "customer.subscription.deleted": self.handle_customer_subscription_deleted,
            "invoice.payment_succeeded": self.handle_invoice_payment_succeeded,
            "invoice.payment_failed": self.handle_invoice_payment_failed,
        }

        handler = event_handlers.get(event_type)
        if handler:
            try:
                handler(event)
            except Exception as e:
                log.error(f"Error handling event {event_type}: {e}", exc_info=True)
        else:
            log.warning(f"Unhandled event type: {event_type}")

    def handle_customer_subscription_created(self, event):
        """
        Handles the customer.subscription.created event.
        This is triggered when a user subscribes to our premium plan.
        The method assigns the customer role to the user in Auth0 based on the customer ID.
        """
        log.info("Triggered customer.subscription.created")

    def handle_customer_subscription_deleted(self, event):
        """
        Handles the customer.subscription.deleted event.
        This is triggered when a user cancels their premium subscription.
        The method removes the customer role from the user in Auth0 based on the customer ID.
        """
        log.info("Triggered customer.subscription.deleted")

        customer_id = event["data"]["object"]["customer"]
        user_id = self.get_user_id(customer_id)
        user_roles = self.auth0.get_role(user_id)
        if any(role["id"] == self.auth0_premium_user_role_id for role in user_roles):
            self.auth0.remove_role(user_id, self.auth0_premium_user_role_id)

    def handle_customer_subscription_updated(self, event):
        """
        Handles the customer.subscription.updated event.
        This is triggered whenever a subscription changes (e.g., switching from one plan to another, or changing the status from trial to active).
        """
        log.info("Triggered customer.subscription.updated")
        # TODO: Implement when have more than one subscription

    def handle_invoice_payment_succeeded(self, event):
        """
        Handle successful invoice payments.
        This event is triggered each time an invoice payment is successfully processed in Stripe,
        typically happening on a recurring basis (e.g., monthly for subscriptions).
        """
        log.info("Triggered invoice.payment_succeeded")

        customer_id = event["data"]["object"]["customer"]
        user_id = self.get_user_id(customer_id)
        user_roles = self.auth0.get_role(user_id)

        if not any(
            role["id"] == self.auth0_premium_user_role_id for role in user_roles
        ):
            self.auth0.assign_role(user_id, self.auth0_premium_user_role_id)

    def handle_invoice_payment_failed(self, event):
        """
        Handle failed invoice payments.
        This event is triggered when a payment attempt for an invoice in Stripe fails.
        This could be due to various reasons like insufficient funds or expired credit card.
        """
        log.info("Triggered invoice.payment_failed")

        customer_id = event["data"]["object"]["customer"]
        user_id = self.get_user_id(customer_id)
        user_roles = self.auth0.get_role(user_id)
        if any(role["id"] == self.auth0_premium_user_role_id for role in user_roles):
            self.auth0.remove_role(user_id, self.auth0_premium_user_role_id)

    def get_user_id(self, customer_id):
        customer = stripe.Customer.retrieve(customer_id)
        user_id = customer.metadata.get("auth0_user_id")
        if not user_id:
            raise ValueError(f"No auth0_user_id found for customer {customer_id}")
        return user_id
