from gpt_researcher import <PERSON><PERSON><PERSON>archer

from app.core.utils.type_enums import ResearchReportType, ResearchToneType


async def get_research_report(
    why_prompt: str,
    report_type: str = ResearchReportType.ResearchReport,
    tone: str = ResearchToneType.Objective,
):
    researcher = GPTResearcher(
        query=why_prompt, report_type=report_type, tone=tone, verbose=False
    )
    await researcher.conduct_research()
    report = await researcher.write_report()

    source_urls = researcher.get_source_urls()
    research_images = researcher.get_research_images()
    research_sources = researcher.get_research_sources()

    return {
        "report": report,
        "source_urls": source_urls,
        "research_images": research_images,
        "research_sources": research_sources,
    }
