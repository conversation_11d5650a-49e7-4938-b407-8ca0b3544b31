import asyncio
import random
import statistics
from typing import Dict, List, Optional, Union

import numpy as np
from fastapi.security import HTTPBearer
from langchain_community.utils.math import cosine_similarity
from langchain_openai import OpenAIEmbeddings
from langsmith import traceable
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

# version 2 attribute levels generation imports
from app.api.v1.schemas.attributes_levels import (
    AttributesResponse,
    LeveledAttribute,
    LeveledAttributeBase,
    LeveledAttributeBaseClaude,
    LeveledAttributeResponse,
    OrdinalResponse,
    ProcessedProducts,
    ProductAttributeRankingRequest,
    ProductAttributeRankingResponse,
    ProductAttributeRequest,
    ProductAttributeResponse,
    ProductPriceLevelRequest,
    ProductPriceLevelResponse,
    RealWorldProductsResponse,
    SpecificOrGenericAttributeCheckResponse,
)
from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel, PromptType
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor, get_perplexity_executor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)


@traceable(
    run_type="chain",
    name="Attributes Levels Generation",
    tags=[
        "Attributes Levels",
        "Generate Attributes",
        "Generate Levels",
    ],
    metadata={"Attribute Levels": "Attributes", "Attribute Levels": "Levels"},
)
def get_attributes_and_levels(
    why_prompt: str,
    country: str,
    year: int,
    level_count: int,
    attribute_count: int,
    max_length: int,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> list[LeveledAttributeBase]:
    """
    Generates list of Attributes and Levels using LLM based upon request parameters
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.attributes_levels_prompt()
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "year": year,
        "attribute_count": attribute_count,
        "max_length": max_length,
        "level_count": level_count,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeResponse,
        description="Generating attributes and levels",
    )
    return [LeveledAttributeBase(**attr) for attr in response.get("attributes", [])]


def get_attributes_and_levels_claude(
    why_prompt: str,
    country: str,
    year: int,
    level_count: int,
    attribute_count: int,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> list[LeveledAttributeBaseClaude]:
    """
    Generates list of Attributes and Levels using LLM based upon request parameters
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.attributes_levels_prompt_claude()
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "year": year,
        "attribute_count": attribute_count,
        "level_count": level_count,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeResponse,
        description="Generating attributes and levels",
    )
    return [
        LeveledAttributeBaseClaude(**attr)
        for attr in response.get("attributes_levels", [])
    ]


def get_attributes_and_levels_claude_with_score(
    why_prompt: str,
    country: str,
    year: int,
    level_count: int,
    attribute_count: int,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> list[LeveledAttributeBaseClaude]:
    """
    Generates list of Attributes and Levels using LLM based upon request parameters
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.attributes_levels_prompt_claude_with_score()
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "year": year,
        "attribute_count": attribute_count,
        "level_count": level_count,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeResponse,
        description="Generating attributes and levels",
    )
    return [
        LeveledAttributeBaseClaude(**attr)
        for attr in response.get("attributes_levels", [])
        if attr.get("levels") and len(set(attr.get("levels"))) >= 2
    ]


@traceable(
    run_type="chain",
    name="Orthogonal Attributes Generation",
    tags=[
        "Attributes Levels",
        "Generate Attributes",
        "Generate Orthogonal Attributes",
    ],
    metadata={
        "Attribute Levels": "Attributes",
        "Attribute Levels": "Orthogonal Attributes",
    },
)
def get_orthogonal_attributes_levels(
    why_prompt: str,
    country: str,
    new_attribute_count: int,
    level_count: int,
    existing_attributes_levels: list[LeveledAttribute],
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH,
) -> list[LeveledAttributeBase]:
    """
    Generate new attributes and levels that are orthogonal to the existing ones.

    Calculates orthogonality between the existing (old) and new attributes and levels,
    filters out duplicates, and sorts the remaining new attributes and levels based on orthogonality.
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.orthogonal_levels_prompt()
    embedding_model = OpenAIEmbeddings(model="text-embedding-3-small")
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "existing_attributes_levels": existing_attributes_levels,
        "new_attribute_count": new_attribute_count,
        "level_count": level_count,
    }

    res = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeBase,
        description="Generating orthogonal attributes and levels",
    )

    new_attributes_levels = res.get("attributes_levels", [])

    old_attributes = [
        str(attr_level.attribute) for attr_level in existing_attributes_levels
    ]
    new_attributes = [
        str(attr_level["attribute"]) for attr_level in new_attributes_levels
    ]

    embeddings_old = embedding_model.embed_documents(old_attributes)
    embeddings_new = embedding_model.embed_documents(new_attributes)

    similarity_matrix = cosine_similarity(embeddings_new, embeddings_old)
    avg_similarity = np.mean(similarity_matrix, axis=1)

    # mapping of each new attribute to its average similarity score. This helps in sorting and filtering.
    attr_similarity_mapping = {
        new_attributes[i]: avg_similarity[i] for i in range(len(new_attributes))
    }

    # Sort new attributes by their average similarity, excluding existing ones
    sorted_unique_attrs = sorted(
        [attr for attr in new_attributes if attr not in old_attributes],
        key=lambda attr: attr_similarity_mapping[attr],
    )

    final_attrs_levels = [
        next(
            (
                attr_level
                for attr_level in new_attributes_levels
                if attr_level["attribute"] == attr
            ),
            None,
        )
        for attr in sorted_unique_attrs
    ]
    final_attrs_levels = [attr_level for attr_level in final_attrs_levels if attr_level]

    return final_attrs_levels


@traceable(
    run_type="chain",
    name="Attributes Generation",
    tags=[
        "Attributes Levels",
        "Generate Attributes",
    ],
    metadata={"Attribute Levels": "Attributes"},
)
def get_attributes(
    why_prompt: str,
    country: str,
    attribute_count: int,
    prompt_type: str = PromptType.GENERIC,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
    include_price_brand: bool = False,
) -> list[str] | Exception:
    """
    Generates a list of attributes using LLM based upon request parameters
    """

    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.llm_attributes_prompt()
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "attribute_count": attribute_count,
        "prompt_type": prompt_type,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=AttributesResponse,
        description="Generating attributes...",
    )
    attributes = response.get("attributes", [])
    if include_price_brand:
        attributes.extend(
            ["What are 10 possible price ranges?", "What are some potential brands?"]
        )
    return attributes


@traceable(
    run_type="chain",
    name="Levels Generation",
    tags=["Attributes Levels", "Generate Levels"],
    metadata={"Attribute Levels": "Levels"},
)
def get_levels(
    why_prompt: str,
    country: str,
    existing_attributes: list[str],
    attribute: str,
    level_count: int,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
) -> LeveledAttributeBase:
    """
    Generates a list of levels for a given attribute
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.llm_levels_prompt()
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "existing_attributes": existing_attributes,
        "attribute": attribute,
        "level_count": level_count,
    }
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=LeveledAttributeBase,
        description="Generating levels...",
    )
    return LeveledAttributeBase(**response)


@traceable(
    run_type="chain",
    name="Product Attributes",
    tags=[
        "Attributes",
        "Perplexity",
        "Generate Product Attributes",
    ],
    metadata={
        "Attribute Levels": "Attributes",
        "Product Attribute Levels": "Product Attributes",
    },
)
async def generate_product_attributes(
    req: ProductAttributeRequest, brands
) -> ProductAttributeResponse:
    async with get_perplexity_executor() as prompt_executor:
        attribute_prompt_template = None
        arguments = None
        prompt_builder = PromptBuilder()

        if brands is not None:
            attribute_prompt_template = (
                prompt_builder.product_attribute_withbrandinwhyprompt()
            )
            arguments = {
                "why_prompt": req.why_prompt,
                "country": req.country,
                "attribute_count": req.attribute_count,
                "brands": brands,
            }
        else:
            arguments = {
                "why_prompt": req.why_prompt,
                "country": req.country,
                "attribute_count": req.attribute_count,
            }
            attribute_prompt_template = prompt_builder.product_attribute_prompt()

        response = await prompt_executor.execute(
            prompt=attribute_prompt_template,
            args=arguments,
            output_object=ProductAttributeResponse,
        )

        if len(response["attributes"]) < req.attribute_count:
            raise ValueError(
                "The number of attributes in the response"
                f" ({len(response['attributes'])}) is less than the requested number"
                f" ({req.attribute_count})."
            )

        return ProductAttributeResponse(**response)


@traceable(
    run_type="chain",
    name="Product Attirbute Ranking",
    tags=["Attributes", "Ranking"],
    metadata={
        "Attribute Levels": "Attributes",
        "Product Attribute Levels": "Product Attributes",
    },
)
def rank_attributes(
    req: ProductAttributeRankingRequest,
) -> ProductAttributeRankingResponse:
    pe = LCELPromptExecutor(llm_model=LLMModel.GCP_GEMINIFLASH)
    pb = PromptBuilder()
    prompt = pb.product_attribute_ranking_prompt()

    args = {
        "why_prompt": req.why_prompt,
        "attributes": req.attributes,
        "attribute_count": req.attribute_count,
    }

    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=ProductAttributeResponse,
    )

    ranking_response = ProductAttributeRankingResponse(
        ranked_attributes=response.get("attributes"),
        original_attributes=req.attributes,
        requested_num_attrs=req.attribute_count,
    )
    return ranking_response


def ordinality_check(
    attribute_levels: Dict[str, List[str]],
    why_prompt: str,
    country: str,
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH,
):
    """
    Determines if each attribute is nominal or ordinal.
    If ordinal, ranks the levels of that attribute.

    Args:
        attribute_levels: attribute/level dictionary
        why_prompt (str): Product why_prompt or context.
        country (str): Country or market context.
        llm_model (LLMModel): Language model to use.

    Returns:
        OrdinalResponse: Dict of Attributes whether nominal or ordinal and if ordinal ranked levels.
    """
    pb = PromptBuilder()
    prompt = pb.ordinal_prompt()
    pe = LCELPromptExecutor(llm_model=llm_model)
    args = {
        "why_prompt": why_prompt,
        "country": country,
        "attribute_levels": attribute_levels,
    }
    try:
        response = pe.execute(
            prompt=prompt,
            args=args,
            output_object=OrdinalResponse,
            description="Determine attribute types and rankings",
        )
    except Exception as e:
        logger.error(f"Error determining attribute types and rankings: {e}")
        return None
    types = response.get("attribute_types", {})
    ranking = response.get("ordinal_rankings", {})
    result = {}

    for attr, attr_type in types.items():
        if attr_type.lower() == "nominal":
            result[attr] = "nominal"
        elif attr_type.lower() == "ordinal":
            ranked_levels = ranking[attr]
            if ranked_levels:
                result[attr] = ranked_levels
            else:
                logger.warning(f"No rankings provided for ordinal attribute '{attr}'")
                result[attr] = "ordinal with no rankings"
        else:
            logger.warning(
                f"Unknown attribute type '{attr_type}' for attribute '{attr}'"
            )
            result[attr] = "unknown"

    return result


@traceable(
    run_type="chain",
    name="Get Product Attributes",
    tags=["Attributes", "Generate and Rank"],
    metadata={
        "Attribute Levels": "Attributes",
        "Product Attribute Levels": "Product Attributes",
    },
)
async def get_product_attributes(
    req: ProductAttributeRequest,
    brands,
) -> ProductAttributeResponse:
    # Generate initial attributes
    initial_attribute_response = await generate_product_attributes(req, brands)
    # Create ranking request
    ranking_request = ProductAttributeRankingRequest(
        why_prompt=req.why_prompt,
        attributes=initial_attribute_response.attributes,
        attribute_count=req.attribute_count,
    )

    ranking_response = rank_attributes(ranking_request)
    # Handle potential coroutine (if rank_attributes becomes async in the future)
    if asyncio.iscoroutine(ranking_response):
        ranking_response = await ranking_response

    return ProductAttributeResponse(attributes=ranking_response.ranked_attributes)


def process_price_levels(
    data: Dict[str, List[Union[float, int]]], units: str, contains_brand: bool
) -> list[LeveledAttributeBase]:

    all_prices = []
    for price_range in data.values():
        all_prices.extend([price_range[0], price_range[-1]])

    min_price = min(all_prices)
    max_price = max(all_prices)
    avg_price = int(statistics.mean(all_prices))

    mid1 = (min_price + avg_price) // 2
    mid2 = (avg_price + max_price) // 2

    price_stats = [min_price, mid1, avg_price, mid2, max_price, (2 * max_price - mid2)]

    # Generate price levels with ranges
    # levels = [f"Under {price_stats[0]} {units}"]
    levels = []
    for i in range(len(price_stats) - 1):
        start = price_stats[i]
        end = price_stats[i + 1]
        # Format last range without space around hyphen
        if i == len(price_stats) - 2:
            levels.append(f"{start} - {end} {units}")
        else:
            levels.append(f"{start} - {end} {units}")

    # Brand/Product logic
    if contains_brand == 1:
        brand_levels = {"attribute": "Product", "levels": [*data]}
    else:
        brand_levels = {"attribute": "Brand", "levels": [*data]}

    price_levels = {
        "attribute": "Price",
        "levels": levels,
    }

    # Return the appropriate list of leveled attributes
    return [brand_levels, price_levels]  # Adjust return as per your needs


@retry(
    stop=stop_after_attempt(5),
    wait=wait_exponential(multiplier=1, min=4, max=60),
    retry=retry_if_exception_type(RuntimeError),
)
async def execute_with_retry(pe, prompt, args, output_object):
    return await pe.execute(prompt=prompt, args=args, output_object=output_object)


async def verify_brand_relevance(
    why_prompt: str, brands: List[str], llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH
) -> bool:
    """
    Verifies if the generated brands are relevant to the why_prompt.
    Returns True if brands are relevant, False otherwise.
    """
    pe = LCELPromptExecutor(llm_model=llm_model)
    pb = PromptBuilder()
    prompt = pb.verify_brand_relevance_prompt()
    args = {"why_prompt": why_prompt, "brands": brands}
    try:
        response = pe.execute(
            prompt=prompt,
            args=args,
            output_object=bool,
            description="Verifying brand relevance",
        )
        return response
    except Exception as e:
        logger.error(f"Error verifying brand relevance: {str(e)}")
        return False


@traceable(
    run_type="chain",
    name="Get Product Price Levels",
    tags=[
        "Levels",
        "Product and Price Levels Generation",
    ],
    metadata={
        "Product Levels": "Product Price Range ",
        "Product Attribute Levels": "Price Levels",
    },
)
async def generate_product_price_levels(
    req: ProductPriceLevelRequest,
    contains_brand: int,
    get_brandsmultiple: bool,
    pre_cooked_brands=None,
    max_retries: int = 3,
) -> ProductPriceLevelResponse:
    for attempt in range(max_retries):
        async with get_perplexity_executor() as prompt_executor:
            pb = PromptBuilder()
            if get_brandsmultiple:
                prompt = pb.product_price_levels_multiplebrandprompt_getbrand()
            elif contains_brand > 1:
                prompt = pb.product_price_levels_multiplebrandprompt_getproducts()
            elif contains_brand == 1:
                print("in generate_product_price_levels ")
                prompt = pb.product_price_levels_withbrandinprompt()
            else:
                print("general brand prompt")
                prompt = pb.product_price_levels_generalbrandprompt()
            if pre_cooked_brands:
                args = {
                    "why_prompt": req.why_prompt,
                    "brands": pre_cooked_brands,
                    "attribute": req.attribute,
                    "country": req.country,
                }
            else:
                args = {
                    "why_prompt": req.why_prompt,
                    "attribute": req.attribute,
                    "country": req.country,
                }
            print(f"ARGS: {args}")
            response = await execute_with_retry(
                prompt_executor, prompt, args, ProductPriceLevelResponse
            )

            # Verify brand relevance
            if not pre_cooked_brands and response.get("levels"):
                is_relevant = await verify_brand_relevance(
                    req.why_prompt, response.get("levels")
                )
                if not is_relevant and attempt < max_retries - 1:
                    print(
                        "Brands not relevant to why_prompt. Retrying... (Attempt"
                        f" {attempt + 1}/{max_retries})"
                    )
                    continue
                elif not is_relevant:
                    print(
                        "Maximum retries reached. Proceeding with generated brands"
                        " despite relevance concerns."
                    )

            print(f"response {response}")
            return ProductPriceLevelResponse(**response)


@traceable(
    run_type="chain",
    name="Generate Product Levels",
    tags=[
        "Levels",
        "Product and Price Levels Generation",
    ],
    metadata={
        "Product Levels": "Get Levels per attribute per product",
        "Product Attribute Levels": "Attribute Levels",
    },
)
async def generate_product_attribute_levels(
    req, price_levels, brands, brands_in_prompt
):
    MAX_CONCURRENT_REQUESTS = 10

    semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    pb = PromptBuilder()
    prompt = pb.product_attribute_levels_prompt()

    categories_in_order = ["Economical", "Mid-Range", "High-end", "Luxurious"]

    selected_brands_data = {}
    brand_attributes_levels = []

    # First, try to get up to 4 brands in order
    for cat in categories_in_order:
        cat_data = price_levels.levels.get(cat, {})
        for brand, price_list in cat_data.items():
            if brand not in selected_brands_data:
                selected_brands_data[brand] = price_list
                if len(selected_brands_data) == 4:
                    break
        if len(selected_brands_data) == 4:
            break

    # If we have fewer than 5 brands at this point, we need to add more from random selection
    if len(selected_brands_data) < 5:
        # Gather all available brands from all categories
        all_brands = []
        for cat_data in price_levels.levels.values():
            all_brands.extend(list(cat_data.items()))

        # Filter out brands we already have
        all_brands = [(b, p) for b, p in all_brands if b not in selected_brands_data]

        # Shuffle and pick enough to reach 5 total brands
        random.shuffle(all_brands)
        needed = 5 - len(selected_brands_data)
        for i in range(needed):
            if not all_brands:
                break
            brand, price_list = all_brands.pop()
            selected_brands_data[brand] = price_list

    data = selected_brands_data
    # data should be the brands!
    if brands_in_prompt:
        data = brands
    brand_and_price = process_price_levels(
        data=data, units=price_levels.units, contains_brand=brands_in_prompt
    )

    def validate_product_attributes(products, attribute_levels):
        """
        Validates that all product attributes have levels that are contained in the
        corresponding attribute_levels list.

        Args:
            products: List of dictionaries where each dict has a brand name as key and
                    attributes/levels as values
            attribute_levels: List of dictionaries with 'attribute' and 'levels' keys

        Returns:
            List of validation errors, empty if all attributes are valid
        """
        # Create a lookup dictionary for faster access to valid levels for each attribute
        valid_levels = {}
        for attr_dict in attribute_levels:
            attribute = attr_dict["attribute"]
            levels = attr_dict["levels"]
            valid_levels[attribute] = levels

        errors = []

        # Validate each product
        for product_dict in products:
            for brand, attributes in product_dict.items():
                for attribute, level in attributes.items():
                    # Check if the attribute exists in our valid_levels
                    if attribute not in valid_levels:
                        errors.append(
                            f"Brand '{brand}' has unknown attribute '{attribute}'"
                        )
                        continue

                    # Check if the level is in the valid levels for this attribute
                    if level not in valid_levels[attribute]:
                        errors.append(
                            f"Brand '{brand}' has invalid level '{level}' for attribute"
                            f" '{attribute}'. Valid levels are:"
                            f" {valid_levels[attribute]}"
                        )

        return errors

    async def proces_brand_for_all_attr(attributes, brand, price):
        async with semaphore:
            async with get_perplexity_executor() as prompt_executor:
                prompt = pb.product_attributes_levels_prompt()
                min_price, max_price = price[0], price[-1]
                args = {
                    "why_prompt": req.why_prompt,
                    "attributes": attributes,
                    "country": req.country,
                    "min_price": min_price,
                    "max_price": max_price,
                    "units": req.units,
                    "brand_or_product": brand,
                }
                return await execute_with_retry(
                    prompt_executor, prompt, args, LeveledAttribute
                )

    async def process_all_attributes(attributes):
        all_attr = {}
        product = []
        tasks = [
            proces_brand_for_all_attr(attributes, brand, price)
            for brand, price in data.items()
        ]
        responses = await asyncio.gather(*tasks)
        i = 1
        for response in responses:
            if isinstance(response, Exception):
                logger.error(
                    f"Error processing attribute {attributes}: {str(response)}"
                )
                continue
            all_attr[f"attributes{i}"] = response["attributes"]
            product.append({response["product"]: response["attributes"]})
            i = i + 1
        return all_attr, product

    # LEAVE ALL PRINT STATEMENTS FOR NOW, ODDS ARE I NEED THEM AGAIN LOL
    print(f"REQ ATTRIUBTES: {req.attributes}")
    attribute_per_brand, products = await process_all_attributes(req.attributes)
    print(f"ALL ATTRIBUTES: {attribute_per_brand}")
    print(f"PROUCTS FOR VERIFICATIONS: {products}")
    transformed_attributes = []
    price = []
    all_attribute_names = set()
    for attr_set in attribute_per_brand.values():
        all_attribute_names.update(attr_set.keys())

    # For each unique attribute name, collect all its values
    for attr_name in all_attribute_names:
        levels = []
        for attr_set in attribute_per_brand.values():
            if attr_name in attr_set and attr_set[attr_name] not in levels:
                levels.append(attr_set[attr_name])
            if attr_name == "Price":
                price = attr_set[attr_name]
        unique_levels = set(levels)
        pe = LCELPromptExecutor(llm_model=LLMModel.GCP_GEMINIFLASH)
        args = {
            "why_prompt": req.why_prompt,
            "attribute": attr_name,
            "country": req.country,
            "all_levels": unique_levels,
            "level_count": req.level_count,
        }
        response = pe.execute(
            prompt=pb.process_levels_for_products(),
            args=args,
            output_object=LeveledAttribute,
        )
        levels = []
        print(response)
        levels = response.get("levels")
        print(levels)

        print(f"Normal levels: {unique_levels}")
        print("------------------------")
        print(f"Tranformed levels: {levels}")
        # Add the attribute with its collected levels to the result
        transformed_attributes.append({"attribute": attr_name, "levels": levels})

    result = transformed_attributes
    pe = LCELPromptExecutor(llm_model=LLMModel.GCP_GEMINIFLASH)
    standardized_products = []
    n = 0
    errors = ["none"]  # Initialize with a non-empty value to enter the loop

    # Continue trying as long as there are errors and we haven't exceeded max attempts
    while errors and n < 2:  # Changed condition to be more intuitive
        standardized_products = []  # Reset at the beginning of each attempt

        for product in products:
            args = {
                "errors": errors,
                "product_data": product,
                "standardized_attributes": transformed_attributes,
            }
            response = pe.execute(
                prompt=pb.align_levels(), args=args, output_object=ProcessedProducts
            )

            standardized_products.append(response.get("products_data")[0])
            test = response.get("products_data")[0]

        # Validate after processing all products
        errors = validate_product_attributes(standardized_products, result)

        if errors:
            print(f"Attempt {n+1}: Fault in validation: {errors}")
        else:
            print(f"Attempt {n+1}: Validation successful!")

        n = n + 1

    # print(f"transformed attributes: {transformed_attributes}")
    # print(f"Products before: {products}")
    # print("------------")
    # print(f"Products after: {standardized_products}")
    # print(f"Attriubtes/Levels: {result}")
    # print(f"Errors: {errors}")

    result.insert(0, brand_and_price[0])

    return result, standardized_products


async def generate_real_world_products(
    products: List[Dict], why_prompt: str, country: str
) -> List[Dict]:
    """
    Generate real-world products based on a list of input products, processing each one individually.
    """

    async def query_perplexity_for_product(
        product: Dict, products_sofar: List[Dict]
    ) -> Dict:
        async with get_perplexity_executor() as prompt_executor:
            pb = PromptBuilder()
            prompt = pb.fetch_product()
            args = {
                "why_prompt": why_prompt,
                "country": country,
                "product": product,
                "products_sofar": products_sofar,
            }

            while True:
                response = await execute_with_retry(
                    prompt_executor,
                    prompt,
                    args,
                    output_object=RealWorldProductsResponse,
                )

                # Ensure all attributes are returned
                missing_attributes = [
                    key
                    for key in product.keys()
                    if key not in response["product"]["attributes"]
                ]
                if missing_attributes:
                    print(f"Missing attributes: {missing_attributes}. Retrying...")
                    continue

                # Check for duplicates
                if any(
                    response["product"]["product_name"] == p["product_name"]
                    for p in products_sofar
                ):
                    print(
                        f"Duplicate found: {response['product']['product_name']}."
                        " Retrying..."
                    )
                    continue

                return response["product"]

    # Process each product sequentially to ensure products_sofar is updated
    real_world_products = []

    for product in products:
        # Pass a copy of the list to avoid unintended side effects
        products_sofar = real_world_products.copy()
        new_product = await query_perplexity_for_product(product, products_sofar)
        real_world_products.append(new_product)

    # Ensure the output matches the length of the input

    return real_world_products


def check_generic_or_specific_attribute(why_prompt: str):
    """
    Check if the why_prompt is asking for a generic or specific attribute.
    """
    pe = LCELPromptExecutor(llm_model=LLMModel.GCP_SONNET)
    pb = PromptBuilder()
    prompt = pb.generic_or_specific_attribute_check()
    args = {"why_prompt": why_prompt}
    response = pe.execute(
        prompt=prompt,
        args=args,
        output_object=SpecificOrGenericAttributeCheckResponse,
        description=(
            "Checking if the why_prompt is asking for a generic or specific attribute"
        ),
    )
    return response
