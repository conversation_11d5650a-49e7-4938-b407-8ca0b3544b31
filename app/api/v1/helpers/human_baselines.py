import json
import os
import re
import subprocess
from typing import Any, Dict

import wandb
from fastapi import HTTPException
from sentry_sdk import capture_exception

from app.core.global_config import settings
from app.core.utils.common import delete_local_artifacts, with_spinner
from app.core.utils.logging import app_logger
from app.core.utils.wandb_api import WandbAPI
from app.core.utils.wandb_runs import WandbRun

logger = app_logger.get_logger(__name__)


def is_run_hidden(run, user_id) -> bool:
    is_private = (
        run["config" if "config" in run else "configs"]
        .get("experiment_design", {})
        .get("is_private", False)
    )
    experiment_user_id = who_owns_experiment(run)
    return (
        is_private and experiment_user_id is not None and user_id != experiment_user_id
    )


def who_owns_experiment(run) -> str:
    return run["config" if "config" in run else "configs"].get("user", None)


def get_wandb_run_amce(run_id, user_id) -> list[dict] | HTTPException:
    """
    Downloads the AMCE artifact from WandB and returns the contents
    """
    wandb_api = WandbAPI()
    run_details = wandb_api.get_wandb_run_details(run_id)
    if is_run_hidden(run_details, user_id):
        raise Exception(
            f"Access denied: Run {run_id} is private and you are not the owner of"
            " the experiment"
        )
    try:
        _, artifact_dir, file_path = wandb_api.get_wandb_run_artifact(
            f"experiment_beta_amce_{run_details.get('run_name')}"
        )
    except Exception as e:
        raise e
    amce = extract_amce(file_path, artifact_dir)
    delete_local_artifacts(artifact_path=file_path)
    logger.success(f"AMCE extracted successfully for run_id {run_id}")
    return amce


@with_spinner("Executing R Analytics")
def run_r_analytics(
    run: WandbRun,
    mappings_path: str,
    survey_results_path: str,
    paper_reported_coefficients_path: str,
    calculations_script_path: str,
    calculations_output_path: str,
    amce_path: str,
    is_hb_run: bool,
    sp=None,
) -> Dict[str, Any]:
    count = 0
    run_name = run.name

    try:
        sp.text = f"Executing R script for run: {run_name}"
        r_command = [
            "Rscript",
            calculations_script_path,
            mappings_path,
            survey_results_path,
            paper_reported_coefficients_path,
            calculations_output_path,
            amce_path,
        ]
        result = subprocess.run(r_command, capture_output=True, text=True, check=True)

        if result.stderr:
            logger.warning(f"R script stderr:\n{result.stderr}")

        if not os.path.exists(calculations_output_path):
            logger.critical(
                "R script did not generate the expected output file:"
                f" {calculations_output_path}"
            )
        else:
            logger.success(
                "R script executed successfully, output saved to"
                f" {calculations_output_path}"
            )
            if not is_hb_run:
                sp.text = f"Pushing dashboard analytics artifact for run: {run_name}"
                push_analytics_artifact(
                    run, calculations_output_path, filename="Analytics_output"
                )
                count += 1
        if not os.path.exists(amce_path):
            logger.critical(
                f"R script did not generate the expected output file: {amce_path}"
            )
        else:
            logger.success(
                f"R script executed successfully, output saved to {amce_path}"
            )
            sp.text = f"Pushing beta AMCE analytics artifact for run: {run_name}"
            push_analytics_artifact(run, amce_path, filename="experiment_beta_amce")

            if is_hb_run:
                count += 1

        if count == 1:
            return "successful"
        else:
            return "failed"
    except subprocess.CalledProcessError as e:
        capture_exception(e)
        logger.critical(f"R script execution failed {e}")
        logger.critical(f"Error output: {e.stderr}")
        return "failed"
    except Exception as e:
        capture_exception(e)
        logger.critical(f"Error in R analytics execution for run {run_name}: {str(e)}")
        return "failed"


def push_analytics_artifact(run: WandbRun, artifact_path: str, filename: str) -> bool:
    run_name = run.name
    artifact_name = f"{filename}_{run_name}"
    logger.info(
        f"Pushing analytics artifact to WandB for run {run_name}: {artifact_name}"
    )
    try:
        artifact = wandb.Artifact(artifact_name, type="analytics_output")
        artifact.add_file(artifact_path)
        run.run.log_artifact(artifact)
        logger.success(
            f"Successfully pushed analytics artifact to WandB for run {run_name}"
        )
        return True
    except FileNotFoundError as e:
        logger.critical(f"Analytics output file not found for run: {run_name}")
        raise Exception(f"Analytics output file not found: {artifact_path}") from e
    except Exception as e:
        logger.critical(
            f"Error pushing analytics artifact to WandB for run {run_name}: {str(e)}"
        )
        capture_exception(e)
        raise


def get_human_baseline_showcase(hb_id, user_details):
    """
    Get the AMCE data for the human baselines, including the paper data and the replication results
    """
    wandb_api = WandbAPI(project=settings.HUMAN_BASELINE_PROJECT)
    hb_run = wandb_api.get_wandb_run_details(hb_id)
    hb_paper_data = (
        hb_run.get("config" if "config" in hb_run else "configs")
        .get("experiment", {})
        .get("paper_data", [])
    )
    try:
        _, artifact_dir, file_path = wandb_api.get_wandb_run_artifact(
            f"ols_experiment_model_output_{hb_id}"
        )
    except Exception as ols_error:
        try:
            _, artifact_dir, file_path = wandb_api.get_wandb_run_artifact(
                f"clm_experiment_model_output_{hb_id}"
            )
        except Exception as clm_error:
            capture_exception()
            logger.error(
                "Error getting hb_showcase: Neither OLS nor CLM artifacts could be"
                f" retrieved for hb_id {hb_id}."
            )
            raise HTTPException(
                status_code=500,
                detail=(
                    "Error(get_hb_showcase): Neither OLS nor CLM artifacts could be"
                    f" retrieved for hb_id {hb_id}."
                ),
            )
    hb_amce = extract_amce(file_path, artifact_dir)
    delete_local_artifacts(artifact_path=file_path)

    replication_studies = wandb_api.get_wandb_runs(
        filters={"config.experiment_design.hb_run_id": hb_id}
    )
    if replication_studies:
        last_replication = replication_studies[0]
        replication_amce = get_wandb_run_amce(last_replication["id"], user_details)
    else:
        replication_amce = []
        last_replication = {}

    return {
        "hb_amce": hb_amce,
        "hb_paper_data": hb_paper_data,
        "replication_amce": replication_amce,
        "replication_data": last_replication,
    }


def extract_amce(file_path: str, artifact_dir: str) -> dict:
    """Extract AMCE data and Attribute Importance from artifact file"""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            artifact_json = json.load(file)
        amce_data = artifact_json.get("AMCE_Results", [])

        amce_results = amce_data.get("AMCE_Results", [])
        attribute_importance = amce_data.get("Attribute_Importance", [])

        processed_amce = [
            {
                "attribute": item.get("attribute_text"),
                "level": item.get("level_text"),
                "coef": float(item.get("AMCE")),
                "err": float(item.get("std_error")),
                "base": bool(item.get("significance") == "Base Level"),
            }
            for item in amce_results
            if item.get("attribute_text") != "Intercept"
        ]

        processed_importance = [
            {
                "attribute": item.get("attribute_text"),
                "importance": float(item.get("importance")),
            }
            for item in attribute_importance
        ]

        return {
            "amce_results": processed_amce,
            "attribute_importance": processed_importance,
        }
    except Exception as e:
        capture_exception()
        logger.error(f"Error extracting AMCE and Attribute Importance: {e}")
        raise HTTPException(status_code=500, detail=f"Error(extract_amce): {e}")
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
        if os.path.exists(artifact_dir):
            os.rmdir(artifact_dir)


def extract_hb_amce(file_path: str, artifact_dir: str) -> list[dict]:
    """Extract AMCE data from artifact file"""
    try:
        with open(file_path, "r") as file:
            artifact_json = json.load(file)
            experiment_amce = [
                {
                    "attribute": remove_numbers_and_underscores(item[1]),
                    "level": item[2],
                    "coef": item[3],
                    "err": item[4],
                    "p_value": item[5],
                    "significance": item[6],
                }
                for item in artifact_json.get("data", [])
                if not item[2].startswith("Reference")
            ]
            return experiment_amce
    except Exception as e:
        capture_exception()
        logger.error(f"Error extracting AMCE: {e}")
        raise HTTPException(status_code=500, detail=f"Error(extract_amce): {e}")
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
        if os.path.exists(artifact_dir):
            os.rmdir(artifact_dir)


def remove_numbers_and_underscores(s):
    return re.sub(r"[\d_]+", "", s)


def get_base_model(llm_model: str) -> str:
    """
    Extracts the base model name by removing known prefixes.

    Args:
        llm_model (str): The full LLM model name.

    Returns:
        str: The base model name.
    """
    prefixes = ["AZURE_OPENAI_", "AZURE_COHERE_", "AZURE_", "GCP_"]
    for prefix in prefixes:
        if llm_model.startswith(prefix):
            return llm_model[len(prefix) :]
    return llm_model
