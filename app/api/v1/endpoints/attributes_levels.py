import asyncio
import random

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sentry_sdk import capture_exception

from app.api.v1.helpers.attributes_levels import (
    generate_product_attribute_levels,
    generate_product_price_levels,
    get_attributes,
    get_attributes_and_levels,
    get_attributes_and_levels_claude,
    get_attributes_and_levels_claude_with_score,
    get_levels,
    get_orthogonal_attributes_levels,
    get_product_attributes,
)
from app.api.v1.schemas.attributes_levels import (
    Attribute,
    AttributesLevelsRequest,
    AttributesLevelsRequestClaude,
    AttributesLevelsResponse,
    AttributesRequest,
    LeveledAttributeBase,
    LeveledAttributeBaseClaude,
    LevelsRequest,
    OrthogonalAttributesLevelsRequest,
    ProductAttributeLevelRequest,
    ProductAttributeRequest,
    ProductPriceLevelRequest,
    contains_brand,
)
from app.core.utils.common import log
from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/attributes-levels", response_model=AttributesLevelsResponse)
@log
def create_attributes_and_levels(
    req: AttributesLevelsRequest,
) -> AttributesLevelsResponse | HTTPException:
    """
    Create attributes and levels for a given why_prompt.

    Parameters:
    - `req`: AttributesLevelsRequest object containing:
        - `why_prompt`: The main concept or prompt
        - `country`: Target country
        - `level_count`: Number of levels per attribute (optional)
        - `max_length`: Maximum length for levels (optional)
        - `llm_model`: Language model to use (optional)
        - `attribute_count`: Number of attributes to generate (optional)

    Returns:
    - List of attributes with their levels.
    """
    try:
        attributes_levels = get_attributes_and_levels(
            why_prompt=req.why_prompt,
            country=req.country,
            year=req.year,
            level_count=req.level_count,
            max_length=req.max_length,
            llm_model=req.llm_model,
            attribute_count=req.attribute_count,
        )
        logger.success(
            "Successfully created attributes and levels for why_prompt:"
            f" {req.why_prompt}"
        )
        return AttributesLevelsResponse(
            attributes_levels=attributes_levels, brand_attribute_combinations=[]
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"Error: {str(e)}")


@router.post(
    "/attributes-levels-claude", response_model=list[LeveledAttributeBaseClaude]
)
def create_attributes_and_levels_claude(
    req: AttributesLevelsRequestClaude,
) -> list[LeveledAttributeBaseClaude] | HTTPException:
    """
    This is a temporary endpoint to test the new Claude model for attributes and levels generation.

    Create attributes and levels for a given why_prompt.

    Parameters:
    - `req`: AttributesLevelsRequest object containing:
        - `why_prompt`: The main concept or prompt
        - `country`: Target country
        - `level_count`: Number of levels per attribute (optional)
        - `attribute_count`: Number of attributes to generate (optional)

    Returns:
    - List of attributes with their levels.

    Raises:
    - HTTPException 404: If an error occurs during creation
    """
    try:
        attributes_levels = get_attributes_and_levels_claude(
            why_prompt=req.why_prompt,
            country=req.country,
            year=req.year,
            level_count=req.level_count,
            attribute_count=req.attribute_count,
            llm_model=req.llm_model,
        )
        logger.success(
            "Successfully created attributes and levels for why_prompt:"
            f" {req.why_prompt}"
        )
        return attributes_levels
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"Error: {str(e)}")


@router.post(
    "/attributes-levels-claude-with-score",
    response_model=list[LeveledAttributeBaseClaude],
)
def create_attributes_and_levels_claude_with_score(
    req: AttributesLevelsRequestClaude,
) -> list[LeveledAttributeBaseClaude] | HTTPException:
    """
    This is a temporary endpoint to test the new Claude model for attributes and levels generation.

    Create attributes and levels for a given why_prompt.

    Parameters:
    - `req`: AttributesLevelsRequest object containing:
        - `why_prompt`: The main concept or prompt
        - `country`: Target country
        - `level_count`: Number of levels per attribute (optional)
        - `attribute_count`: Number of attributes to generate (optional)

    Returns:
    - List of attributes with their levels.

    Raises:
    - HTTPException 404: If an error occurs during creation
    """
    try:
        attributes_levels = get_attributes_and_levels_claude_with_score(
            why_prompt=req.why_prompt,
            country=req.country,
            year=req.year,
            level_count=req.level_count,
            attribute_count=req.attribute_count,
            llm_model=req.llm_model,
        )
        logger.success(
            "Successfully created attributes and levels for why_prompt:"
            f" {req.why_prompt}"
        )
        return attributes_levels
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"Error: {str(e)}")


@router.post("/attributes-levels/orthogonal", response_model=AttributesLevelsResponse)
def create_orthogonal_attributes_and_levels(
    req: OrthogonalAttributesLevelsRequest,
) -> AttributesLevelsResponse | HTTPException:
    """
    Create new, independent (orthogonal) attributes and levels hat do not intersect with existing ones..

    Parameters:
    - `req`: OrthogonalAttributesLevelsRequest object containing:
        - `why_prompt`: The main concept - prompt text
        - `country`: Target country
        - `existing_attributes_levels`: List of existing attributes and levels
        - `new_attribute_count`: Number of new attributes to generate (optional)
        - `level_count`: Number of levels per attribute (optional)
        - `llm_model`: Language model to use (optional)

    Returns:
    - List of orthogonal attributes with their levels.

    Raises:
    - HTTPException 404: If an error occurs during creation
    """
    try:
        levels = get_orthogonal_attributes_levels(
            why_prompt=req.why_prompt,
            country=req.country,
            existing_attributes_levels=req.existing_attributes_levels,
            new_attribute_count=req.new_attribute_count,
            level_count=req.level_count,
            llm_model=req.llm_model,
        )
        logger.success("Successfully created orthogonal attributes and levels")
        return AttributesLevelsResponse(
            attributes_levels=levels, brand_attribute_combinations=[]
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=404,
            detail=f"Error creating orthogonal attributes and levels: {str(e)}",
        )


@router.post("/attributes", response_model=list[Attribute])
def create_attributes(req: AttributesRequest) -> list[Attribute] | HTTPException:
    """
    Create a list of attributes for a given why_prompt.

    Parameters:
    - `req`: AttributesRequest object containing:
        - `why_prompt`: The main concept or input question
        - `country`: Target country
        - `attribute_count`: Number of attributes to generate (optional)
        - `prompt_type`: Type of prompt to use (optional)
        - `llm_model`: Language model to use (optional)

    Returns:
    - List of attributes.

    Example Response:
    ```json
    [
        "Price",
        "Range",
        "Charging Time",
        "Safety Rating"
    ]
    ```

    Raises:
    - HTTPException 404: If an error occurs during creation
    """
    try:
        attributes = get_attributes(
            why_prompt=req.why_prompt,
            country=req.country,
            attribute_count=req.attribute_count,
            prompt_type=req.prompt_type,
            llm_model=req.llm_model,
        )
        logger.success(
            f"Successfully created attributes for why_prompt: {req.why_prompt}"
        )
        return attributes
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=404, detail=f"Error creating attributes: {str(e)}"
        )


@router.post("/levels", response_model=list[LeveledAttributeBase])
def create_levels(req: LevelsRequest) -> list[LeveledAttributeBase] | HTTPException:
    """
    Create levels for given attribute(s) of an why_prompt.

    Parameters:
    - `req`: LevelsRequest object containing:
        - `why_prompt`: The main concept or input question
        - `country`: Target country
        - `attributes`: List of attributes to generate levels for
        - `level_count`: Number of levels per attribute (optional)
        - `llm_model`: Language model to use (optional)

    Returns:
    - List of attributes with their levels.

    Raises:
    - HTTPException 404: If an error occurs during creation
    """
    try:
        levels = [
            get_levels(
                why_prompt=req.why_prompt,
                country=req.country,
                existing_attributes=req.existing_attributes,
                attribute=attribute,
                level_count=req.level_count,
                llm_model=req.llm_model,
            )
            for attribute in req.attributes
        ]
        logger.success(f"Successfully created levels for why_prompt: {req.why_prompt}")
        return levels
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=f"Error creating levels: {str(e)}")


@router.post("/product-attributes-levels", response_model=AttributesLevelsResponse)
async def product_attribute_levels(
    req: ProductAttributeRequest,
) -> AttributesLevelsResponse | HTTPException:
    """
    Generate product levels based on attributes and pricing information.

    This endpoint uses Perplexity API to process the input to generate a list of attributes that define the product or idea,
    generates pricing levels (brand & price pair), and creates levels for the provided attributes per brand/product.

    Parameters:
    - `req`: ProductAttributeRequest object containing:
        - `why_prompt`: The main question or prompt for the product
        - `country`: Target country for pricing and attributes
        - `num_attrs`: Number of attributes to generate (optional)
        - `level_count`: Number of levels per attribute to generate (optional)

    Returns:
    - A dictionary with the following keys:
        - `attribute_levels`: List of LeveledAttributeBase objects
        - `brand_attribute_combinations`: List of dictionaries with brand-attribute combinations

    Raises:
    - HTTPException 404: If an error occurs during the processing of product levels

    Note:
    - The number of attributes and levels may vary based on the input parameters and product complexity
    - This function runs attribute generation and price level generation concurrently for efficiency
    """
    try:
        llm_model = LLMModel.GCP_GEMINIFLASH
        pb = PromptBuilder()
        pe = LCELPromptExecutor(llm_model=llm_model)
        prompt2 = pb.contains_brand()
        print(req.why_prompt)
        args = {
            "why_prompt": req.why_prompt,
        }
        try:
            response = pe.execute(
                prompt=prompt2,
                args=args,
                output_object=contains_brand,
                description="Checking if the prompt contains a brand",
            )
        except Exception as e:
            logger.error(f"Error finding contains_brand {e}")
            return None
        brand_in_prompt = response.get("contains_brand")
        remove_brand = True if (brand_in_prompt == -1) else False
        if brand_in_prompt == -1:
            req.attribute_count = req.attribute_count + 1
            brand_in_prompt = 0
        contain_price = response.get("contains_price")
        req.attribute_count = req.attribute_count - 2
        if not (contain_price):
            req.attribute_count = req.attribute_count + 1
        price_levels = None
        attributes_response = None
        products = None
        price_req = ProductPriceLevelRequest(
            why_prompt=req.why_prompt, country=req.country, attribute="Price"
        )
        if brand_in_prompt > 1:
            price_levels = await generate_product_price_levels(
                price_req, contains_brand=brand_in_prompt, get_brandsmultiple=True
            )
            mid_range_brands = list(price_levels.levels["Mid-Range"].items())

            # price_levels = await generate_product_price_levels(price_req, contains_brand=brand_in_prompt, get_brandsmultiple = False, pre_cooked_brands = mid_range_brands)

            # mid_range_products = list(price_levels_brand.levels["Mid-Range"].items())

            products = dict(mid_range_brands)

            attributes_response = await get_product_attributes(req, products)
        elif brand_in_prompt == 1:
            price_levels = await generate_product_price_levels(
                price_req, contains_brand=brand_in_prompt, get_brandsmultiple=False
            )
            mid_range_items = list(price_levels.levels["Mid-Range"].items())

            products = dict(random.sample(mid_range_items, 5))

            req.attribute_count = req.attribute_count + 1
            attributes_response = await get_product_attributes(req, products)
        else:
            attributes_task = asyncio.create_task(get_product_attributes(req, products))
            price_levels_task = asyncio.create_task(
                generate_product_price_levels(
                    price_req, contains_brand=brand_in_prompt, get_brandsmultiple=False
                )
            )
            attributes_response, price_levels = await asyncio.gather(
                attributes_task, price_levels_task
            )

        attr_level_req = ProductAttributeLevelRequest(
            why_prompt=req.why_prompt,
            country=req.country,
            attributes=[
                attr
                for attr in attributes_response.attributes
                if "price" not in attr.lower()
            ],
            units=price_levels.units,
            level_count=req.level_count,
        )
        attribute_levels, brand_att = await generate_product_attribute_levels(
            attr_level_req,
            price_levels,
            brands=products,
            brands_in_prompt=brand_in_prompt,
        )
        attribute_names = [items.get("attribute") for items in attribute_levels]
        first_product = list(brand_att[0].values())[0]
        product_attributes = list(first_product.keys())
        missing_attributes = list(set(attribute_names) - set(product_attributes))
        enriched_products = []

        for product in brand_att:
            for brand_name, attributes in product.items():
                attributes["Brand"] = brand_name
                enriched_products.append(attributes)
        if brand_in_prompt == 1:
            print(f"Attribute Levels: {attribute_levels}")
            attribute_levels = [
                d for d in attribute_levels if d.get("attribute") != "Product"
            ]
        # elif brand_in_prompt > 1:
        #     price_levels = await generate_product_price_levels(
        #         price_req, contains_brand=brand_in_prompt, get_brandsmultiple=True
        #     )
        #     # Convert dictionary items to a list before sampling
        #     # Extract only the brand names (first element of each tuple)
        #     mid_range_brands = [
        #         brand for brand, _ in price_levels.levels["Mid-Range"].items()
        #     ]

        #     # Select exactly 5 random brand names

        #     # Remove existing "Brands" attribute if present
        #     attribute_levels = [
        #         d for d in attribute_levels if d.get("attribute") != "Brand"
        #     ]

        #     # Append the correctly formatted entry
        #     attribute_levels.append({
        #         "attribute": "Brand",
        #         "levels": mid_range_brands,  # Just brand names as a list
        #     })
        #     attribute_levels = [
        #         {**d, "levels": set(d["levels"])}
        #         for d in attribute_levels
        #         if len(set(d["levels"]))
        # Remove Price attribute and values if contain_price is False
        if not contain_price:
            # Remove Price from attribute_levels
            attribute_levels = [
                d for d in attribute_levels if d.get("attribute") != "Price"
            ]
            # Remove Price from each dictionary in enriched_products
            for product in enriched_products:
                if "Price" in product:
                    del product["Price"]
        if remove_brand:
            # Remove Price from attribute_levels
            attribute_levels = [
                d for d in attribute_levels if d.get("attribute") != "Brand"
            ]
            # Remove Price from each dictionary in enriched_products
        print(f"Attribute Levels: {attribute_levels}")
        print(f"Enriched Products: {enriched_products}")
        return AttributesLevelsResponse(
            attributes_levels=attribute_levels,
            brand_attribute_combinations=enriched_products,
        )

    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=404, detail=f"Error creating product levels: {str(e)}"
        )
