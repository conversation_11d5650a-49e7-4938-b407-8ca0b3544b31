import json
import logging
import os
import re
import tempfile
from typing import Dict, List

import pymupdf
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile, status
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from langchain.chains import RetrievalQ<PERSON>
from langchain.prompts import PromptTemplate
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from sentry_sdk import capture_exception

from app.api.v1.helpers.personas import TraitLevel
from app.api.v1.helpers.traits import (
    get_suggested_trait_levels,
    get_suggested_traits,
    get_trait_language_level_description,
)
from app.api.v1.schemas.traits import (
    NaturalLanguageTraitRequest,
    TraitGenerationRequest,
    TraitLevelsSuggestionRequest,
    TraitLevelsSuggestionResponse,
    TraitSuggestionResponse,
)
from app.auth.validation import validate_token
from app.core.utils.databases import get_firebase_collection, store_traits
from app.llm_prompt.prompt_templates import PDF_TO_TRAITS

oauth2_scheme = HTTPBearer()
logger = logging.getLogger(__name__)

router = APIRouter(dependencies=[Depends(validate_token), Depends(oauth2_scheme)])


@router.get("", response_model=List[TraitLevel])
def get_traits() -> List[TraitLevel]:
    """
    Retrieve all traits from the database.

    Returns:
    - `List[TraitLevel]`: A list of TraitLevel objects representing all valid traits

    TraitLevel structure:
        - `id`: Unique identifier for the trait
        - `set_type`: Optional category of the trait (e.g., "Big Five", "8 Values")
        - `type`: The type of trait (e.g., "religion")
        - `measurement_type`: How the trait is measured ("nominal", "ordinal", "interval", "ratio")
        - `ordinal_rank`: Optional ranking for ordinal traits
        - `short_description`: Brief description of the trait level (e.g., "Satanist")
        - `long_description`: Detailed description of the trait level

    Example Response:
    ```json
    [
        {
            "id": "religion_1",
            "set_type": "Personal Values",
            "type": "religion",
            "measurement_type": "nominal",
            "short_description": "Satanist",
            "long_description": "My religion is Satanism"
        },
        {
            "id": "openness_2",
            "set_type": "Big Five",
            "type": "personality",
            "measurement_type": "ordinal",
            "ordinal_rank": 2,
            "short_description": "Medium Openness",
            "long_description": "I am somewhat open to new experiences"
        }
    ]
    ```
    Notes:
    - Invalid traits are logged and skipped
    - The total number of retrieved traits is logged

    Raises:
    - HTTPException 500: If an error occurs while fetching traits
    """
    try:
        traits = get_firebase_collection("traits").get()
        trait_list = []
        for item in traits:
            data = item.to_dict()
            data["id"] = item.id
            try:
                trait = TraitLevel(**data)
                trait_list.append(trait)
            except ValueError as ve:
                logger.warning(
                    f"Skipping invalid trait {data.get('id', 'unknown')}: {ve}"
                )
                continue

        logger.info(f"Retrieved {len(trait_list)} traits")
        return trait_list
    except Exception as e:
        capture_exception(e)
        logger.error(f"Error fetching traits: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="An error occurred while fetching traits"
        )


@router.post("/extract-from-pdf", response_model=Dict[str, Dict])
def extract_traits_from_pdf_with_question(
    files: List[UploadFile] = File(...),
    question: str = Query(..., description="A user question to guide trait extraction"),
):
    """
    Upload a PDF and provide a question. The system will:
    1. Extract text from the PDF.
    2. Create embeddings and store in a vector store.
    3. Run a RetrievalQA chain to find traits relevant to the user's question.
    """
    # Combine all PDFs text (if multiple) into one corpus
    combined_text = []
    for file in files:
        if file.content_type != "application/pdf":
            raise HTTPException(
                status_code=400, detail=f"File '{file.filename}' is not a PDF."
            )

        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                tmp_file.write(file.file.read())
                tmp_file_path = tmp_file.name

            pdf_document = pymupdf.open(tmp_file_path)
            # Extract text
            text_data = []
            for page_number in range(pdf_document.page_count):
                page = pdf_document[page_number]
                text = page.get_text("text")
                text_data.append(text)
            pdf_document.close()
        finally:
            if os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)

        # Combine extracted text
        file_text = " ".join(text_data)
        file_text = re.sub(r"\s+", " ", file_text).strip()
        combined_text.append(file_text)

    # Full text of all PDFs
    full_text = " ".join(combined_text)

    # Split text into chunks for embeddings
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=100,
        length_function=len,
    )
    docs = text_splitter.create_documents([full_text])

    # Create embeddings & vector store
    embeddings = OpenAIEmbeddings()
    vectorstore = FAISS.from_documents(docs, embeddings)

    # Create a retrieval QA chain
    retriever = vectorstore.as_retriever(
        search_type="similarity", search_kwargs={"k": 4}
    )
    llm = ChatOpenAI(model_name="gpt-4", temperature=0)
    prompt = PromptTemplate(
        template=PDF_TO_TRAITS, input_variables=["context", "question"]
    )

    chain = RetrievalQA.from_chain_type(
        llm=llm,
        chain_type="stuff",
        retriever=retriever,
        chain_type_kwargs={"prompt": prompt},
    )

    # Execute the chain with the user question
    result = chain.invoke({"query": question})

    # Attempt to parse result as JSON
    try:
        traits_data = json.loads(result["result"])
    except:
        traits_data = {"traits": []}
    store_traits(traits_data)
    return JSONResponse(status_code=status.HTTP_201_CREATED, content=traits_data)


@router.post("/suggest", response_model=TraitSuggestionResponse)
async def suggest_traits(request: TraitGenerationRequest) -> TraitSuggestionResponse:
    """
    Suggest additional traits with their possible levels based on the why_prompt and currently selected traits.

    Args:
        request: TraitGenerationRequest containing:
            - why_prompt: The use case explanation
            - selected_traits: List of already selected trait names

    Returns:
        TraitSuggestionResponse containing:
            - suggested_traits: Dictionary mapping trait names to their possible levels

    Raises:
        HTTPException: If there's an error in trait suggestion process
    """
    try:
        suggested_traits = get_suggested_traits(
            why_prompt=request.why_prompt,
            selected_traits=request.selected_traits,
            number_of_new_traits=request.number_of_new_traits,
            number_of_levels_per_trait=request.number_of_levels_per_trait,
            llm_model=request.llm_model,
            country=request.country,
            max_length=request.max_length,
        )
        return TraitSuggestionResponse(suggested_traits=suggested_traits)
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail="An error occurred while suggesting traits"
        )


@router.post("/suggest/levels")
def suggest_trait_levels(
    request: TraitLevelsSuggestionRequest,
) -> TraitLevelsSuggestionResponse:
    """
    Provides levels for a given trait based on why prompt and existing traits.
    """
    try:
        suggested_levels = get_suggested_trait_levels(
            why_prompt=request.why_prompt,
            existing_traits=request.existing_traits,
            new_trait=request.new_trait,
            levels_count=request.levels_count,
            llm_model=request.llm_model,
            max_length=request.max_length,
            country=request.country,
        )
        return suggested_levels
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail="An error occurred while providing trait levels"
        )


@router.post("/natural-language", response_model=dict)
def get_natural_language_trait_description(
    request: NaturalLanguageTraitRequest,
) -> dict:
    """Get the description for a given trait level."""
    try:
        response = get_trait_language_level_description(traits=request.traits)
        return response
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail="An error occurred while getting trait description"
        )
