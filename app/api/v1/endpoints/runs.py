import os
from typing import List, Union

import wandb
from fastapi import APIRouter, Depends, HTTPException, Response, status
from fastapi.responses import FileResponse, JSONResponse
from fastapi.security import HTTPBearer
from sentry_sdk import capture_exception
from starlette.background import BackgroundTask

from app.api.v1.helpers.human_baselines import is_run_hidden, who_owns_experiment
from app.api.v1.schemas.runs import (
    CleanedRunResponse,
    RunRequest,
    RunResponse,
    UpdateConfig,
)
from app.auth.validation import validate_token
from app.core.global_config import settings
from app.core.utils.common import delete_local_dir
from app.core.utils.wandb_api import WandbAPI

oauth2_scheme = HTTPBearer()

router = APIRouter(dependencies=[Depends(validate_token), Depends(oauth2_scheme)])


@router.post("", response_model=Union[List[RunResponse], None])
def get_runs(
    filters: RunRequest = None, user_details=Depends(validate_token)
) -> list[RunResponse] | JSONResponse:
    """
    Fetch experiment runs based on provided filters.

    Parameters:
    - `filters`: RunRequest object containing filter criteria

    Returns:
    - List of RunResponse objects matching the filters

    Example filter:
    ```json
    {
      "filters": {
        "config.user": {
          "$eq": "Add a user_id here. e.g. auth0|1234567890"
        },
        "created_at": {
          "$gt": "2024-10-16T19:18:48.753240",
          "$lte": "2024-10-17T19:18:48.753258"
        }
      }
    }
    ```

    Notes:
    - Returns up to 10 runs due to performance issues - WANDB does not have pagination so for performance we should return less runs (temporary solution).
    - Excludes runs hidden from the user

    Raises:
    - HTTPException 500: If fetching runs fails
    """
    try:
        wandb_api = WandbAPI()
        runs = wandb_api.get_wandb_runs(
            filters=filters.model_dump()["filters"] if filters else None
        )[:10]
        filtered_runs = [
            run for run in runs if not is_run_hidden(run, user_id=user_details["sub"])
        ]
        wandb_api.flush()
        return [RunResponse(**run) for run in filtered_runs]
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error fetching runs: {e}")


@router.get("/all")
def get_all_user_runs(user_details=Depends(validate_token)) -> list[CleanedRunResponse]:
    """Get all the user runs/experiments."""
    try:
        wandb_api = WandbAPI()
        runs = wandb_api.get_all_user_runs(
            user_id=user_details["sub"],
        )
        wandb_api.flush()
        return runs
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error fetching runs: {e}")


@router.delete(
    path="/{run_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete an experiment run",
)
def delete_run(run_id: str, user_details: dict = Depends(validate_token)) -> Response:
    """
    Delete an experiment run by ID.

    Args:
        run_id (str): The unique identifier of the run to delete.

    Returns:
        Response: Empty response with 204 status code on successful deletion.

    Raises:
        HTTPException: For various error scenarios including unauthorized access, not found, or server errors.
    """
    wandb_api = WandbAPI()

    try:
        run = wandb_api.get_wandb_run_details(run_id)
    except HTTPException as e:
        if e.status_code == status.HTTP_404_NOT_FOUND:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Run not found"
            )
        raise

    if who_owns_experiment(run) != user_details["sub"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this run",
        )

    success, message = wandb_api.delete_wandb_run(run_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=message
        )

    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.get("/public-count")
def get_public_runs_count():
    """
    Get the count of public experiments runs.

    Returns:
    JSONResponse with:
    - `count`: Number of public runs

    Example Response:
    ```json
    {
        "count": 42
    }
    ```
    Notes:
    - Public runs are those where `config.experiment_design.is_private` is `False` or `None`

    Raises:
    - HTTPException 500: If fetching the count fails
    """
    try:
        wandb_api = WandbAPI()
        public_runs = wandb_api.get_wandb_runs(
            filters={"config.experiment_design.is_private": {"$in": [False, None]}}
        )
        count = len(public_runs)
        wandb_api.flush()
        return JSONResponse(content={"count": count})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error fetching public runs count: {e}"
        )


@router.get("/user-runs-count")
def get_user_runs_count(user_details: dict = Depends(validate_token)):
    """
    Get the count of runs for the authenticated user.

    Parameters:
    - `user_details`: User authentication details (injected by dependency)

    Returns:
    JSONResponse with:
    - `runs_count`: Number of runs for the user

    Example Response:
    ```json
    {
        "runs_count": 15
    }
    ```
    Raises:
    - HTTPException 500: For other errors in fetching the count
    """
    try:
        wandb_api = wandb.Api(api_key=os.getenv("WANDB_API_KEY"))
        runs = wandb_api.runs(
            path=f"{settings.WANDB_ENTITY}/{settings.WANDB_PROJECT}",
            filters={"config.user": user_details["sub"]},
        )
        count = len(runs)
        wandb_api.flush()
        return JSONResponse(content={"runs_count": count})
    except wandb.CommError as e:
        capture_exception(e)
        raise HTTPException(
            status_code=503, detail=f"Error communicating with W&B: {str(e)}"
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error fetching user runs count: {str(e)}"
        )


@router.get("/{run_id}")
def get_run(run_id: str, user_details=Depends(validate_token)):
    """
    Fetch details of a specific experiment run.

    Parameters:
    - `run_id`: Unique identifier of the run.

    Returns:
    JSONResponse with:
    - `run_details`: Details of the requested run

    Notes:
    - Returns 403 if the run is private and not accessible to the user

    Raises:
    - HTTPException 500: If fetching run details fails
    """
    try:
        wandb_api = WandbAPI()
        wandb_run_details = wandb_api.get_wandb_run_details(run_id)
        # NOTE: Uncomment the following lines if you want to enforce privacy checks
        # Disable for now to allow the chatbot to access all runs
        # if is_run_hidden(wandb_run_details, user_id=user_details["sub"]):
        #     return JSONResponse(
        #         content={"error": "Experiment is private"}, status_code=403
        #     )
        wandb_api.flush()
        return JSONResponse(content={"run_details": wandb_run_details})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error fetching run details: {e}")


@router.get("/{hb_id}/details")
def get_hb_details(hb_id: str):
    """
    Fetch details of a specific Human Baseline experiment.

    Parameters:
    - `hb_id`: Unique identifier of the Human Baseline experiment. e.g. `7h2ks593`

    Returns:

    JSONResponse containing the experiment details

    Raises:
    - HTTPException 500: If fetching HB experiment details fails
    """
    try:
        wandb_api = WandbAPI(project=settings.HUMAN_BASELINE_PROJECT)
        hb_details = wandb_api.get_wandb_run_details(hb_id)
        wandb_api.flush()
        return JSONResponse(content=hb_details)
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error fetching HB experiment details: {e}"
        )


@router.put("/{run_id}/update_config")
async def update_config(
    run_id: str, config_update: UpdateConfig, user_details=Depends(validate_token)
):
    """
    Update configuration for a specific experiment run.

    Parameters:
    - `run_id`: Unique identifier of the run
    - `config_update`: UpdateConfig object containing configuration changes

    Returns:
    JSONResponse with:
    - `message`: Status of the update
    - `configs`: Updated configuration (if successful)

    Notes:
    - Only the experiment owner can update the configuration
    - Currently supports toggling the privacy setting

    Raises:
    - HTTPException 401: If user is not authorized to edit the run
    - HTTPException 400: If the config update is invalid
    - HTTPException 500: If updating the run config fails
    """
    try:
        wandb_api = WandbAPI()
        run = wandb_api.get_wandb_run_details(run_id)

        if who_owns_experiment(run) != user_details["sub"]:
            return JSONResponse(
                status_code=401, content={"message": "User not authorized to edit run"}
            )

        if "set_privacy" in config_update.config_update:
            config_key = "config" if "config" in run else "configs"
            current_privacy = run[config_key]["experiment_design"].get(
                "is_private", False
            )
            new_privacy = not current_privacy  # Toggle the privacy value
            updated_configs = wandb_api.update_configs(
                run_id,
                config_updates={"experiment_design": {"is_private": new_privacy}},
            )
            wandb_api.flush()

            return JSONResponse(
                status_code=200,
                content={
                    "message": "Configuration updated successfully",
                    "configs": updated_configs,
                },
            )
        return JSONResponse(
            status_code=400, content={"message": "Invalid config update"}
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error updating run config: {e}")


@router.get("/artifact/{file_name}")
def get_run_artifact(file_name: str):
    """
    Fetch a specific artifact file from a run.

    Parameters:
    - `file_name`: Name of the artifact file to retrieve

    Returns:
    - FileResponse: The requested artifact file
    - JSONResponse: Error message if the file is not found

    Notes:
    - Automatically deletes the local artifact directory after serving the file

    Raises:
    - HTTPException 504: If the request times out
    - HTTPException 500: For other errors in fetching the artifact
    """
    try:
        wandb_api = WandbAPI()
        api_res = wandb_api.get_wandb_run_artifact(file_name=file_name)

        if api_res[0]:
            wandb_artifact_directory = api_res[1]
            wandb_artifact_file = api_res[2]
            artifact_file = FileResponse(
                wandb_artifact_file,
                background=BackgroundTask(
                    delete_local_dir, "", wandb_artifact_directory
                ),
            )
            wandb_api.flush()
            return artifact_file
        else:
            return JSONResponse(content={"error": str(api_res[1])}, status_code=404)
    except TimeoutError:
        raise HTTPException(
            status_code=504, detail=f"Request timed out while fetching {file_name}."
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error fetching run artifact: {e}")
