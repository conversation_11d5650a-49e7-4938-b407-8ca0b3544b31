import asyncio
import json
from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from sentry_sdk import capture_exception
from sse_starlette.sse import EventSourceResponse

from app.auth.validation import validate_token
from app.core.utils.auth0 import auth0_client
from app.core.utils.logging import app_logger
from app.core.utils.novu_notifs import trigger_novu_notification

router = APIRouter()
logger = app_logger.get_logger(__name__)

subscribers: Dict[str, asyncio.Queue] = {}
subscribers_lock = asyncio.Lock()


@router.post("/webhook")
async def webhook(request: Request):
    """
    Handle experiment state webhooks and trigger notifications.
    """
    data = await request.json()
    user_id = data.get("user_id")
    experiment_state = data.get("state")
    url = data.get("url")
    prompt = data.get("why_prompt")

    logger.info(f"Webhook received for user_id: {user_id}, state: {experiment_state}")

    try:
        user_info = auth0_client.get_user_info(user_id)
        email = user_info.get("email")
        given_name = user_info.get("given_name", email.split("@")[0])
    except Exception as e:
        capture_exception(e)
        logger.error(f"Failed to retrieve user info for user_id: {user_id}")
        email = "<EMAIL>"
        given_name = "User"

    try:
        await handle_experiment_state(
            user_id, experiment_state, email, given_name, url, prompt, data
        )
        return JSONResponse({"status": "received"})
    except Exception as e:
        capture_exception(e)
        logger.error(f"Failed to process webhook data for user_id: {user_id}")
        raise HTTPException(status_code=500, detail="Failed to process webhook data.")


@router.get("/events")
async def events(token: Dict = Depends(validate_token)):
    """
    Establish SSE connection for real-time updates.
    """
    user_id = token.get("sub")
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid token")

    logger.info(f"User {user_id} connected to SSE")
    return EventSourceResponse(event_generator(user_id))


async def handle_experiment_state(
    user_id: str,
    experiment_state: str,
    email: str,
    given_name: str,
    url: str = None,
    prompt: str = None,
    data: dict = None,
):
    """
    Handle different experiment states and trigger notifications.
    """
    notifications = {
        "started": {
            "workflow_id": "on-boarding-notification-ivVE42urZ",
            "payload": {
                "message": "A new experiment has started.",
                "email": email,
                "firstname": given_name,
            },
        },
        "crashed": {
            "workflow_id": "experiment-crashed",
            "payload": {
                "message": "An experiment has crashed.",
                "email": email,
                "firstname": given_name,
            },
        },
        "finished": {
            "workflow_id": "on-boarding-notification-t8PW7Q8dz",
            "payload": {
                "message": "An experiment has finished.",
                "email": email,
                "firstname": given_name,
                "url": url,
                "prompt": prompt,
            },
        },
    }

    if experiment_state in notifications:
        notification = notifications[experiment_state]
        await trigger_novu_notification(
            user_id, notification["workflow_id"], notification["payload"]
        )
        logger.info(
            f"Notification triggered for {experiment_state} experiment by user"
            f" {user_id}"
        )

    async with subscribers_lock:
        if user_id in subscribers:
            await subscribers[user_id].put(data)
            logger.info(f"User {user_id} notified about new run")
        else:
            logger.error(f"User {user_id} not found in subscribers.")


async def event_generator(user_id: str):
    """Generate SSE events."""
    queue = asyncio.Queue()
    try:
        async with subscribers_lock:
            subscribers[user_id] = queue

        while True:
            try:
                data = await asyncio.wait_for(queue.get(), timeout=60)
                yield {
                    "event": data["state"],
                    "data": json.dumps(data),
                }
            except asyncio.TimeoutError:
                yield {
                    "event": "keepalive",
                    "data": json.dumps({"message": "keepalive"}),
                }
    except asyncio.CancelledError:
        logger.info(f"User {user_id} disconnected from SSE")
    finally:
        async with subscribers_lock:
            subscribers.pop(user_id, None)
