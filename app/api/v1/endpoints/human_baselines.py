import datetime
import json
import os
import re
import tempfile
import time
from datetime import datetime, timezone
from typing import Dict, List

import pandas as pd
import pymupdf
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, status
from fastapi.responses import J<PERSON>NResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_openai import ChatOpenAI
from sentry_sdk import capture_exception

from app.api.v1.endpoints.experiments import create_experiment_async
from app.api.v1.helpers.experiments import (
    get_hb_amce_from_github,
    get_hb_experiment_definition_from_github,
)
from app.api.v1.helpers.human_baselines import get_base_model
from app.api.v1.schemas.experiments import Experiment
from app.auth.validation import validate_token
from app.core.global_config import settings
from app.core.utils.common import delete_local_artifacts
from app.core.utils.get_human_baselines import (
    check_folder_exists_in_github,
    file_exists_in_github,
    get_transcription_csv_from_github,
    write_file_to_github,
)
from app.core.utils.hb_estimation import HB_Transcription_Check
from app.core.utils.logging import app_logger
from app.core.utils.wandb_api import WandbAPI
from app.llm_prompt.prompt_templates import fields

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.get("/transcriptions/{hb_folder}/validate")
def validate_transcriptions(hb_folder: str):
    """This endpoint validates the transcriptions"""
    df = get_transcription_csv_from_github(hb_folder=hb_folder)
    encoding_type, Attribute_base, Missing_base = HB_Transcription_Check(HB_data=df)
    if encoding_type != "Unknown" and Missing_base is False:
        return JSONResponse(
            content={
                "message": "Valid transcription",
                "encoding_type": encoding_type,
                "Missing_base": Missing_base,
                "Attribute_base": Attribute_base,
            }
        )
    else:
        return JSONResponse(
            content={
                "message": "Invalid transcription",
                "encoding_type": encoding_type,
                "Missing_base": Missing_base,
                "Attribute_base": Attribute_base,
            },
            status_code=400,
        )


@router.get("/{hb_folder}/replications-amces")
def fetch_human_baseline_replications_amces(hb_folder: str) -> dict:
    """
    Fetches human baseline experiment data and AMCE results for unique LLM schemas.

    Args:
        hb_folder (str): The name of the human baseline folder.

    Returns:
        dict: A dictionary containing hb data and unique replication details.
    """
    wandb_api = WandbAPI(project="hb_core2")

    try:
        hb_experiment_definition = get_hb_experiment_definition_from_github(
            hb_folder=hb_folder
        )
    except Exception as e:
        logger.error(
            f"Failed to fetch experiment definition for folder '{hb_folder}': {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Experiment folder '{hb_folder}' not found or inaccessible.",
        )

    try:
        human_baseline_amce = get_hb_amce_from_github(hb_folder)
    except Exception as e:
        logger.error(f"Failed to fetch AMCE data for folder '{hb_folder}': {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"AMCE data not found for folder '{hb_folder}'.",
        )
    try:
        replications = wandb_api.get_wandb_runs(
            filters={"config.experiment_design.hb_folder": hb_folder}
        )
        wandb_api.flush()
    except Exception as e:
        logger.error(
            f"Failed to fetch replications from WandB for folder '{hb_folder}': {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve replication details for folder '{hb_folder}'.",
        )
    unique_models = set()
    replication_details = []
    hb_amce = None
    cutoff_date = datetime(2025, 2, 1, tzinfo=timezone.utc)
    for replication in replications:
        created_at_str = replication.get("created_at")
        if created_at_str:
            created_at = datetime.fromisoformat(
                created_at_str.replace("Z", "+00:00")
            )  # This is offset-aware

            # Ensure both are timezone-aware before comparing
            if created_at < cutoff_date:
                continue  # Skip this replication if it was created before February 2025

        config = replication.get("config", {}).get("experiment_design", {})
        llm_model_a = config.get("expr_llm_model")
        llm_model = get_base_model(llm_model_a)
        replication_name = replication.get("name")
        replication_id = replication.get("id")

        if llm_model in unique_models:
            continue
        unique_models.add(llm_model)

        file_name = f"amce_table-{replication_name}"

        try:
            _, _, amce_file = wandb_api.get_wandb_run_artifact(file_name)
            hb_amce = None
            replication_amce = []

            df = pd.read_csv(amce_file)
            delete_local_artifacts(artifact_path=amce_file)

            for _, row in df.iterrows():
                attribute_text = row["Attribute"]
                level_text = row["Levels"]
                base = row["Base"] == "Yes"
                calculated_amce = row["Calculated_AMCE"]
                calculated_std_error = row["Calculated_Standard_Error"]
                reported_amce = row["Reported_AMCE"]
                reported_std_error = row["Reported_Standard_Error"]

                replication_amce.append({
                    "base": base,
                    "attribute_text": attribute_text,
                    "level_text": level_text,
                    "Calculated AMCE": calculated_amce,
                    "Calculated std_error": calculated_std_error,
                })
                if not hb_amce:
                    hb_amce = []

                hb_amce.append({
                    "base": base,
                    "attribute_text": attribute_text,
                    "level_text": level_text,
                    "Reported AMCE": reported_amce,
                    "Reported std_error": reported_std_error,
                })
        except Exception as e:
            capture_exception(e)
            logger.error(
                f"Failed to process AMCE data for replication {replication_name}: {e}"
            )
            replication_amce = None
        run_details = wandb_api.get_wandb_run_details(replication_id)
        run_summary = run_details.get("summary")
        desired_keys = [
            "Coverage Probability for Betas",
            "Frobenius_similarity",
            "Manhattan_similarity",
            "Model",
            "Number of Alternatives",
            "Number of Parameters Estimated",
            "Opt-out Included ",
            "Percentage of Parameters with same Calculated & Reported Sign",
            "Spearman Correlation Beta Values",
            "Spearman Correlation Feature Importance",
            "Spectral_similarity",
            "Standard Deviation Normalized Mean Absolute Error Beta Values",
            "Standard Deviation Normalized Mean Absolute Error Feature Importance",
            "sample_size",  # if present
            "tasks_per_respondent",  # if present
            "total_number_of_task",  # if present
        ]

        # Create a new dictionary with only the desired keys
        filtered_summary = {
            key.replace(" ", "_"): run_summary[key]
            for key in desired_keys
            if key in run_summary
        }
        if "Opt-out_Included_" in filtered_summary:
            filtered_summary["Opt_out_Included"] = filtered_summary.pop(
                "Opt-out_Included_"
            )
        replication_details.append({
            "replication_name": replication_name,
            "replication_id": replication_id,
            "llm_model": llm_model,
            "amce_results": replication_amce,
            "Measure_of_Effectiveness": filtered_summary,
        })
    # delete the artifact from local directory after fetching the data
    return {
        "hb_experiment_definition": hb_experiment_definition,
        "hb_experiment_amce": hb_amce,
        "replications_amces": replication_details,
    }


@router.post("/transcribe-from-pdf", response_model=Dict[str, Dict])
def transcription_humanbaseline(
    files: List[UploadFile] = File(...),
):
    """
    Upload a PDF of the paper you want to transcribe. The system will:
    1. Extract text from the PDF.
    2. Create embeddings and store in a vector store.
    3. Run a RetrievalQA chain to find all relevant fields to transcribe the paper.
    """
    # Combine all PDFs text (if multiple) into one corpus
    combined_text = []
    for file in files:
        if file.content_type != "application/pdf":
            raise HTTPException(
                status_code=400, detail=f"File '{file.filename}' is not a PDF."
            )

        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                tmp_file.write(file.file.read())
                tmp_file_path = tmp_file.name

            pdf_document = pymupdf.open(tmp_file_path)
            # Extract text
            text_data = []
            for page_number in range(pdf_document.page_count):
                page = pdf_document[page_number]
                text = page.get_text("text")
                text_data.append(text)
            pdf_document.close()
        finally:
            if os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)

        # Combine extracted text
        file_text = " ".join(text_data)
        file_text = re.sub(r"\s+", " ", file_text).strip()
        combined_text.append(file_text)

    # Full text of all PDFs
    full_text = " ".join(combined_text)

    # Split text into chunks for embeddings
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=4500,
        chunk_overlap=700,
        length_function=len,
    )
    docs = text_splitter.create_documents([full_text])

    # Create embeddings & vector store
    embeddings = HuggingFaceEmbeddings(
        model_name="sentence-transformers/msmarco-distilbert-base-v4"
    )
    vectorstore = FAISS.from_documents(docs, embeddings)

    # Create a retrieval QA chain
    retriever = vectorstore.as_retriever(
        search_type="similarity", search_kwargs={"k": 6}
    )
    llm = ChatOpenAI(model_name="gpt-4", temperature=0)
    results = {}
    for field, details in fields.items():
        query = details["query"]
        prompt_template = details["prompt"]
        prompt = PromptTemplate(
            template=prompt_template, input_variables=["context", "query"]
        )

        chain = RetrievalQA.from_chain_type(
            llm=llm,
            chain_type="stuff",
            retriever=retriever,
            chain_type_kwargs={"prompt": prompt},
        )

        # Execute the chain with the user query
        response = chain.invoke({"query": query})

        # Attempt to parse result as JSON
        try:
            results[field] = json.loads(response["result"])
        except json.JSONDecodeError:
            results[field] = response["result"]
    # store_traits(traits_data)
    return JSONResponse(status_code=status.HTTP_201_CREATED, content=results)


@router.post("/execute/batch")
def execute_human_baselines(
    hb_folders_csv_file: UploadFile = File(...),
    output_filename: str = "human_baseline_output",
    user_details=Depends(validate_token),
):
    """
    This endpoint is used run the human baselines based on the list of
    folders provided in the csv request file. The folder names should be
    available in the Ditto repository here:
    https://github.com/Subconscious-ai/Ditto/tree/main/transcriptions/transcriptions_consolidated
    """
    if "." in output_filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=(
                "Output file name should not contain any extension. Please provide a"
                " valid file name."
            ),
        )
    if file_exists_in_github(file_name=output_filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Output file already exists. Please choose a different filename.",
        )

    if hb_folders_csv_file.content_type != "text/csv":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=(
                "Invalid file format. Please provide a CSV file with the list of human"
                " baselines folders."
            ),
        )
    results = []
    all_runs_completed = False
    wandb_api = WandbAPI(project=settings.WANDB_REPLICATION_PROJECT)
    try:
        df = pd.read_csv(hb_folders_csv_file.file, index_col=0, encoding="utf-8")
        folders = df.index.tolist()
        llm_models = df["LLM"].tolist()
        for hb_folder, llm_model in zip(folders, llm_models):
            logger.info(f"Running human baselines for folder '{hb_folder}'")
            folder_exists = check_folder_exists_in_github(hb_folder)
            if folder_exists:
                experiment_request = Experiment(
                    hb_folder=hb_folder,
                    expr_llm_model=llm_model,
                )
                response = create_experiment_async(
                    req=experiment_request,
                    user_details=user_details,
                )
                if isinstance(response, JSONResponse):
                    results.append([
                        hb_folder,
                        llm_model,
                        "NA",
                        "NA",
                        True,
                        "Invalid Transcription",
                    ])
                else:
                    logger.info(f"Experiment created with response: {response}")
                    wandb_run_id = response.wandb_run_id
                    wandb_run_name = response.wandb_run_name
                    if wandb_run_name:
                        results.append([
                            hb_folder,
                            llm_model,
                            wandb_run_id,
                            wandb_run_name,
                            False,
                            "Valid Transcription",
                        ])
                    else:
                        results.append([
                            hb_folder,
                            llm_model,
                            wandb_run_id,
                            wandb_run_name,
                            True,
                            "Run could not be started",
                        ])
            else:
                results.append([
                    hb_folder,
                    llm_model,
                    "NA",
                    "NA",
                    True,
                    "HB folder does not exists",
                ])
    except Exception as e:
        logger.error(f"Failed to read CSV file: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=(
                "Failed to read CSV file. Please check the file format and try again."
            ),
        )
    valid_transcriptions = list(set([result[5] for result in results]))
    if "Valid Transcription" in valid_transcriptions:
        while not all_runs_completed:
            for index, result in enumerate(results):
                (
                    hb_folder,
                    llm_model,
                    wandb_run_id,
                    wandb_run_name,
                    run_completed,
                    experiment_flag,
                ) = result
                if experiment_flag == "Valid Transcription" and wandb_run_name:
                    if not run_completed:
                        try:
                            run = wandb_api.get_wandb_run_details(wandb_run_id)
                            wandb_api.flush()
                            logger.info(
                                f"Run status for '{wandb_run_name}':"
                                f" {run.get('run_state')}"
                            )
                            if (
                                run.get("run_state") == "finished"
                                or run.get("run_state") == "crashed"
                            ):
                                results[index][4] = True
                        except Exception as e:
                            logger.error(f"Error checking run status: {e}")
                            pass
            all_runs_completed = all([result[4] for result in results])
            if not all_runs_completed:
                time.sleep(30)
        output = []
        for index, result in enumerate(results):
            if result[5] == "Valid Transcription":
                run_details = wandb_api.get_wandb_run_details(result[2])
                run_summary = run_details.get("summary")
                output.append([
                    result[0],
                    result[1],
                    result[2],
                    run_summary.get("Model", "NA"),
                    run_summary.get("Number of Alternatives", 0),
                    run_summary.get("Number of Parameters Estimated", 0),
                    run_summary.get("Opt-out Included", False),
                    run_summary.get("Coverage Probability for Betas", 0),
                    run_summary.get(
                        "Percentage of Parameters with same Calculated & Reported Sign",
                        0,
                    ),
                    run_summary.get("Spearman Correlation Beta Values", 0),
                    run_summary.get("Spearman Correlation Feature Importance", 0),
                    run_summary.get(
                        "Standard Deviation Normalized Mean Absolute Error Beta Values",
                        100,
                    ),
                    run_summary.get(
                        "Standard Deviation Normalized Mean Absolute Error Feature"
                        " Importance",
                        100,
                    ),
                    run_summary.get("Frobenius_similarity", 0),
                    run_summary.get("Manhattan_similarity", 0),
                    run_summary.get("Spectral_similarity", 0),
                    run_summary.get("tasks_per_respondent", 0),
                    run_summary.get("sample_size", 0),
                    run_summary.get("total_number_of_tasks", 0),
                    (
                        "success"
                        if run_summary.get("Number of Alternatives", 0) > 0
                        else "failed"
                    ),
                    "Valid Transcription",
                ])
            else:
                output.append([
                    result[0],
                    result[1],
                    result[2],
                    "NA",
                    0,
                    0,
                    False,
                    0,
                    0,
                    0,
                    0,
                    100,
                    100,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    "NA",
                    "Invalid Transcription",
                ])

        output_df = pd.DataFrame(
            output,
            columns=[
                "hb_folder",
                "llm_model",
                "wandb_run_id",
                "Model",
                "Number of Alternatives",
                "Number of Parameters Estimated",
                "Opt-out Included",
                "Coverage Probability for Betas",
                "Percentage of Parameters with same Calculated & Reported Sign",
                "Spearman Correlation Beta Values",
                "Spearman Correlation Feature Importance",
                "Standard Deviation Normalized Mean Absolute Error Beta Values",
                "Standard Deviation Normalized Mean Absolute Error Feature Importance",
                "Frobenius_similarity",
                "Manhattan_similarity",
                "Spectral_similarity",
                "Tasks_per_respondent",
                "Sample_size",
                "Total_number_of_tasks",
                "Status",
                "Transcription Type",
            ],
        )
        write_file_to_github(output_df, output_filename)
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "message": "Human baselines have been executed successfully.",
                "output_filename": f"{output_filename}.csv",
            },
        )
