from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sentry_sdk import capture_exception

from app.api.v1.schemas.markekt_simulator import (
    CheckProductRealWorldResponse,
    CheckProductRequest,
)
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import Prompt<PERSON>uilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/check-realworld-product", response_model=CheckProductRealWorldResponse)
def check_for_product(req: CheckProductRequest) -> CheckProductRealWorldResponse:
    """
    Checks if a why_prompt can correspond to a real-world product.
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model)
    pb = PromptBuilder()
    prompt = pb.check_for_product()

    try:
        response = pe.execute(
            prompt=prompt,
            args={"why_prompt": req.why_prompt},
            description="Checking if the product exists in the real world",
            output_object=CheckProductRealWorldResponse,
        )

        product_exists = response.get("product_exists")
        score = response.get("score")
        # product_exists = True if product_exists and score == 5 else False
        # if product_exists:
        #     attribute_reason = check_generic_or_specific_attribute(
        #         why_prompt=req.why_prompt
        #     )
        #     attribute_type = attribute_reason.get("attribute_type")
        #     if attribute_type == "generic":
        #         product_exists = False
        #         score = 0
        return CheckProductRealWorldResponse(
            product_exists=product_exists,
            score=score,
            measurable_attributes=response.get("measurable_attributes"),
            specific_levels_definable=response.get("specific_levels_definable"),
            data_available_collectible=response.get("data_available_collectible"),
        )
    except Exception as e:
        capture_exception()
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
