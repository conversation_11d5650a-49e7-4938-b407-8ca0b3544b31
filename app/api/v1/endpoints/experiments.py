import json
import os
import random
import shutil
import uuid
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from langsmith import traceable
from pydantic.json import pydantic_encoder
from sentry_sdk import capture_exception

from app.api.v1.endpoints.copilot import check_causality
from app.api.v1.helpers.dependent_variables import generate_dependent_variable
from app.api.v1.helpers.experiments import (
    generate_conjoint_statements,
    get_experiment_io,
    get_hb_experiment_definition_from_github,
    get_hb_experiment_definition_from_wandb,
    get_recursive_experiment,
    iterate_over_experiment_idea,
    run_experiment,
)
from app.api.v1.schemas.copilot import CausalityRequest
from app.api.v1.schemas.experiments import (
    DependentVariable,
    DependentVariableResponse,
    Experiment,
    ExperimentAsyncResponse,
    ExperimentIdea,
    LikertLabelRequest,
    LikertLabelResponse,
    RealWorldConjoinStatementFinalResponse,
    RealWorldConjoinStatementIndividualResponse,
    RealWorldConjoinStatementRequest,
)
from app.auth.validation import validate_token
from app.core.core import get_study_subject
from app.core.global_config import settings
from app.core.utils.aws_s3 import download_file_from_s3
from app.core.utils.common import (
    convert_image_to_base64,
    delete_local_artifacts,
    summarize_image,
)
from app.core.utils.constants import CAUSAL_WORDS
from app.core.utils.get_human_baselines import get_transcription_csv_from_github
from app.core.utils.hb_estimation import HB_Transcription_Check
from app.core.utils.logging import app_logger
from app.core.utils.type_enums import LLMModel
from app.core.utils.wandb_api import WandbAPI
from app.core.utils.workers import (
    enqueue_experiment_with_priority,
    get_queued_experiments_for_user,
)
from app.llm_prompt.prompt_builder import PromptBuilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor
from app.llm_prompt.prompt_templates import (
    DETAILED_IMAGE_DESCRIPTION_TEMPLATE,
    IMAGE_BASED_INTRO_TEMPLATE,
)

oauth2_scheme = HTTPBearer()

router = APIRouter(dependencies=[Depends(oauth2_scheme)])

logger = app_logger.get_logger(__name__)


@traceable(
    run_type="llm",
    name="Create Experiment Async",
    tags=[
        "Experiment",
        "Async Creation",
        "Causal Analysis",
    ],
    metadata={
        "Input": "Experiment Configuration",
        "Output": "Wandb Run ID and Name",
        "Processing": "Async Queue or Debug Mode",
    },
)
@router.post("", response_model=ExperimentAsyncResponse)
def create_experiment_async(
    req: Experiment,
    user_details=Depends(validate_token),
) -> JSONResponse | ExperimentAsyncResponse:
    """
    Run a novel experiment asynchronously using provided requirements.

    This endpoint initiates an experiment based on the input parameters.
    It can handle both regular experiments
    and human baseline (HB) experiments. The experiment is either run immediately (in debug mode) or enqueued
    for processing.

    Parameters:
    - `req`: Experiment object containing experiment details

    Notes:
    - For HB experiments, provide either `hb_folder` or `hb_run_id` in the request
    - The experiment is run immediately if DEBUG mode is True, otherwise it's enqueued
    - The `wandb_run_name` is generated using a random causal word and the current timestamp
    """

    user_id = user_details["sub"]
    wandb_id = str(uuid.uuid4())
    wandb_run_name = f"{random.choice(CAUSAL_WORDS)}-{datetime.now(timezone.utc).strftime('%y-%m-%d-%H-%M-%S-%f')[:-3]}"
    is_hb_run = bool(req.hb_run_id or req.hb_folder)

    if is_hb_run and req.hb_folder:
        df = get_transcription_csv_from_github(hb_folder=req.hb_folder)
        encoding_type, Attribute_base, Missing_base = HB_Transcription_Check(HB_data=df)
        if encoding_type == "Unknown" and Missing_base:
            return JSONResponse(
                status_code=500,
                content=(
                    f"Data encoding type used in the paper: {encoding_type}. "
                    f"Variables missing base level: {Missing_base}. "
                    f"Attributes and their base levels: {Attribute_base}."
                ),
            )
    try:
        # Create custom request for human baseline experiments
        if req.hb_folder:
            experiment = get_hb_experiment_definition_from_github(
                req.hb_folder, req.expr_llm_model
            )
        elif req.hb_run_id:
            experiment = get_hb_experiment_definition_from_wandb(
                req.hb_folder, req.expr_llm_model
            )
        else:
            experiment = req
            experiment.is_hb_run = is_hb_run

        experiment.why_prompt = (
            experiment.why_prompt[0].upper() + experiment.why_prompt[1:]
        )
        if settings.DEBUG == "True":
            run_experiment(
                experiment,
                user_id=user_id,
                wandb_id=wandb_id,
                wandb_name=wandb_run_name,
            )
        else:
            # We need to convert to JSON before pushing to Celery queue
            experiment_json = json.dumps(
                experiment.model_dump(), default=pydantic_encoder
            )
            enqueue_experiment_with_priority(
                experiment_json, wandb_id, wandb_run_name, user_id
            )
        return ExperimentAsyncResponse(
            wandb_run_id=wandb_id, wandb_run_name=wandb_run_name
        )
    except Exception as e:
        capture_exception(e)
        return ExperimentAsyncResponse(wandb_run_id="", wandb_run_name="")


@router.post("/next-experiment-suggestion")
def create_next_experiment_suggestion(
    experiment_wandb_id: str, user_details=Depends(validate_token)
):
    """Create another experiment based on Q&A of previous experiment."""
    try:
        experiment_data = get_experiment_io(wandb_id=experiment_wandb_id)

        experiment_ideas, context = get_recursive_experiment(
            data=experiment_data,
            llm_model=LLMModel.AZURE_GPTO3_MINI,
            run_id=experiment_wandb_id,
        )
        validated_experiment_ideas = []
        for idea in experiment_ideas:
            try:
                experiment_idea = ExperimentIdea(**idea)
            except Exception as e:
                continue

            validated_experiment = iterate_over_experiment_idea(
                experiment_idea.why_question,
                experiment_idea.rationale,
                context,
                experiment_wandb_id,
                llm_model=LLMModel.AZURE_GPTO3_MINI,
            )
            if validated_experiment == None:
                continue
            last_why_question = validated_experiment["why_questions_refined"][
                -1
            ].why_question
            last_rationale = validated_experiment["why_questions_refined"][-1].rationale
            request = CausalityRequest(why_prompt=last_why_question)
            causality_check = check_causality(request)
            print(f"Final causality check: {causality_check}")
            if causality_check.is_causal:
                validated_idea = {
                    "why_question": last_why_question,
                    "rationale": last_rationale,
                }
                validated_experiment_ideas.append(validated_idea)
        return validated_experiment_ideas
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error creating new ideas: {e}")


@router.post("/dependent-variable", response_model=DependentVariableResponse)
def create_dependent_variable(req: DependentVariable):
    """
    Generate a dependent variable string (survey prompt) based on the input question.

    This endpoint creates a dependent variable that synthetic respondents will respond to in an experiment.
    It allows users to review the generated survey prompt before running the experiment.

    Parameters:
    - `req`: DependentVariable object containing:
        - `prompt_type`: Enum representing the type or focus of the experiment
        - `why_prompt`: String containing the user's input question

    Returns:
    - DependentVariableResponse object with the following structure:
        - `dependent_variable`: String representing the generated survey prompt

    Example Request:
    ```json
    {
        "prompt_type": "generic",
        "why_prompt": "How does social media usage affect sleep patterns?"
    }
    ```

    Example Response:
    ```json
    {
      "dependent_variable": "Please read the descriptions of the two social media usage scenarios carefully. Then, please indicate which of the two scenarios you believe has a more significant impact on sleep patterns."
    }
    ```

    Notes:
    - The dependent variable is generated using the GPT-4 language model
    - The generated dependent variable can be added to the experiment object

    Raises:
    - HTTPException 500: If an error occurs during the generation of the dependent variable
    """
    try:
        dependent_variable = generate_dependent_variable(
            prompt_type=req.prompt_type,
            why_prompt=req.why_prompt,
            dv_llm_model=req.llm_model,
        )
        return DependentVariableResponse(dependent_variable=dependent_variable)
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error generating dependent variable: {e}"
        )


@router.post("/study-subject")
def create_study_subject(why_prompt: str):
    """Create a study subject based on the input question."""
    llm_model = LLMModel.GCP_GEMINIFLASH
    try:
        return get_study_subject(why_prompt=why_prompt, llm_model=llm_model)
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error creating study subject: {e}"
        )


@router.get("/queued")
def get_queued_experiments(user_details=Depends(validate_token)):
    """Get queued experiments of a user"""
    return get_queued_experiments_for_user(user_id=user_details["sub"])


@router.post("/rerun-analytics")
async def rerun_analytics(
    wandb_id: str = Form(...),
):
    """Re-run R analytics for a single experiment and update the WandB run with new artifacts.

    Args:
        wandb_id: WandB run ID to process

    Returns:
        Success or error message
    """
    try:
        # Initialize WandB API
        wandb_api = WandbAPI()

        logger.info(f"Processing wandb_id: {wandb_id}")

        try:
            # Get run details
            run_details = wandb_api.get_wandb_run_details(wandb_id)

            # Get run name
            if "display_name" in run_details:
                run_name = run_details["display_name"]
            elif "name" in run_details:
                run_name = run_details["name"]
            else:
                run_name = wandb_id

            # Get necessary file paths from wandb artifacts
            if (
                "configs" not in run_details
                or "artifacts" not in run_details["configs"]
            ):
                return JSONResponse(
                    status_code=400,
                    content={
                        "status": "error",
                        "message": f"No artifacts found for run {wandb_id}",
                    },
                )

            artifacts = run_details["configs"]["artifacts"]

            # Find mappings file
            mappings_artifact = next(
                (file for file in artifacts if "experiment_mappings" in file), None
            )
            if not mappings_artifact:
                return JSONResponse(
                    status_code=400,
                    content={
                        "status": "error",
                        "message": f"Mappings file not found for run {wandb_id}",
                    },
                )
            mappings_path = mappings_artifact["experiment_mappings"]

            # Find survey results file
            survey_results_artifact = next(
                (file for file in artifacts if "experiment_survey_results" in file),
                None,
            )
            if not survey_results_artifact:
                return JSONResponse(
                    status_code=400,
                    content={
                        "status": "error",
                        "message": f"Survey results file not found for run {wandb_id}",
                    },
                )
            survey_results_path = survey_results_artifact["experiment_survey_results"]

            # Download the required files
            _, _, local_mappings_path = wandb_api.get_wandb_run_artifact(mappings_path)
            _, _, local_survey_path = wandb_api.get_wandb_run_artifact(
                survey_results_path
            )

            # Set up output paths
            calculations_script_path = os.path.join(
                settings.PROJECT_ROOT_DIRECTORY,
                "R",
                "Calculations.R",
            )

            # Create a unique directory for this re-run
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(
                settings.PROJECT_ROOT_DIRECTORY,
                "app",
                "logs",
                "analytics",
                f"{run_name}_rerun_{timestamp}",
            )

            calculations_output_path = os.path.join(
                output_dir,
                f"calculations_{run_name}.json",
            )
            amce_path = os.path.join(
                output_dir,
                f"amce_{run_name}.json",
            )

            # Create an empty file for paper_reported_coefficients
            paper_reported_coefficients_path = os.path.join(
                output_dir, "paper_reported_coefficients.json"
            )

            # Create output directories
            os.makedirs(output_dir, exist_ok=True)

            # Create empty paper_reported_coefficients file
            with open(paper_reported_coefficients_path, "w") as f:
                f.write("{}")  # Write empty JSON object

            # Import the original push_analytics_artifact function and save it
            from app.api.v1.helpers.human_baselines import push_analytics_artifact

            original_push_analytics_artifact = push_analytics_artifact

            # Import wandb
            import wandb

            api = wandb.Api()

            # Format the path in the required format
            project_path = run_details.get("project", {}).get("path", "")
            if "/" in wandb_id:  # If it's already a full path
                full_path = wandb_id
            else:
                full_path = f"{project_path}/{wandb_id}" if project_path else wandb_id

            # Get run information from API
            api_run = api.run(full_path)

            # Instead of using the API run directly, we'll initialize a wandb run and set it up
            # to update the existing run instead of creating a new one
            wandb.init(
                id=wandb_id,
                resume="must",
                project=api_run.project,
                entity=api_run.entity,
            )
            active_run = wandb.run

            # Import the run_r_analytics and push_analytics_artifact functions
            from app.api.v1.helpers.human_baselines import (
                push_analytics_artifact,
                run_r_analytics,
            )

            # Create a WandbRun wrapper that matches the expected interface for push_analytics_artifact
            class WandbRunWrapper:
                def __init__(self, active_run, run_name, run_id):
                    self.run = (
                        active_run  # This is the actual wandb run that has log_artifact
                    )
                    self.name = run_name
                    self.run_id = run_id

            # Create the wrapper instance
            wrapped_run = WandbRunWrapper(active_run, run_name, wandb_id)

            try:

                # Run the R analytics
                message = run_r_analytics(
                    run=wrapped_run,
                    mappings_path=local_mappings_path,
                    survey_results_path=local_survey_path,
                    paper_reported_coefficients_path=paper_reported_coefficients_path,
                    calculations_script_path=calculations_script_path,
                    calculations_output_path=calculations_output_path,
                    amce_path=amce_path,
                    is_hb_run=False,
                    sp=None,  # No spinner needed
                )

                # Check if the calculations file was created
                if os.path.exists(calculations_output_path):
                    logger.info(
                        f"Successfully created calculations file for run {wandb_id}"
                    )

                    # Manually upload artifacts to ensure they're attached to the run
                    # Create artifact names with timestamp for uniqueness
                    calculations_filename = f"Analytics_output_rerun_{timestamp}"
                    amce_filename = f"AMCE_output_rerun_{timestamp}"

                    # Clean up downloaded files and output directory
                    # Use os.remove instead of delete_local_artifacts function
                    try:
                        if os.path.exists(local_mappings_path):
                            os.remove(local_mappings_path)
                        if os.path.exists(local_survey_path):
                            os.remove(local_survey_path)
                        shutil.rmtree(output_dir, ignore_errors=True)
                    except Exception as cleanup_error:
                        logger.warning(f"Error during cleanup: {str(cleanup_error)}")

                    # Return success response
                    return JSONResponse(
                        status_code=200,
                        content={
                            "status": "success",
                            "message": f"Successfully rerun analytics for {wandb_id}",
                            "artifacts": {
                                "calculations": f"{calculations_filename}_{run_name}",
                                "amce": (
                                    f"{amce_filename}_{run_name}"
                                    if os.path.exists(amce_path)
                                    else None
                                ),
                            },
                        },
                    )
                else:
                    return JSONResponse(
                        status_code=500,
                        content={
                            "status": "error",
                            "message": (
                                f"Calculations file not created for run {wandb_id}"
                            ),
                        },
                    )

            finally:
                # Make sure to finish the wandb run
                if "active_run" in locals() and active_run:
                    wandb.finish()

        except Exception as e:
            logger.error(f"Error processing run {wandb_id}: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": f"Error processing run {wandb_id}: {str(e)}",
                },
            )

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        capture_exception(e)
        logger.error(f"Error processing run: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing run: {str(e)}")


@router.post(
    "/conjoint-statements", response_model=RealWorldConjoinStatementFinalResponse
)
async def create_real_world_conjoint_statements(
    request: RealWorldConjoinStatementRequest,
):
    """
    Create a set of conjoint statements based on the input prompt.

    This endpoint generates a specified number of conjoint statements
    using the provided prompt. The generated statements can be used in experiments
    or surveys.
    """
    try:
        response = generate_conjoint_statements(
            why_prompt=request.why_prompt,
            suggestion_count=request.suggestion_count,
            attribute_count=request.attribute_count,
            statement_history=request.statement_history,
        )
        suggestions = [
            RealWorldConjoinStatementIndividualResponse(
                text=s["text"],
                attributes=s["attributes"],
            )
            for s in response.get("suggestions", [])
        ]

        return RealWorldConjoinStatementFinalResponse(
            suggestions=suggestions,
            is_causal=response.get("is_causal", False),
            flag=response.get("flag", "") or "",
        )
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error generating statements: {e}")


@router.post("/likert-label", response_model=LikertLabelResponse)
async def create_likert_label(
    request: LikertLabelRequest,
) -> LikertLabelResponse:
    """
    Create a set of Likert scale labels based on the input prompt.

    This endpoint generates a specified number of Likert scale labels
    using the provided prompt. The generated labels can be used in experiments
    or surveys.
    """
    pe = LCELPromptExecutor(llm_model=LLMModel.AZURE_GPT4O_MINI)
    pb = PromptBuilder()
    likert_prompt = pb.get_likert_label_prompt()

    try:
        if request.image_name:
            images_folder = os.path.join(
                settings.PROJECT_ROOT_DIRECTORY,
                "app",
                "logs",
                "images",
            )
            if not os.path.exists(images_folder):
                os.makedirs(images_folder, exist_ok=True)
            local_image_path = os.path.join(
                images_folder,
                request.image_name,
            )
            download_file_from_s3(
                bucket_name=settings.AWS_S3_BUCKET_NAME,
                object_name=request.image_name,
                local_file_path=local_image_path,
            )
            base64_image = convert_image_to_base64(local_image_path)
            delete_local_artifacts(local_image_path)
            image_details = summarize_image(
                img_base64=base64_image, prompt=DETAILED_IMAGE_DESCRIPTION_TEMPLATE
            )
            if not request.description:
                request.description = summarize_image(
                    img_base64=base64_image, prompt=IMAGE_BASED_INTRO_TEMPLATE
                )
        else:
            image_details = ""
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        image_details = ""

    if request.image_name or request.description:
        args = {
            "context": request.description,
            "statement": request.statements,
            "scale": request.scale,
            "scale_range": [i + 1 for i in range(request.scale)],
            "image_details": image_details,
        }
        try:
            response = pe.execute(
                prompt=likert_prompt,
                args=args,
                output_object=LikertLabelResponse,
                description="Generating Likert scale labels...",
            )
            return LikertLabelResponse(likert_labels=response.get("likert_labels", []))
        except Exception:
            likert_labels = []
            for statement in request.statements:
                likert_labels.append({
                    "statement": statement,
                    "labels": ["Could not generate labels"],
                })
            return LikertLabelResponse(
                likert_labels=likert_labels,
            )
