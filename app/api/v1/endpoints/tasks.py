from celery.result import AsyncResult
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

router = APIRouter()


@router.get("/{task_id}")
def get_status(task_id):
    """
    Get the status of a Celery task.

    Parameters:
    - `task_id`: Unique identifier of the Celery task

    Returns:
    JSONResponse with:
    - `task_id`: The ID of the task
    - `task_status`: Current status of the task
    - `task_result`: Result of the task (if available)

    Example Response:
    {
        "task_id": "abc123",
        "task_status": "SUCCESS",
        "task_result": {"some_key": "some_value"}
    }

    Raises:
    - HTTPException 500: If fetching the task status fails
    """
    try:
        task_result = AsyncResult(task_id)
        result = {
            "task_id": task_id,
            "task_status": task_result.status,
            "task_result": task_result.result,
        }
        return JSONResponse(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching task status: {e}")
