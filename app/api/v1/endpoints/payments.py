import os

import stripe
from fastapi import <PERSON>Rout<PERSON>, BackgroundTasks, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from sentry_sdk import capture_exception

from app.api.v1.helpers.stripe_handlers import StripeEventHandler
from app.core.utils.logging import app_logger

oauth2_scheme = HTTPBearer()
stripe.api_key = os.getenv("STRIPE_API_KEY")

router = APIRouter()
logger = app_logger.get_logger(__name__)


@router.post("/create-customer-portal-session/")
async def create_customer_portal_session(
    customer_id: str = Query(..., description="Stripe customer ID"),
    token: str = Depends(oauth2_scheme),
):
    """
    Create a Stripe Customer Portal session.

    Parameters:
    - `customer_id`: Stripe customer ID
    - `token`: Authentication token (injected by dependency)

    Returns:
    JSONResponse with:
    - `url`: URL for the customer portal session

    Raises:
    - HTTPException 400: If session creation fails
    """

    try:
        session = stripe.billing_portal.Session.create(
            customer=customer_id,
            return_url=os.getenv("AUTH0_BASE_URL") + "settings",
        )
        return JSONResponse(status_code=200, content={"url": session.url})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=400, detail=f"Error creating customer portal session: {e}"
        )


@router.post("/create-checkout-session/")
async def create_checkout_session(
    token: str = Depends(oauth2_scheme),
    customer_id: str = Query(..., description="Stripe customer ID"),
):
    price_id = os.getenv("STRIPE_PREMIUM_SUBSCRIPTION_PRICE_ID")

    if not price_id:
        raise HTTPException(
            status_code=500, detail="Premium Price ID is not configured."
        )

    try:
        session = stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=["card"],
            line_items=[{
                "price": price_id,
                "quantity": 1,
            }],
            mode="subscription",
            subscription_data={
                "trial_period_days": 14,
                "trial_settings": {
                    "end_behavior": {"missing_payment_method": "cancel"}
                },
            },
            payment_method_collection="always",
            success_url=os.getenv("STRIPE_SUCCESS_URL"),
            allow_promotion_codes=True,
            cancel_url=os.getenv("STRIPE_CANCEL_URL"),
        )
        return JSONResponse(status_code=200, content={"session_id": session.id})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=400, detail=f"Error creating checkout session: {e}"
        )


@router.get("/check-subscription/")
async def check_subscription(
    customer_id: str = Query(..., description="Stripe customer ID"),
    token: str = Depends(oauth2_scheme),
):
    """
    Check the subscription status for a customer.

    Parameters:
    - `customer_id`: Stripe customer ID
    - `token`: Authentication token (injected by dependency)

    Returns:
    Dict with:
    - `customer_id`: Stripe customer ID
    - `subscription_status`: Current subscription status or details

    Raises:
    - HTTPException 404: If customer or subscription retrieval fails
    """
    try:
        customer = stripe.Customer.retrieve(customer_id)
        subscriptions = stripe.Subscription.list(customer=customer_id)

        if subscriptions.data:
            subscription_status = subscriptions.data[0]
            return {
                "customer_id": customer_id,
                "subscription_status": subscription_status,
            }
        else:
            return {
                "customer_id": customer_id,
                "subscription_status": "no active subscriptions",
            }

    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=404, detail=str(e))


@router.post("/cancel-subscription/")
async def cancel_subscription(
    customer_id: str = Query(..., description="Stripe customer ID"),
    token: str = Depends(oauth2_scheme),
):
    """
    Cancel the current subscription for a customer.

    Parameters:
    - `customer_id`: Stripe customer ID
    - `token`: Authentication token (injected by dependency)

    Returns:
    Dict with:
    - `message`: Status of the cancellation operation

    Raises:
    - HTTPException 400: If cancellation fails
    """
    try:
        subscriptions = stripe.Subscription.list(customer=customer_id)
        if subscriptions.data:
            subscription_id = subscriptions.data[0].id
            stripe.Subscription.cancel(subscription_id)
            return {"message": "Subscription canceled successfully"}
        else:
            return {"message": "No active subscriptions found"}
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=400, detail=f"Error cancelling subscription: {e}"
        )


@router.post("/webhook/")
async def stripe_webhook(request: Request, background_tasks: BackgroundTasks):
    """
      Receive and process Stripe webhook events asynchronously.

      Parameters:
      - `request`: The incoming webhook request
      - `background_tasks`: FastAPI background tasks handler

      Returns:
      JSONResponse acknowledging receipt of the webhook

    Notes:
      - Validates the Stripe signature to ensure authenticity
      - Immediately acknowledges the event to Stripe
      - Processes the event asynchronously in the background
      - Supports various Stripe event types (e.g., subscription updates, payment successes)


      Raises:
      - HTTPException 400: For invalid payload or signature
      - HTTPException 500: For general webhook handling errors
    """
    try:
        payload = await request.body()
        sig_header = request.headers["stripe-signature"]
        webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

        try:
            event = stripe.Webhook.construct_event(
                payload=payload, sig_header=sig_header, secret=webhook_secret
            )
        except ValueError:
            logger.warning("Invalid payload in Stripe webhook")
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError:
            logger.warning("Invalid signature in Stripe webhook")
            raise HTTPException(status_code=400, detail="Invalid signature")

        background_tasks.add_task(StripeEventHandler().handle_event, event)
        return JSONResponse(status_code=200, content={"received": True})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Error handling Stripe webhook: {e}"
        )
