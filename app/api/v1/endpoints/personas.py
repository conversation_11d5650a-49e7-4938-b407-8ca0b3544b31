from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from sentry_sdk import capture_exception

from app.api.v1.helpers.personas import (
    extract_personas_from_pdf,
    extract_text_from_pdf,
    generate_personas,
    get_constraints,
    get_history,
    get_motivation,
    get_orthogonal_dimensions,
    get_question,
)
from app.api.v1.schemas.personas import (
    ConstraintRequest,
    ConstraintResponse,
    HistoryRequest,
    HistoryResponse,
    MotivationRequest,
    MotivationResponse,
    PersonasResponse,
    QuestionRequest,
    QuestionResponse,
)
from app.core.utils.common import log
from app.core.utils.logging import app_logger

oauth2_scheme = HTTPBearer()

router = APIRouter(dependencies=[Depends(oauth2_scheme)])
logger = app_logger.get_logger(__name__)


@router.get("")
def get_personas(levels_per_trait: int, trait_keys: str):
    """
    Generate personas based on specified traits and levels.

    Parameters:
    - `levels_per_trait`: Number of levels for each trait
    - `trait_keys`: Comma-separated list of trait keys

    Returns:
    JSONResponse with:
    - `all_combinations_index`: List of trait level combinations
    - `traits_lookup_map`: Dictionary of traits and their levels
    - `personas_full_string_definition`: List of persona descriptions

    Raises:
    - HTTPException 500: If persona generation fails
    """
    try:
        (
            all_combinations_index,
            traits_lookup_map,
            personas_full_string_definition,
        ) = generate_personas(
            levels_per_trait, [k.strip() for k in trait_keys.split(",")]
        )
        resp = jsonable_encoder(
            (all_combinations_index, traits_lookup_map, personas_full_string_definition)
        )
        return JSONResponse(content=resp)
    except Exception as e:
        capture_exception(e)
        raise HTTPException(status_code=500, detail=f"Error generating personas: {e}")


@router.post("/history", response_model=HistoryResponse)
def create_history(req: HistoryRequest) -> HistoryResponse | HTTPException:
    """
    Generate historical factors relevant to a user's decision-making process.

    This endpoint analyzes a given question and user profile to produce a set of
    historical factors that could influence the user's decision. These factors
    provide context for understanding past behaviors and preferences.

    Parameters:
    - `req (HistoryRequest)`: Contains the question, user profile, and relevant attributes

    Returns:
    - `HistoryResponse`: A dictionary of historical factors and their descriptions

    Raises:
    - HTTPException 404: If there's an error in generating historical factors
    """

    try:
        # Get initial dimensions
        initial_response = get_history(req=req)

        # Apply orthogonality sorting
        orthogonal_response = get_orthogonal_dimensions(initial_response)

        logger.success(
            "Successfully generated and sorted orthogonal history dimensions for"
            f" prompt: {req.why_prompt}"
        )
        return orthogonal_response
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Error creating history dimensions : {str(e)}",
        )


@router.post("/constraints", response_model=ConstraintResponse)
@log
def create_constraints(req: ConstraintRequest) -> ConstraintResponse | HTTPException:
    """
    Generate constraints that may affect a user's decision-making process.

     This endpoint analyzes a given question, user profile, and relevant attributes to produce
     a set of constraints or limiting factors that could influence the user's decision.
     These constraints help in understanding the boundaries within which a decision is made.

     Parameters:
     - `req (ConstraintRequest)`: Contains the question, user profile, and relevant attributes

     Returns:
     - `ConstraintResponse`: A dictionary of constraints and their descriptions
    """

    try:
        # Get initial dimensions
        initial_response = get_constraints(req=req)

        # Apply orthogonality sorting
        orthogonal_response = get_orthogonal_dimensions(initial_response)

        logger.success(
            f"Successfully generated constraint dimensions for prompt: {req.why_prompt}"
        )

        return orthogonal_response
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Error creating constraint dimensions: {str(e)}",
        )


@router.post("/generate_questions", response_model=QuestionResponse)
async def create_questions(req: QuestionRequest) -> QuestionResponse | HTTPException:
    """
    Generate questions for an experiment based on historical factors and constraints.

    This endpoint creates a set of questions tailored to the given context, user profile,
    and specific dimensions of interest. The questions are designed to gather insights
    about the user's decision-making process, considering both historical behavior and
    current constraints.

    Parameters:
    - `req (QuestionRequest)`: Contains the main question, user profile, and dimensions to explore

    Returns:
    - `QuestionResponse`: A structured set of questions categorized by model type (history/constraints)
      and dimensions, each with sample responses

    The response includes:
    - Two questions for each specified dimension
    - A set of sample responses for each question

    Raises:
    - HTTPException 404: If there's an error in generating the questions

    Note:
    - Questions are generated asynchronously for efficiency
    - The structure is flexible and can accommodate various schemas and dimensions
    """
    try:
        questions = await get_question(**req.model_dump())
        logger.success(f"Successfully generated questions for prompt: {req.why_prompt}")
        return questions
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Failed to get QnA data for: {str(e)}",
        )


@router.post("/motivation", response_model=MotivationResponse)
@log
def create_motivation(
    req: MotivationRequest,
) -> MotivationResponse | HTTPException:
    """
    Generate motivation factors influencing a user's decision-making process.

    This endpoint analyzes the given context, user profile, historical factors, and constraints
    to produce a set of motivation factors that could drive the user's decision. These factors
    help in understanding the underlying reasons behind a user's choices.

    Parameters:
    - `req (MotivationRequest)`: Contains the main question, user profile, selected historical factors, and constraints

    Returns:
    - `MotivationResponse`: A dictionary of motivation factors and their descriptions

    Raises:
    - HTTPException 404: If there's an error in generating motivation factors

    Note:
    - The generated motivation factors are based on the provided context and previously selected factors
    """
    try:
        response = get_motivation(req=req)
        logger.success(
            f"Successfully generated motivation dimensions for prompt: {req.why_prompt}"
        )

        return response
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Error creating motivation : {str(e)}",
        )


@router.post("/extract-from-pdf", response_model=PersonasResponse)
async def extract_from_pdf(
    file: UploadFile = File(...),
) -> PersonasResponse | HTTPException:
    """
    Extracts personas from a PDF file and returns the extracted personas.
    """
    if file.content_type != "application/pdf":
        raise HTTPException(
            status_code=400, detail=f"File '{file.filename}' is not a PDF file."
        )
    try:
        response = await extract_personas_from_pdf(file)
        return response
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Error extracting personas from PDF: {str(e)}",
        )


@router.post("/extract-text-from-pdf", response_model=str)
async def get_text_from_pdf(file: UploadFile = File(...)) -> str | HTTPException:
    """
    Extracts text from a PDF file and returns the extracted text.
    """
    if file.content_type != "application/pdf":
        raise HTTPException(
            status_code=400, detail=f"File '{file.filename}' is not a PDF file."
        )
    try:
        response = await extract_text_from_pdf(file)
        return response
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=404,
            detail=f"Error extracting text from PDF: {str(e)}",
        )
