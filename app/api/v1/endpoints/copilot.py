from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sentry_sdk import capture_exception

from app.api.v1.schemas.copilot import CausalityRequest, CausalityResponse
from app.core.utils.logging import app_logger
from app.llm_prompt.prompt_builder import Prompt<PERSON>uilder
from app.llm_prompt.prompt_executor import LCELPromptExecutor

oauth2_scheme = HTTPBearer()
logger = app_logger.get_logger(__name__)

router = APIRouter(dependencies=[Depends(oauth2_scheme)])


@router.post("/check-causality", response_model=CausalityResponse, deprecated=True)
def check_causality(req: CausalityRequest) -> CausalityResponse:
    """
    Check whether a why_prompt is causal or not.

    Raises:
    - HTTPException 500: If an error occurs during the causality check
    """
    pe = LCELPromptExecutor(llm_model=req.llm_model)
    pb = PromptBuilder()
    causality_prompt = pb.causality_prompt()

    try:
        causality_info = pe.execute(
            prompt=causality_prompt,
            args={"why_prompt": req.why_prompt},
            output_object=CausalityResponse,
            description="Checking causality",
        )
        logger.success(
            f"Causality check executed successfully for prompt: {req.why_prompt}"
        )
        return CausalityResponse(**causality_info)
    except Exception as e:
        capture_exception()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check causality: {str(e)}",
        )


@router.post("/check-moderation")
def moderate(why_prompt: str):
    """
    Check whether a why_prompt complies with our content policy.

    Parameters:
    - `why_prompt`: The prompt text to check for content policy compliance

    Returns:
    - JSONResponse object with the following structure:
        - `flagged`: Boolean indicating whether the content was flagged
        - `flagged_categories`: List of categories that were flagged (empty if not flagged)

    Example Response (flagged content):
    ```json
    {
        "flagged": true,
        "flagged_categories": ["violence", "hate"]
    }
    ```

    Example Response (non-flagged content):
    ```json
    {
        "flagged": false,
        "flagged_categories": []
    }
    ```

    Raises:
    - HTTPException 500: If an error occurs during the moderation check
    """
    from openai import OpenAI

    client = OpenAI()
    try:
        response = client.moderations.create(input=why_prompt)

        if response.results[0].flagged:
            categories = response.results[0].categories.model_dump()
            flagged_categories = [k for k, v in categories.items() if v]
            return JSONResponse(
                content={
                    "flagged": True,
                    "flagged_categories": list(set(flagged_categories)),
                }
            )

        return JSONResponse(content={"flagged": False, "flagged_categories": []})
    except Exception as e:
        capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to moderate: {str(e)}",
        )
