import copy
import time

from fastapi import APIRouter, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from supabase import Client

from app.api.v1.helpers.population import (
    QueryConfig,
    convertTraitLevelEnumsToValue,
    expand_range_until_limit,
)
from app.api.v1.schemas.experiments import RacialGroupEnum
from app.api.v1.schemas.population import (
    PopulationRecommendationResponse,
    PopulationRequest,
)
from app.core.utils.constants import (
    INCOME_UPPER_THRESHOLD,
    categorical,
    numerical,
    order_of_display,
    order_of_execution,
)
from app.core.utils.databases import get_supabase_client
from app.core.utils.logging import app_logger

oauth2_scheme = HTTPBearer()

router = APIRouter(dependencies=[Depends(oauth2_scheme)])
logger = app_logger.get_logger(__name__)

#


@router.post("/validate", response_model=PopulationRecommendationResponse)
def validate_population(
    request: PopulationRequest,
    db: Client = Depends(get_supabase_client),
) -> PopulationRecommendationResponse:
    start_time = time.time()
    logger.info("Validating population request")
    if not request.age:
        request.age = [18, 100]
    if not request.age[0]:
        request.age[0] = 18
    if not request.age[1]:
        request.age[1] = 100
    if not request.household_income:
        request.household_income = [0, INCOME_UPPER_THRESHOLD]
    if not request.household_income[0]:
        request.household_income[0] = 0
    if not request.household_income[1]:
        request.household_income[1] = INCOME_UPPER_THRESHOLD

    if RacialGroupEnum.AFRICAN_AMERICAN in request.racial_group:
        request.racial_group.append(RacialGroupEnum.BLACK)
        request.racial_group.remove(RacialGroupEnum.AFRICAN_AMERICAN)

    config = {
        "age": request.age,
        "number_of_children": (
            request.number_of_children if request.number_of_children else []
        ),
        "education_level": request.education_level if request.education_level else [],
        "household_income": request.household_income,
        "racial_group": request.racial_group if request.racial_group else [],
        "gender": request.gender if request.gender else [],
        "state": request.state,
    }

    if RacialGroupEnum.AFRICAN_AMERICAN in config["racial_group"]:
        config["racial_group"].append(RacialGroupEnum.BLACK)
        config["racial_group"].remove(RacialGroupEnum.AFRICAN_AMERICAN)
    MINIMUM_POPULATION = request.number_of_records

    population_traits = config.copy()
    base_query_config = QueryConfig(population_traits, "ipums", db)

    # fill in config with population traits
    for key, value in config.items():
        if value is None or value == "" or value == []:
            if key in numerical:
                if key == "age":
                    population_traits[key] = [18, 100]
                elif key == "household_income":
                    population_traits[key] = [0, INCOME_UPPER_THRESHOLD]
            elif key in categorical:
                all_values = base_query_config.get_column_possible_values(key)
                population_traits[key] = all_values

    original_query_config = QueryConfig(population_traits, "ipums", db)

    query_config_copy = copy.deepcopy(original_query_config)

    if original_query_config.get_count() < MINIMUM_POPULATION:
        res_one_trait_change = {}
        for trait in order_of_execution:
            if (
                trait not in population_traits
                or population_traits[trait] is None
                or population_traits[trait] == (None, None)
            ):
                continue
            else:
                query_config_expanded = expand_range_until_limit(
                    query_config_copy, trait, MINIMUM_POPULATION
                )

                # Store results
                res_one_trait_change[trait] = {
                    "population_size": query_config_expanded.get_count(),
                    "population_traits": query_config_expanded.get_config()[trait],
                }

        starter_suggestion_config = {}
        if request.state:
            starter_suggestion_config["state"] = request.state.value
        suggestion_query_config = QueryConfig(starter_suggestion_config, "ipums", db)
        for trait in order_of_execution:
            if (
                trait not in population_traits
                or population_traits[trait] is None
                or population_traits[trait] == (None, None)
            ):
                continue

            # get trait levels
            trait_levels = population_traits[trait]

            # update config
            suggestion_config = suggestion_query_config.get_config()
            suggestion_config[trait] = trait_levels
            suggestion_query_config.update_config(suggestion_config)

            # If population is too small, expand the trait range
            if suggestion_query_config.get_count() < MINIMUM_POPULATION:
                suggestion_query_config = expand_range_until_limit(
                    suggestion_query_config, trait, MINIMUM_POPULATION
                )

        population_traits_post = suggestion_query_config.get_config()

        # Reorder traits for display
        population_traits_suggestion_order = {
            trait: population_traits_post[trait]
            for trait in order_of_display
            if trait in population_traits_post
        }

        population_traits_original = original_query_config.get_config()
        population_traits_original_order = {
            trait: population_traits_original[trait]
            for trait in order_of_display
            if trait in population_traits_original
        }

        population_res_one_trait_order = {
            trait: res_one_trait_change[trait]
            for trait in order_of_display
            if trait in res_one_trait_change
        }

        if request.state:
            population_traits_suggestion_order["state"] = request.state.value
        res_suggestion = {
            "population_size": suggestion_query_config.get_count(),
            "population_traits": population_traits_suggestion_order,
        }

        res_original = {
            "population_size": original_query_config.get_count(),
            "population_traits": population_traits_original_order,
        }

        if "Black" in res_original.get("population_traits", {}).get("racial_group", []):
            res_original["population_traits"]["racial_group"].append("African American")
            res_original["population_traits"]["racial_group"].remove("Black")

        if "Black" in res_suggestion.get("population_traits", {}).get(
            "racial_group", []
        ):
            res_suggestion["population_traits"]["racial_group"].append(
                "African American"
            )
            res_suggestion["population_traits"]["racial_group"].remove("Black")

        if "Black" in population_res_one_trait_order.get("racial_group").get(
            "population_traits"
        ):
            population_res_one_trait_order.get("racial_group").get(
                "population_traits"
            ).append("African American")
            population_res_one_trait_order.get("racial_group").get(
                "population_traits"
            ).remove("Black")

        if (
            "state" not in res_original.get("population_traits", {}).keys()
            and request.state
        ):
            res_original["population_traits"]["state"] = request.state.value

        if (
            "state" not in res_suggestion.get("population_traits", {}).keys()
            and request.state
        ):
            res_suggestion["population_traits"]["state"] = request.state.value
        logger.info(
            "Total time for validation of population:"
            f" {time.time() - start_time} seconds"
        )

        return PopulationRecommendationResponse(
            suggestion=res_suggestion,
            original=res_original,
            one_trait_change=population_res_one_trait_order,
        )
    else:
        population_traits_original_order = {}
        for trait in order_of_display:
            trait_levels = population_traits[trait]
            trait_levels = convertTraitLevelEnumsToValue(trait_levels)
            population_traits_original_order[trait] = trait_levels

        res_original = {
            "population_size": original_query_config.get_count(),
            "population_traits": population_traits_original_order,
        }
        if "Black" in res_original.get("population_traits", {}).get("racial_group", []):
            res_original["population_traits"]["racial_group"].append("African American")
            res_original["population_traits"]["racial_group"].remove("Black")

        if (
            "state" not in res_original.get("population_traits", {}).keys()
            and request.state
        ):
            res_original["population_traits"]["state"] = request.state.value
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"Total time: {total_time}")
        return PopulationRecommendationResponse(
            suggestion={},
            original=res_original,
            one_trait_change={},
        )
