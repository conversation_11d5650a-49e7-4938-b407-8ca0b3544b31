from typing import Optional

from pydantic import BaseModel, Field

from app.core.utils.type_enums import LLMModel


class CheckProductRequest(BaseModel):
    why_prompt: str = Field(
        ...,
        description=(
            "Prompt to use for checking the product's existence in the real world"
        ),
    )
    llm_model: Optional[LLMModel] = LLMModel.AZURE_GPTO3_MINI

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors affect the choice of buying an electric car?"
                )
            }
        }


class CheckProductRealWorldResponse(BaseModel):
    product_exists: bool = Field(
        ...,
        description="Boolean indicating whether the product exists in the real world",
    )
    score: int = Field(
        None,
        ge=0,
        le=5,
        description=(
            "Confidence score of the model in predicting the existence of the product"
        ),
    )
    measurable_attributes: Optional[bool] = Field(
        None,
        description="Boolean indicating whether the product has measurable attributes",
    )
    specific_levels_definable: Optional[bool] = Field(
        None,
        description=(
            "Boolean indicating whether the product has specific levels definable"
        ),
    )
    data_available_collectible: Optional[bool] = Field(
        None,
        description=(
            "Boolean indicating whether data is available and collectible for the"
            " product"
        ),
    )

    class Config:
        json_schema_extra = {
            "example": {
                "product_exists": True,
                "score": 3,
                "measurable_attributes": True,
                "specific_levels_definable": True,
                "data_available_collectible": True,
            }
        }
