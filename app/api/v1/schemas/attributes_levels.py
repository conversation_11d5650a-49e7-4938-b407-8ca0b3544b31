from typing import Dict, List, Literal, Optional, TypeAlias, Union

from pydantic import BaseModel, Field, model_validator

from app.core.utils.type_enums import LLMModel, PromptType

Attribute: TypeAlias = str


class LeveledAttributeBase(BaseModel):
    attribute: Attribute = Field(description="Name of the attribute")
    levels: list[str] = Field(description="List of levels for the attribute")
    attribute_type: str = Field(
        default="non-monetary",
        description="Type of the attribute either monetary or non-monetary",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "attribute": "Battery Range per Charge",
                "levels": [
                    "Less than 150 miles",
                    "150 to 250 miles",
                    "250 to 350 miles",
                    "350 to 450 miles",
                    "More than 450 miles",
                ],
                "attribute_type": "non-monetary",
            }
        }


class LeveledAttributeBaseClaude(BaseModel):
    attribute: Attribute = Field(description="Name of the attribute")
    levels: list[str] = Field(description="List of levels for the attribute")
    level_type: Optional[str] = Field(
        description="Type of level, e.g. quantitative, qualitative"
    )
    attribute_type: str = Field(
        default="non-monetary",
        description="Type of the attribute either monetary or non-monetary",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "attribute": "Battery Range per Charge",
                "levels": [
                    "Less than 150 miles",
                    "150 to 250 miles",
                    "250 to 350 miles",
                    "350 to 450 miles",
                    "More than 450 miles",
                ],
                "level_type": "quantitative",
                "attribute_type": "non-monetary",
            }
        }


class MonetaryAttribute(BaseModel):
    attribute: str = Field(description="Name of the attribute")
    levels: list[str] = Field(
        description="List of levels for the attribute", min_length=2
    )
    attribute_type: str = Field(
        description="Type of the attribute: monetary or non-monetary"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "attribute": "Price",
                "level": ["$100", "$200", "$300", "$400", "$500"],
                "attribute_type": "monetary",
            }
        }


class MonetaryAttributeResponse(BaseModel):
    attributes_levels: list[MonetaryAttribute]

    class Config:
        json_schema_extra = {
            "example": {
                "attributes_levels": [
                    {
                        "attribute": "Price",
                        "levels": ["$100", "$200", "$300", "$400", "$500"],
                        "attribute_type": "monetary",
                    },
                    {
                        "attribute": "Battery Range per Charge",
                        "levels": [
                            "Less than 150 miles",
                            "150 to 250 miles",
                            "250 to 350 miles",
                            "350 to 450 miles",
                            "More than 450 miles",
                        ],
                        "attribute_type": "non-monetary",
                    },
                ]
            }
        }


class LeveledAttribute(LeveledAttributeBase):
    """
    Notes:
    # 1: Remember the brands and price attributes when setting ranges.
    # 2: GPT3 sometimes generates more or less than required, so the range should be flexible by 2-3.

    """

    # TODO: min_length shouldn't be, this is just temporarily until we have proper reporting of error messages
    levels: list[str] = Field(min_length=2, max_length=15)

    class Config:
        json_schema_extra = {
            "example": {
                "levels": ["Black", "Brown", "Beige", "Navy", "Gray"],
            }
        }


class ProcessedProducts(BaseModel):
    """
    A schema for the output of product attribute level alignment.
    This class ensures that the output maintains the exact same structure as the input,
    with only the specified attribute values changed to match standardized levels.
    """

    # Define the schema to match the input products_data structure
    products_data: list = Field(
        description="List of products with updated attribute values"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "2025 Toyota bZ4X": {
                    "Range (miles)": "222 - 252 miles",
                    "Charging time": "8 - 10 hours",
                    "Battery capacity (kWh)": "63 - 66 kWh",
                }
            }
        }


class LeveledAttributeFailed(LeveledAttributeBase):
    """
    This model is used for handling validation errors
    """

    levels: list[str] = Field(min_length=0, max_length=0)


class AttributesLevelsRequest(BaseModel):
    """
    Pydantic model for a request to create levels for attributes.
    """

    why_prompt: str
    country: str
    year: Optional[int] = Field(None, description="Year of the product")
    prompt_type: Optional[PromptType] = PromptType.PRODUCT
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    attribute_count: int = Field(
        ge=2,
        le=7,
        default=4,
        description="The number of attributes in the experiment",
    )
    level_count: int = Field(
        ge=2,
        le=5,
        default=4,
        description="The number of levels for each attribute in the experiment",
    )
    max_length: int = Field(
        ge=40,
        le=80,
        default=40,
        description="The maximum # Characters per attribute",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "What factor affect the adoption of an electric vehicle?",
                "country": "United States of America",
                "year": 2025,
                "attribute_count": 6,
                "level_count": 4,
            }
        }


class AttributesLevelsRequestClaude(BaseModel):
    """
    Pydantic model for a request to create levels for attributes.
    """

    why_prompt: str
    country: str
    year: Optional[int] = Field(None, description="Year of the survey")
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    attribute_count: int = Field(
        ge=2,
        le=10,
        default=5,
        description="The number of attributes in the experiment",
    )
    level_count: int = Field(
        ge=2,
        le=5,
        default=5,
        description="The number of levels for each attribute in the experiment",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors affect the choice of long haul holiday flights?"
                ),
                "country": "United States of America",
                "year": 2024,
                "attribute_count": 5,
                "level_count": 5,
            }
        }


class AttributesLevelsResponse(BaseModel):
    """
    Pydantic model for a response with the list of attributes and levels.
    """

    attributes_levels: list[LeveledAttributeBase]
    brand_attribute_combinations: Optional[list[dict]]

    class Config:
        json_schema_extra = {
            "example": {
                "attributes_levels": [
                    {
                        "attribute": "Battery Range per Charge",
                        "levels": [
                            "Less than 150 miles",
                            "150 to 250 miles",
                            "250 to 350 miles",
                            "350 to 450 miles",
                            "More than 450 miles",
                        ],
                    },
                    {
                        "attribute": "Vehicle Warranty",
                        "levels": [
                            "3 years or 36,000 miles",
                            "4 years or 50,000 miles",
                            "5 years or 60,000 miles",
                            "6 years or 70,000 miles",
                            "7 years or 100,000 miles",
                        ],
                    },
                ],
                "brand_attribute_combinations": [
                    {
                        "Nissan Leaf": {
                            "Charging time": "Under 5 minutes (80% charge)",
                            "Price": "29500.00 USD",
                            "Range (miles)": "258 miles",
                            "Availability of charging infrastructure": (
                                "Access to exclusive charging networks"
                            ),
                        }
                    },
                    {
                        "Chevrolet Bolt": {
                            "Charging time": "2-4 hours to full charge",
                            "Price": "30500.00 USD",
                            "Range (miles)": "258 miles",
                            "Availability of charging infrastructure": (
                                "Level 2 (240V) charging compatibility"
                            ),
                        }
                    },
                ],
            }
        }


class OrthogonalAttributesLevelsRequest(BaseModel):
    """
    Pydantic model for a request to create more, new, orthogonal levels for attributes.
    """

    why_prompt: str
    country: str
    existing_attributes_levels: list[LeveledAttribute]
    new_attribute_count: int = Field(ge=1, le=5, default=4)
    level_count: int = Field(ge=2, le=5, default=3)
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "What factor affect the adoption of an electric vehicle?",
                "country": "United States of America",
                "existing_attributes_levels": [
                    {
                        "attribute": "Battery Range per Charge",
                        "levels": [
                            "Less than 150 miles",
                            "150 to 250 miles",
                            "250 to 350 miles",
                            "350 to 450 miles",
                            "More than 450 miles",
                        ],
                    },
                    {
                        "attribute": "Vehicle Warranty",
                        "levels": [
                            "3 years or 36,000 miles",
                            "4 years or 50,000 miles",
                            "5 years or 60,000 miles",
                            "6 years or 70,000 miles",
                            "7 years or 100,000 miles",
                        ],
                    },
                ],
                "new_attribute_count": 4,
                "level_count": 3,
                "llm_model": "gpt4",
            }
        }


class AttributesRequest(BaseModel):
    why_prompt: str
    country: str
    prompt_type: Optional[PromptType] = PromptType.GENERIC
    attribute_count: int = Field(ge=2, le=10, default=4)
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "What factor affect the adoption of an electric vehicle?",
                "country": "United States of America",
                "prompt_type": PromptType.GENERIC,
                "attribute_count": 4,
                "llm_model": "gpt4",
            }
        }


class LevelsRequest(BaseModel):
    why_prompt: str
    country: str
    existing_attributes: list[Attribute]
    attributes: list[Attribute]
    level_count: int = Field(ge=2, le=10, default=4)
    llm_model: LLMModel | None = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "What factor affect the adoption of an electric vehicle?",
                "country": "United States of America",
                "existing_attributes": ["Price", "Vehicle Warranty"],
                "attributes": [
                    "Battery Range per Charge",
                ],
                "level_count": 4,
                "llm_model": "gpt4",
            }
        }


class AttributesResponse(BaseModel):
    """List of attributes"""

    attributes: List[Attribute]


class LeveledAttributeResponse(BaseModel):
    """List of attributes and levels"""

    attributes_levels: List[LeveledAttributeBase]


class LeveledAttributeResponseClaude(BaseModel):
    """List of attributes and levels"""

    attributes_levels: List[LeveledAttributeBaseClaude]

    class Config:
        json_schema_extra = {
            "example": {
                "attributes_levels": [
                    {
                        "attribute": "Battery Range per Charge",
                        "levels": [
                            "Less than 150 miles",
                            "150 to 250 miles",
                            "250 to 350 miles",
                            "350 to 450 miles",
                            "More than 450 miles",
                        ],
                        "attribute_type": "numerical",
                    },
                    {
                        "attribute": "Vehicle Warranty",
                        "levels": [
                            "3 years or 36,000 miles",
                            "4 years or 50,000 miles",
                            "5 years or 60,000 miles",
                            "6 years or 70,000 miles",
                            "7 years or 100,000 miles",
                        ],
                        "attribute_type": "numerical",
                    },
                ],
            }
        }


# Version 2
class ProductAttributeRequest(BaseModel):
    why_prompt: str
    country: str
    attribute_count: int = Field(ge=2, le=10, default=7)
    level_count: int = Field(ge=2, le=10, default=5)
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "Why are you considering purchasing an electric vehicle?",
                "country": "USA",
                "attribute_count": 3,
                "level_count": 4,
            }
        }


class ProductAttributeResponse(BaseModel):
    attributes: List[Attribute]

    class Config:
        json_schema_extra = {
            "example": {
                "attributes": ["Range", "Price", "Battery capacity (kWh)", "Warranty"],
            }
        }


class ProductAttributeRankingRequest(BaseModel):
    why_prompt: str
    attributes: List[str]  # Changed from Dict to List
    attribute_count: int = Field(ge=2, le=10, default=8)
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH


class ProductAttributeRankingResponse(BaseModel):
    ranked_attributes: List[str]  # The ranked attributes
    requested_num_attrs: int
    original_attributes: List[str]

    @model_validator(mode="before")
    def validate_prioritization(cls, values):
        ranked_attributes = values.get("ranked_attributes")
        original_attributes = values.get("original_attributes")
        requested_count = values.get("requested_num_attrs")

        # Check if prioritized attributes are a subset of original attributes
        if not set(ranked_attributes).issubset(set(original_attributes)):
            raise ValueError(
                f"Prioritized attributes {ranked_attributes} are not a subset of"
                f" original attributes generated {original_attributes}"
            )

        # Check if the length of ranked attributes equals the requested number of attributes
        if len(ranked_attributes) != requested_count:
            raise ValueError(
                f"The number of prioritized attributes ({len(ranked_attributes)})"
                f" does not match the requested number ({requested_count})."
            )
        return values


class ProductScoreRequest(BaseModel):
    why_prompt: str
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "Why are you considering purchasing an electric vehicle?",
                "llm_model": "gpt4",
            }
        }


class ProductScoreResponse(BaseModel):
    score: int = Field(
        ge=1, le=5, description="Likelihood score for the product in why_prompt"
    )

    class Config:
        json_schema_extra = {"example": {"score": 5}}


class ProductLevelsRequest(BaseModel):
    why_prompt: str
    attributes: List[Attribute]
    level_count: int = Field(ge=2, le=10, default=5)
    country: str

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What motivates you to consider purchasing an electric vehicle?"
                ),
                "attributes": ["Range", "Price", "Battery capacity (kWh)", "Warranty"],
                "level_count": 5,
                "country": "USA",
            }
        }


class ProductPriceLevelRequest(BaseModel):
    why_prompt: str
    country: str
    attribute: str

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "Why are you considering purchasing an electric vehicle?",
                "country": "USA",
                "attribute": "price",
            }
        }


class ProductPriceLevelResponse(BaseModel):
    units: str
    levels: Dict[str, Dict[str, List[Union[int | float]]]]

    class Config:
        json_schema_extra = {
            "example": {
                "units": "USD",
                "levels": {
                    "Economical": {
                        "Montana West": [10, 20],
                        "KKXIU": [20, 40],
                        "Dasein": [40, 50],
                    },
                    "Mid-Range": {
                        "Guess Factory": [50, 65],
                        "Michael Kors": [66, 100],
                        "Kate Spade": [100, 120],
                    },
                    "High-end": {"Coach": [200, 400], "Tory Burch": [300, 500]},
                    "Luxurious": {
                        "Burberry": [1000, 2000],
                        "Gucci": [1000, 2000],
                        "Celine": [1000, 2000],
                        "Birkin": [10000, 20000],
                    },
                },
            }
        }


class ProductAttributeLevelRequest(BaseModel):
    why_prompt: str
    country: str
    attributes: List[str]
    units: str
    level_count: int = Field(ge=2, le=10, default=5)

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "Why are you considering purchasing an electric vehicle?",
                "country": "USA",
                "attributes": ["Range (miles)"],
                "units": "USD",
                "level_count": 5,
            }
        }


class OrdinalResponse(BaseModel):
    attribute_types: Dict[str, str]
    ordinal_rankings: Dict[str, List[str]] = {}

    @model_validator(mode="before")
    def validate_attribute_types(cls, v):
        if not isinstance(v, dict):
            raise ValueError("attribute_types must be a dictionary")
        for attr, attr_type in v.items():
            if attr_type not in ("nominal", "ordinal"):
                raise ValueError(
                    f"Attribute type for '{attr}' must be 'nominal' or 'ordinal'"
                )
        return v

    @model_validator(mode="before")
    def validate_ordinal_rankings(cls, v, values):
        if "attribute_types" in values:
            ordinal_attrs = [
                attr
                for attr, attr_type in values["attribute_types"].items()
                if attr_type == "ordinal"
            ]
            for attr in ordinal_attrs:
                if attr not in v:
                    raise ValueError(f"Ordinal rankings missing for attribute '{attr}'")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "attribute_types": {
                    "Sustainability": "ordinal",
                    "Color Options": "nominal",
                    "Warranty Period": "ordinal",
                },
                "ordinal_rankings": {
                    "Sustainability": [
                        "Packaging is made from 50% recycled materials",
                        "Packaging is made from plant-based materials",
                        "The packaging is compostable at home",
                        "Packaging is biodegradable within 1 year",
                        "The packaging is 100% recyclable",
                    ],
                    "Warranty Period": [
                        "1 year",
                        "2 years",
                        "3 years",
                    ],
                },
            }
        }


class RealWorldProduct(BaseModel):
    product_name: str
    attributes: Dict[str, str]

    class Config:
        json_schema_extra = {
            "example": {
                "product_name": "Samsung Galaxy S23",
                "attributes": {
                    "Brand": "Samsung",
                    "Model": "Galaxy S23",
                    "Price": "$999",
                    "Battery Life": "24 hours",
                    "Screen Size": "6.1 inches",
                },
            }
        }


class RealWorldProductsResponse(BaseModel):
    product: RealWorldProduct

    class Config:
        json_schema_extra = {
            "products": {
                "product_name": "Samsung Galaxy S23",
                "attributes": {
                    "Brand": "Samsung",
                    "Model": "Galaxy S23",
                    "Price": "$999",
                    "Battery Life": "24 hours",
                    "Screen Size": "6.1 inches",
                },
            }
        }


class RealProductRequest(BaseModel):
    why_prompt: str
    country: str
    products: list[dict[str, str]]

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What are factors effecting peoples choice of buying handbags?"
                ),
                "country": "France",
                "products": [
                    {
                        "Price": "€2,500",
                        "Material": "Leather",
                        "Color": "Brown",
                        "Size": "Medium",
                        "Closure": "Zipper",
                    },
                    {
                        "Price": "€1,800",
                        "Material": "Canvas",
                        "Color": "Black",
                        "Size": "Large",
                        "Closure": "Magnetic",
                    },
                    {
                        "Price": "€10,000",
                        "Material": "Leather",
                        "Color": "Orange",
                        "Size": "Medium",
                        "Closure": "Buckle",
                    },
                    {
                        "Price": "€5,500",
                        "Material": "Tweed",
                        "Color": "Beige",
                        "Size": "Small",
                        "Closure": "Twist Lock",
                    },
                    {
                        "Price": "€1,200",
                        "Material": "Nylon",
                        "Color": "Blue",
                        "Size": "Large",
                        "Closure": "Zipper",
                    },
                ],
            }
        }


class contains_brand(BaseModel):
    contains_brand: int
    contains_price: bool

    # class Config:
    #     json_schema_extra = {"contains_brand": 1, "contains_price": True}


class SpecificOrGenericAttributeCheckResponse(BaseModel):
    attribute_type: Literal["generic", "product_specific"]
    reason: str

    class Config:
        json_schema_extra = {
            "example": {
                "attribute_type": "generic",
                "reason": (
                    "The focus of the question is on undertsnading overall Apple"
                    " product preferences and not one specific category.Therefore, the"
                    " attributes must measure overall inclination towards apple product"
                    " covering entire range."
                ),
            }
        }


class SpecificOrGenericAttributeCheckStrictResponse(BaseModel):
    attribute_type: Literal["generic", "product_specific"]
    reason: str
    modified_statement: str

    class Config:
        json_schema_extra = {
            "example": {
                "attribute_type": "generic",
                "reason": (
                    "The focus of the question is on understanding overall Apple"
                    " product preferences and not one specific category.Therefore, the"
                    " attributes must measure overall inclination towards apple product"
                    " covering entire range."
                ),
                "modified_statement": (
                    "What factors make Apple products appealing compared to its"
                    " competitors?"
                ),
            }
        }


class BrandRelevanceResponse(BaseModel):
    """Response model for brand relevance verification"""

    is_relevant: bool
