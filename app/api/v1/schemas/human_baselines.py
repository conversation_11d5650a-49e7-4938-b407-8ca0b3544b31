from fastapi import File
from pydantic import BaseModel

from app.core.utils.common import get_date


class HumanBaselineBatchExecuteRequest(BaseModel):
    """
    Request object for the Human Baseline Batch Execute endpoint.
    """

    hb_folders_csv_file: File(
        ..., description="CSV file containing the input data for the experiment."
    )
    output_filename: str = f"human_baseline_output_{get_date()}"

    class Config:
        json_schema_extra = {
            "example": {
                "hb_folders_csv_file": "file.csv",
                "output_filename": "human_baseline_output_2022-01-01",
            }
        }
