from datetime import datetime, timedelta

from pydantic import BaseModel


class RunRequest(BaseModel):
    filters: dict

    class Config:
        # Test example for the API docs
        json_schema_extra = {
            "example": {
                "filters": {
                    "created_at": {
                        "$gt": datetime.now() - timedelta(hours=24),
                        "$lte": datetime.now().isoformat(),
                    },
                    "config.user": {
                        "$eq": "user_id e.g. auth0|1234567890",
                    },
                }
            }
        }


class UpdateConfig(BaseModel):
    config_update: dict

    class Config:
        # Test example for the API docs
        json_schema_extra = {
            "example": {
                "config_update": {
                    "toggle_privacy": True,
                },
            },
        }


class RunResponse(BaseModel):
    id: str
    name: str
    state: str
    tags: list[str]
    created_at: str
    config: dict


class CleanedRunResponse(BaseModel):
    id: str | None = None
    name: str | None = None
    state: str | None = None
    created_at: str | None = None
    why_prompt: str | None = None
    is_private: bool | None = None
    r_squared: int | None = None
    sample_size: int | None = None
    tasks_per_respondent: int | None = None
    total_number_of_tasks: int | None = None
    expr_llm_model: str | None = None
    confidence_level: str | None = None
    experiment_type: str = "conjoint"

    class Config:
        json_schema_extra = {
            "example": {
                "id": "04700e81-832f-4c7d-ba1e-d6225716a7db",
                "name": "transformation-25-03-07-14-23-55-126",
                "state": "finished",
                "created_at": "2025-03-07T14:23:55.126Z",
                "why_prompt": "What influences social media engagement?",
                "is_private": False,
                "r_squared": 45,
                "sample_size": 100,
                "tasks_per_respondent": 5,
                "total_number_of_tasks": 500,
                "expr_llm_model": "GPT4",
                "confidence_level": "Reasonable",
                "experiment_type": "conjoint",
            }
        }


class RunOutput(BaseModel):
    model: object
    num_obs: int
    num_vars: int
    num_sig_vars: int

    class Config:
        protected_namespaces = ("settings_",)
        # allows general DataFrame object type validation
        arbitrary_types_allowed = True
