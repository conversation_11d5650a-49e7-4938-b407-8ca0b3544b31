from datetime import datetime
from enum import Enum
from typing import List, Literal, Optional, Union

from pydantic import BaseModel, Field, model_validator, root_validator
from pydantic_settings import BaseSettings

from app.core.global_config import settings
from app.core.utils.type_enums import LLMModel, PromptType
from app.llm_prompt.prompt_templates import SURVEY_PROMPT_TEMPLATE


# todo:  I would like to pull this from the "educational attainment" object in firestore. See https://console.firebase.google.com/u/0/project/why-earth/firestore/databases/-default-/data/~2Ftraits~2F2ZLvY9XU8wBKjZsixFKg
class EducationLevelEnum(str, Enum):
    SOME_COLLEGE = "Some College"
    HIGH_SCHOOL_DIPLOMA = "High School Diploma"
    LESS_THAN_HIGH_SCHOOL = "Less than high school"
    HIGH_SCHOOL_NO_DIPLOMA = "High School but no diploma"
    BACHELORS = "Bachelors"
    ASSOCIATES = "Associates"
    MASTERS = "Masters"
    PHD = "PhD"


class ChildrenEnum(str, Enum):
    ZERO = "0"
    ONE = "1"
    TWO = "2"
    THREE = "3"
    FOUR_PLUS = "4+"


class RacialGroupEnum(str, Enum):
    MIXED_RACE = "Mixed race"
    CAUCASION = "White"
    AFRICAN_AMERICAN = "African American"
    ASIAN = "Asian or Pacific Islander"
    OTHER = "Other race"
    BLACK = "Black"


# todo:  I would like to pull this from the "sex" object in firestore. https://console.firebase.google.com/u/0/project/why-earth/firestore/databases/-default-/data/~2Ftraits~2FGhwNVDv8Q6uZW98RfdgW
class GenderEnum(str, Enum):
    MALE = "Male"
    FEMALE = "Female"


class USStatesEnum(str, Enum):
    Alabama = "Alabama"
    Alaska = "Alaska"
    Arizona = "Arizona"
    Arkansas = "Arkansas"
    California = "California"
    Colorado = "Colorado"
    Connecticut = "Connecticut"
    Delaware = "Delaware"
    Florida = "Florida"
    Georgia = "Georgia"
    Hawaii = "Hawaii"
    Idaho = "Idaho"
    Illinois = "Illinois"
    Indiana = "Indiana"
    Iowa = "Iowa"
    Kansas = "Kansas"
    Kentucky = "Kentucky"
    Louisiana = "Louisiana"
    Maine = "Maine"
    Maryland = "Maryland"
    Massachusetts = "Massachusetts"
    Michigan = "Michigan"
    Minnesota = "Minnesota"
    Mississippi = "Mississippi"
    Missouri = "Missouri"
    Montana = "Montana"
    Nebraska = "Nebraska"
    Nevada = "Nevada"
    New_Hampshire = "New Hampshire"
    New_Jersey = "New Jersey"
    New_Mexico = "New Mexico"
    New_York = "New York"
    North_Carolina = "North Carolina"
    North_Dakota = "North Dakota"
    Ohio = "Ohio"
    Oklahoma = "Oklahoma"
    Oregon = "Oregon"
    Pennsylvania = "Pennsylvania"
    Rhode_Island = "Rhode Island"
    South_Carolina = "South Carolina"
    South_Dakota = "South Dakota"
    Tennessee = "Tennessee"
    Texas = "Texas"
    Utah = "Utah"
    Vermont = "Vermont"
    Virginia = "Virginia"
    Washington = "Washington"
    West_Virginia = "West Virginia"
    Wisconsin = "Wisconsin"
    Wyoming = "Wyoming"


class PopulationTraits(BaseModel):
    # contains the population traits that exist AND that will be added uniformly to the population
    class Config:
        extra = "allow"

    @root_validator(pre=True)
    def validate_extra_fields(cls, values):
        for key, value in values.items():
            if not (
                isinstance(value, (str, int))
                or (
                    isinstance(value, list)
                    and all(isinstance(item, (int, str)) for item in value)
                )
            ):
                raise ValueError(
                    f"Field '{key}' must be a str, int, List[int], or List[str]."
                )
        return values


class TargetPopulation(BaseModel):
    age: list[int] | None = None
    education_level: list[EducationLevelEnum] | None = None
    gender: list[GenderEnum] | None = None
    household_income: list[int] | None = None
    number_of_children: list[ChildrenEnum] | None = None
    racial_group: list[RacialGroupEnum] | None = None
    state: USStatesEnum | None = None


class SurveyResponseType(str, Enum):
    PROBABILISTIC = "probabilistic"
    DISCRETE = "discrete"


class Experiment(BaseSettings):
    title: Optional[str] = ""

    attribute_shuffle_seed: Optional[int] = 1000
    price_generation_seed: Optional[int] = 1000
    population_seed: Optional[int] = 100
    block_optimization_iteration_limit: int = (
        500 if settings.ENVIRONMENT == "dev" else 200
    )
    alternative_shuffle_seed: Optional[int] = 10000

    expr_llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    dv_llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    study_subject_llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    levels_llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    llm_temperature: float = 0.0 if settings.ENVIRONMENT == "prod" else 0.7

    use_api: Optional[bool] = True
    use_threading: Optional[bool] = True
    use_multiprocessing: Optional[bool] = False
    use_batching: Optional[bool] = True
    is_private: Optional[bool] = False
    target_behavior_context: Optional[bool] = False
    binary_choice: Optional[bool] = False
    do_research: Optional[bool] = (
        True if settings.ENVIRONMENT in ["prod", "dev"] else False
    )
    add_decision_time: Optional[bool] = True
    latent_variables: bool = True

    target_behavior: str = ""
    optimization_context: str = ""
    context: str = ""

    experiment_type: Literal["concept_testing", "conjoint"] = "conjoint"
    image_name: str | None = None
    concept_description: str | None = None
    concept_statements: list[dict] | None = None

    survey_prompt: Optional[str] = SURVEY_PROMPT_TEMPLATE

    max_length: Optional[int] = 40
    batch_size: Optional[int] = 20
    prob_to_discrete_seed: Optional[int] = 500
    decision_time_seed: Optional[int] = 1000

    attribute_count: Optional[int] = Field(ge=2, le=7, default=4)
    level_count: Optional[int] = Field(ge=2, le=10, default=4)
    pre_cooked_attributes_and_levels_lookup: Optional[
        List[List[Union[str, List[str]]]]
    ] = []

    # TODO: levels_per_trait isn't used in survey generation. Consider removing it with caution.
    levels_per_trait: Optional[int] = 100
    null_levels: Optional[bool] = True
    total_number_of_tasks: Optional[int] = 0
    confidence_level: Optional[str] = "Low"
    r_squared: Optional[int] = 0
    spearman_correlation: Optional[float] = 0.0
    coverage_probability: Optional[float] = 0.0
    sample_size: Optional[int] = 0
    tasks_per_respondent: Optional[int] = 0
    realworld_products: Optional[list[dict]] = []
    external_personas: Optional[list[dict]] = []

    token_count: Optional[int] = 0
    cost_amount: Optional[float] = 0.0

    add_neither_option: Optional[bool] = False
    include_price_brand: Optional[bool] = False
    mnp_model: Optional[bool] = True
    use_halton_draws: Optional[bool] = False

    # TODO: all_combinations_index is not used in the current implementation. Consider removing it with caution.
    all_combinations_index: Optional[list[list[str]]] = [[""]]

    population_traits: Optional[PopulationTraits] = {}
    # target_population: Optional[TargetPopulation] = Field(
    #     default_factory=TargetPopulation
    # )
    target_population: Optional[TargetPopulation] = {}
    response_type: Optional[SurveyResponseType] = (
        SurveyResponseType.DISCRETE
        if settings.ENVIRONMENT == "prod"
        else SurveyResponseType.PROBABILISTIC
    )

    respondent_dependent_variable: Optional[str] = ""
    respondent_instructions_preamble: Optional[str] = ""

    prompt_type: Optional[str] = PromptType.GENERIC  # Ideation focus
    why_prompt: Optional[str] = ""

    country: str = "United States"
    state: str | None = None
    year: str = str(datetime.now().year)

    hb_folder: Optional[str] = ""
    hb_run_id: Optional[str] = ""
    is_hb_run: Optional[bool] = False
    paper_data: Optional[list[list[str]]] = []

    @model_validator(mode="before")
    @classmethod
    def check_hb_fields(cls, values):
        hb_folder = values.get("hb_folder")
        hb_run_id = values.get("hb_run_id")

        if hb_folder and hb_run_id:
            raise ValueError(
                "Only one of 'hb_folder' or 'hb_run_id' should have a value."
            )

        return values

    class Config:
        json_schema_extra = {
            "example": {
                "experiment_type": "conjoint",
                "why_prompt": "What factor affect the adoption of an electric vehicle?",
                "attribute_count": 2,
                "level_count": 5,
                "pre_cooked_attributes_and_levels_lookup": [
                    [
                        "Brands",
                        [
                            "Nissan Leaf",
                            "Chevrolet Bolt",
                            "Hyundai Kona Electric",
                            "Ford Mustang Mach-E",
                            "Porsche Taycan",
                        ],
                        "non-monetary",
                    ],
                    [
                        "Price",
                        [
                            "26000 USD",
                            "37150 USD",
                            "48300 USD",
                            "84150 USD",
                            "120000 USD",
                        ],
                        "monetary",
                    ],
                ],
                "image_name": "milk-image.png",
                "concept_description": (
                    "We're evaluating a new on the go smoothie product and would like"
                    " to understand people's perception towards it."
                ),
                "concept_statements": [
                    {
                        "statement": "Would taste great",
                        "labels": [
                            "Strongly Disagree",
                            "Disagree",
                            "Neutral",
                            "Agree",
                            "Strongly Agree",
                        ],
                    },
                    {
                        "statement": "Would have good texture",
                        "labels": [
                            "Strongly Disagree",
                            "Disagree",
                            "Neutral",
                            "Agree",
                            "Strongly Agree",
                        ],
                    },
                    {
                        "statement": (
                            "Would be something I, or others in my family, look forward"
                            " to drinking"
                        ),
                        "labels": [
                            "Strongly Disagree",
                            "Disagree",
                            "Neutral",
                            "Agree",
                            "Strongly Agree",
                        ],
                    },
                ],
                "realworld_products": [],
                "population_traits": {
                    "political affiliation": ["republican", "democrat"],
                    "Hearing disability": ["Yes", "No"],
                },
                "target_population": {
                    "age": [20, 26],
                    "education_level": [
                        "Less than high school",
                        "High School but no diploma",
                        "High School Diploma",
                        "Some College",
                        "Associates",
                        "Bachelors",
                        "Masters",
                        "PhD",
                    ],
                    "gender": ["Female"],
                    "household_income": [50000, 70000],
                    "number_of_children": ["1", "2", "3", "4+"],
                    "racial_group": [
                        "White",
                        "African American",
                        "Asian or Pacific Islander",
                        "Mixed race",
                        "Other race",
                    ],
                    "state": None,
                },
                "external_personas": [
                    {
                        "name": "Ella",
                        "age": "18-25",
                        "gender": "Female",
                        "maritalStatus": "",
                        "income": "",
                        "education": "",
                        "racialGroup": "",
                        "homeOwnership": "",
                        "vehiclesOwned": "",
                        "hasDrivingLicense": "",
                        "location": "Urban areas",
                        "occupation": "",
                        "background": (
                            "Tech-savvy, socially responsible, driven by authenticity"
                            " and transparency"
                        ),
                        "goals": (
                            "Maintain a health-conscious and sustainable lifestyle,"
                            " make ethically responsible purchasing decisions, inspire"
                            " others to adopt similar values"
                        ),
                        "painPoints": (
                            "Balancing ethical consumption with budget constraints,"
                            " finding authentic and sustainable luxury brands, dealing"
                            " with inauthentic marketing and greenwashing"
                        ),
                        "personalityTraits": (
                            "Analytical, tech-savvy, socially responsible"
                        ),
                        "behaviors": (
                            "Actively researches and evaluates brands online, engages"
                            " with social media communities, values influencer"
                            " insights, seeks transparency and authenticity in brand"
                            " communications"
                        ),
                        "isDescriptionBased": False,
                        "id": "1743231143589a0sqzh8",
                    },
                    {
                        "name": "Jordan",
                        "age": "18-25",
                        "gender": "Female",
                        "maritalStatus": "",
                        "income": "",
                        "education": "",
                        "racialGroup": "",
                        "homeOwnership": "",
                        "vehiclesOwned": "",
                        "hasDrivingLicense": "",
                        "location": "Urban areas",
                        "occupation": "",
                        "background": (
                            "Creative explorer, values sustainability and authenticity"
                        ),
                        "goals": (
                            "Express individuality through scent while supporting"
                            " sustainable and ethical brands, seek affordable luxury"
                            " options that align with values"
                        ),
                        "painPoints": (
                            "Finding sustainable fragrance options that are affordable,"
                            " overwhelmed by inauthentic marketing and greenwashing"
                        ),
                        "personalityTraits": (
                            "Open-minded, innovative, tech-savvy, strong sense of"
                            " social responsibility"
                        ),
                        "behaviors": (
                            "Follows perfume influencers on social media, enjoys"
                            " experimenting with different scents to express"
                            " individuality"
                        ),
                        "isDescriptionBased": False,
                        "id": "17432311435893jnm4ve",
                    },
                    {
                        "name": "Sam",
                        "age": "18-25",
                        "gender": "Female",
                        "maritalStatus": "",
                        "income": "",
                        "education": "",
                        "racialGroup": "",
                        "homeOwnership": "",
                        "vehiclesOwned": "",
                        "hasDrivingLicense": "",
                        "location": "Urban areas",
                        "occupation": "",
                        "background": (
                            "Sustainability advocate, prioritizes social responsibility"
                        ),
                        "goals": (
                            "Support brands that align with sustainability and ethical"
                            " practices, express individuality through fragrance"
                        ),
                        "painPoints": (
                            "Difficulty in accessing authentic sustainable luxury"
                            " products, skepticism towards marketing claims"
                        ),
                        "personalityTraits": (
                            "Deeply rooted in sustainability, authenticity,"
                            " transparency"
                        ),
                        "behaviors": (
                            "Researches fragrance trends online, engages with social"
                            " media communities, values ethical sourcing and"
                            " transparency in brands"
                        ),
                        "isDescriptionBased": False,
                        "id": "1743231143589xqb84s8",
                    },
                    {
                        "name": "Elijah",
                        "age": "",
                        "gender": "Male",
                        "maritalStatus": "",
                        "income": "",
                        "education": "",
                        "racialGroup": "",
                        "homeOwnership": "",
                        "vehiclesOwned": "",
                        "hasDrivingLicense": "",
                        "location": "Urban USA markets",
                        "occupation": "",
                        "background": (
                            "Tech enthusiast, interested in EVs and charging"
                            " infrastructure"
                        ),
                        "goals": "",
                        "painPoints": "",
                        "personalityTraits": "",
                        "behaviors": "",
                        "isDescriptionBased": False,
                        "id": "1743231143589xhps1r7",
                    },
                ],
                "country": "United States",
                "state": None,
                "year": str(datetime.now().year),
                "expr_llm_model": LLMModel.GCP_SONNET,
                "response_type": SurveyResponseType.PROBABILISTIC,
                "is_private": False,
                "hb_run_id": "",
                "hb_folder": "",
                "mnp_model": True,
                "add_neither_option": False,
                "use_halton_draws": False,
                "do_research": False,
            }
        }
        extra = "ignore"


class ExperimentAsyncResponse(BaseModel):
    wandb_run_id: str
    wandb_run_name: str

    class Config:
        json_extra_schema = {
            "example": {
                "wandb_run_id": "550e8400-e29b-41d4-a716-446655440000",
                "wandb_run_name": "causal-23-10-17-14-30-45-123",
            }
        }


class ExperimentResponse(BaseModel):
    mappings: dict  # Mappings of experiment data
    survey_results: str  # Survey results in string format
    wandb_run_id: str | None  # Weights & Biases run ID, optional
    wandb_run_name: str | None  # Weights & Biases run name, optional


class DependentVariable(BaseModel):
    prompt_type: Optional[PromptType] = PromptType.GENERIC
    why_prompt: str
    llm_model: LLMModel = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "How does social media usage affect sleep patterns?",
                "prompt_type": PromptType.GENERIC,
            }
        }


class DependentVariableResponse(BaseModel):
    dependent_variable: str  # Survey prompt that synthetic respondents will respond to


class ObjectOfStudyResponse(BaseModel):
    object_of_study: str = None

    class Config:
        json_schema_extra = {
            "example": {
                "object_of_study": "design of electric car for the American markets"
            }
        }


class OutcomePhraseResponse(BaseModel):
    # TODO: Rename 'outcomephrase' to study_subject
    outcome_phrase: str = None

    class Config:
        json_schema_extra = {
            "example": {
                "outcome_phrase": (
                    "We are trying to understand the factors which affects the choice"
                    " of a new electric car for the American markets"
                )
            }
        }


class TargetBehaviorResponse(BaseModel):
    target_behavior: str = Field(..., title="The behavior being studied")
    optimization: str = Field(
        ..., title="Whether the behavior should be maximized or minimized"
    )
    reasoning: str = Field(
        ..., title="Brief explanation of why you chose this interpretation"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "target_behavior": "The number of electric cars sold",
                "optimization": "maximize",
                "reasoning": (
                    "I chose this interpretation because we want to maximize the number"
                    " of electric cars sold."
                ),
            }
        }


class StudyContextResponse(BaseModel):
    context: str = Field(..., title="The decision context sentence")
    reasoning: str = Field(
        ..., title="Brief explanation of why this context is appropriate"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "context": "You need to complete an important task",
                "reasoning": (
                    "This context is appropriate because it is a common scenario in"
                    " which people need to make decisions."
                ),
            }
        }


class ExperimentIdea(BaseModel):
    why_question: str
    rationale: str

    class Config:
        json_schema_extra = {
            "example": {
                "why_question": (
                    "Why do people prefer electric cars over gasoline cars?"
                ),
                "rationale": (
                    "Electric cars are preferred due to their environmental benefits"
                    " and lower running costs."
                ),
            }
        }


class ExperimentIdeationResponse(BaseModel):
    data: List[ExperimentIdea]

    class Config:
        json_schema_extra = {
            "example": {
                "data": [
                    {
                        "why_question": (
                            "Why do people prefer electric cars over gasoline cars?"
                        ),
                        "rationale": (
                            "Electric cars are preferred due to their environmental"
                            " benefits and lower running costs."
                        ),
                    },
                    {
                        "why_question": (
                            "Why do people choose renewable energy sources?"
                        ),
                        "rationale": (
                            "Renewable energy sources are chosen because they are"
                            " sustainable and reduce carbon emissions."
                        ),
                    },
                ]
            }
        }


class CausalityCheckResponse(BaseModel):
    is_causal: bool
    rationale: str

    class Config:
        json_schema_extra = {
            "example": {
                "is_causal": True,
                "rationale": (
                    "The relationship between the variables is causal because the"
                    " independent variable directly influences the dependent variable."
                ),
            }
        }


class RealWorldConjoinStatementRequest(BaseModel):
    why_prompt: str
    suggestion_count: int = Field(default=3, ge=1, le=5)
    attribute_count: int = Field(default=4, ge=2, le=10)
    statement_history: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors affect the choice of purchasing a smartphone?"
                ),
                "suggestion_count": 3,
                "attribute_count": 8,
                "statement_history": [
                    "What factors affect the choice of purchasing a smartphone?",
                    (
                        "What factors affect the choice of purchasing an expensive"
                        " smartphone?"
                    ),
                ],
            }
        }


class RealWorldConjoinStatementSuggestion(BaseModel):
    text: str = Field(
        title="Text of the suggestion",
        description="The suggestion generated by the model.",
    )
    attributes: list[str] = Field(
        title="List of attributes",
        description="The attributes associated with the suggestion.",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "text": (
                    "What device specifications influence consumer choice for"
                    " purchasing a Samsung Galaxy smartphone?"
                ),
                "attributes": ["Price", "Screen Size", "Battery Life"],
            }
        }


class RealWorldConjoinStatementIndividualResponse(BaseModel):
    text: str
    attributes: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "text": "What factors affect the choice of purchasing a smartphone?",
                "attributes": ["Price", "Screen Size", "Battery Life"],
            }
        }


class RealWorldConjoinStatementFinalResponse(BaseModel):
    is_causal: bool
    flag: str | None
    suggestions: list[RealWorldConjoinStatementIndividualResponse]

    class Config:
        json_schema_extra = {
            "example": {
                "suggestions": [
                    {
                        "text": (
                            "What factors affect the choice of purchasing a smartphone?"
                        ),
                        "attributes": ["Price", "Screen Size", "Battery Life"],
                    },
                    {
                        "text": (
                            "What factors affect the choice of purchasing an expensive"
                            " smartphone?"
                        ),
                        "attributes": ["Brand", "Camera Quality", "Battery Life"],
                    },
                ]
            }
        }


class RealWorldConjointStatementResponse(BaseModel):
    is_causal: bool
    flag: str | None
    suggestions: list[RealWorldConjoinStatementSuggestion] = Field(
        title="List of suggestions for the experiment",
        description="Each suggestion contains a text, feasibility, and attribute type.",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "is_causal": True,
                "flag": None,
                "suggestions": [{
                    "text": (
                        "What device specifications influence consumer choice for"
                        " purchasing a Samsung Galaxy smartphone?"
                    ),
                    "attributes": ["Price", "Screen Size", "Battery Life"],
                }],
            }
        }


class LikertLabelRequest(BaseModel):
    description: str | None = None
    image_name: str | None = None
    statements: list[str] = []
    scale: int = Field(default=5, ge=1, le=10)

    class Config:
        json_schema_extra = {
            "example": {
                "description": (
                    "We're evaluating a new on the go smoothie product and would like"
                    " to understand people's perception towards it."
                ),
                "image_name": "milk-image.png",
                "statements": [
                    "Would taste great",
                    "Would have good texture",
                    (
                        "Would be something I, or others in my family, look forward to"
                        " drinking"
                    ),
                ],
                "scale": 5,
            }
        }


class LikertLabel(BaseModel):
    statement: str
    labels: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "statement": "The onboarding process was helpful.",
                "labels": [
                    "Strongly Disagree",
                    "Disagree",
                    "Neutral",
                    "Agree",
                    "Strongly Agree",
                ],
            }
        }


class LikertLabelResponse(BaseModel):
    likert_labels: list[LikertLabel]

    class Config:
        json_schema_extra = {
            "example": {
                "likert_labels": [{
                    "statement": "The onboarding process was helpful.",
                    "labels": [
                        "Strongly Disagree",
                        "Disagree",
                        "Neutral",
                        "Agree",
                        "Strongly Agree",
                    ],
                }]
            }
        }


class ImageContextResponse(BaseModel):
    description: str

    class Config:
        json_schema_extra = {
            "example": {
                "description": (
                    "A photo of a crowded beach with people sunbathing and swimming."
                )
            }
        }
