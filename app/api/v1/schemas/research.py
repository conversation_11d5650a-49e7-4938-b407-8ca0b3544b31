from typing import Optional

from pydantic import BaseModel, Field

from app.core.utils.type_enums import ResearchReportType, ResearchToneType


class ResearchRequest(BaseModel):
    why_prompt: str = Field(..., description="The research query to investigate")
    report_type: ResearchReportType = Field(
        ..., description="Type of report to generate"
    )
    tone: Optional[ResearchToneType] = Field(
        default=ResearchToneType.Objective, description="The tone of the report"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors influence a person's decision to buy a house?"
                ),
                "report_type": ResearchReportType.ResearchReport,
                "tone": ResearchToneType.Objective,
            }
        }
