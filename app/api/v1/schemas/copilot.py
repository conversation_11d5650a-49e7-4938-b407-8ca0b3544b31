from pydantic import BaseModel

from app.core.utils.type_enums import LLMModel


class CausalityRequest(BaseModel):
    why_prompt: str
    llm_model: LLMModel | None = LLMModel.AZURE_GPTO3_MINI

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "Why do humans have two eyes?",
            }
        }


class CausalityResponse(BaseModel):
    is_causal: bool
    is_harmful: bool
    suggestions: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "is_causal": False,
                "is_harmful": False,
                "suggestions": [
                    (
                        "What factors contribute to the evolutionary advantage of"
                        " having two eyes?"
                    ),
                    (
                        "What are the key features or benefits of binocular vision in"
                        " humans?"
                    ),
                    (
                        "How do different aspects of having two eyes impact human"
                        " perception and survival?"
                    ),
                ],
            },
        }


class FinalCausalityResponse(BaseModel):
    is_causal: bool = False
    suggestions: list[str]

    class Config:
        json_schema_extra = {
            "example": {
                "is_causal": False,
                "suggestions": [
                    (
                        "What factors contribute to the evolutionary advantage of"
                        " having two eyes?"
                    ),
                    (
                        "What are the key features or benefits of binocular vision in"
                        " humans?"
                    ),
                    (
                        "How do different aspects of having two eyes impact human"
                        " perception and survival?"
                    ),
                ],
            },
        }
