from typing import Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator

from app.api.v1.schemas.experiments import (
    ChildrenEnum,
    EducationLevelEnum,
    GenderEnum,
    RacialGroupEnum,
    USStatesEnum,
)


class PopulationRequest(BaseModel):
    age: Optional[list[int]] = None
    number_of_children: Optional[list[ChildrenEnum]] = None
    household_with_children: Optional[Literal["yes", "no"]] = None
    education_level: Optional[list[EducationLevelEnum]] = None
    household_income: Optional[list[int]] = None
    state: Optional[USStatesEnum] = None
    gender: Optional[list[GenderEnum]] = None
    racial_group: Optional[list[RacialGroupEnum]] = None
    number_of_records: int = Field(250, le=5000)

    @field_validator("age")
    def validate_age(cls, v):
        if v is not None:
            if len(v) != 2:
                raise ValueError("Age must be a list of exactly 2 integers [min, max]")
            if not (18 <= v[0] <= 100 and 18 <= v[1] <= 100):
                raise ValueError("Age must be between 18 and 100")
            if v[0] > v[1]:
                raise ValueError(
                    "Minimum age must be less than or equal to maximum age"
                )
        return v

    @field_validator("household_income")
    def validate_household_income(cls, v):
        if v is not None:
            if len(v) != 2:
                raise ValueError(
                    "Household income must be a list of exactly 2 integers [min, max]"
                )
            if not (0 <= v[0] <= 3_000_000 and 0 <= v[1] <= 3_000_000):
                raise ValueError("Household income must be between 0 and 3,000,000")
            if v[0] > v[1]:
                raise ValueError(
                    "Minimum income must be less than or equal to maximum income"
                )
        return v

    @model_validator(mode="after")
    def validate_children_with_household(cls, values):
        household_with_children = values.household_with_children
        number_of_children = values.number_of_children
        children_exist = [
            ChildrenEnum.ONE,
            ChildrenEnum.TWO,
            ChildrenEnum.THREE,
            ChildrenEnum.FOUR_PLUS,
        ]
        if (
            household_with_children == "yes"
            and number_of_children
            and ChildrenEnum.ZERO in number_of_children
        ):
            raise ValueError(
                "If 'household_with_children' is 'yes', then 'number_of_children'"
                " cannot be 0"
            )
        if (
            household_with_children == "no"
            and number_of_children
            and len([i for i in children_exist if i in number_of_children]) > 0
        ):
            raise ValueError(
                "If 'household_with_children' is 'no', then 'number_of_children' cannot"
                " be more than 0"
            )

        return values

    class Config:
        json_schema_extra = {
            "example": {
                "age": [20, 26],
                "number_of_children": [
                    ChildrenEnum.ONE,
                    ChildrenEnum.TWO,
                    ChildrenEnum.THREE,
                    ChildrenEnum.FOUR_PLUS,
                ],
                "education_level": [
                    EducationLevelEnum.LESS_THAN_HIGH_SCHOOL,
                    EducationLevelEnum.HIGH_SCHOOL_NO_DIPLOMA,
                    EducationLevelEnum.HIGH_SCHOOL_DIPLOMA,
                    EducationLevelEnum.SOME_COLLEGE,
                    EducationLevelEnum.ASSOCIATES,
                    EducationLevelEnum.BACHELORS,
                    EducationLevelEnum.MASTERS,
                    EducationLevelEnum.PHD,
                ],
                "household_income": [50000, 70000],
                "racial_group": [
                    RacialGroupEnum.CAUCASION,
                    RacialGroupEnum.AFRICAN_AMERICAN,
                    RacialGroupEnum.ASIAN,
                    RacialGroupEnum.MIXED_RACE,
                    RacialGroupEnum.OTHER,
                ],
                "state": USStatesEnum.New_York,
                "number_of_records": 500,
                "gender": [GenderEnum.FEMALE],
            }
        }


class PopulationRecommendationResponse(BaseModel):
    suggestion: dict
    original: dict
    one_trait_change: dict

    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "suggestion": {
                    "population_size": 154788,
                    "population_traits": {
                        "state": "New York",
                        "gender": ["Female", "Male"],
                        "age": [18, 95],
                        "household_with_children": None,
                        "number_of_children": ["1", "4+", "0", "2", "3"],
                        "education_level": [
                            "Associates",
                            "Bachelors",
                            "High School Diploma",
                            "High School but no diploma",
                            "Less than high school",
                            "Masters",
                            "PhD",
                            "Some College",
                        ],
                        "household_income": [0, 2221200],
                        "racial_group": [
                            "White",
                            "African American",
                            "Asian or Pacific Islander",
                            "Mixed race",
                            "Other race",
                        ],
                    },
                },
                "original": {
                    "population_size": 42,
                    "population_traits": {
                        "gender": ["Female"],
                        "age": [20, 22],
                        "household_with_children": "yes",
                        "number_of_children": ["1", "4+"],
                        "education_level": [
                            "Associates",
                            "Bachelors",
                            "High School Diploma",
                            "High School but no diploma",
                        ],
                        "household_income": [0, 3000000],
                        "racial_group": ["White", "Black", "Asian or Pacific Islander"],
                        "state": "New York",
                    },
                },
                "one_trait_change": {
                    "gender": {
                        "population_size": 59,
                        "population_traits": {
                            "age": [20, 22],
                            "household_with_children": "yes",
                            "number_of_children": ["1", "4+"],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "gender": ["Female", "Male"],
                            "state": "New York",
                        },
                    },
                    "age": {
                        "population_size": 8810,
                        "population_traits": {
                            "gender": ["Female"],
                            "household_with_children": "yes",
                            "number_of_children": ["1", "4+"],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "age": [18, 95],
                            "state": "New York",
                        },
                    },
                    "household_with_children": {
                        "population_size": 42,
                        "population_traits": {
                            "gender": ["Female"],
                            "age": [20, 22],
                            "number_of_children": ["1", "4+"],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "household_with_children": None,
                            "state": "New York",
                        },
                    },
                    "number_of_children": {
                        "population_size": 65,
                        "population_traits": {
                            "gender": ["Female"],
                            "age": [20, 22],
                            "household_with_children": "yes",
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "number_of_children": ["1", "4+", "2", "3"],
                            "state": "New York",
                        },
                    },
                    "education_level": {
                        "population_size": 66,
                        "population_traits": {
                            "gender": ["Female"],
                            "age": [20, 22],
                            "household_with_children": "yes",
                            "number_of_children": ["1", "4+"],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                                "Less than high school",
                                "Masters",
                                "Some College",
                            ],
                            "state": "New York",
                        },
                    },
                    "household_income": {
                        "population_size": 42,
                        "population_traits": {
                            "gender": ["Female"],
                            "age": [20, 22],
                            "household_with_children": "yes",
                            "number_of_children": ["1", "4+"],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                            ],
                            "household_income": [4000, 812360],
                            "state": "New York",
                        },
                    },
                    "racial_group": {
                        "population_size": 57,
                        "population_traits": {
                            "gender": ["Female"],
                            "age": [20, 22],
                            "household_with_children": "yes",
                            "number_of_children": ["1", "4+"],
                            "education_level": [
                                "Associates",
                                "Bachelors",
                                "High School Diploma",
                                "High School but no diploma",
                            ],
                            "household_income": [0, 3000000],
                            "racial_group": [
                                "White",
                                "African American",
                                "Asian or Pacific Islander",
                                "Mixed race",
                                "Other race",
                            ],
                            "state": "New York",
                        },
                    },
                },
            }
        }


class PopulationResponse(BaseModel):
    population: List[
        Dict[Union[int, str], Union[str, int, List[str], List[int], List[float], None]]
    ]

    class Config:
        # This allows the alias to be used in query parameters
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "population": [
                    {
                        "household_income": 75200,
                        "vehicles_in_household": 3,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Female",
                        "marital_status": "Never married/single",
                        "family_size": 5,
                        "age": 31,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 83,
                        "person_wt": 96,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Wage/salary, private",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "United States",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Grandchild",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                    {
                        "household_income": 93000,
                        "vehicles_in_household": 3,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Married, spouse present",
                        "family_size": 4,
                        "age": 30,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 17,
                        "person_wt": 18,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Federal govt employee",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Not Reported",
                        "number_of_own_children": "2",
                        "relationship_to_head": "Head/Householder",
                        "number_of_children": "2",
                        "racial_group": "White",
                        "household_with_children": "yes",
                    },
                    {
                        "household_income": 93000,
                        "vehicles_in_household": 3,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Female",
                        "marital_status": "Married, spouse present",
                        "family_size": 4,
                        "age": 29,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 17,
                        "person_wt": 31,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Self-employed, not incorporated",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Not Reported",
                        "number_of_own_children": "2",
                        "relationship_to_head": "Spouse",
                        "number_of_children": "2",
                        "racial_group": "White",
                        "household_with_children": "yes",
                    },
                    {
                        "household_income": 60000,
                        "vehicles_in_household": 9,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Female",
                        "marital_status": "Married, spouse present",
                        "family_size": 4,
                        "age": 26,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 32,
                        "person_wt": 23,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": None,
                        "migration_status": "Same house",
                        "occupational_prestige": None,
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": None,
                        "speaks_english": "Yes, speaks well",
                        "health_insurance_coverage": "No health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "German",
                        "ancestry": "Swiss",
                        "number_of_own_children": "2",
                        "relationship_to_head": "Spouse",
                        "number_of_children": "2",
                        "racial_group": "White",
                        "household_with_children": "yes",
                    },
                    {
                        "household_income": 50300,
                        "vehicles_in_household": 2,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Never married/single",
                        "family_size": 1,
                        "age": 30,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 55,
                        "person_wt": 56,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Wage/salary, private",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "German",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Head/Householder",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                    {
                        "household_income": 101400,
                        "vehicles_in_household": 2,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Female",
                        "marital_status": "Separated",
                        "family_size": 3,
                        "age": 35,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 115,
                        "person_wt": 97,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": None,
                        "migration_status": "Moved between states",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Yes, available for work",
                        "class_of_worker_detailed": "Wage/salary, private",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Not Reported",
                        "number_of_own_children": "1 child present",
                        "relationship_to_head": "Sibling",
                        "number_of_children": "1",
                        "racial_group": "White",
                        "household_with_children": "yes",
                    },
                    {
                        "household_income": 41300,
                        "vehicles_in_household": 2,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Never married/single",
                        "family_size": 3,
                        "age": 33,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 350,
                        "person_wt": 601,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Wage/salary, private",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "No health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Not Reported",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Child",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                    {
                        "household_income": 59000,
                        "vehicles_in_household": 1,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Never married/single",
                        "family_size": 2,
                        "age": 30,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 23,
                        "person_wt": 18,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": None,
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Yes, available for work",
                        "class_of_worker_detailed": "Self-employed, incorporated",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "No health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "White/Caucasian",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Partner, friend, visitor",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                    {
                        "household_income": 114000,
                        "vehicles_in_household": 2,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Married, spouse present",
                        "family_size": 2,
                        "age": 29,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 76,
                        "person_wt": 75,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": "Auto, truck, or van",
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Not reported",
                        "class_of_worker_detailed": "Federal govt employee",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Not Reported",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Head/Householder",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                    {
                        "household_income": 51100,
                        "vehicles_in_household": 4,
                        "census_division": "Middle Atlantic",
                        "state": "Pennsylvania",
                        "gender": "Male",
                        "marital_status": "Never married/single",
                        "family_size": 2,
                        "age": 31,
                        "race": "White",
                        "education_level": "High School Diploma",
                        "hispanic_latino": "No",
                        "household_wt": 70,
                        "person_wt": 65,
                        "poverty_status": "In poverty universe",
                        "veteran_status": "Not a veteran",
                        "veteran_status_detailed": "No military service",
                        "means_of_transportation": None,
                        "migration_status": "Same house",
                        "occupational_prestige": "Unknown",
                        "availability_for_work": "Yes, available for work",
                        "class_of_worker_detailed": "Wage/salary, private",
                        "speaks_english": "Yes, speaks only English",
                        "health_insurance_coverage": "With health insurance coverage",
                        "years_in_us": None,
                        "language_spoken": "English",
                        "ancestry": "Welsh",
                        "number_of_own_children": "0 children present",
                        "relationship_to_head": "Child",
                        "number_of_children": "0",
                        "racial_group": "White",
                        "household_with_children": "no",
                    },
                ]
            }
        }
