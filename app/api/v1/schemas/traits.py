from typing import List

from pydantic import BaseModel, Field

from app.core.utils.type_enums import LLMModel


class Trait(BaseModel):
    name: str
    levels: list[str]

    class Config:
        json_schema_extra = {
            "example": {"name": "gender", "levels": ["male", "female", "other"]}
        }


class TraitGenerationRequest(BaseModel):
    why_prompt: str = Field(
        ...,
        description="The why prompt explaining the use case",
        examples=["How does social media usage affect sleep patterns?"],
    )
    selected_traits: List[str] = Field(
        ...,
        description="List of trait names already selected",
        examples=["age", "education", "occupation"],
    )
    number_of_new_traits: int = Field(default=4, ge=1, le=10)
    number_of_levels_per_trait: int = Field(default=3, ge=2, le=5)
    llm_model: LLMModel = LLMModel.AZURE_GPT4O_MINI
    max_length: int = 100
    country: str = "USA"

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": "How does social media usage affect sleep patterns?",
                "selected_traits": ["age", "education", "occupation"],
                "number_of_new_traits": 4,
                "number_of_levels_per_trait": 3,
                "max_length": 100,
                "country": "USA",
            }
        }


class TraitSuggestionResponse(BaseModel):
    suggested_traits: list[Trait] = Field(
        ..., description="List of suggested traits with their possible levels"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "suggested_traits": [
                    {
                        "name": "personality_type",
                        "levels": ["Introverted", "Extroverted", "Ambivert"],
                    },
                    {
                        "name": "impulsivity_level",
                        "levels": [
                            "Low Self-Control",
                            "Moderate Self-Control",
                            "High Self-Control",
                        ],
                    },
                ]
            }
        }


class TraitLevelsSuggestionRequest(BaseModel):
    existing_traits: List[str] = Field(
        ...,
        description="List of existing traits",
        examples=[
            "Personality Type",
            "Age",
            "Mental Health Status",
            "Educational Background",
        ],
    )
    new_trait: str = Field(
        ...,
        description="Name of the new trait for which levels need to be suggested",
    )
    levels_count: int = Field(
        3,
        description="Number of levels to suggest for the new trait",
        ge=2,
        le=5,
    )
    why_prompt: str = Field(
        ...,
        description="The why prompt explaining the use case",
        examples=["How does social media usage affect sleep patterns?"],
    )
    llm_model: LLMModel = LLMModel.AZURE_GPTO3_MINI
    max_length: int = Field(
        default=100,
        description="Maximum length of the trait levels",
    )
    country: str = Field(default="USA", description="The country of the survey")

    class Config:
        json_schema_extra = {
            "example": {
                "existing_traits": [
                    "Personality Type",
                    "Age",
                    "Mental Health Status",
                    "Educational Background",
                ],
                "new_trait": "Cultural Orientation",
                "levels_count": 3,
                "max_length": 100,
                "country": "USA",
                "why_prompt": "How does social media usage affect sleep patterns?",
            }
        }


class TraitLevelsSuggestionResponse(BaseModel):
    trait: str = Field(
        ..., description="Name of the trait for which levels are suggested"
    )
    levels: List[str] = Field(..., description="List of suggested levels for the trait")

    class Config:
        json_schema_extra = {
            "example": {
                "trait": "Cultural Orientation",
                "levels": [
                    "Traditional",
                    "Moderate",
                    "Tech-Embracing",
                ],
            }
        }


class NaturalLanguageTrait(BaseModel):
    variable: str = Field(
        ...,
        description="The variable name of the trait",
        examples=["age", "gender", "education"],
    )
    demo: str = Field(
        ...,
        description="Natural language description of the trait",
        examples=["() years old", "Your experiences as a ()"],
    )
    guide: str = Field(
        ...,
        description="Natural language guide for the trait",
        examples=["Your life experience and perspective as someone in their ()s"],
    )

    class Config:
        json_schema_extra = {
            "example": {
                "variable": "age",
                "demo": "{} years old",
                "guide": "Your life experience and perspective as someone in their {}s",
            }
        }


class NaturalLanguageTraitResponse(BaseModel):
    traits: List[NaturalLanguageTrait] = Field(
        ..., description="List of natural language descriptions for traits"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "traits": [
                    {
                        "variable": "age",
                        "demo": "() years old",
                        "guide": (
                            "Your life experience and perspective as someone in"
                            " their ()s"
                        ),
                    },
                    {
                        "variable": "gender",
                        "demo": "()",
                        "guide": "Your experiences as a ()",
                    },
                ]
            }
        }


class NaturalLanguageTraitRequest(BaseModel):
    traits: list[dict] = Field(
        ...,
        description=(
            "List of trait names and their type (binary or non-binary) for which to get"
            " natural language descriptions"
        ),
    )

    class Config:
        json_schema_extra = {
            "example": {
                "traits": [
                    {"attribute": "latino", "type": "binary"},
                    {"attribute": "lactose intolerant", "type": "binary"},
                    {"attribute": "age", "type": "non-binary"},
                ]
            }
        }


class BinaryVariableToNaturalLanguageResponse(BaseModel):
    persona: str = Field(
        ...,
        description="Persona in natural language format",
    )

    class Config:
        json_schema_extra = {
            "example": {
                "persona": (
                    "I am a 18 years old non-hispanic white female with one children"
                    " of age 0 - 5 years old"
                )
            }
        }
