from typing import Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, RootModel

from app.core.utils.type_enums import LLMModel


class HistoryRequest(BaseModel):
    why_prompt: str
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]]
    attributes: List[str]
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors influence your choice in selecting a mobile phone?"
                ),
                "user_profile_attributes": {
                    "age_levels": [25, 30],
                    "income_levels": [50000, 75000],
                    "education_levels": ["Bachelors Degree"],
                    "gender_levels": ["Male"],
                },
                "attributes": [
                    "price",
                    "operating_system",
                    "battery_life",
                    "camera_quality",
                    "storage_capacity",
                ],
                "llm_model": "gpt4",
            }
        }


class ConstraintRequest(BaseModel):
    why_prompt: str
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]]
    attributes: List[str]
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors influence your choice in selecting a mobile phone?"
                ),
                "user_profile_attributes": {
                    "age_levels": [25, 30],
                    "income_levels": [50000, 75000],
                    "education_levels": ["Bachelors Degree"],
                    "gender_levels": ["Male"],
                },
                "attributes": [
                    "price",
                    "operating_system",
                    "battery_life",
                    "camera_quality",
                    "storage_capacity",
                ],
                "llm_model": "gpt4",
            }
        }


class HistoryResponse(BaseModel):
    history: Dict[str, str]

    class Config:
        json_schema_extra = {
            "example": {
                "history": {
                    "Previous Phone Brands": (
                        "The specific mobile phone brands the user has used in the past"
                    ),
                    "Previous Purchase Channels": (
                        "The places or methods through which the user typically buys"
                        " mobile phones (e.g., online, retail stores)"
                    ),
                    "Previous Purchase Frequency": (
                        "How often the user typically buys new mobile phones"
                    ),
                    "Price Sensitivity": (
                        "The user's historical responsiveness to mobile phone prices"
                        " and willingness to pay"
                    ),
                    "Feature Preferences": (
                        "The specific features the user has prioritized in past mobile"
                        " phone purchases"
                    ),
                    "Brand Loyalty": (
                        "The user's tendency to stick with particular mobile phone"
                        " brands over time"
                    ),
                }
            }
        }


class ConstraintResponse(BaseModel):
    constraints: Dict[str, str]

    class Config:
        json_schema_extra = {
            "example": {
                "constraints": {
                    "Budget": (
                        "Financial limitations affecting mobile phone purchase"
                        " decisions"
                    ),
                    "Brand Loyalty": (
                        "User's tendency to stick with familiar or preferred brands"
                    ),
                    "User Reviews": (
                        "Influence of other users' experiences and feedback"
                    ),
                    "Warranty": (
                        "Availability and terms of warranty for the mobile phone"
                    ),
                    "After-Sales Service": (
                        "Quality and accessibility of customer support and service"
                    ),
                    "Availability": (
                        "Ease of finding and purchasing the desired mobile phone model"
                    ),
                    "Technological Compatibility": (
                        "Compatibility with existing devices and software"
                    ),
                }
            }
        }


class QuestionRequest(BaseModel):
    why_prompt: str
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]]
    model_dimensions: Dict[str, List[str]]
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH

    class Config:
        protected_namespaces = ("settings_",)
        json_schema_extra = {
            "example": {
                "why_prompt": (
                    "What factors influence your choice in selecting a mobile phone?"
                ),
                "user_profile_attributes": {
                    "age_levels": [25, 30],
                    "income_levels": [50000, 75000],
                    "education_levels": ["Bachelors Degree"],
                },
                "model_dimensions": {
                    "history": ["Previous Phone Brands", "Price Sensitivity"],
                    "constraints": ["User Review", "Budget"],
                },
                "llm_model": "gpt4",
            }
        }


class SampleResponse(BaseModel):
    response: str

    class Config:
        json_schema_extra = {"example": {"response": "Always the same brand"}}


class Question(BaseModel):
    question: str
    sample_responses: List[SampleResponse]

    class Config:
        json_schema_extra = {
            "example": {
                "question": (
                    "How often have you purchased the same brand of mobile phone in the"
                    " past?"
                ),
                "sample_responses": [
                    {"response": "Always the same brand"},
                    {"response": "Mostly the same brand"},
                    {"response": "Occasionally the same brand"},
                    {"response": "Rarely the same brand"},
                    {"response": "Never the same brand"},
                ],
            }
        }


class QuestionList(RootModel):
    root: List[Question] = Field(..., min_length=2, max_length=2)


class QuestionResponse(RootModel):
    root: Dict[Literal["history", "constraints"], Dict[str, QuestionList]]

    class Config:
        json_schema_extra = {
            "example": {
                "history": {
                    "Previous Phone Brands": [
                        {
                            "question": (
                                "How often have you purchased the same brand of mobile"
                                " phone in the past?"
                            ),
                            "sample_responses": [
                                {"response": "Always"},
                                {"response": "Mostly"},
                                {"response": "Occasionally"},
                                {"response": "Rarely"},
                                {"response": "Never"},
                            ],
                        },
                        {
                            "question": (
                                "How satisfied have you been with the mobile phone"
                                " brands you have used previously?"
                            ),
                            "sample_responses": [
                                {"response": "Extremely satisfied"},
                                {"response": "Very satisfied"},
                                {"response": "Moderately satisfied"},
                                {"response": "Slightly satisfied"},
                                {"response": "Not satisfied at all"},
                            ],
                        },
                    ],
                    "Price Sensitivity": [
                        {
                            "question": (
                                "How often have you chosen a mobile phone based on its"
                                " price in the past?"
                            ),
                            "sample_responses": [
                                {"response": "Always"},
                                {"response": "Most of the time"},
                                {"response": "Sometimes"},
                                {"response": "Rarely"},
                                {"response": "Never"},
                            ],
                        },
                        {
                            "question": (
                                "When you last purchased a mobile phone, how"
                                " significant was the price in your decision?"
                            ),
                            "sample_responses": [
                                {"response": "Extremely significant"},
                                {"response": "Very significant"},
                                {"response": "Moderately significant"},
                                {"response": "Slightly significant"},
                                {"response": "Not significant at all"},
                            ],
                        },
                    ],
                },
                "constraints": {
                    "User Review": [
                        {
                            "question": (
                                "How often do you rely on user reviews when selecting a"
                                " mobile phone?"
                            ),
                            "sample_responses": [
                                {"response": "Always"},
                                {"response": "Often"},
                                {"response": "Sometimes"},
                                {"response": "Rarely"},
                                {"response": "Never"},
                            ],
                        },
                        {
                            "question": (
                                "To what extent do negative user reviews influence your"
                                " decision to avoid purchasing a particular mobile"
                                " phone?"
                            ),
                            "sample_responses": [
                                {"response": "Significantly"},
                                {"response": "Moderately"},
                                {"response": "Somewhat"},
                                {"response": "Slightly"},
                                {"response": "Not at all"},
                            ],
                        },
                    ],
                    "Budget": [
                        {
                            "question": (
                                "How much does your budget limit your choice when"
                                " selecting a mobile phone?"
                            ),
                            "sample_responses": [
                                {
                                    "response": (
                                        "It is the primary factor, I only consider"
                                        " phones within my budget"
                                    )
                                },
                                {
                                    "response": (
                                        "It is a major factor, but I might stretch my"
                                        " budget for a good deal"
                                    )
                                },
                                {
                                    "response": (
                                        "It is somewhat important, but I consider other"
                                        " factors equally"
                                    )
                                },
                                {
                                    "response": (
                                        "It is a minor factor, I prioritize features"
                                        " over budget"
                                    )
                                },
                                {
                                    "response": (
                                        "It does not limit my choice at all, I choose"
                                        " based on features and brand"
                                    )
                                },
                            ],
                        },
                        {
                            "question": (
                                "How often do you find yourself compromising on"
                                " features due to budget constraints when selecting a"
                                " mobile phone?"
                            ),
                            "sample_responses": [
                                {"response": "Always"},
                                {"response": "Frequently"},
                                {"response": "Occasionally"},
                                {"response": "Rarely"},
                                {"response": "Never"},
                            ],
                        },
                    ],
                },
            }
        }


class MotivationRequest(BaseModel):
    why_prompt: str
    user_profile_attributes: Dict[str, Union[List[str], List[int], List[float]]]
    attributes: List[str]
    llm_model: Optional[LLMModel] = LLMModel.GCP_GEMINIFLASH
    country: str
    num_questions: int

    class Config:
        json_schema_extra = {
            "example": {
                "attributes": [
                    "Range of Car",
                    "Price of Car",
                    "Battery Charging Time",
                    "Maintenance Costs",
                    "Environmental Impact",
                    "Social Status",
                    "Government Incentives",
                    "Initial Purchase Cost",
                    "Long-term Savings",
                    "Brand Image",
                    "Technology Level",
                ],
                "country": "USA",
                "why_prompt": "Why are you considering purchasing an electric vehicle?",
                "user_profile_attributes": {
                    "age_levels": [35, 45],
                    "income_levels": [100000, 150000],
                    "education_levels": ["Masters Degree"],
                    "gender_levels": ["Female", "Male"],
                    "race": ["Hispanic", "White", "African American"],
                },
                "num_questions": 4,
                "country": "USA",
                "llm_model": "gpt4",
            }
        }


class MotivationResponse(BaseModel):
    motivation: Dict

    class Config:
        json_schema_extra = {
            "example": {
                "motivation": {
                    "Safety Consciousness": [
                        "How important for you are bike lanes in your town?",
                        "How often do you wear your seatbelt when driving?",
                    ],
                    "Cost Savings": [
                        "How important is saving money on fuel costs to you?",
                        (
                            "Would you consider a higher initial cost if it means lower"
                            " long-term savings?"
                        ),
                    ],
                },
            }
        }


class PersonasResponse(BaseModel):
    personas: list[dict] = Field(
        ...,
        description=(
            "List of personas, each represented as a dictionary with dynamic attributes"
            " (e.g., name, age_range, personality_traits)"
        ),
    )

    class Config:
        json_schema_extra = {
            "example": {
                "personas": [
                    {
                        "name": "Sophia",
                        "age_range": "18-25",
                        "gender": "Female",
                        "location": "Urban areas",
                        "personality_traits": [
                            "analytical",
                            "tech-savvy",
                            "socially responsible",
                            "driven by authenticity and transparency",
                        ],
                        "pain_points": [
                            "balancing ethical consumption with budget constraints",
                            "finding authentic and sustainable luxury brands",
                            "dealing with inauthentic marketing and greenwashing",
                        ],
                        "behaviors": [
                            "actively researches and evaluates brands online",
                            "engages with social media communities",
                            "values influencer insights",
                            (
                                "consistently seeks transparency and authenticity in"
                                " brand communications"
                            ),
                        ],
                        "goals": [
                            "maintain a health-conscious and sustainable lifestyle",
                            "make ethically responsible purchasing decisions",
                            "inspire others to adopt similar values",
                            "balance luxury with ethical consumption",
                        ],
                    },
                    {
                        "name": "Jordan",
                        "age_range": "18-25",
                        "gender": "Female",
                        "location": "Urban areas",
                        "personality_traits": [
                            "creative",
                            "explorer",
                            "values sustainability",
                            "authenticity",
                            "ethics",
                        ],
                        "pain_points": [
                            "avoiding greenwashing",
                            "finding brands that offer personalized experiences",
                        ],
                        "behaviors": [
                            "trusts peer and influencer recommendations",
                            "does own research",
                            "aims to inspire ethical consumption and shared values",
                        ],
                        "goals": [
                            "inspire ethical consumption",
                            "find brands that align with personal values",
                        ],
                    },
                ]
            }
        }
