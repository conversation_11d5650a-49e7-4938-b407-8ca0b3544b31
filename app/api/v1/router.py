from fastapi import APIRouter

from app.api.v1.endpoints import (
    attributes_levels,
    copilot,
    experiments,
    human_baselines,
    market_simulator,
    notifications,
    payments,
    personas,
    population,
    runs,
    tasks,
    traits,
)

router = APIRouter()


router.include_router(
    experiments.router, prefix="/experiments", tags=["v1.experiments"]
)
router.include_router(tasks.router, prefix="/tasks", tags=["v1.tasks"])
router.include_router(runs.router, prefix="/runs", tags=["v1.runs"])
router.include_router(attributes_levels.router, tags=["v1.attributes_levels"])
router.include_router(personas.router, prefix="/personas", tags=["v1.personas"])
router.include_router(traits.router, prefix="/traits", tags=["v1.traits"])
router.include_router(payments.router, prefix="/payments", tags=["v1.payments"])
router.include_router(copilot.router, prefix="/copilot", tags=["v1.copilot"])
router.include_router(
    human_baselines.router, prefix="/human-baselines", tags=["v1.human-baselines"]
)
router.include_router(
    market_simulator.router, prefix="/market-simulator", tags=["v1.market-simulator"]
)
router.include_router(
    notifications.router, prefix="/notifications", tags=["v1.notifications"]
)
router.include_router(population.router, prefix="/populations", tags=["v1.populations"])
