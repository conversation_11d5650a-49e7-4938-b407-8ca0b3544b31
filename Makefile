.PHONY: remote-dev startup debug test clean update prerequisites setup docs help

remote-dev:
	@./scripts/ssh_remote-dev.sh

startup:
	@./scripts/startup.sh

debug:
	@./scripts/startup.sh --debug

test:
	@./scripts/test.sh

clean:
	@./scripts/clean.sh

update:
	@./scripts/update.sh

prerequisites:
	@./scripts/install_prerequisites.sh

setup:
	@./scripts/setup.sh

docs:
	mkdocs serve

.DEFAULT_GOAL := help
help:
	@echo "Available commands:"
	@echo "  remote-dev               - SSH into remote-dev machine"
	@echo "  startup                  - Run the app"
	@echo "  debug                    - Run the app in debug mode"
	@echo "  test                     - Run tests with coverage"
	@echo "  clean                    - Clean up cache and temporary files"
	@echo "  update                   - Update dependencies"
	@echo "  prerequisites            - Install/verify prerequisites"
	@echo "  setup                    - Set up development environment"
	@echo "  docs                     - Serve documentation locally"
