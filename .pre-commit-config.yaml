default_language_version:
  python: python3.11
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: mixed-line-ending
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
        additional_dependencies: [ 'click==8.0.4' ]
        args: [ "--config=pyproject.toml" ]
  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        additional_dependencies: [ 'flake8-pyproject==1.1.0' ]
