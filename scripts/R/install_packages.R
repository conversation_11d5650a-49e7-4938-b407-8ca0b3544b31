args <- commandArgs(trailingOnly = TRUE)
packages_file <- ifelse(length(args) > 0, args[1], "r-packages.txt")

user_lib <- Sys.getenv("R_LIBS_USER")
dir.create(user_lib, recursive = TRUE, showWarnings = FALSE)
.libPaths(user_lib)

packages <- readLines(packages_file)

# Check which packages are not installed
missing <- packages[!sapply(packages, require, character.only = TRUE, quietly = TRUE)]

if (length(missing) > 0) {
    cat("Installing missing packages:", paste(missing, collapse = ", "), "\n")
    install.packages(missing, repos = "https://cloud.r-project.org/", lib = user_lib)
} else {
    cat("All packages are already installed.\n")
}
