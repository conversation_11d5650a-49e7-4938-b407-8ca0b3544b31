args <- commandArgs(trailingOnly = TRUE)
packages_file <- ifelse(length(args) > 0, args[1], "r-packages.txt")

packages <- readLines(packages_file)
missing <- packages[!sapply(packages, require, character.only = TRUE)]

if (length(missing)) {
    cat("❌ Missing packages:\n", paste(missing, collapse = ", "), "\n")
    quit(status = 1)
} else {
    cat("✅ All packages are successfully installed.\n")
    quit(status = 0)
}
