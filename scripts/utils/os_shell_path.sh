#!/bin/bash

# Supported platforms for installation: macOS, debian/ubuntu
detect_platform() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macOS"
        return 0
    elif [[ -f /etc/os-release ]]; then
        source /etc/os-release
        if [[ "$ID" == "debian" || "$ID" == "ubuntu" ]]; then
            echo "debian"
            return 0
        fi
    fi

    echo "unsupported"
    return 1
}

# Detect appropriate shell config file for updating $PATH
detect_shell_config_file() {
    case "$SHELL" in
        */zsh)
            echo "$HOME/.zshrc"
            ;;
        */bash)
            if [[ "$(uname)" == "Darwin" ]]; then
                echo "$HOME/.bash_profile"
            else
                echo "$HOME/.bashrc"
            fi
            ;;
        *)
            echo "$HOME/.profile"  # Fallback
            ;;
    esac
}

# For Poetry installation
ensure_local_bin_in_path() {
    local LOCAL_BIN_PATH="$HOME/.local/bin"

    if [[ ":$PATH:" == *":$LOCAL_BIN_PATH:"* ]]; then
        echo "- $LOCAL_BIN_PATH is already in PATH"
        return 0  # Already in PATH
    fi

    EXPORT_CMD='export PATH="$HOME/.local/bin:$PATH"'
    echo "- Adding $EXPORT_CMD update to $CONFIG_FILE"
    echo "$EXPORT_CMD" >> "$CONFIG_FILE"

    # Apply it to the current shell session
    export PATH="$LOCAL_BIN_PATH:$PATH"

    return 1  # PATH was modified
}
