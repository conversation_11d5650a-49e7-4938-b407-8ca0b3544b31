#!/bin/bash
set -e

SCRIPT_PATH="${BASH_SOURCE[0]:-${(%):-%N}}"
SCRIPT_DIR="$(cd "$(dirname $SCRIPT_PATH)" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

if [[ "$OSTYPE" != "linux-gnu" ]]; then
    echo "⚠️  Filebeat installation is only supported for linux systems"
    exit 1
fi

# -----------------
# Main script logic
# -----------------

echo "🔧 Starting filebeat installation/setup..."

if ! command -v filebeat &>/dev/null; then
    # Install Filebeat
    sudo apt-get update -qq
    sudo apt-get install sudo apt-transport-https -y -qq --no-install-recommends

    wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | \
        sudo gpg --dearmor -o /usr/share/keyrings/elastic-keyring.gpg

    echo "deb [signed-by=/usr/share/keyrings/elastic-keyring.gpg] \
https://artifacts.elastic.co/packages/8.x/apt stable main" | \
        sudo tee /etc/apt/sources.list.d/elastic-8.x.list

    sudo apt-get update -qq
    sudo apt-get install filebeat -y -qq --no-install-recommends
fi

if systemctl --version &>/dev/null; then
    sudo systemctl enable filebeat
else
    sudo update-rc.d filebeat defaults 95 10
fi

# Add filebeat configuration
# config, data, logs will be stored in filebeat/
FILEBEAT_DIR="${PROJECT_ROOT}"/filebeat

# Note: Filebeat is not detecting path.config, so /etc/filebeat/filebeat.yml is used
mkdir -p "$FILEBEAT_DIR"
sudo rm -f /etc/filebeat/filebeat.yml
# sudo cp "${PROJECT_ROOT}"/filebeat.yml /etc/filebeat
sudo cp "${PROJECT_ROOT}"/filebeat.yml "$FILEBEAT_DIR"
sudo chmod 644 "${FILEBEAT_DIR}"/filebeat.yml
# sudo chmod 644 /etc/filebeat/filebeat.yml # -rw-r--r--*

mkdir -p "${PROJECT_ROOT}"/filebeat/{data,logs}

# Create application log directories
LOGS_DIR="${PROJECT_ROOT}"/app/logs/elk_logs
mkdir -p "$LOGS_DIR"
touch "${LOGS_DIR}/"{core_logs.json,poc_api_logs.json,main_api_logs.json}
