#!/bin/bash
set -e

SSH_CONFIG="$HOME/.ssh/config"
SSH_HOST_ALIAS="remote-dev-machine"
DEFAULT_HOST=************  # default value
DEFAULT_PORT=8080  # default value

# ---------
# Utilities
# ---------

get_ssh_config_value() {
    local host="$1"
    local key="$2"
    awk -v host="$host" -v key="$key" '
        $1 == "Host" && $2 == host {in_block=1; next}
        in_block && $1 == key {print $2; exit}
        in_block && $1 == "Host" {in_block=0}
    ' ~/.ssh/config
}

# -----------------
# Main script logic
# -----------------

echo -e "🔌 Attempting to connect to $SSH_HOST_ALIAS"

if [[ ! -f "$SSH_CONFIG" ]]; then
  echo "- ❌ SSH config not found at $SSH_CONFIG"
  exit 1
fi

if ! grep -qE "^Host\s+$SSH_HOST_ALIAS" "$SSH_CONFIG"; then
  echo "- ❌ SSH host '$SSH_HOST_ALIAS' not found in $SSH_CONFIG"
  exit 1
fi

echo "- Loading User, Hostname and LocalForward (port) for $SSH_HOST_ALIAS from $SSH_CONFIG..."

REMOTE_USER=$(get_ssh_config_value "$SSH_HOST_ALIAS" "User")
REMOTE_HOST=$(get_ssh_config_value "$SSH_HOST_ALIAS" "Hostname")
PORT=$(get_ssh_config_value "$SSH_HOST_ALIAS" "LocalForward" | cut -d ' ' -f1)

if [[ -z "$REMOTE_USER" ]]; then
    echo "- ❌ Missing required 'User' value in $SSH_CONFIG under $SSH_HOST_ALIAS"
    exit 1
fi
echo "- ✅ User: '$REMOTE_USER' loaded from $SSH_CONFIG"

if [[ -z "$REMOTE_HOST" ]]; then
    echo "- ⚠️  Hostname"
    echo "  - Missing value in $SSH_CONFIG under $SSH_HOST_ALIAS"
    echo "  - Using default Hostname: $DEFAULT_HOST"
    REMOTE_HOST="$DEFAULT_HOST"
else
    echo "- ✅ Hostname: '$REMOTE_HOST' loaded from $SSH_CONFIG"
fi

if [[ -z "$PORT" ]]; then
    echo "- ⚠️  Port"
    echo "  - Missing 'LocalForward' value in $SSH_CONFIG under $SSH_HOST_ALIAS"
    echo "  - Using default Port: $DEFAULT_PORT"
    PORT="$DEFAULT_PORT"
else
    echo "- ✅ Port: $PORT loaded from $SSH_CONFIG"
fi

echo "- SSH connection settings:"
echo "  - REMOTE_USER = $REMOTE_USER"
echo "  - REMOTE_HOST = $REMOTE_HOST"
echo "  - PORT        = $PORT"

echo "- Attempting to SSH into $REMOTE_USER@$REMOTE_HOST..."
echo -e "- Forwarding local port $PORT to remote localhost:$PORT\n"


# Try SSH with 3s connection timeout, suppress host key prompts
ssh -L "$PORT":localhost:"$PORT" \
    -o ConnectTimeout=5 \
    -o StrictHostKeyChecking=no \
    "$REMOTE_USER@$REMOTE_HOST"

SSH_EXIT_CODE=$?

if [[ $SSH_EXIT_CODE -eq 0 ]]; then
    echo "- SSH session completed successfully."
elif [[ $SSH_EXIT_CODE -eq 255 ]]; then
    echo "- SSH failed to connect (likely permission denied or unreachable)."
    echo "If applicable, please add the dev team to add your \
~/.ssh/id_ed25519.pub to remote dev."
else
    echo "- SSH exited with code $SSH_EXIT_CODE"
fi
