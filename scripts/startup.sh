#!/bin/bash

SCRIPT_PATH="${BASH_SOURCE[0]:-${(%):-%N}}"
SCRIPT_DIR="$(cd "$(dirname $SCRIPT_PATH)" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

SSH_CONFIG="$HOME/.ssh/config"
SSH_HOST_ALIAS="remote-dev-machine"

PORT=8080  # default value
PORT_SET=false
DEBUGGER=false
FILEBEAT=false

source "$SCRIPT_DIR/utils/os_shell_path.sh"
PLATFORM=$(detect_platform)

# ---------
# Utilities
# ---------

get_ssh_config_value() {
    local host="$1"
    local key="$2"
    awk -v host="$host" -v key="$key" '
        $1 == "Host" && $2 == host {in_block=1; next}
        in_block && $1 == key {print $2; exit}
        in_block && $1 == "Host" {in_block=0}
    ' ~/.ssh/config
}

# -----------------
# Main script logic
# -----------------

echo "🚀 Starting App..."

# Parse arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --port)
            PORT="$2"
            PORT_SET=true
            shift 2
            ;;
        --filebeat)
            FILEBEAT=true
            shift
            ;;
        --debug)
            DEBUGGER=true
            shift
            ;;
        --help)
            echo "Usage: $0 [--port PORT] [--filebeat] [--debug]"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Getting LocalForward port from SSH_CONFIG
if [[ "$PORT_SET" == false ]] && [[ -f "$SSH_CONFIG" ]]; then
    if grep -qE "^Host[[:space:]]+$SSH_HOST_ALIAS" "$SSH_CONFIG"; then
        VALUE=$(get_ssh_config_value "$SSH_HOST_ALIAS" "LocalForward" | cut -d ' ' -f1)
        if [[ -n "$VALUE" ]]; then
            PORT="$VALUE"
            echo "- Using port $PORT from $SSH_CONFIG (remote development)"
        fi
    fi
fi

CMD="uvicorn app.main:app \
  --host 0.0.0.0 \
  --reload \
  --port $PORT \
  --timeout-keep-alive 60"


if [[ "$DEBUGGER" == true ]]; then
    echo "- 👉 Using debugpy. Please attach to debugger listening on port 5678."
    CMD="python ./tmp/debugpy --wait-for-client --listen 0.0.0.0:5678 -m $CMD"
fi

if [[ "$FILEBEAT" == true ]]; then
    if ! "${SCRIPT_DIR}/setup_filebeat.sh"; then
        echo "- ❌ filebeat setup failed. Please run without --filebeat."
        exit 1
    fi

    echo "- Starting filebeat in the background..."

    FILEBEAT_DIR="${PROJECT_ROOT}/filebeat"

    # TODO: filebeat is not reading filebeat.yml:
    # check with `-e` in the following command and removing --path.*
    filebeat -c "filebeat.yml" \
        --path.config "${FILEBEAT_DIR}" \
        --path.data "${FILEBEAT_DIR}/data" \
        --path.logs "${FILEBEAT_DIR}/logs" &
fi

doppler run -- poetry run bash -c "$CMD" || \
    echo -e "- ⚠️  Doppler failed.\n  - 👉 Make sure you have \
activated your environment by running 'source scripts/activate_env.sh'"
