#!/bin/bash
set -e

SCRIPT_PATH="${BASH_SOURCE[0]:-${(%):-%N}}"
SCRIPT_DIR="$(cd "$(dirname $SCRIPT_PATH)" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APT_UPDATE=true  # run apt-update when installing debian packages
POETRY_MSG=""
# default Python version
TARGET_PYTHON_VERSION="3.11"
PYTHON_VERSION="$TARGET_PYTHON_VERSION"

source "$SCRIPT_DIR/utils/os_shell_path.sh"
PLATFORM=$(detect_platform)
CONFIG_FILE=$(detect_shell_config_file)

if [[ "$PLATFORM" == "debian" ]]; then
    source /etc/os-release
fi

# ---------
# Utilities
# ---------

# Check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

## Brew verification and installation (macOS)

install_brew() {
    url="https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh"
    /bin/bash -c "$(curl -fsSL "$url")"

    echo "- Adding homebrew exports to $CONFIG_FILE"

    # add exports to "$CONFIG_FILE" and variables to current shell
    if [[ -d "/opt/homebrew/bin" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> "$CONFIG_FILE"
        eval "$(/opt/homebrew/bin/brew shellenv)"
    elif [[ -d "/usr/local/bin" ]]; then
        echo 'eval "$(/usr/local/bin/brew shellenv)"' >> "$CONFIG_FILE"
        eval "$(/usr/local/bin/brew shellenv)"
    fi
}

verify_install_brew() {
    if ! command_exists brew; then
        echo "- Brew is not installed"
        echo "- 📝 Installing Brew"
        install_brew

        if ! command_exists brew; then
            echo "- ❌ Failed to install brew. \
            Please install manually."
            exit 1
        fi
    fi
}

## apt/brew general package verification and installation

install_apt_pkgs() {
    local packages="$1"

    if [[ "$APT_UPDATE" == true ]]; then
        sudo apt-get update -qq
        APT_UPDATE=false
    fi
    sudo apt-get install -y -qq --no-install-recommends $packages
}

install_brew_pkgs() {
    local packages="$1"

    HOMEBREW_NO_INTERACTIVE=1 \
    HOMEBREW_NO_ENV_HINTS=1 \
    HOMEBREW_NO_ANALYTICS=1 \
    HOMEBREW_NO_AUTO_UPDATE=1 \
    brew install $packages --quiet
}

install_pkgs() {
    local apt_pkgs="$1"
    # same as apt_pkgs if "$2" is not provided
    local brew_pkgs="${2:-$1}"

    case "$PLATFORM" in
        debian)
            install_apt_pkgs "$apt_pkgs"
            ;;
        macOS)
            verify_install_brew
            install_brew_pkgs "$brew_pkgs"
            ;;
        *)
            echo "- ❌ Automatic installation is not supported for this OS."
            exit 1
            ;;
    esac
}

verify_install_pkg() {
    local package="$1"

    if ! command_exists "$package"; then
        echo "- $package is not installed"
        echo "- 📝 Installing $package"
        install_pkgs "$package"
    fi
}

verify_install_cmd_pkg(){
    local command="$1"

    verify_install_pkg "$command"

    if ! command_exists "$command"; then
        echo "- ❌ Failed to install $command package"
        exit 1
    fi
}

### prerequisite-specific verification and installation functions

## Python
check_python_version() {
    local required_version="$TARGET_PYTHON_VERSION"
    local current_version=$(
        python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))'
    )
    local comparison=$(
        printf '%s\n' "$required_version" "$current_version" | sort -V | head -n1
    )

    if [[ "$comparison" != "$required_version" ]]; then
        echo "- Default python version $current_version < $required_version"
        return 1
    fi
    echo "- Default python version $current_version >= $required_version"

    return 0
}

check_python_deps() {
    if [[ "$PLATFORM" != "debian" ]]; then
        return 0
    fi

    local version="$1"

    pkgs=()
    # for suffix in "" "-dev" "-venv"; do
    for suffix in "" "-venv"; do
        pkgs+=("python${version}${suffix}")
    done

    for pkg in "${pkgs[@]}"; do
        if ! dpkg -s "$pkg" >/dev/null 2>&1; then
            echo "- Package $pkg is not installed"
            return 1
        fi
    done

    return 0
}

verify_install_python(){
    if command_exists python3; then
        current_version=$(
            python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))'
        )
        if check_python_version && check_python_deps "$current_version"; then
            PYTHON_VERSION="$current_version"  # overwrite default Python version
            return 0
        fi
        echo "- Checking python${PYTHON_VERSION} installation"
        if check_python_deps "$PYTHON_VERSION"; then
            return 0
        fi
    fi

    echo "- 📝 Installing Python $PYTHON_VERSION."

    case "$PLATFORM" in
        debian)
            install_apt_pkgs "software-properties-common"
            if [[ "$ID" == "ubuntu" ]]; then
                sudo add-apt-repository -y ppa:deadsnakes/ppa
            fi
            sudo apt-get update -qq
            # Get latest version of python${PYTHON_VERSION} from apt cache
            PYVER=$(
                apt-cache policy python${PYTHON_VERSION} | \
                grep Candidate | awk '{print $2}'
            )

            # Install all matching versions of python${PYTHON_VERSION} components
            packages="python${PYTHON_VERSION}=${PYVER} \
                python${PYTHON_VERSION}-dev=${PYVER} \
                python${PYTHON_VERSION}-venv=${PYVER} \
                libpython${PYTHON_VERSION}-dev=${PYVER} \
                libpython${PYTHON_VERSION}=${PYVER}"

            install_apt_pkgs "$packages"
            ;;
        macOS)
            verify_install_brew
            install_brew_pkgs "python@${PYTHON_VERSION}"
            ;;
        *)
            echo "- ❌ Automatic installation is not supported for this OS."
            exit 1
            ;;
    esac
    if ! command_exists python${PYTHON_VERSION}; then
        echo "- ❌ Failed to install python${PYTHON_VERSION}. Please install manually."
        exit 1
    fi
}

## Doppler
verify_install_doppler(){
    if ! command_exists doppler; then
        echo "- Doppler is not installed"
        echo "- 📝 Installing Doppler..."

        echo "  - Verifying dependencies: curl, gnupg"
        verify_install_cmd_pkg "curl"
        verify_install_pkg "gnupg"
        ( curl -Ls --tlsv1.2 --proto "=https" --retry 3 \
            https://cli.doppler.com/install.sh \
        || wget -t 3 -qO- https://cli.doppler.com/install.sh
        ) | sudo sh

        if ! command_exists doppler; then
            echo "- ❌ Failed to install Doppler"
            exit 1
        fi
    fi
}

verify_install_poetry() {
    if ! command_exists poetry; then
        echo "- Poetry is not installed"
        echo "- 📝 Installing Poetry..."

        echo "  - Verifying dependencies: curl"
        verify_install_cmd_pkg "curl"
        curl -sSL https://install.python-poetry.org | python${PYTHON_VERSION} -
        if ! ensure_local_bin_in_path; then  # add $HOME/.local/bin to $PATH
            POETRY_MSG="👉 Make sure you run 'source $CONFIG_FILE' \
to add Poetry to your PATH."  # .local/bin was added to $PATH
        fi

        if ! command_exists poetry; then
            echo "- ❌ Failed to install Poetry"
            exit 1
        fi
    fi
}

## R
verify_install_r() {
    if ! command_exists R; then
        echo "- R is not installed"
        echo "- 📝 Installing R..."

        source /etc/os-release

        case "$PLATFORM" in
            debian)
                if [[ "$ID" == "ubuntu" ]]; then
                    wget -qO- https://cloud.r-project.org/bin/linux/ubuntu/marutter_pubkey.asc | \
                        gpg --dearmor | \
                        sudo tee /etc/apt/trusted.gpg.d/cran_ubuntu_key.gpg > /dev/null

                    REPO="deb https://cloud.r-project.org/bin/linux/ubuntu $VERSION_CODENAME-cran40/"
                    echo "$REPO" | sudo tee /etc/apt/sources.list.d/cran.list > /dev/null

                elif [[ "$ID" == "debian" ]]; then
                    gpg --keyserver keyserver.ubuntu.com \
                        --recv-key '95C0FAF38DB3CCAD0C080A7BDC78B2DDEABC47B7'

                    gpg --armor --export '95C0FAF38DB3CCAD0C080A7BDC78B2DDEABC47B7' | \
                        sudo tee /etc/apt/trusted.gpg.d/cran_debian_key.asc > /dev/null

                    REPO="deb https://cloud.r-project.org/bin/linux/debian $VERSION_CODENAME-cran40/"
                    echo "$REPO" | sudo tee /etc/apt/sources.list.d/cran.list > /dev/null
                else
                    echo "- ❌ Automatic installation is not supported for OS: $ID"
                    exit 1
                fi

                sudo apt-get update -qq

                install_apt_pkgs "r-base"
                ;;
            macOS)
                verify_install_brew
                install_brew_pkgs "r"
                ;;
            *)
                echo "- ❌ Automatic installation is not supported for this OS."
                exit 1
                ;;
        esac
        if ! command_exists R; then
            echo "- ❌ Failed to install R. Please install manually."
            exit 1
        fi
    fi
}

# -----------------
# Main script logic
# -----------------

cd "$PROJECT_ROOT"

# Identify Debian/Ubuntu or macOS platform
case "$PLATFORM" in
    debian)
        echo "💾 Debian/Ubuntu system detected"
        ;;
    macOS)
        echo "🍎 macOS system detected"
        ;;
    *)
        echo "Automatic installation is not supported for this OS"
        exit 1
        ;;
esac

echo "🔍 Installing/verifying prerequisites..."
echo "- [Python ${TARGET_PYTHON_VERSION}+, Git, Doppler, Poetry, R, Docker (optional)]"

# Python
verify_install_python
echo "- ✅ Python ${TARGET_PYTHON_VERSION}+ is installed"

# Git
verify_install_cmd_pkg "git"
echo "- ✅ Git is installed"

# Doppler
verify_install_doppler
echo "- ✅ Doppler CLI is installed"

# Poetry
verify_install_poetry
poetry env use ${PYTHON_VERSION}
echo "- ✅ Poetry is installed"

# R
verify_install_r
echo "- ✅ R is installed"

# Docker (optional)
if command_exists docker; then
    echo "- ✅ Docker is installed (optional)"
else
    echo "- ⚠️  Docker is not installed (optional)"
fi

echo "- ✨ All required prerequisites are satisfied!"

if [[ -n "$POETRY_MSG" ]]; then
    echo "- $POETRY_MSG"
fi
