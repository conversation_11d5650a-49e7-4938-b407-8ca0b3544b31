#!/bin/bash
set -e

SCRIPT_PATH="${BASH_SOURCE[0]:-${(%):-%N}}"
SCRIPT_DIR="$(cd "$(dirname $SCRIPT_PATH)" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
R_DIR="$SCRIPT_DIR/R"

source "$SCRIPT_DIR/utils/os_shell_path.sh"
PLATFORM=$(detect_platform)
CONFIG_FILE=$(detect_shell_config_file)
POETRY_MSG=""

# ---------
# Utilities
# ---------

install_r_deps() {
    if [[ "$PLATFORM" == "debian" ]]; then
        echo "- Checking R build dependencies"

        # List of required system packages for R build support
        r_build_deps=(
            build-essential gfortran libcurl4-openssl-dev libssl-dev libxml2-dev
            libpng-dev libjpeg-dev zlib1g-dev libicu-dev libgmp-dev libgsl-dev
            libtiff5-dev libreadline-dev libx11-dev libxt-dev liblapack-dev
            libblas-dev libglpk-dev
        )

        # Find which packages are missing
        missing=()
        for pkg in "${r_build_deps[@]}"; do
            if ! dpkg -s "$pkg" &>/dev/null; then
                missing+=("$pkg")
            fi
        done

        # Install missing packages, if any
        if [[ ${#missing[@]} -eq 0 ]]; then
            echo "- All R build dependencies are already installed."
        else
            echo "- Installing missing R build dependencies: ${missing[*]}"
            sudo apt update
            sudo apt install -y "${missing[@]}"
        fi
    fi
}

install_r_packages() {
    echo "- Installing R packages..."
    Rscript "$R_DIR"/install_packages.R "$R_DIR"/r-packages.txt
}

check_r_packages() {
    echo "- Verifying R packages installation..."
    Rscript "$R_DIR"/check_packages.R "$R_DIR"/r-packages.txt
}

# -----------------
# Main script logic
# -----------------

cd "$PROJECT_ROOT"

echo "🔧 Starting installation setup"

echo ''
if ! "$SCRIPT_DIR/install_prerequisites.sh"; then
    echo "- ❌ Prerequisites check failed. Please install missing requirements."
    exit 1
fi

# ensure poetry bin is in PATH
if ! ensure_local_bin_in_path; then
    POETRY_MSG="👉 Make sure you run 'source $CONFIG_FILE' \
to add Poetry to your PATH."  # .local/bin was added to $PATH
fi

echo ''
if ! source "$SCRIPT_DIR/activate_env.sh"; then
    echo "- ❌ Environment activation failed."
    exit 1
fi

echo ''
echo "⏳ Installing poetry dependencies..."
export PYTHON_KEYRING_BACKEND=keyring.backends.fail.Keyring
poetry run poetry install

# # TODO: consider adding pre-commit hooks; right now, pre-commit is not in poetry.lock
# echo -e "\n🔧 Setting up pre-commit hooks..."
# poetry add --dev pre-commit
# poetry run pre-commit install

echo ''
echo "🔍 Verifying Doppler token..."

if [ -z "$DOPPLER_TOKEN" ]; then
    echo "- ⚠️  DOPPLER_TOKEN is not set"
    echo "- 📝 Please set your Doppler token before running the application"
    echo "  You can get this from the development team"
fi
echo "  - ✅ Doppler token found"

# ----------------------
# R package installation
# ----------------------

INSTALL_R=false

echo ''
echo "🔍 Verifying R packages..."

if check_r_packages > /dev/null 2>&1; then
    echo "  - ✅ R environment is ready!"
else
    read -p "- Would you like to install the missing R packages? [y/N]: " confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        INSTALL_R=true
    fi
fi

if [[ "$INSTALL_R" == true ]]; then
    install_r_deps
    install_r_packages

    if check_r_packages > /dev/null 2>&1; then
        echo "- ✅ R environment is ready!"
    else
        echo "- ⚠️  Some R packages are still missing."
        echo "- 👉 Try doing a manual installation of R."
        exit 1
    fi
fi

echo "  - ✨ Setup complete!"

if [[ -n "$POETRY_MSG" ]]; then
    echo "  - $POETRY_MSG"
fi

echo "
- Next steps:
    1. Run 'make docs' to view the documentation
    2. Run 'make format' and 'make lint' before committing changes
    3. Use 'make help' to see all available commands
- For more  information, check the documentation in docs/"
