#!/bin/bash

SCRIPT_PATH="${BASH_SOURCE[0]:-${(%):-%N}}"
SCRIPT_DIR="$(cd "$(dirname $SCRIPT_PATH)" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="${PROJECT_ROOT}/.env"

# ---------
# Utilities
# ---------

clean_field() {
    # strip out inline comments; trim whitespaces
    echo "$1" |
    sed -E 's/\s+#.*$//' |
    sed -E 's/^[[:space:]]+|[[:space:]]+$//g'
}

# -----------------
# Main script logic
# -----------------

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "❌ This script must be sourced, not executed."
    echo "👉 Use: source ${BASH_SOURCE[0]}"
    exit 1
fi

cd "$PROJECT_ROOT"

echo "🔧 Activating the dev environment"

echo "- Setting up the poetry env"
poetry_env_path=$(poetry run poetry env info -p 2>/dev/null)

if [[ -n "$poetry_env_path" ]]; then
    source "$poetry_env_path"/bin/activate
else
    echo "- ❌ No Poetry environment found. \
Make sure you have added poetry to your PATH."
    return 1
fi

echo "- Exporting variables from .env"

if [[ ! -f "$ENV_FILE" ]]; then
    echo "- ❌ No .env file detected. Please copy and update from .env.example."
    return 1
fi

while IFS='=' read -r key value; do
    # Skip empty keys and commented lines
    if [[ -n "$key" && "$key" != \#* ]]; then

        key=$(clean_field "$key")
        value=$(clean_field "$value")

        # Check if value is empty or a placeholder like <...>
        if [[ -z "$value" || $(echo "$value" | grep -E "^<.*>$") ]]; then
            continue  # ignore key,value pair
        fi

        export "$key=$value"
        echo "  - $key=$value"
    fi
done < "$ENV_FILE"

echo "- ✨ The environment is set!"
